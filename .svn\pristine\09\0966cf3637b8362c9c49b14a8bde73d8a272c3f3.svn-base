{% load iclock_tags %}
{% load i18n %}
{% autoescape off %}
<div id="id_form">
    <form method="post" id="id_edit_form" enctype="multipart/form-data">
        <div class="left"><table>
            <tr><th></th>
             <td>
                <span style="display:none" id="id_span_title">
                    {% if add %}{% trans "Add" %}  {% else %}{% trans "Edit" %} {% endif %} {{ dataOpt.verbose_name|escape }}
                </span>
             </td>
            </tr>
            <tr>
                <th><font color='red'>*</font>{{ form.code.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.code.as_widget }}</td>
                <th><font color="red">*</font>{{ form.name.label_tag }}</th>
                <td style='vertical-align:top;'> {{ form.name.as_widget }}</td>
            </tr>
            <tr>
                <th><font color='red'>*</font>{{ form.pos_time.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.pos_time.as_widget }}</td>
                <th><font color="red">*</font>{{ form.discount.label_tag }}</th>
                <td style='vertical-align:top;'> {{ form.discount.as_widget }}
                    <span><i class='icon iconfont icon-xiaoxi' title='{{form.discount.help_text}}'></i></span> </td>
            </tr>

            <tr>
                <th><font color="red">*</font>{{ form.date_max_money.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.date_max_money.as_widget }}</td>
                <th><font color="red">*</font>{{ form.date_max_count.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.date_max_count.as_widget }}</td>
            </tr>

            <tr>
                <th><font color="red">*</font>{{ form.per_max_money.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.per_max_money.as_widget }}</td>
            </tr>

            <tr>
                <th><font color="red">*</font>{{ form.meal_max_money.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.meal_max_money.as_widget }}</td>
                <th><font color="red">*</font>{{ form.meal_max_count.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.meal_max_count.as_widget }}</td>
            </tr>
            <tr>
                <th><font color="red">*</font>{{ form.less_money.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.less_money.as_widget }}</td>
                <th><font color="red">*</font>{{ form.max_money.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.max_money.as_widget }}</td>
            </tr>

            <tr>
                <th><font color="red">*</font>{{ form.use_date.label_tag }}</th>
                <td style='vertical-align:top;'>{{ form.use_date.as_widget }}</td>
                {% if "POS_ID"|filter_config_option %}
                    {% if "iapp_book"|filter_config_option_book %}
                        {{form.check_book_dinner|field_as_td_h:1}}
                    {% endif %}
                {% endif %}
            </tr>
            <tr>
                <th> {{ form.remark.label_tag }} </th>
                <td style='vertical-align:top;'> {{ form.remark.as_widget }} </td>
            </tr>

        </table></div>
    </form>
    <div class="left" id="device_sign"></div>
    {% if "POS_ID"|filter_config_option %}
        <div id="tips" style="height:20px;clear:both;color:red">{% trans 'Note: Available meals, equipment, if not checked, the default is all' %}</div>
    {% endif %}
    <div id="id_error" style="height:20px;clear:both;"></div>
{% endautoescape %}

</div>
