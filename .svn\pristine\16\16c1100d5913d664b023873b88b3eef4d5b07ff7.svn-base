{% autoescape off %}
{% load iclock_tags %}
{% load i18n %}
[
{% for item in latest_item_list %}
{"id":"{{ item.SN }}",
"SN":"{% if can_change %}<a class='can_edit'  href='#' onclick='javascript:editclick(\"{{item.SN}}\");'>{{ item.SN }}</a>{% else %}{{ item.SN }}{% endif %}",
"Alias":"{{ item.Alias|default:'' }}",
"Dept":"{{ item.BackupDev|default:'' }}",
"IPAddress":"{{ item.GetDevice.IPAddress|default:'' }}",
"State":"{{ item.getDynState|getStateStr:item.SN }}",
"LastActivity":"{{ item.GetDevice.LastActivity|shortDTime0|default:'' }}",
"PosLogStamp":"{{ item.GetDevice|show_LastLogStamp }}",
"LastLogId":"{{ item.GetDevice|show_LastLogId|default:'' }}",
"LogStamp":"{{ item.GetDevice.LogStamp|transLogStampY|default:"" }}",
"FWVersion":"{{ item.GetDevice.FWVersion|default:'' }}",
"City":"{{ item.City|default:'' }}",
"DeviceName":"{{ item.GetDevice.DeviceName|default:'' }}",
"UserCount":"{{ item|AlarmUserCount|default:'0' }}",
"templateinfo":"{{ item.get_template_count}}",
"TransactionCount":"{{ item|AlamTransaction|default:'0' }}",
"MaxAttLogCount":"{{ item.GetDevice.MaxAttLogCount|default:0 }}",
"MaxUserCount":"{{ item.GetDevice.MaxUserCount|default:0 }}",
"ProductType":"{{ item.GetDevice.ProductType|show_ProductType }}",
"DeptIDS":"<a title='{% trans " attribution details" %}' onclick='getdpts_iclock(\"{{item.SN}}\")' style='color:green;'>{{item|deptShowStr }}</a>",
"Data_":"&nbsp;<a  title='{% trans "Server issues command log" %}' onclick='getLog_device(\"{{item.SN}}\")'style='color:green;'>C</a>&nbsp;<a title='{% trans "original record" %}'style='color:green;' onclick='getTran(\"{{item.SN}}\")'>L</a>&nbsp;<a title='{% trans "Device upload data log" %}' style='color:green;' onclick='getUplog(\"{{item.SN}}\")'>U</a>&nbsp;<a  title='{% trans "List of people on the device" %}' style='color:green;' onclick='getempofdevice_iclock(\"{{item.SN}}\")'>E</a>&nbsp;{% if user|HasPerm:"iclock.browselogPic" %}<a  title='{% trans "recording photos" %}'  style='color:green;' onclick='getpitureofdevice(\"{{item.SN}}\")'>P</a>{% endif %}{% if ''|debug %}&nbsp;<a title='{% trans "Device Parameter" %}'style='color:green;' onclick='getOption(\"{{item.SN}}\")'>O</a>{% endif %}",
"Data_pos":"&nbsp;<a  title='{% trans "Server issues command log" %}' onclick='getLog_device(\"{{item.SN}}\")'style='color:green;'>C</a>&nbsp;<a title='{% trans "Consumer details" %}'style='color:green;' onclick='getPosTran(\"{{item.SN}}\")'>L</a>{% if ''|debug %}&nbsp;<a title='{% trans "Device Parameter" %}'style='color:green;' onclick='getOption(\"{{item.SN}}\")'>O</a>&nbsp;{% endif %}{% if item.SN|is_show_ziplogfiles %}<a  title='{% trans "Device Log" %}' onclick='getPosZipLog(\"{{item.SN}}\")'style='color:green;'>Z</a>{% endif %}",
"Data_acc":"&nbsp;<a  title='{% trans "Server issues command log" %}' onclick='getLog_device(\"{{item.SN}}\")'style='color:green;'>C</a>&nbsp;<a title='{% trans "Access Control Record" %}'style='color:green;' onclick='getAccTran(\"{{item.SN}}\")'>L</a>&nbsp;<a title='{% trans "Device upload data log" %}' style='color:green;' onclick='getUplog(\"{{item.SN}}\")'>U</a>&nbsp;<a  title='{% trans "List of people on the device" %}' style='color:green;' onclick='getempofdevice_iclock(\"{{item.SN}}\")'>E</a>{% if ''|debug %}&nbsp;<a title='{% trans "Device Parameter" %}'style='color:green;' onclick='getOption(\"{{item.SN}}\")'>O</a>{% endif %}",
"Authentication":"{{ item.get_Authentication_display }}",
"AlgVer":"{{ item.GetDevice.AlgVer|default:'' }}",
"pushver":"{{ item.GetDevice.pushver }}",
"CommType":"{{ item.GetCommType|default:''  }}",
"Memo":"{{ item.GetDevice|device_memo }}",
"getImgUrl":"{{ item.getImgUrl }}",
"Totalcells": "{{ item.get_locker_cells }}"
}
{%if not forloop.last%},{%endif%}
{% endfor %}
]
{% endautoescape %}
