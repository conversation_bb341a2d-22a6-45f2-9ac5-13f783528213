# -*- coding:utf-8 -*-
__author__ = 'xavier'

from subprocess import Popen, PIPE
import json
import time
import os
import re
import sys

def getAttSite(opTypy, opName):
    cfFileName='/home/<USER>/attsite.ini'
    import configparser
    cf = configparser.RawConfigParser(strict=False,allow_no_value=True)
    cf.read(cfFileName,encoding='utf-8')
    return cf.get(opTypy, opName)
    
def exec_command(command, flag=True):
    process = Popen(command, shell=flag, stdout=PIPE, stderr=PIPE)
    stdout, stderr = process.communicate()
    return stdout

def getmac():
    if os.name == 'posix':
        r = os.popen('ifconfig').read()
        if r:
            L = re.findall(
                '[0-9,a-f,A-F]{2}:[0-9,a-f,A-F]{2}:[0-9,a-f,A-F]{2}:[0-9,a-f,A-F]{2}:[0-9,a-f,A-F]{2}:[0-9,a-f,A-F]{2}', r)
            if L:
                return L[0].replace(':', '')
    return ''

def writeinfo(info, fn):
    s = ''
    with open(fn, "w") as f:
        try:
            s = '\n'.join("%s:%s"%(key,str(info[key])) for key in info.keys())
            print(s)
            f.write(s)
            f.close()
        except Exception as e:
            print(e)
            pass
    return s

def writestr(s, fn):
    with open(fn, "w") as f:
        try:
            f.write(s)
            f.close()
        except Exception as e:
            print(e)
            pass

def getOSInfo():
    fn = '/tmp/info.linux'
    if os.path.exists(fn):
        os.remove(fn)
    
    # ip = ''
    coreinfo = exec_command("cat /proc/version | awk '{printf(\"%s\",$0)}'")
    release = exec_command("grep -w 'PRETTY_NAME' /etc/os-release |  cut -f 2 -d '='")

    # 服务器制造商
    manufacturer = exec_command("dmidecode -s system-manufacturer | awk '{printf(\"%s\",$0)}'")
    # 服务器型号
    model = exec_command("dmidecode -s system-product-name | awk '{printf(\"%s\",$0)}'")
    
    sn = exec_command("dmidecode -s system-serial-number | awk '{printf(\"%s\",$0)}'")
    ip = exec_command("/sbin/ifconfig -a|grep inet|grep -v 127.0.0.1|grep -v inet6|awk '{printf $2}'|tr -d 'addr:'")
    # print ipex
    # if re.search("((^10\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[0-9]))|(^172\.(1[6789]|2[0-9]|3[01]))|(^192\.168)\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[0-9])\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[0-9])$)", ipex):
    #     ip = ipex
    # print ip

    # CPU型号
    cpuModel = exec_command("dmidecode -t processor | grep Version | uniq -c| awk -F ':'  '{printf $2}'| sed 's/^ *\| *$//g'")
    # 物理CPU核心数
    cpuPhyNum = exec_command("cat /proc/cpuinfo | grep 'physical id' | sort | uniq | wc -l | awk '{printf(\"%s\",$0)}'")
    # 逻辑CPU核心数
    cpuLogicNun = exec_command("cat /proc/cpuinfo| grep processor| wc -l| awk '{printf(\"%s\",$0)}'")
    # CPU数量
    cpuNum = exec_command("cat /proc/cpuinfo | grep 'cpu cores' | uniq | cut -d: -f2| awk '{printf(\"%s\",$0)}'")
    # CPU总核数=物理CPU个数X每颗物理CPU的核数
    cpuCore = int(cpuPhyNum) * int(cpuNum)

    # 总内存大小
    MemTotal = exec_command("cat /proc/meminfo | grep 'MemTotal' | awk -F ':' '{printf $2}'")
    MemFree = exec_command("cat /proc/meminfo | grep 'MemAvailable' | awk -F ':' '{printf $2}'")
    memSize = '%s/%s(Free/Total)'%(MemFree.decode().replace(' ',''), MemTotal.decode().replace(' ',''))
    # 内存品牌
    memModel = exec_command(
        "dmidecode|grep -P -A16 'Memory\s+Device' | grep 'Manufacturer' | grep -v ':  '|awk -F ':' '{print $2}' | uniq -c | awk -F' ' '{printf $2}'")

    # 网卡型号
    netModel = exec_command("lspci | grep -i eth | awk -F : '{printf $3}'| uniq -c | sed 's/^      [0-9]  //'")

    # 硬盘型号和序列号
    diskModel = exec_command(
        "/opt/MegaRAID/MegaCli/MegaCli64 -cfgdsply -aALL -NoLog|grep 'Inquiry Data'| awk -F ':' '{print $2}'| sed 's/^ *\| *$//g'")
    # 硬盘数量
    diskNum = exec_command("/opt/MegaRAID/MegaCli/MegaCli64 -cfgdsply -aALL -NoLog|grep 'Inquiry Data' |wc -l | awk '{printf(\"%s\",$0)}'")
    # 每块硬盘大小
    diskSizeOne = exec_command(
        "/opt/MegaRAID/MegaCli/MegaCli64 -PDlist -aALL -NoLog| grep 'Raw Size' | awk '{print $3}' | awk 'BEGIN{i=1} {while(i<NF) print NF,$i,i++}{sum = int($i*1024*1024*1024/1000/1000/1000)}{printf sum\"T\"}'")
    # 总硬盘大小
    diskSize = exec_command(
        "/opt/MegaRAID/MegaCli/MegaCli64 -PDlist -aALL -NoLog| grep 'Raw Size' | awk '{print $3}' | awk 'BEGIN{i=1} {while(i<NF) print NF,$i,i++}{sum = int($i*1024*1024*1024/1000/1000/1000)}{u += sum}END{printf u\"T\"}'")

    output = {
        #system Info
        "coreinfo":coreinfo.decode(),
        "release":release.decode().replace('"','').replace('\n',''),
        "ip": ip.decode(),
        "mac":getmac(),
        "memSize": memSize, #可用/总计
        "netModel" : netModel,
        #device info
        "sn": sn,
        "manufacturer": manufacturer,
        "model": model,
        #cpu
        "cpuModel": cpuModel,
        "cpuNum": cpuNum,
        "cpuCore": cpuCore,
        "memModel": memModel,
        "diskModel" : diskModel,
        "diskNum": diskNum,
        "diskSize": diskSize
        }
    print(output)
    return writeinfo(output, fn)
#    return output

def getProcInfo():
    fn = '/tmp/proc.linux'
    if os.path.exists(fn):
        os.remove(fn)
    
    dbType = getAttSite("DATABASE", "ENGINE")
    output = {}
    apache = exec_command('ps -ef | grep "httpd24" | grep -v grep | wc -l')
    output["apache"] = int(apache.decode().replace('\n',''))
    cache = exec_command('ps -ef | grep "memcached" | grep -v grep | wc -l')
    output["cache"] = int(cache.decode().replace('\n',''))
    pyeco = exec_command('ps -ef | grep "python-ecopro timer.py" | grep -v grep | wc -l')
    output["pyeco"] = int(pyeco.decode().replace('\n',''))
    nginx = exec_command('ps -ef | grep "nginx: master process" | grep -v grep | wc -l')
    output["nginx"] = int(nginx.decode().replace('\n',''))
    port = exec_command("cat /usr/local/httpd24/conf/httpd.conf | grep ^Listen | awk NR==1 | awk '{printf $2}'")
    output["port"] = int(port.decode())
    if dbType == "postgresql":
        pgsql = exec_command('ps -ef | grep "postgres" | grep -v grep | wc -l')
        output["pgsql"] = int(pgsql.decode().replace('\n',''))
    elif dbType == "mysql":
        mysql = exec_command('ps -ef | grep "mysqld" | grep -v grep | wc -l')
        output["mysql"] = int(mysql.decode().replace('\n',''))

    print(output)
    return writeinfo(output, fn)

def net_is_used(port,ip='127.0.0.1'):
    import socket
    s = socket.socket(socket.AF_INET,socket.SOCK_STREAM)
    try:
        s.settimeout(1)
        s.connect((ip,port))
        s.close()
        print('端口:%d 被占用' % (port))
        return True
    except:
        print('端口:%d 可使用' % (port))
        return False

def writeport(wport, fn):
    fn_port = "/usr/local/httpd24/conf/httpd.conf"
    #site_port = "/etc/apache2/sites-enabled/000-default.conf"
    
    if os.access(fn_port, os.W_OK):# and os.access(site_port, os.W_OK)
        port_line = exec_command('cat /usr/local/httpd24/conf/httpd.conf | grep ^Listen | awk NR==1')
        virtual_line = exec_command("cat /usr/local/httpd24/conf/httpd.conf | grep '^<VirtualHost *' | awk NR==1")
        #virt_line = exec_command('cat /etc/apache2/sites-enabled/000-default.conf | grep VirtualHost | awk NR==1')

        infile = open(fn_port, "r")
        data = infile.read()
        rdata = data.replace(port_line.decode().replace('\n',''), 'Listen %s'%wport)
        rdata = rdata.replace(virtual_line.decode().replace('\n',''), '<VirtualHost *:%s>'%wport)
        infile.close()
        infile = open(fn_port, "w")
        infile.write(rdata)
        infile.close()
        
        #infile = open(site_port, "r")
        #data = infile.read()
        #infile.close()
        #infile = open(site_port, "w")
        #infile.write(data.replace(virt_line.decode().replace('\n',''), '<VirtualHost *:%s>'%wport))
        #infile.close()
        print('端口%s设置成功, 重启服务生效'%wport)
#        writestr("端口%s设置成功, 重启服务生效"%wport, fn)
    else:
        print('修改权限不足, 请使用root权限操作')
#        writestr("修改权限不足, 请使用root权限操作", fn)

def checkSys(): #系统检测, 输出异常日志 端口号超出范围
    return 

def checkroot():
    fn = '/usr/local/httpd24/conf/httpd.conf'
    if os.access(fn, os.W_OK):
        print(1)
    else:
        print(0)

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'info': #系统信息
        getOSInfo()
    elif len(sys.argv) > 1 and sys.argv[1] == 'proc':   #服务信息
        getProcInfo()
    elif len(sys.argv) > 1 and sys.argv[1] == 'checkroot':  #系统root权限
        checkroot()
    elif len(sys.argv) > 1 and sys.argv[1] == 'check':  #系统检测报告
        checkSys()
    elif len(sys.argv) > 1 and sys.argv[1] == 'wport':  #修改端口
        fn = '/tmp/wport.linux'
        if os.path.exists(fn):
            os.remove(fn)
        wport = int(sys.argv[2])
        if not net_is_used(wport):
            writeport(wport, fn)
        else:
            print("端口%s被占用"%wport)
#            writestr("端口%s被占用"%wport, fn)
