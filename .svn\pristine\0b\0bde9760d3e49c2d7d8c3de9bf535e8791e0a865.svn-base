{% extends "report_ipos.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block date_range_ext %}
    <div id="search_conditions" style='display:inline;width:315px'>
        <label id="search_id_deptID">{% trans 'department' %}</label>
        <input type='text' name=''  id='search_id_dept' style='width:120px;' onclick='deptTree()'>
    </div>


{% endblock %}
{% block search %}

{% endblock %}

{% block getDateUrl %}

    function getDateUrl(pos_start_date,pos_end_date)
    {

    $("#id_con_error").css("display","none");

    var urlStr=g_urls[g_activeTabID]

    var st=moment().startOf('month').format('YYYY-MM-DD')
    var et=moment().endOf('month').format('YYYY-MM-DD')
    var dept =$("#search_id_deptID").val();
    if(pos_start_date) st=pos_start_date
    if(pos_end_date) et=pos_end_date

    if(urlStr.indexOf("?")!=-1){
    urlStr=urlStr+"&StartDate="+st+"&EndDate="+et+"&deptID="+dept;
    }
    else{
    urlStr=urlStr+"?StartDate="+st+"&EndDate="+et+"&deptID="+dept;
    }

    return urlStr

    }
    
    //授权部门
function deptTree(){
    createQueryDlgbypage('report_dept',false,true);
    $("#dlg_dept_title_report_dept").css('display','none');
    $("#dlg_for_query_report_dept").dialog({width:460,height:460});
    $('#dlg_for_query_report_dept').dialog({
        position: { my: "left top-120", at: "right top"},

        buttons:[
            {id:"btnShowOK",text:'{% trans 'Sure' %}',click:function(){save_Deptments('report_dept');}},
            {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy");}}
        ]
        });
}

function save_Deptments(page){
    if (page=='report_dept'){
        deptNames=getSelected_deptNames("showTree_"+page);
        $("input[id='search_id_dept']").val(formatArrayEx(deptNames));
        var deptIDs=getSelected_dept("showTree_"+page);
        var deptData=[]
        for (i in deptIDs)
		{
			if($("#diyBtn_"+deptIDs[i]).prop("checked"))
				var Data={deptid:deptIDs[i],iscascadecheck:1}
			else
				var Data={deptid:deptIDs[i],iscascadecheck:0}
			deptData.push(Data)
		}
        $("#search_id_deptID").val(JSON.stringify(deptData));
        dlgdestroy(page)
    }
}

   
{% endblock %}

