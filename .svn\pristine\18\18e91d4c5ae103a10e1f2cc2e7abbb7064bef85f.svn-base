{% load iclock_tags %}
{% load i18n %}


<script>
pageQueryString=location.search;  {# 当前 页面 的查询字符串，页面 #}
options[g_activeTabID]={pagerId:"pages", tblId:"tbl", showSelect: true, canSelectRow:false,showStyle:false,
//	canEdit: {% if request|reqHasPerm:"change" %}true{% else %}false{% endif %},
	canAdd: {% if request|reqHasPerm:"add" %}true{% else %}false{% endif %},
	canDelete: {% if request|reqHasPerm:"delete" %}true{% else %}false{% endif %},
	canSearch: true, keyFieldIndex:"0", title:'{{ dataOpt.verbose_name|cap }}',

//	addition_fields:[], 	//要求服务器返回的附加字段
//	addition_columns:[],	//本地表格自行生成的列
//	exception_fields:['id'],	//要求服务器不要返回的列
	tblHeader:'{% trans "Please set a template file" %}options.tblHeader',
	dlg_width:810,
	dlg_height:'auto',
	edit_col:1,
	canHome:false
	
};

tblName[g_activeTabID]='emp_door'    
        
//var jqOptions=copyObj(jq_Options);
jqOptions[g_activeTabID]=copyObj(jq_Options);
var jqOptions_east=copyObj(jq_Options);

{% autoescape off %}
jqOptions[g_activeTabID].rowNum={{ limit }}
jqOptions[g_activeTabID].colModel={{colModel}}
jqOptions[g_activeTabID].pager='id_pager_emp_door'
jqOptions[g_activeTabID].onSelectRow=function(ids){show_detail_level(ids);}
jqOptions[g_activeTabID].multiselect=false


jqOptions_east.rowNum=1000
jqOptions_east.colModel={{colModel_level}}
jqOptions_east.pager='id_pager_emp_door_east'
jqOptions_east.multiselect=false

{% endautoescape %} 
 
 
 
    
var layoutSettings_center = {
    
 					east__paneSelector:		".inner-east"
                                ,       east__size:'400'
				,	spacing_open:			6  // ALL panes
				,	spacing_closed:			6  // ALL panes
				,	east__spacing_closed:	10

   
    
}
 
 
function show_detail_level(ids)
{

        $("#id_grid_emp_door_east").jqGrid('setGridParam',{url:"/acc/getData/?id="+ids+'&func=emp_door',datatype: "json"}).trigger('reloadGrid');			

}
 
function actionAddLevel(url,ss)
{
        $.ajax({type: "POST",
                        url: '/acc/employeelevel/?id='+url,//getQueryStr(window.location.href, ["action"],a),
                        data: "K="+ss,
                        dataType:"json",
                        success: actionSuccess,
                        error: function(request){
                                alert($.validator.format("{% trans '{0} failed!\n\n{1}' %}", opName, request.statusText)); $.unblockUI();
                                //$('body').html(request.responseText);
                                }
                        });
 
}
function addLevel(id)
{
    	
        createDialog(id, actionAddLevel, '/acc/data/level/', "{% trans "Add a permission group for a person" %}", "{% trans "Rights Groups" %}", 600,true,'level');

    
    
    
}

function searchShowEmp_emp_door(){
    var v=$("#"+g_activeTabID+" #searchbar").val();
    if(v=='{% trans "Personnel number, name, card number" %}'){		v=''
	}
    var url="/acc/data/employee/?t=level_emp.js&q="+encodeURI(v)
    savecookie("search_urlstr",url);
    $("#id_grid_emp_door").jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
    
}


$(function(){

    $('#id_inner_center').layout(layoutSettings_center)


    var hcontent=$("#id_inner_center").height();
    var hbar=$("#id_inner_center"+" #id_top").length>0?$("#id_inner_center"+" #id_top").height():0;
    var h=hcontent-hbar-42
    $('.emp_door_module').css('height',h)

    g_urls[g_activeTabID]='/acc/data/employee/?t=level_emp.js'
    loadPageData();
    
    
		jqOptions_east.data=[]
		jqOptions_east.datatype='local'
		jqOptions_east.height=h
		$("#id_grid_emp_door_east").jqGrid(jqOptions_east);
    
	$("#"+g_activeTabID+" #searchButton").click(function(){
		searchShowEmp_emp_door();
	})

	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
		if(event.keyCode==13){
			searchShowEmp_emp_door();
		}	
	})
    
    $("#"+g_activeTabID+" #queryButton").hide()
    

});

	var inputEl = $("#"+g_activeTabID+" .search-input")
         defVal[g_activeTabID] = inputEl.val();
    	 inputEl.on("focus",function(){
		             var _this = $(this);
				if (_this.val() == defVal[g_activeTabID]) {
				    _this.val('');
				    _this.css('color','#000000');
				    //_this.attr('role','disabled')
				}
		})
	inputEl.on("blur",function(){
		        var _this = $(this);
			if (_this.val() == '') {
			    _this.val(defVal[g_activeTabID]);
			    _this.css('color','#CCCCCC');
			    _this.attr('role','defvalue')
			}
			else
			    _this.attr('role','cansearch')
		})






</script>


<div id='id_inner_center' style="width: 100%;height: 100%;">

<div class="ui-layout-center">
    <div id="id_top">
	<div class="sear-box quick-sear-box" >
		
		
		<div class="s-info left" id="sear_area">
			<div class="nui-ipt nui-ipt-hasIconBtn " >
				<input id="searchbar" class="search-input" type="text"  value="{% trans "Personnel number, name, card number" %}" role='defvalue' autocomplete="off" />
				<span id ="queryButton" class="nui-ipt-iconBtn">
					<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
				</span>
				
			</div>
			
			<div class="main-search-btn" style="padding: 0 0 0 3px">
{#				<span id="searchButton" class="chaxun icon iconfont icon-chaxun"></span>#}
                <span id='searchButton' ><a class='m-btn  zkgreen rnd mini'>{% trans 'Query' %}</a></span>
			</div>
		</div>
		
				
	</div>
                    
        <div id="id_toolbar">	
            <UL class="toolbar" id="navi" style='z-index: 1 !important;'></UL>
        </div>            
    </div>               

    <div class="emp_door_module" style="width: 99%;padding-top: 3px;">
	
	<table id="id_grid_emp_door" >	</table>
	<div id="id_pager_emp_door"></div>
    </div>
</div>
		
                
<div class="inner-east" >

    <div id="id_top">
	<div class="sear-box quick-sear-box" >
		
		
		
				
	</div>
                    
        <div id="id_toolbar" >	
            <UL class="toolbar" id="navi" style='z-index: 1 !important;'>
		<!--
				{% if request|reqHasPerm:"delete" %}
				<LI id="aDelete" class="button-aDelete"  onclick="batchOp('?action=del',itemCanBeDelete,'{% trans "Delete" %}','_','/acc/data/level_emp/');"><SPAN></SPAN>{% trans "Delete" %}</LI>
				{% endif %}
                
                -->
                
            </UL>
        </div>            
    </div>               



    <div class="east_module" style="width: 99%;height:100%;padding-top: 3px;">

	<table id="id_grid_emp_door_east" >	</table>
	<!-- <div id="id_pager_emp_door_east"></div> -->
	</div>


</div>
