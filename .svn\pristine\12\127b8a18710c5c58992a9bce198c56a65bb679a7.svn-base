import sys

import datetime
import hashlib
import hmac
import requests
import json
import six
import time

from django.core.cache import cache
from mysite.utils import dumps1
from optionaldict import optionaldict
from django.conf import settings
from mysite.ipos.models import BioPayRequestLog
from django.utils.translation import gettext_lazy as _

import os
import django
os.environ['DJANGO_SETTINGS_MODULE'] = 'mysite.settings'
django.setup()
if str(sys.version[0]) == "3":
    from urllib.parse import quote_plus
else:
    from urllib import quote_plus


class BaseZKBioPay(object):
    def __init__(self, app_id=None, app_key=None, version=None, time_stamp=None, data_format=None, sign_method=None):
        """
        初始化BaseZKBioPay类
        :param app_id: 安防云支付分配的app_id
        :param app_key: 安防云支付分配的app_key
        :param version: 接口版本，目前默认4.0
        :param time_stamp: 当前时间的时间戳精确到毫秒
        :param data_format: 数据格式json
        :param sign_method: 签名算法，目前支持的签名算法有两种：MD5(sign_method=md5)，HMAC_MD5(sign_method=hmac)
        """
        from mysite.utils import loads
        from mysite.base.sysview import GetParamValue
        bio_pay_params = loads(GetParamValue('zkbio_pay_params', '{}', 'zkbio_pay'))
        if not bio_pay_params and (app_id is None or app_key is None):
            raise Exception(_(u"Please import the BioSecurity Cloud payment parameter file to save the parameters!"))
        self.app_key = app_key or bio_pay_params['appKey']
        self.get_data = {
            "app_id": app_id or bio_pay_params['appId'],
            "v": version or '4.0',
            "timestamp": time_stamp or str(int(time.time() * 1000)),
            "format": data_format or 'json',
            "sign_method": sign_method or 'hmac'
        }
        self._http = requests.session()
        self.headers = {'Content-Type': 'application/json'}
        # self._zkbio_url = "http://10.8.14.75:8899/router/rest"
        # self._zkbio_url = "https://demo.xmzkteco.com/BiosecurityApp/router/rest"
        # self._zkbio_url = "https://devcloud.zkbiosecurity.com/BiosecurityApp/router/rest"  # 测试地址
        self._zkbio_url = "https://zkcloud.zkbiosecurity.com/BiosecurityApp/router/rest"  # 正式地址

    @staticmethod
    def to_text(value, encoding='utf-8'):
        """
        将值转换为unicode，默认编码为utf-8
        :param value: 要转换的值
        :param encoding: 所需编码
        """
        if not value:
            return ''
        if isinstance(value, six.text_type):
            return value
        if isinstance(value, six.binary_type):
            return value.decode(encoding)
        return six.text_type(value)

    @staticmethod
    def _ordered_data(data):
        """
        排序字典根据参数名称的ASCII码表的顺序排序。如：foo:1, bar:2, foo_bar:3, foobar:4排序后的顺序是bar:2, foo:1, foo_bar:3, foobar:4。
        :param data:待排序字典
        :return:排序后的字典
        """
        complex_keys = [k for k, v in data.items() if isinstance(v, dict)]
        # 将字典类型的数据dump出来
        for key in complex_keys:
            data[key] = dumps1(data[key], separators=(',', ':'), ensure_ascii=False)
        return sorted([(k, v) for k, v in data.items()])

    def _sign(self, unsigned_string):
        """
        计算签名
        :param unsigned_string:
        :return:
        """
        # 开始计算签名
        if self.get_data.get('sign_method', 'md5') == "md5":
            signature = hashlib.md5((self.app_key + unsigned_string + self.app_key).encode('utf-8')).hexdigest().upper()
        else:
            signature = hmac.new(self.app_key.encode('utf-8'), msg=unsigned_string.encode('utf-8'),
                                 digestmod=hashlib.md5).hexdigest().upper()
        sign = self.to_text(signature)
        return sign

    def get_api_url(self, post_data, method_dict, access_token_dict=None):
        """
        签名，并且返回拼接后api完整的请求url
        :param post_data: post数据
        :param method_dict: method字段，每个接口的method都不一样
        :param access_token_dict: access_token字段，需要token的请求需要这个参数
        :return:
        """
        get_dict = self.get_data.copy()  # get请求字典
        get_dict.update(method_dict)  # 更新method字段，每个接口的method都不一样
        if access_token_dict is not None:
            get_dict.update(access_token_dict)  # 更新access_token字段，需要token的请求需要这个参数

        sgin_dict = {}  # 待签名字典，需要post的参数加上get的参数进行签名
        sgin_dict.update(post_data.copy())
        sgin_dict.update(get_dict)

        # 排序后的字字典
        ordered_items = self._ordered_data(sgin_dict)
        # 拼接待签名的字符串
        unsigned_string = "".join("{}{}".format(k, v) for k, v in ordered_items)
        # 获取签名
        sign = self._sign(unsigned_string)
        # 拼接后url请求的字符串
        signed_string = "&".join("{}={}".format(k, quote_plus(v)) for k, v in get_dict.items()) + "&sign=" + quote_plus(sign)
        # api完整的请求url
        api_url = self._zkbio_url + "?" + signed_string
        return api_url

    @staticmethod
    def set_token_cache(ret):
        """
        设置token
        :param ret: 获取token请求得到的字典
        :return:
        """
        cache.set("zkbio_access_token", ret['data']['access_token'], timeout=ret['data']['expires_in'])  # 7200s后过期
        cache.set("zkbio_refresh_token", ret['data']['refresh_token'],
                  timeout=ret['data']['refresh_token_expires_in'])  # 604800s后过期
        return ret['data']['access_token']

    def get_token_cache(self):
        """
        从缓存获取token
        :return: token值
        """
        access_token = cache.get("zkbio_access_token", "")
        # refresh_token = cache.get("zkbio_refresh_token", "")
        if access_token:
            return access_token
        # if refresh_token:
        #     return self.get_refresh_token(refresh_token)
        else:
            return self.get_access_token()

    def get_access_token(self):
        """
        获取token
        :return: {"code": 0, "msg": "成功", "sub_code": "", "sub_msg": "", "data": }
                data: access_token：验证token
                    refresh_token：刷新token
                    refresh_token_expires_in：刷新token过期时长（秒）
                    expires_in:验证token过期时长（秒）
        """
        method_dict = {'method': "zkteco.api.auth.token.create"}
        sha256_key = self.app_key[8:24][::-1]  # 取api_key key的第9位到23位做为加密的key
        app_key = hmac.new(sha256_key.encode('utf-8'), msg=self.app_key.encode('utf-8'),
                           digestmod=hashlib.sha256).hexdigest()
        zk_data = {
            "grantType": "client_credential",
            "version": "4.0",
            "appKey": "HMACSHA256$" + app_key
        }
        zk_data = optionaldict(zk_data)
        post_data = {
            'zk_data': zk_data
        }
        api_url = self.get_api_url(post_data, method_dict)  # api完整的请求url
        ret = self._http.post(api_url, data=dumps1(post_data), headers=self.headers)
        ret = json.loads(ret.content.decode())
        try:
            # 安防云支付调用日志
            BioPayRequestLog.objects.create(
                api_url=api_url,
                post_data=dumps1(post_data),
                result_data=dumps1(ret),
                request_time=datetime.datetime.now(),
            )
        except:
            if settings.DEBUG:
                import traceback
                traceback.print_exc()
        if int(ret['code']) == 0:
            return self.set_token_cache(ret)
        else:
            return ""

    def get_refresh_token(self, refresh_token):
        """
        刷新token，目前用不着
        :param refresh_token: 创建access_token获取, 必填；
        :return: {"code": 0, "msg": "成功", "sub_code": "", "sub_msg": "", "data": }
        """
        method_dict = {'method': 'zkteco.api.auth.token.refresh'}
        zk_data = {
            "grantType": "refresh_token",
            # "version": "4.0",
            "refreshToken": refresh_token
        }
        zk_data = optionaldict(zk_data)
        post_data = {
            'zk_data': zk_data
        }
        api_url = self.get_api_url(post_data, method_dict)  # api完整的请求url
        ret = self._http.post(api_url, data=dumps1(post_data), headers=self.headers)
        ret = json.loads(ret.content.decode())
        try:
            # 安防云支付调用日志
            BioPayRequestLog.objects.create(
                api_url=api_url,
                post_data=dumps1(post_data),
                result_data=dumps1(ret),
                request_time=datetime.datetime.now(),
            )
        except:
            if settings.DEBUG:
                import traceback
                traceback.print_exc()
        if int(ret['code']) == 0:
            return self.set_token_cache(ret)
        else:
            return ""

    def request_by_token(self, post_data, method_dict):
        """
        签名并且请求封装token然后post请求获取数据
        :param post_data:  post数据
        :param method_dict:  method字段，每个接口的method都不一样
        :return: 返回的content内容
        """
        access_token = self.get_token_cache()
        access_token_dict = {'access_token': access_token}
        api_url = self.get_api_url(post_data, method_dict, access_token_dict)  # api完整的请求url
        ret = self._http.post(api_url, data=dumps1(post_data), headers=self.headers)
        ret = json.loads(ret.content.decode())
        try:
            # 安防云支付调用日志
            BioPayRequestLog.objects.create(
                api_url=api_url,
                post_data=dumps1(post_data),
                result_data=dumps1(ret),
                request_time=datetime.datetime.now(),
            )
        except:
            if settings.DEBUG:
                import traceback
                traceback.print_exc()
        if settings.DEBUG:
            # 如果有开启debug打印请求信息。
            print('-----------------------------------------------------')
            print('1----:', ret)
            print('1api_url----:', api_url)
            print('1post_data----:', post_data)
            print('-----------------------------------------------------')
        if ret['code'] in (-26, -27):
            # -26缺少 Access Token参数，-27无效的 Access Token参数，重新获取token，再次请求一次。
            access_token = self.get_access_token()
            access_token_dict = {'access_token': access_token}
            api_url = self.get_api_url(post_data, method_dict, access_token_dict)  # api完整的请求url
            ret = self._http.post(api_url, data=dumps1(post_data), headers=self.headers)
            ret = json.loads(ret.content.decode())
            try:
                # 安防云支付调用日志
                BioPayRequestLog.objects.create(
                    api_url=api_url,
                    post_data=dumps1(post_data),
                    result_data=dumps1(ret),
                    request_time=datetime.datetime.now(),
                )
            except:
                if settings.DEBUG:
                    import traceback
                    traceback.print_exc()
            if settings.DEBUG:
                # 如果有开启debug打印请求信息。
                print('2----:', ret)
                print('2api_url----:', api_url)
                print('2post_data----:', post_data)
        return ret

    def get_verify_app(self):
        """
        验证应用是否开通支付能力
        :return: {"code": 0, "msg": "成功", "sub_code": "", "sub_msg": "", "data": }
                "data": "ALIPAY_QR, ALIPAY_WAP, ALIPAY_BARCODE"
        """
        method_dict = {'method': 'zkteco.api.zkpay.order.verifyApp'}
        # 更新method字段，每个接口的method都不一样
        self.get_data.update(method_dict)
        zk_data = {
            "zkcloudProductCode": "ZKEcoPro"
        }
        zk_data = optionaldict(zk_data)
        post_data = {
            'zk_data': zk_data
        }
        return self.request_by_token(post_data, method_dict) 

    def get_result_notify(self, content_id):
        """
        通知云平台支付业务处理完成
        :param content_id: 业务识别id 异步通知返回
        :return:
                {"appId":"bd1c90bcf1a66dd4418ceb2d656f6d10",
                "async":false,
                "content":{"payOrderId":"P0020220624155820081000021",
                "mchOrderNo":"165605751100000101954000012"},
                "contentId":"0fbdb906-dd52-4c9e-8454-2e2220eef50f",
                "repeat":true,
                "repeatCount":2}
        """
        method_dict = {'method': 'zkteco.api.zkpay.order.resultNotify'}
        # 更新method字段，每个接口的method都不一样
        self.get_data.update(method_dict)
        zk_data = {
            "contentId": content_id
        }
        zk_data = optionaldict(zk_data)
        post_data = {
            'zk_data': zk_data
        }
        return self.request_by_token(post_data, method_dict) 

    def create(self, mch_order_no, amount, subject, trade_type, code=None, currency=None,
               body=None, param1=None, param2=None, openid=None):
        """
        统一下单接口
        :param mch_order_no: 订单id，必填；
        :param amount: 金额, 必填；
        :param subject: 商品标题，必填;
        :param trade_type: 支付类型，必填；
        tradeType:支付类型: 支付宝WAP支付: "ALIPAY_WAP"; 支付宝当面付之扫码支付: "ALIPAY_QR"; 支付宝当面付之条码支付: "ALIPAY_BARCODE";
        :param code: 扫码支付场景下必填，传买家出示的付款码
        :param currency: 币种, 非必填，默认人民币；
        :param body: 商品描述，非必填；
        :param param1: 支付结果通知客户端的接口
        :param param2:
        :param openid: 用户openid，微信JSAPI支付下必填，微信公众号、微信小程序支付场景必填
        :return: {"code": 0, "msg": "成功", "sub_code": "", "sub_msg": "", "data": }
                1、ALIPAY_QR,ALIPAY_BARCODE,ALIPAY_WAP
                "data":{
                    "payOrderId",//"云平台生成的订单id",
                    "sign":"",
                    "payUrl"//"二维码码串"
                },
                2、ALIPAY_JSAPI
                "data":{
                    "payOrderId",//"云平台生成的订单id",
                    "sign":"",
                },
                3、ALIPAY_BARCODE
                "data":{
                    "payOrderId",//"云平台生成的订单id",
                    "tradeNo":""//"支付宝生成的订单id"
                },
        """
        method_dict = {'method': 'zkteco.api.zkpay.order.aggregatePayOrder'}
        # 更新method字段，每个接口的method都不一样
        self.get_data.update(method_dict)
        zk_data = {
            "mchOrderNo": mch_order_no,
            "amount": amount,
            "code": code,
            "currency": currency,
            "subject": subject,
            "body": body,
            "tradeType": trade_type,
            "param1": param1,
            "param2": param2,
            "openId": openid
        }
        zk_data = optionaldict(zk_data)
        post_data = {
            'zk_data': zk_data
        }
        return self.request_by_token(post_data, method_dict) 

    def get_query_order(self, mch_order_no=None, pay_order_id=None, transaction_id=None, channel_type='ALIPAY'):
        """
        根据商户订单号查询支付状态, mch_order_no、transaction_id、payOrderId不能同时为空，建议传transactionId、pay_order_id
        :param mch_order_no: 订单号，ecopro方
        :param pay_order_id: 云平台生成的订单id
        :param transaction_id: 微信、支付宝订单id
        :param channel_type: ALIPAY WX 支付宝支付或微信支付 不传值默认ALIPAY
        :return: {"code": 0, "msg": "成功", "sub_code": "", "sub_msg": "", "data": }
                "data": {"tradeStatus",
                //支付宝支付
                    支付状态 WAIT_BUYER_PAY（交易创建，等待买家付款）、TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、
                    TRADE_SUCCESS（交易支付成功）、TRADE_FINISHED（交易结束，不可退款）、
                    ACQ.TRADE_NOT_EXIST--订单号有误，查询不到此订单号信息
                // 微信支付
                    SUCCESS—支付成功,REFUND—转入退款,NOTPAY—未支付,CLOSED—已关闭,REVOKED—已撤销（刷卡支付）,USERPAYING--用户支付中,
                    PAYERROR--支付失败(其他原因，如银行返回失败)、ORDERNOTEXIST--订单号有误，查询不到此订单号信息
                // ORDERNONEEXISTS--查询不到安防云支付的订单
                },
        """
        if (mch_order_no is None) and (pay_order_id is None) and (transaction_id is None):
            # mch_order_no、transaction_id、payOrderId不能同时为空
            return {"code": -1,
                    "msg": _(u"mch_order_no、pay_order_id and transaction_id cannot be empty at the same time !")}
        method_dict = {'method': 'zkteco.api.zkpay.order.queryOrder'}
        # 更新method字段，每个接口的method都不一样
        self.get_data.update(method_dict)
        zk_data = {
            "mchOrderNo": mch_order_no,
            "payOrderId": pay_order_id,
            "transactionId": transaction_id,
            "channelType": channel_type,
        }
        if mch_order_no and (pay_order_id is None) and (transaction_id is None):
            # 只传商户号需要传appid
            zk_data['appId'] = self.get_data['app_id']
        zk_data = optionaldict(zk_data)  # 去除zk_data字典中值位none的键值。
        post_data = {
            'zk_data': zk_data
        }
        return self.request_by_token(post_data, method_dict) 


# if __name__ == '__main__':
#     def get_out_trade_no(sys_card_no, card_serial_num, money):
#         """
#         移动支付订单号
#         :param sys_card_no:
#         :param card_serial_num:
#         :param money:
#         :return:
#         """
#         return "{}{:0>6}{:0>10}{}".format(
#             int(time.time()),
#             money,
#             sys_card_no,
#             card_serial_num
#         )
#     try:
#         out_trade_no = get_out_trade_no('1111111111', '121', int(1))
#         pay_obj = BaseZKBioPay()
#         # time_stamp = int(time.time() * 1000)
#         # pay_obj = BaseZKBioPay(
#         #     app_id='bd1c90bcf1a66dd4418ceb2d656f6d10',
#         #     app_key='1915d69a87ed5c508ae77fef031c19a4',
#         #     version='4.0',
#         #     time_stamp=str(time_stamp),
#         #     data_format='json',
#         #     sign_method='hmac'
#         # )
#         # ret = pay_obj.get_query_order(pay_order_id='165545488500000101954000010')
#         # ret = pay_obj.get_query_order(pay_order_id='2022061722001422241427491369')
#         # ret = pay_obj.get_verify_app()
#         ret = pay_obj.get_result_notify('965b21d6-7a35-4c3c-9e89-00c92167a1fc')
#         # ret = pay_obj.get_access_token()
#         # print(out_trade_no)
#         # # qrcode
#         # # payment_code = '284441295239577880'
#         # payment_code = '284375025163560263'
#         # ret = pay_obj.create(
#         #     mch_order_no=out_trade_no,
#         #     amount='1',
#         #     # subject='自助充值',
#         #     subject='recharge',
#         #     trade_type='ALIPAY_BARCODE',
#         #     code=payment_code
#         # )
#         # wap
#         # ret = pay_obj.create(
#         #     mch_order_no=out_trade_no,
#         #     amount='1',
#         #     # subject='自助充值',
#         #     subject='recharge',
#         #     trade_type='ALIPAY_WAP'
#         # )
#         # ALIPAY_QR
#         # ret = pay_obj.create(
#         #     mch_order_no=out_trade_no,
#         #     amount='1',
#         #     # subject='自助充值',
#         #     subject='recharge',
#         #     trade_type='ALIPAY_QR'
#         # )
#     except:
#         import traceback
#         traceback.print_exc()

