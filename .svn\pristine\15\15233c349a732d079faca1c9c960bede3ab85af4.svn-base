# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-02-21 11:17
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0027_auto_20200114_0925'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='adminmanagement',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Administrative management', 'verbose_name_plural': 'Administrative management'},
        ),
        migrations.AlterModelOptions(
            name='cookbook',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'App menu', 'verbose_name_plural': 'App menu'},
        ),
        migrations.AlterModelOptions(
            name='foodclassify',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Variety of dishes', 'verbose_name_plural': 'Variety of dishes'},
        ),
        migrations.AlterModelOptions(
            name='foodcommend',
            options={'default_permissions': ('browse',), 'verbose_name': 'App reviews of dishes', 'verbose_name_plural': 'App reviews of dishes'},
        ),
        migrations.AlterModelOptions(
            name='healthydiet',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Healthy diet', 'verbose_name_plural': 'Healthy diet'},
        ),
        migrations.AlterModelOptions(
            name='iclockdininghall',
            options={'default_permissions': (), 'permissions': (('iclockdininghall_cardcashsz', 'cardcashsz'), ('iclockdininghall_icconsumerlist', 'icconsumerlist'), ('iclockdininghall_reports', 'reports'), ('iclockdininghall_orderlist', 'reports'), ('iclockdininghall_foodcommend', 'reports')), 'verbose_name': 'owned restaurant', 'verbose_name_plural': 'owned restaurant'},
        ),
        migrations.AlterModelOptions(
            name='messagecenter',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'app Message Center', 'verbose_name_plural': 'app Message Center'},
        ),
        migrations.AlterModelOptions(
            name='orderlist',
            options={'default_permissions': ('browse',), 'verbose_name': 'App order record detail', 'verbose_name_plural': 'App order record detail'},
        ),
        migrations.AlterModelOptions(
            name='orderrecord',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'App order record', 'verbose_name_plural': 'App order record'},
        ),
        migrations.AddField(
            model_name='meal',
            name='endbooking',
            field=models.TimeField(null=True, verbose_name='Order end time'),
        ),
        migrations.AlterField(
            model_name='healthydiet',
            name='LikeSum',
            field=models.CharField(db_column='likesum', editable=False, max_length=40, null=True, verbose_name='Like people number'),
        ),
    ]
