# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-06-21 09:35
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0054_auto_20210524_0936'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='ademployee',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('adrotatepic_ademployee', 'Pictures Rotating'),), 'verbose_name': 'Information Screen Registration Officer', 'verbose_name_plural': 'Information Screen Registration Officer'},
        ),
        migrations.AlterModelOptions(
            name='announcement',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('send_Announcement_Email', 'Send Announcement Email'),), 'verbose_name': 'announcement', 'verbose_name_plural': 'announcement'},
        ),
        migrations.AlterModelOptions(
            name='applocation',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'permissions': (('setzone', 'Setting the home area'),), 'verbose_name': 'applocation'},
        ),
        migrations.AlterModelOptions(
            name='errorlog',
            options={'default_permissions': ('browse', 'delete', 'export'), 'verbose_name': 'Commands Error Log', 'verbose_name_plural': 'Commands Error Log'},
        ),
        migrations.AlterModelOptions(
            name='iclock',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'permissions': (('pause_iclock', 'Pause device'), ('resume_iclock', 'Resume a resumed device'), ('reloaddata_iclock', 'Upload data again'), ('reloadlogdata_iclock', 'Upload transactions again'), ('info_iclock', 'Refresh device information'), ('reboot_iclock', 'Reboot device'), ('loaddata_iclock', 'Upload new data'), ('cleardata_iclock', 'Clear data in device'), ('clearlog_iclock', 'Clear transactions in device'), ('devoption_iclock', 'Set options of device'), ('unlock_iclock', 'Output unlock signal'), ('unalarm_iclock', 'Terminate alarm signal'), ('attdataProof_iclock', 'Attendance data proofreading'), ('toDevWithin_iclock', 'Transfer to the device templately'), ('mvToDev_iclock', 'Move employee to a new device'), ('AutoToDev_employee', 'Auto transfer employee to the device'), ('Upload_AC_Options', 'Upload AC Options'), ('Upload_User_AC_Options', 'Upload User AC Options'), ('deptEmptoDev_iclock', 'Transfer employee of department to the device'), ('deptEmptoDelete_iclock', 'Delete employee from the device'), ('browselogPic', 'browse logPic'), ('deptEmpDelFromDev_iclock', 'Remove people or feature templates from device'), ('Upload_pos_all_data', 'Upload All Data'), ('Upload_pos_Merchandise', 'Upload Merchandise'), ('Upload_pos_Meal', 'Upload Meal'), ('Upload_Iclock_Photo', 'Upload Iclock Photo'), ('delDevPic_iclock', 'delDevPic employee'), ('set_device_asp', 'set divice ascription'), ('consumer_record_detection', 'Consumer Record Detection'), ('sync_locker_data', 'Sync locker data'), ('download_attphoto', 'Download attendance photos')), 'verbose_name': 'device', 'verbose_name_plural': 'device'},
        ),
        migrations.AlterField(
            model_name='employee',
            name='Educational',
            field=models.CharField(blank=True, choices=[(6, 'Primary school'), (7, 'Junior middle school'), (0, 'Secondary school'), (1, 'High school'), (2, 'College'), (3, 'Undergraduate'), (4, 'Master'), (5, 'Doctorate '), (8, 'Other')], db_column='educational', max_length=2, null=True, verbose_name='Educational'),
        ),
        migrations.AlterIndexTogether(
            name='devcmds',
            index_together=set([('SN', 'CmdCommitTime', 'CmdTransTime', 'CmdOverTime')]),
        ),
    ]
