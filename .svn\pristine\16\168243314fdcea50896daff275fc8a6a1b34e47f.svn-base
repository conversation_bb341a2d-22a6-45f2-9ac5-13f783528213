﻿{% load iclock_tags %}
{% load i18n %}

{% autoescape off %}
    <div id="id_form" style='overflow-y: hidden;'>
        <div>
            <form method="post" id="id_edit_form" enctype="multipart/form-data">
                <table>
                    <tr><th></th><td>
                        <span  style="display:none" id="id_span_title">
                            {% if add %}{% trans "Add" %}  {% else %} {% trans "Edit" %} {% endif %} {{ dataOpt.verbose_name|escape }}</span>
                    </td></tr>
                    <tr>
                        <th> {{ form.address.label_tag }} </th>
                        <td style='vertical-align:top;width: 300px' colspan="3"> {{ form.address.as_widget }} </td>
                    </tr>
                    <tr id='id_coordinate'>
                        <th> {{ form.longitude.label_tag }} </th>
                        <td style='vertical-align:top;'> {{ form.longitude.as_widget }} </td>
                        <th> {{ form.latitude.label_tag }} </th>
                        <td style='vertical-align:top;'> {{ form.latitude.as_widget }} </td>
                    </tr>
                    <tr>
                        <th> {{ form.valid_range.label_tag }} </th>
                        <td style='vertical-align:top;'> {{ form.valid_range.as_widget }}{% trans "Units are meters (m)" %}</td>
                    </tr>
                </table>
            </form>
            <div id="searchResultPanel" style="border:1px solid #C0C0C0;width:150px;height:auto; display:none;"></div>
            <div id="l-map" style="margin-top: 15px;width:777px;height: 360px"></div>
            <!-- <div id="r-result" style="margin-top: 15px;">
                <input type="button" onclick="add_control();" value="{% trans 'Thumbnail addition' %}"/>
                <input type="button" onclick="delete_control();" value="{% trans 'Thumbnail deletion' %}"/>
                <input type="button" onclick="add_control_navigation();" value="{% trans 'Map Zoom Add' %}"/>
                <input type="button" onclick="delete_control_navigation();" value="{% trans 'Map Zoom Delete' %}"/>
            </div> -->
        </div>
        {% block extrjs %}
            <style type="text/css">
                .tangram-suggestion-main {
                    z-index: 200;
                }
                #id_address {
                    width: 385px;
                }
                #id_coordinate {
                    color: grey;
                }
            </style>
            <script type="text/javascript">
                function G(id) {
                    return document.getElementById(id);
                }
                var map = new BMap.Map("l-map");// 百度地图API功能
                map.centerAndZoom(new BMap.Point(116.331398, 39.897445), 17);
                var longitude= $("#id_longitude").val();
                var latitude= $("#id_latitude").val();
                if(longitude==''|| latitude=='' ){
                    var geolocation = new BMap.Geolocation();
                    geolocation.getCurrentPosition(function (r) {//获取当前位置
                        if (this.getStatus() == BMAP_STATUS_SUCCESS) {//状态码标识如底下
                            map.clearOverlays();
                            var mk = new BMap.Marker(r.point);
                            map.addOverlay(mk);
                            map.panTo(r.point);
                        }
                        else {
                            alert('failed' + this.getStatus());
                            return
                        }
                    }, {enableHighAccuracy: true})
                }else{
                    map.clearOverlays();
                    var new_point = new BMap.Point(longitude,latitude);
                    var marker = new BMap.Marker(new_point);  // 创建标注
                    map.addOverlay(marker);              // 将标注添加到地图中
                    map.panTo(new_point);
                }

                var top_left_control = new BMap.ScaleControl({anchor: BMAP_ANCHOR_BOTTOM_LEFT});// 左上角，添加比例尺
                var top_left_navigation = new BMap.NavigationControl();  //左上角，添加默认缩放平移控件
                var top_right_navigation = new BMap.NavigationControl({
                    anchor: BMAP_ANCHOR_TOP_RIGHT,
                    type: BMAP_NAVIGATION_CONTROL_SMALL
                }); //右上角，仅包含平移和缩放按钮
                 //添加控件和比例尺
                function add_control_navigation() {
                    map.addControl(top_left_control);
                    map.addControl(top_left_navigation);
                    map.addControl(top_right_navigation);
                }
                //移除控件和比例尺
                function delete_control_navigation() {
                    map.removeControl(top_left_control);
                    map.removeControl(top_left_navigation);
                    map.removeControl(top_right_navigation);
                }

                var mapType1 = new BMap.MapTypeControl(
                    {
                        mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
                        anchor: BMAP_ANCHOR_TOP_LEFT
                    }
                );
                var overView = new BMap.OverviewMapControl();
                var overViewOpen = new BMap.OverviewMapControl({isOpen: true, anchor: BMAP_ANCHOR_BOTTOM_RIGHT});
                //添加地图类型和缩略图
                function add_control() {
                    map.addControl(mapType1);          //2D图，混合图
                    map.addControl(overView);          //添加默认缩略地图控件
                    map.addControl(overViewOpen);      //右下角，打开
                }
                //移除地图类型和缩略图
                function delete_control() {
                    map.removeControl(mapType1);   //移除2D图，混合图
                    map.removeControl(overView);
                    map.removeControl(overViewOpen);
                }
                map.enableScrollWheelZoom(true);

                var geoc = new BMap.Geocoder();//鼠标点击事件,赋值对应的经纬度
                map.addEventListener("click", function (e) { //通过点击百度地图，可以获取到对应的point, 由point的lng、lat属性就可以获取对应的经度纬度
                    var pt = e.point;
                    geoc.getLocation(pt, function (rs) { //addressComponents对象可以获取到详细的地址信息
                        var addComp = rs.addressComponents;
                        var site = addComp.province + ", " + addComp.city + ", " + addComp.district + ", " + addComp.street + ", " + addComp.streetNumber; //将对应的HTML元素设置值
                        $("#id_address").val(site);
                        $("#id_longitude").val(pt.lng);
                        $("#id_latitude").val(pt.lat);
                        $("#option_iapp_map_id_error").hide();
                    });
                });
                var address = $("input[name='address']").val();
                var ac = new BMap.Autocomplete(    //建立一个自动完成的对象,根据地址查询获取对应的地图信息
                    {
                        "input": "id_address"
                        , "location": map
                    });
                $("input[name='address']").val(address);
                ac.addEventListener("onhighlight", function (e) {  //鼠标放在下拉列表上的事件
                    var str = "";
                    var _value = e.fromitem.value;

                    var value = "";
                    if (e.fromitem.index > -1) {
                        value = _value.province + _value.city + _value.district + _value.street + _value.business;
                    }
                    str = "FromItem<br />index = " + e.fromitem.index + "<br />value = " + value;

                    value = "";
                    if (e.toitem.index > -1) {
                        _value = e.toitem.value;
                        value = _value.province + _value.city + _value.district + _value.street + _value.business;
                    }
                    str += "<br />ToItem<br />index = " + e.toitem.index + "<br />value = " + value;
                    G("searchResultPanel").innerHTML = str;
                });
                var myValue;
                ac.addEventListener("onconfirm", function (e) {    //鼠标点击下拉列表后的事件
                    var _value = e.item.value;
                    myValue = _value.province + _value.city + _value.district + _value.street + _value.business;
                    G("searchResultPanel").innerHTML = "onconfirm<br />index = " + e.item.index + "<br />myValue = " + myValue;
                    setPlace();
                });
                function setPlace() {
                    map.clearOverlays();    //清除地图上所有覆盖物
                    function myFun() {
                        var pp = local.getResults().getPoi(0).point;    //获取第一个智能搜索的结果
                        map.centerAndZoom(pp, 18);
                        map.addOverlay(new BMap.Marker(pp));    //添加标注
                        $("#id_longitude").val(pp.lng);
                        $("#id_latitude").val(pp.lat);
                    }

                    var local = new BMap.LocalSearch(map, { //智能搜索
                        onSearchComplete: myFun
                    });
                    local.search(myValue);
                }

            </script>
        {% endblock %}
{% endautoescape %}