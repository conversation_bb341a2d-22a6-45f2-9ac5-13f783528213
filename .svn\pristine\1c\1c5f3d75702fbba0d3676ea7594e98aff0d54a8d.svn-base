/*!
 * CSS3 Microsoft Metro Buttons
 * Inspired by <PERSON>'s CSS3 Google Buttons, Twitter Bootstrap, and Microsoft. Icons from glyphicons.com and Syncfusion's Metro Studio.
 * I do not claim ownership on the origin of design and icons.
 * Built by <PERSON> Subido (http://github.com/ace-subido)
 */
a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
a:hover, a:active {
  outline: 0;
}
button,
input,
select,
textarea {
  margin: 0;
  font-size: 100%;
  /*vertical-align: middle;*/

}
button, input {
  *overflow: visible;
  line-height: normal;
}
button::-moz-focus-inner, input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
  -moz-appearance: none; 
}
/* overrides button presets for mozilla clients*/
@-moz-document url-prefix() {
	button,
	input[type="button"],
	input[type="reset"],
	input[type="submit"] {
		cursor: pointer;
	 	padding: 6px 14px;
	}
}
input[type="search"] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-decoration, input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
.dropdown {
  position: relative;
}
.dropdown-toggle {
  *margin-bottom: -3px;  
}
.dropdown-toggle:active, .open .dropdown-toggle {
  outline: 0;
}

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  text-indent: -99999px;
  *text-indent: 0;
  vertical-align: top;
  margin-top: 5px;
  margin-left: 2px;
  margin-right: 2px;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid black;
  opacity: 0.5;
  filter: alpha(opacity=50);
  content: "\2193";  
}
.caret.white{
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid white;
  opacity: 0.95;
  filter: alpha(opacity=95);
}
.dropdown .caret {
  margin-top: 8px;
  margin-left: 2px;
}

.dropdown:hover .caret, .open.dropdown .caret {
  opacity: 1;
  filter: alpha(opacity=100);
}

.m-dropdown-menu {
  position: absolute;
  top: 98%;
  left: 0;
  z-index: 1000;
  float: left;
  display: none;
  min-width: 225px;
  max-width: 225px;
  padding: 0 0 6px 0;
  margin: 0 0 0;
  list-style: none;
  background-color: white; 
  
  -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
  
  font-size: 14px;
  font-family: "Segoe UI",Helvetica, Arial, sans-serif;
  
  border: 1px solid #eeeeee;
  
}

.m-dropdown-menu.bottom-up {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}

.m-dropdown-menu .divider {
  border-top: 1px solid #ebebeb;
  margin-top: 9px;
  margin-bottom: 10px;
  padding: 0;
  cursor: default; 
}
.m-dropdown-menu a {
  position: relative;
  padding: 6px 0 6px 30px;
  color: #333;
  text-decoration: none;
  display: block;
  clear: both;
  font-weight: normal;
  line-height: 18px;
  white-space: nowrap;
}
.m-dropdown-menu a [class^="icon-"] {
  position: absolute;
  left: 7px;
  top: 8px;
}
.m-dropdown-menu li > a:hover, .m-dropdown-menu .active > a, .m-dropdown-menu .active > a:hover {
  text-decoration: none;
  background-color: #eee;
}
.dropdown.open {
  *z-index: 1000;
}
.dropdown.open .dropdown-toggle {
  color: #08c;
  background: #ccc;
  background: rgba(0, 0, 0, 0.3);
}
.dropdown.open .m-dropdown-menu {
  display: block;
}

.m-btn {
  position: relative;
  display: inline-block;
  overflow: visible;
  margin: 0;
  margin-bottom: 2px;
  padding: 10px 14px;   
/*  margin-top: 8px;   
*/  cursor: pointer;
  outline: none;
  border: none; 
/*  background-color: #eeeeee;
  background-image: -moz-linear-gradient(top, #eeeeee, #eeeeee);
  background-image: -ms-linear-gradient(top, #eeeeee, #eeeeee);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#eeeeee));
  background-image: -webkit-linear-gradient(top, #eeeeee, #eeeeee);
  background-image: -o-linear-gradient(top, #eeeeee, #eeeeee);
  background-image: linear-gradient(top, #eeeeee, #eeeeee);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#eeeeee', GradientType=0);
*/  -webkit-background-clip: padding;
  -moz-background-clip: padding;
  background-clip: padding;
  /* IE hacks */

  zoom: 1;
  z-index: 1;
  *display: inline;
  font-size: 16px;
  line-height: 16px;
  color: #333333;
  min-width: 35px;
  
  text-shadow: #ffffff 0 1px 0;	 
  text-align: center;  
  text-decoration: none;
  white-space: nowrap;
  
  vertical-align: inherit;
}
.m-btn:hover,
.m-btn:focus,
.m-btn:active,
.m-btn.active {
  color: #333;
  text-decoration: none;  
  background-color: #dcdcdc;
  background-image: -moz-linear-gradient(top, #dcdcdc, #dcdcdc);
  background-image: -ms-linear-gradient(top, #dcdcdc, #dcdcdc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#dcdcdc), to(#dcdcdc));
  background-image: -webkit-linear-gradient(top, #dcdcdc, #dcdcdc);
  background-image: -o-linear-gradient(top, #dcdcdc, #dcdcdc);
  background-image: linear-gradient(top, #dcdcdc, #dcdcdc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#dcdcdc', endColorstr='#dcdcdc', GradientType=0); 
  z-index: 100;
  outline: none;
}
.m-btn:active, .m-btn.active {
  background-color: #eeeeee;
  background-image: -moz-linear-gradient(top, #eeeeee, #dcdcdc);
  background-image: -ms-linear-gradient(top, #eeeeee, #dcdcdc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#dcdcdc));
  background-image: -webkit-linear-gradient(top, #eeeeee, #dcdcdc);
  background-image: -o-linear-gradient(top, #eeeeee, #dcdcdc);
  background-image: linear-gradient(top, #eeeeee, #dcdcdc);
  background-repeat: repeat-x;
   -webkit-box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.25);
  box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#dcdcdc', GradientType=0); 
}
.m-btn:focus {
  /* Blue border on button focus. */
  border-color: #4D90FE;
}
/* overrides extra padding on button elements in Firefox */
.m-btn::-moz-focus-inner {
  padding: 0;
  border: 0;
}
.m-btn.red-stripe
{
	border-left: 3px solid #d84a38;
}

.m-btn.blue-stripe
{
	border-left: 3px solid #4d90fe;
}

.m-btn.purple-stripe
{
	border-left: 3px solid #852b99;
}

.m-btn.green-stripe
{
	border-left: 3px solid #35aa47;
}

/* Common button classes */
.m-btn.red:active,
.m-btn.red.active,
.m-btn.red.disabled,
.m-btn.red[disabled],
.m-btn.blue:active,
.m-btn.blue.active,
.m-btn.blue.disabled,
.m-btn.blue[disabled],
.m-btn.purple:active,
.m-btn.purple.active,
.m-btn.purple.disabled,
.m-btn.purple[disabled],
.m-btn.green:active,
.m-btn.green.active,
.m-btn.green.disabled,
.m-btn.green[disabled],
.m-btn.black:active,
.m-btn.black.active,
.m-btn.black.disabled,
.m-btn.black[disabled]
{
  -webkit-box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.25);
  box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.25);
  color: white !important;  
}
.m-btn.red.disabled,
.m-btn.red[disabled],
.m-btn.blue.disabled,
.m-btn.blue[disabled],
.m-btn.purple.disabled,
.m-btn.purple[disabled],
.m-btn.green.disabled,
.m-btn.green[disabled]
{
	opacity: .5;
}

.m-btn.black.disabled,
.m-btn.black[disabled]
{
	opacity: .5;
}

/*  Red */
.m-btn.red {
  color: white;  
  text-shadow: none;	
  background-color: #d84a38;
  background-image: -moz-linear-gradient(top, #dd4b39, #d14836);
  background-image: -ms-linear-gradient(top, #dd4b39, #d14836);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#dd4b39), to(#d14836));
  background-image: -webkit-linear-gradient(top, #dd4b39, #d14836);
  background-image: -o-linear-gradient(top, #dd4b39, #d14836);
  background-image: linear-gradient(top, #dd4b39, #d14836);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#dd4b39', endColorstr='#d14836', GradientType=0);
}
.m-btn.red:hover, 
.m-btn.red:focus, 
.m-btn.red:active, 
.m-btn.red.active,
.m-btn.red[disabled], 
.m-btn.red.disabled {    
  background-color: #c53727;
  background-image: -moz-linear-gradient(top, #c53727, #c53727);
  background-image: -ms-linear-gradient(top, #c53727, #c53727);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#c53727), to(#c53727));
  background-image: -webkit-linear-gradient(top, #c53727, #c53727);
  background-image: -o-linear-gradient(top, #c53727, #c53727);
  background-image: linear-gradient(top, #c53727, #c53727);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#c53727', endColorstr='#c53727', GradientType=0); 
}


.m-btn.red:active,
.m-btn.red.active
{
  background-color: #dd4b39;
  background-image: -moz-linear-gradient(top, #dd4b39, #c53727);
  background-image: -ms-linear-gradient(top, #dd4b39, #c53727);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#dd4b39), to(#c53727));
  background-image: -webkit-linear-gradient(top, #dd4b39, #c53727);
  background-image: -o-lineark-gradient(top, #dd4b39, #c53727);
  background-image: linear-gradient(top, #dd4b39, #c53727);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#dd4b39', endColorstr='#c53727', GradientType=0);

}

/*  Blue */
.m-btn.blue   
{
  color: white;  
  text-shadow: none;	
  background-color: #4d90fe;
  background-image: -moz-linear-gradient(top, #4d90fe, #4787ed);
  background-image: -ms-linear-gradient(top, #4d90fe, #4787ed);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#4d90fe), to(#4787ed));
  background-image: -webkit-linear-gradient(top, #4d90fe, #4787ed);
  background-image: -o-linear-gradient(top, #4d90fe, #4787ed);
  background-image: linear-gradient(top, #4d90fe, #4787ed);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4d90fe', endColorstr='#4787ed', GradientType=0);
}
.m-btn.blue:hover, 
.m-btn.blue:focus, 
.m-btn.blue:active,
.m-btn.blue.active,
.m-btn.blue[disabled],
.m-btn.blue.disabled {  
  background-color: #0072bb;
  background-image: -moz-linear-gradient(top, #0072bb, #0072bb);
  background-image: -ms-linear-gradient(top, #0072bb, #0072bb);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0072bb), to(#0072bb));
  background-image: -webkit-linear-gradient(top, #0072bb, #0072bb);
  background-image: -o-linear-gradient(top, #0072bb, #0072bb);
  background-image: linear-gradient(top, #0072bb, #0072bb);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0072bb', endColorstr='#0072bb', GradientType=0);
}

.m-btn.blue:active,
.m-btn.blue.active
{
  background-color: #4d90fe;
  background-image: -moz-linear-gradient(top, #4d90fe, #0072bb);
  background-image: -ms-linear-gradient(top, #4d90fe, #0072bb);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#4d90fe), to(#0072bb));
  background-image: -webkit-linear-gradient(top, #4d90fe, #0072bb);
  background-image: -o-linear-gradient(top, #4d90fe, #0072bb);
  background-image: linear-gradient(top, #4d90fe, #0072bb);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4d90fe', endColorstr='#0072bb', GradientType=0);

}
/*  Green */
.m-btn.green {
  color: white;
  text-shadow: none;	 
  background-color: #35aa47;
  background-image: -moz-linear-gradient(top, #35aa47, #35aa47);
  background-image: -ms-linear-gradient(top, #35aa47, #35aa47);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#35aa47), to(#35aa47));
  background-image: -webkit-linear-gradient(top, #35aa47, #35aa47);
  background-image: -o-linear-gradient(top, #35aa47, #35aa47);
  background-image: linear-gradient(top, #35aa47, #35aa47);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#35aa47', endColorstr='#35aa47', GradientType=0);
}
.m-btn.green:hover, 
.m-btn.green:focus, 
.m-btn.green:active, 
.m-btn.green.active,
.m-btn.green.disabled, 
.m-btn.green[disabled]{ 
  background-color: #1d943b;
  background-image: -moz-linear-gradient(top, #1d943b, #1d943b);
  background-image: -ms-linear-gradient(top, #1d943b, #1d943b);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#1d943b), to(#1d943b));
  background-image: -webkit-linear-gradient(top, #1d943b, #1d943b);
  background-image: -o-linear-gradient(top, #1d943b, #1d943b);
  background-image: linear-gradient(top, #1d943b, #1d943b);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1d943b', endColorstr='#1d943b', GradientType=0);
}

.m-btn.green:active,
.m-btn.green.active
{
  background-color: #35aa47;
  background-image: -moz-linear-gradient(top, #35aa47, #1d943b);
  background-image: -ms-linear-gradient(top, #35aa47, #1d943b);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#35aa47), to(#1d943b));
  background-image: -webkit-linear-gradient(top, #35aa47, #1d943b);
  background-image: -o-linear-gradient(top, #35aa47, #1d943b);
  background-image: linear-gradient(top, #35aa47, #1d943b);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#35aa47', endColorstr='#1d943b', GradientType=0);

}
/*  Purple */
.m-btn.purple {
  color: white; 
  text-shadow: none;	
  background-color: #852b99;
  background-image: -moz-linear-gradient(top, #852b99, #852b99);
  background-image: -ms-linear-gradient(top, #852b99, #852b99);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#852b99), to(#852b99));
  background-image: -webkit-linear-gradient(top, #852b99, #852b99);
  background-image: -o-linear-gradient(top, #852b99, #852b99);
  background-image: linear-gradient(top, #852b99, #852b99);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#852b99', endColorstr='#852b99', GradientType=0);
}
.m-btn.purple:hover, 
.m-btn.purple:focus, 
.m-btn.purple:active, 
.m-btn.purple.active, 
.m-btn.purple.disabled,
.m-btn.purple[disabled] { 
  background-color: #6d1b81;
  background-image: -moz-linear-gradient(top, #6d1b81, #6d1b81);
  background-image: -ms-linear-gradient(top, #6d1b81, #6d1b81);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#6d1b81), to(#6d1b81));
  background-image: -webkit-linear-gradient(top, #6d1b81, #6d1b81);
  background-image: -o-linear-gradient(top, #6d1b81, #6d1b81);
  background-image: linear-gradient(top, #6d1b81, #6d1b81);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#6d1b81', endColorstr='#6d1b81', GradientType=0);
}

.m-btn.purple:active,
.m-btn.purple.active
{
  background-color: #35aa47;
  background-image: -moz-linear-gradient(top, #852b99, #6d1b81);
  background-image: -ms-linear-gradient(top, #852b99, #6d1b81);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#852b99), to(#6d1b81));
  background-image: -webkit-linear-gradient(top, #852b99, #6d1b81);
  background-image: -o-linear-gradient(top, #852b99, #6d1b81);
  background-image: linear-gradient(top, #852b99, #6d1b81);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#852b99', endColorstr='#6d1b81', GradientType=0);

}


.m-btn.black {
  color: white; 
  text-shadow: none;	
  background-color: #555555;
  background-image: -moz-linear-gradient(top, #555555, #555555);
  background-image: -ms-linear-gradient(top, #555555, #555555);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#555555), to(#555555));
  background-image: -webkit-linear-gradient(top, #555555, #555555);
  background-image: -o-linear-gradient(top, #555555, #555555);
  background-image: linear-gradient(top, #555555, #555555);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#555555', endColorstr='#555555', GradientType=0);
}
.m-btn.black:hover, 
.m-btn.black:focus, 
.m-btn.black:active, 
.m-btn.black.active, 
.m-btn.black.disabled,
.m-btn.black[disabled] { 
  background-color: #222222;
  background-image: -moz-linear-gradient(top, #222222, #222222);
  background-image: -ms-linear-gradient(top, #222222, #222222);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#222222), to(#222222));
  background-image: -webkit-linear-gradient(top, #222222, #222222);
  background-image: -o-linear-gradient(top, #222222, #222222);
  background-image: linear-gradient(top, #222222, #222222);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#222222', endColorstr='#222222', GradientType=0);
}

.m-btn.black:active,
.m-btn.black.active
{
  background-color: #222222;
  background-image: -moz-linear-gradient(top, #444444, #222222);
  background-image: -ms-linear-gradient(top, #444444, #222222);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#222222), to(#222222));
  background-image: -webkit-linear-gradient(top, #444444, #222222);
  background-image: -o-linear-gradient(top, #444444, #222222);
  background-image: linear-gradient(top, #444444, #222222);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#444444', endColorstr='#222222', GradientType=0);

}


/*  ZK Green */
.m-btn.zkgreen {
  color: white;
  text-shadow: none;	 
  background: rgba(40,161,253,1);
 /* background-color: #7ac143;
  background-image: -moz-linear-gradient(top, #7ac143, #7ac143) !important;
  background-image: -ms-linear-gradient(top, #7ac143, #7ac143);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#7ac143), to(#7ac143));
  background-image: -webkit-linear-gradient(top, #7ac143, #7ac143);
  background-image: -o-linear-gradient(top, #7ac143, #7ac143);
  background-image: linear-gradient(top, #7ac143, #7ac143);
  background-repeat: repeat-x;
*/}
.m-btn.zkgreen:hover, 
.m-btn.zkgreen:focus, 
.m-btn.zkgreen:active, 
.m-btn.zkgreen.active
{
 background:rgba(54, 101, 253, 1);
}
.m-btn.zkgreen.disabled,
.m-btn.zkgreen[disabled]{
 background: #dddddd;
}

.m-btn.zkgreen:active,
.m-btn.zkgreen.active
{
  opacity: 1;
  background-color: #35aa47;
  background-image: -moz-linear-gradient(top, #35aa47, #1d943b);
  background-image: -ms-linear-gradient(top, #35aa47, #1d943b);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#35aa47), to(#1d943b));
  background-image: -webkit-linear-gradient(top, #35aa47, #1d943b);
  background-image: -o-linear-gradient(top, #35aa47, #1d943b);
  background-image: linear-gradient(top, #35aa47, #1d943b);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#35aa47', endColorstr='#1d943b', GradientType=0);

}


[class^="m-icon-"] {
    background-image: url("../img/syncfusion-icons-white.png");
    background-position: 0 0;
    background-repeat: no-repeat;
    display: inline-block;
    height: 14px;
    line-height: 14px;
    margin-top: 0;
    vertical-align: top;
    width: 14px;
}

.m-icon-white {
    background-image: url("../img/syncfusion-icons-white.png");
}
.m-icon-swapright {
    background-position: -27px -10px;
}

/*  Mini-button */
.sm {
  font-size: 10px;
  padding: 1px 1px;
  margin: 0 !important;
  
}
.mini 
{
  height: 20px;
  font-size: 12px;  
  line-height: 16px;
  padding: 2px 10px !important;
  margin: 0;
}
.big
{
	height: 38px;
	font-size: 18px; 
	line-height: 38px; 	
	padding: 20px 26px;
}

.rnd
{
    -webkit-border-radius: 3px;
    -moz-border-radius: 5px;
    border-radius: 3px;
}

.big.rnd
{
    -webkit-border-radius: 11px;
    -moz-border-radius: 11px;
    border-radius: 11px;
}

/*  Disabled */
.m-btn.disabled, .m-btn[disabled] {
  color: #999999;  
  background-color: #f5f5f5;
  background-image: -moz-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -ms-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#dddddd));
  background-image: -webkit-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -o-linear-gradient(top, #eeeeee, #dddddd);
  background-image: linear-gradient(top, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#dddddd', GradientType=0);      
  cursor: default;
  /*-webkit-box-shadow: inset 0 1px 6px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: inset 0 1px 6px rgba(0, 0, 0, 0.25);
  box-shadow: inset 0 1px 6px rgba(0, 0, 0, 0.25);*/
}
/*  Misc */
.m-btn.icn-only {
  min-width: 14px;
}
.m-btn.bigicn-only {
  min-width: 34px;
}
.m-btn-group {
  position: relative;
  display: inline-block;
  list-style: none;
  padding: 0;
  margin: 0;
  /* IE hacks */

  zoom: 1;
  *display: inline;
}
.m-btn + .m-btn,
.m-btn + .m-btn-group,
.m-btn-group + .m-btn,
.m-btn-group + .m-btn-group {
  margin-left: 15px; 
}

.m-btn.dropdown-carettoggle {
  min-width: 5px;
  height: 18px;
  padding: 8px;
}
.m-btn.dropdown-carettoggle > .caret {
  margin-top: 8px;
}
.m-btn.caret:hover {
  opacity: 1;
}

.m-btn-group .m-btn {
  position: relative;
  float: left;
  margin-left: -1px; 
}

.m-btn-group .m-btn:first-child {
  margin-left: 0;
}

.m-btn-group .m-btn.rnd:first-child {
    -webkit-border-radius: 5px 0 0 5px;
    -moz-border-radius: 5px 0 0 5px;
    border-radius: 5px 0 0 5px;
}

.m-btn-group .m-btn.rnd.dropdown-carettoggle {
    -webkit-border-radius: 0 5px 5px 0;
    -moz-border-radius: 0 5px 5px 0;
    border-radius: 0 5px 5px 0;
}

/* BUTTON CONTAINER */
/* For mixing buttons and button groups, e.g., in a navigation bar */
.m-btn-strip .m-btn, .m-btn-strip .m-btn-group {
  vertical-align: top;
}
.m-btn-group.open {
  *z-index: 1000;
}

.m-btn-group.open .dropdown-carettoggle,
.m-btn-group.open .dropdown-toggle {
  background-image: none;
  -webkit-box-shadow: inset 0 1px 6px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: inset 0 1px 6px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 6px rgba(0, 0, 0, 0.2);
}

.m-btn-group.open .m-dropdown-menu {
  display: block;
  margin-top: 1px;
}