#!/usr/bin/env python
#coding=utf-8
import urllib
import datetime
import os
from sgmllib import SGMLParser
from django.conf import settings
from mysite.iclock.models import iclock, devcmds
from mysite.utils import *
from mysite.iclock.dataproc import appendDevCmd

citys = {
    u"%s"%(_(u"Beijing")): u"54511",
    u"%s"%(_(u"Shanghai")): u"58367",
    u"%s"%(_(u"Tianjin")): u"54517",
    u"%s"%(_(u"Chongqing")): u"57516",
    u"%s"%(_(u"Hong Kong")): u"45005",
    u"%s"%(_(u"Macao")): u"45011",
    u"%s"%(_(u"Harbin")): u"50953",
    u"%s"%(_(u"Qiqihar")): u"50745",
    u"%s"%(_(u"Mudanjiang")): u"54094",
    u"%s"%(_(u"Daqing")): u"50842",
    u"%s"%(_(u"Yichun")): u"50774",
    u"%s"%(_(u"<PERSON>angyashan")): u"50884",
    u"%s"%(_(u"Hegang")): u"50775",
    u"%s"%(_(u"Chixi")): u"50978",
    u"%s"%(_(u"Jiamusi")): u"50873",
    u"%s"%(_(u"Qitaihe")): u"50971",
    u"%s"%(_(u"Black River")): u"50468",
    u"%s"%(_(u"Suihua")): u"50853",
    u"%s"%(_(u"Daxinganling")): u"50442",
    u"%s"%(_(u"Changchun")): u"54161",
    u"%s"%(_(u"Yanbian")): u"99999",
    u"%s"%(_(u"Jilin")): u"54172",
    u"%s"%(_(u"White Mountain")): u"54371",
    u"%s"%(_(u"White City")): u"50936",
    u"%s"%(_(u"Siping")): u"54157",
    u"%s"%(_(u"Songyuan")): u"50946",
    u"%s"%(_(u"Liaoyuan")): u"54260",
    u"%s"%(_(u"Daan")): u"50945",
    u"%s"%(_(u"Tonghua")): u"54363",
    u"%s"%(_(u"Shenyang")): u"54342",
    u"%s"%(_(u"Dalian")): u"54662",
    u"%s"%(_(u"Huludao")): u"54453",
    u"%s"%(_(u"Lushun")): u"54660",
    u"%s"%(_(u"Benxi")): u"54346",
    u"%s"%(_(u"Fushun")): u"54353",
    u"%s"%(_(u"Tieling")): u"54249",
    u"%s"%(_(u"Liaoyang")): u"54347",
    u"%s"%(_(u"Yingkou")): u"54471",
    u"%s"%(_(u"Fuxin")): u"54237",
    u"%s"%(_(u"Chaoyang")): u"54324",
    u"%s"%(_(u"Jinzhou")): u"54337",
    u"%s"%(_(u"Dandong")): u"54497",
    u"%s"%(_(u"Anshan")): u"54339",
    u"%s"%(_(u"Hohhot")): u"53463",
    u"%s"%(_(u"Hulunbeier")): u"99999",
    u"%s"%(_(u"baotou")): u"53446",
    u"%s"%(_(u"Chifeng")): u"54218",
    u"%s"%(_(u"Hailar")): u"50527",
    u"%s"%(_(u"Wuhai")): u"53512",
    u"%s"%(_(u"Ordos")): u"53543",
    u"%s"%(_(u"Xilinhot")): u"54102",
    u"%s"%(_(u"Tongliao")): u"54135",
    u"%s"%(_(u"Shijiazhuang")): u"53698",
    u"%s"%(_(u"Tangshan")): u"54534",
    u"%s"%(_(u"Zhangjiakou")): u"54401",
    u"%s"%(_(u"Langfang")): u"54515",
    u"%s"%(_(u"Xingtai")): u"53798",
    u"%s"%(_(u"Handan")): u"53892",
    u"%s"%(_(u"Cangzhou")): u"54616",
    u"%s"%(_(u"Hengshui")): u"54702",
    u"%s"%(_(u"Chengde")): u"54423",
    u"%s"%(_(u"Baoding")): u"54602",
    u"%s"%(_(u"Qinhuangdao")): u"54449",
    u"%s"%(_(u"Zhengzhou")): u"57083",
    u"%s"%(_(u"Opening")): u"57091",
    u"%s"%(_(u"Luoyang")): u"57073",
    u"%s"%(_(u"Pingding Mountain")): u"57171",
    u"%s"%(_(u"Jiao Zuo")): u"53982",
    u"%s"%(_(u"Hebi")): u"53990",
    u"%s"%(_(u"Xinxiang")): u"53986",
    u"%s"%(_(u"Anyang")): u"53898",
    u"%s"%(_(u"Puyang")): u"54900",
    u"%s"%(_(u"Xuchang")): u"57089",
    u"%s"%(_(u"Tahe")): u"57186",
    u"%s"%(_(u"Sanmenxia")): u"57051",
    u"%s"%(_(u"Nanyang")): u"57178",
    u"%s"%(_(u"Shangqiu")): u"58005",
    u"%s"%(_(u"Xinyang")): u"57297",
    u"%s"%(_(u"Zhoukou")): u"57195",
    u"%s"%(_(u"Zhumadian")): u"57290",
    u"%s"%(_(u"Jinan")): u"54823",
    u"%s"%(_(u"Qingdao")): u"54857",
    u"%s"%(_(u"Zibo")): u"54830",
    u"%s"%(_(u"Weihai")): u"54774",
    u"%s"%(_(u"Qufu")): u"54918",
    u"%s"%(_(u"Linyi")): u"54938",
    u"%s"%(_(u"Yantai")): u"54765",
    u"%s"%(_(u"Zaozhuang")): u"58024",
    u"%s"%(_(u"Liaocheng")): u"54806",
    u"%s"%(_(u"Jining")): u"54915",
    u"%s"%(_(u"Heze")): u"54906",
    u"%s"%(_(u"Taian")): u"54827",
    u"%s"%(_(u"sunshine")): u"54945",
    u"%s"%(_(u"Dongying")): u"54736",
    u"%s"%(_(u"Texas")): u"54714",
    u"%s"%(_(u"Binzhou")): u"54734",
    u"%s"%(_(u"Laiwu")): u"54828",
    u"%s"%(_(u"Weifang")): u"54843",
    u"%s"%(_(u"Taiyuan")): u"53772",
    u"%s"%(_(u"Yangquan")): u"53782",
    u"%s"%(_(u"Jincheng")): u"53976",
    u"%s"%(_(u"Jinzhong")): u"53778",
    u"%s"%(_(u"Linfen")): u"53868",
    u"%s"%(_(u"Yuncheng")): u"53959",
    u"%s"%(_(u"Changzhi")): u"53882",
    u"%s"%(_(u"Shuozhou")): u"53578",
    u"%s"%(_(u"Xinzhou")): u"53674",
    u"%s"%(_(u"Datong")): u"53487",
    u"%s"%(_(u"Nanjing")): u"58238",
    u"%s"%(_(u"Suzhou")): u"58357",
    u"%s"%(_(u"Kunshan")): u"58356",
    u"%s"%(_(u"Nantong")): u"58259",
    u"%s"%(_(u"Taicang")): u"58377",
    u"%s"%(_(u"Wu County")): u"58349",
    u"%s"%(_(u"Xuzhou")): u"58027",
    u"%s"%(_(u"Yixing")): u"58346",
    u"%s"%(_(u"Zhenjiang")): u"58248",
    u"%s"%(_(u"Huai&#39;an")): u"58145",
    u"%s"%(_(u"Changshu")): u"58352",
    u"%s"%(_(u"Yancheng")): u"58151",
    u"%s"%(_(u"Taizhou")): u"58246",
    u"%s"%(_(u"Wuxi")): u"58354",
    u"%s"%(_(u"Lianyungang")): u"58044",
    u"%s"%(_(u"Yangzhou")): u"58245",
    u"%s"%(_(u"Changzhou")): u"58343",
    u"%s"%(_(u"Suqian")): u"58131",
    u"%s"%(_(u"Hefei")): u"58321",
    u"%s"%(_(u"Chaohu")): u"58326",
    u"%s"%(_(u"Bengbu")): u"58221",
    u"%s"%(_(u"Anqing")): u"58424",
    u"%s"%(_(u"Lu&#39;an")): u"58311",
    u"%s"%(_(u"Chuzhou")): u"58236",
    u"%s"%(_(u"Ma On Shan")): u"58336",
    u"%s"%(_(u"Fuyang")): u"58203",
    u"%s"%(_(u"Xuancheng")): u"58433",
    u"%s"%(_(u"Tongling")): u"58429",
    u"%s"%(_(u"Huaibei")): u"58116",
    u"%s"%(_(u"Wuhu")): u"58334",
    u"%s"%(_(u"millian state")): u"99999",
    u"%s"%(_(u"Suzhou")): u"58122",
    u"%s"%(_(u"Huainan")): u"58224",
    u"%s"%(_(u"Chizhou")): u"58427",
    u"%s"%(_(u"Xi&#39;an")): u"57036",
    u"%s"%(_(u"Han City")): u"53955",
    u"%s"%(_(u"Ankang")): u"57245",
    u"%s"%(_(u"Hanzhong")): u"57127",
    u"%s"%(_(u"Baoji")): u"57016",
    u"%s"%(_(u"Xianyang")): u"57048",
    u"%s"%(_(u"Yulin")): u"53646",
    u"%s"%(_(u"Southern Anhui")): u"57045",
    u"%s"%(_(u"Shangluo")): u"57143",
    u"%s"%(_(u"Tongchuan")): u"53947",
    u"%s"%(_(u"Yan&#39;an")): u"53845",
    u"%s"%(_(u"Yinchuan")): u"53614",
    u"%s"%(_(u"Guyuan")): u"53817",
    u"%s"%(_(u"Zhongwei")): u"53704",
    u"%s"%(_(u"Shizuishan")): u"53518",
    u"%s"%(_(u"Wu Zhong")): u"53612",
    u"%s"%(_(u"Lanzhou")): u"52889",
    u"%s"%(_(u"silver")): u"52896",
    u"%s"%(_(u"Qingyang")): u"53829",
    u"%s"%(_(u"Jiuquan")): u"52533",
    u"%s"%(_(u"Tianshui")): u"57006",
    u"%s"%(_(u"Wuwei")): u"52679",
    u"%s"%(_(u"Zhangye")): u"52652",
    u"%s"%(_(u"Gannan")): u"50741",
    u"%s"%(_(u"Linxia")): u"52984",
    u"%s"%(_(u"Pingliang")): u"53915",
    u"%s"%(_(u"Dingxi")): u"52995",
    u"%s"%(_(u"Jinchang")): u"52675",
    u"%s"%(_(u"Xining")): u"52866",
    u"%s"%(_(u"Habei")): u"52754",
    u"%s"%(_(u"Haixi")): u"52737",
    u"%s"%(_(u"Huangnan")): u"56065",
    u"%s"%(_(u"Golo")): u"56043",
    u"%s"%(_(u"Yushu")): u"56029",
    u"%s"%(_(u"Haidong")): u"52875",
    u"%s"%(_(u"Hainan")): u"52856",
    u"%s"%(_(u"Wuhan")): u"57494",
    u"%s"%(_(u"Yichang")): u"57461",
    u"%s"%(_(u"Huanggang")): u"57498",
    u"%s"%(_(u"Enshi")): u"57447",
    u"%s"%(_(u"Jingzhou")): u"57476",
    u"%s"%(_(u"Shen Nongjia")): u"57362",
    u"%s"%(_(u"Ten Ten")): u"57256",
    u"%s"%(_(u"Xianning")): u"57590",
    u"%s"%(_(u"Xiangfan")): u"57278",
    u"%s"%(_(u"Xiaogan")): u"57482",
    u"%s"%(_(u"Suizhou")): u"57381",
    u"%s"%(_(u"Yellowstone")): u"58407",
    u"%s"%(_(u"Jingmen")): u"57377",
    u"%s"%(_(u"Ezhou")): u"57496",
    u"%s"%(_(u"Changsha")): u"57687",
    u"%s"%(_(u"Shaoyang")): u"57766",
    u"%s"%(_(u"Changde")): u"57662",
    u"%s"%(_(u"Chenzhou")): u"57972",
    u"%s"%(_(u"Ji Shou")): u"57649",
    u"%s"%(_(u"Zhuzhou")): u"57780",
    u"%s"%(_(u"Bottom")): u"57763",
    u"%s"%(_(u"Xiangtan")): u"57773",
    u"%s"%(_(u"Yiyang")): u"99999",
    u"%s"%(_(u"Yongzhou")): u"57866",
    u"%s"%(_(u"Yue Yang")): u"57584",
    u"%s"%(_(u"Hengyang")): u"57872",
    u"%s"%(_(u"Huaihua")): u"57749",
    u"%s"%(_(u"Shaoshan")): u"57771",
    u"%s"%(_(u"Zhangjiajie")): u"57558",
    u"%s"%(_(u"Hangzhou")): u"58457",
    u"%s"%(_(u"Huzhou")): u"58450",
    u"%s"%(_(u"Jinhua")): u"58549",
    u"%s"%(_(u"Ningbo")): u"58563",
    u"%s"%(_(u"Lishui")): u"58646",
    u"%s"%(_(u"Shaoxing")): u"58453",
    u"%s"%(_(u"Yandang Mountain")): u"99999",
    u"%s"%(_(u"Quzhou")): u"58633",
    u"%s"%(_(u"Jiaxing")): u"58452",
    u"%s"%(_(u"Taizhou")): u"58660",
    u"%s"%(_(u"Zhoushan")): u"58477",
    u"%s"%(_(u"Wenzhou")): u"58659",
    u"%s"%(_(u"Nanchang")): u"58606",
    u"%s"%(_(u"Pingxiang")): u"57786",
    u"%s"%(_(u"Jiujiang")): u"58502",
    u"%s"%(_(u"Shangrao")): u"58637",
    u"%s"%(_(u"Fuzhou")): u"58617",
    u"%s"%(_(u"Jian")): u"57799",
    u"%s"%(_(u"Yingtan")): u"58627",
    u"%s"%(_(u"Yichun")): u"57793",
    u"%s"%(_(u"New Yu")): u"57796",
    u"%s"%(_(u"Jingdezhen")): u"58527",
    u"%s"%(_(u"Ganzhou")): u"57993",
    u"%s"%(_(u"Fuzhou")): u"58847",
    u"%s"%(_(u"Xiamen")): u"59134",
    u"%s"%(_(u"Longyan")): u"58927",
    u"%s"%(_(u"Nanping")): u"58834",
    u"%s"%(_(u"Ningde")): u"58846",
    u"%s"%(_(u"Putian")): u"58946",
    u"%s"%(_(u"Quanzhou")): u"59137",
    u"%s"%(_(u"Sanming")): u"58828",
    u"%s"%(_(u"Zhangzhou")): u"59126",
    u"%s"%(_(u"Guiyang")): u"57816",
    u"%s"%(_(u"Anshun")): u"57806",
    u"%s"%(_(u"Chishui")): u"57609",
    u"%s"%(_(u"Zunyi")): u"57713",
    u"%s"%(_(u"Tongren")): u"57741",
    u"%s"%(_(u"Six Panshui")): u"56693",
    u"%s"%(_(u"Bijie")): u"57707",
    u"%s"%(_(u"Kerry")): u"57825",
    u"%s"%(_(u"Duyun")): u"57827",
    u"%s"%(_(u"Chengdu")): u"56294",
    u"%s"%(_(u"Luzhou")): u"57602",
    u"%s"%(_(u"Neijiang")): u"57504",
    u"%s"%(_(u"Liangshan")): u"56571",
    u"%s"%(_(u"Aba")): u"56171",
    u"%s"%(_(u"Bazhong")): u"57313",
    u"%s"%(_(u"Guangyuan")): u"57206",
    u"%s"%(_(u"Leshan")): u"56386",
    u"%s"%(_(u"Mianyang")): u"56196",
    u"%s"%(_(u"Deyang")): u"56198",
    u"%s"%(_(u"Panzhihua")): u"56666",
    u"%s"%(_(u"Ya&#39;an")): u"56287",
    u"%s"%(_(u"Yibin")): u"56492",
    u"%s"%(_(u"Zigong")): u"56396",
    u"%s"%(_(u"Ganzi Prefecture")): u"56146",
    u"%s"%(_(u"Dazhou")): u"57328",
    u"%s"%(_(u"Ziyang")): u"56298",
    u"%s"%(_(u"Guangan")): u"57415",
    u"%s"%(_(u"Suining")): u"57405",
    u"%s"%(_(u"Meimei")): u"56391",
    u"%s"%(_(u"Nanchong")): u"57411",
    u"%s"%(_(u"Guangzhou")): u"59287",
    u"%s"%(_(u"Shenzhen")): u"59493",
    u"%s"%(_(u"Chaozhou")): u"59312",
    u"%s"%(_(u"Shaoguan")): u"59082",
    u"%s"%(_(u"Zhanjiang")): u"59658",
    u"%s"%(_(u"Huizhou")): u"59298",
    u"%s"%(_(u"Qingyuan")): u"59280",
    u"%s"%(_(u"Dongguan")): u"59289",
    u"%s"%(_(u"Jiangmen")): u"59473",
    u"%s"%(_(u"Mao Ming")): u"59659",
    u"%s"%(_(u"Zhaoqing")): u"59278",
    u"%s"%(_(u"Iris")): u"59501",
    u"%s"%(_(u"Heyuan")): u"59293",
    u"%s"%(_(u"Jieyang")): u"59315",
    u"%s"%(_(u"Meizhou")): u"59117",
    u"%s"%(_(u"Zhongshan")): u"59485",
    u"%s"%(_(u"Deqing")): u"59269",
    u"%s"%(_(u"Yangjiang")): u"59663",
    u"%s"%(_(u"Yunfu")): u"59471",
    u"%s"%(_(u"Zhuhai")): u"59488",
    u"%s"%(_(u"Shantou")): u"59316",
    u"%s"%(_(u"Foshan")): u"59279",
    u"%s"%(_(u"Nanning")): u"59432",
    u"%s"%(_(u"Guilin")): u"57957",
    u"%s"%(_(u"Yangshuo")): u"59051",
    u"%s"%(_(u"Liuzhou")): u"59046",
    u"%s"%(_(u"Wuzhou")): u"59265",
    u"%s"%(_(u"Yulin")): u"59453",
    u"%s"%(_(u"Gui Ping")): u"59254",
    u"%s"%(_(u"Hezhou")): u"59065",
    u"%s"%(_(u"Qinzhou")): u"59632",
    u"%s"%(_(u"Guigang")): u"59249",
    u"%s"%(_(u"Fighting City Port")): u"59635",
    u"%s"%(_(u"Bai")): u"59211",
    u"%s"%(_(u"North Sea")): u"59644",
    u"%s"%(_(u"Hechi")): u"59023",
    u"%s"%(_(u"guest")): u"59242",
    u"%s"%(_(u"Chong Zuo")): u"59425",
    u"%s"%(_(u"Kunming")): u"56778",
    u"%s"%(_(u"Baoshan")): u"56748",
    u"%s"%(_(u"Chu Xiong")): u"56768",
    u"%s"%(_(u"Dehong")): u"56844",
    u"%s"%(_(u"Red River")): u"56975",
    u"%s"%(_(u"Lincang")): u"56951",
    u"%s"%(_(u"Nujiang")): u"56533",
    u"%s"%(_(u"Qujing")): u"56783",
    u"%s"%(_(u"Siomao")): u"56964",
    u"%s"%(_(u"Wenshan")): u"56994",
    u"%s"%(_(u"Yuxi")): u"56875",
    u"%s"%(_(u"Zhaotong")): u"56586",
    u"%s"%(_(u"Lijiang")): u"56651",
    u"%s"%(_(u"Dali")): u"56751",
    u"%s"%(_(u"Haikou")): u"59758",
    u"%s"%(_(u"Sanya")): u"59948",
    u"%s"%(_(u"Danzhou")): u"59845",
    u"%s"%(_(u"Qiong Mountain")): u"59757",
    u"%s"%(_(u"Tongshen")): u"59941",
    u"%s"%(_(u"Wenchang")): u"59856",
    u"%s"%(_(u"Urumqi")): u"51463",
    u"%s"%(_(u"Altay")): u"51076",
    u"%s"%(_(u"Aksu")): u"51628",
    u"%s"%(_(u"Changji")): u"51368",
    u"%s"%(_(u"Hami")): u"52203",
    u"%s"%(_(u"Hetian")): u"51828",
    u"%s"%(_(u"Kashi")): u"51709",
    u"%s"%(_(u"Karamai")): u"51243",
    u"%s"%(_(u"Shihezi")): u"51356",
    u"%s"%(_(u"Tacheng")): u"51133",
    u"%s"%(_(u"Korla")): u"51656",
    u"%s"%(_(u"Turpan")): u"51573",
    u"%s"%(_(u"Yining")): u"51431",
    u"%s"%(_(u"Lhasa")): u"55591",
    u"%s"%(_(u"Ali")): u"55437",
    u"%s"%(_(u"Changdu")): u"56137",
    u"%s"%(_(u"Naqu")): u"55299",
    u"%s"%(_(u"Shigatse")): u"55578",
    u"%s"%(_(u"Shannan")): u"55598",
    u"%s"%(_(u"Linzhi")): u"56312",
    u"%s"%(_(u"Taipei")): u"58968",
    u"%s"%(_(u"Kaohsiung")): u"59554",
}

class WeatherStation(SGMLParser):
	def __init__(self, url): 
		SGMLParser.__init__(self)
		self._is_weather, self._is_td  = False, False
		self._page_datas, self._weather = None, []	# 网页数据，天气数据
		self._get_page_datas(url)
		self.feed(self._page_datas)
		
	def _get_page_datas(self, url): # 抓取网页数据
		sock = urllib.urlopen(url) 
		lines = sock.read().split("\n")
		sock.close()		
		lines1=[]
		for line in lines: #一行一行转换，避免一行出错影响全部
			try:
				l=line.decode("GB18030")
				lines1.append(l)
			except: 
				pass
		self._page_datas=u"\n".join(lines1)
		
	def start_td(self, attrs):
		self._is_td = True
		
	def end_td(self):
		self._is_td = False
	
	def handle_data(self, text):
		text = self._process_data(text)		
		if  text and self._is_weather:
			self._weather.append(text)            
		if "时间".decode("utf-8") in text:
			self._is_weather = True		
		if "指数查询".decode("utf-8") in text:
			self._is_weather = False
	
	def show(self):
		if len(self._weather) > 16:
			return self._weather[:12] + self._formatWinds(self._weather[12:])
		return self._weather
	
	def _formatWinds(self, li): # 风力/风向格式不规整
		s = "\n".join(li)
		s = s.replace("\n<\n", "<")
		return s.split("\n")
	
	def _process_data(self, text):	# 清理冗余数据
		text = self._cut_words(text, ['\n', '\r', '\t', ' '])		
		return text
	
	def _cut_words(self, text, words):  # 清理指定字符
		if type(words) == type([]):
			for row in words:
				while row in text:					
					pos = text.index(row)
					text = text[:pos] + text[(pos+1):]
		elif words is not None:			
			while words in text:
				pos = text.index(words)
				text = text[:pos] + text[(pos+1):]			
		return text
			


def getWeather(area):
#	print "getWeather", area, 
	if area is None: return None
	try:
		areaID=citys[area]
	except Exception as e:
		try:
			areaID=citys[area.decode("GBK")]
		except Exception as e:
			areaID=area
    WeatherDB = getStoredFileName('weather', None)
	WeatherDir = WeatherDB + areaID
	today = (str(datetime.datetime.now())[:10]).replace("-", "")
	WeatherFile = WeatherDir + "/" + today + ".txt"
	if not os.path.isfile(WeatherFile): # 下载天气预报
		url = "http://www.nmc.gov.cn/weatherdetail/%s.html" % (areaID,)        
		try:
			lister = WeatherStation(url)
			datas = lister.show()[:16]
		except Exception as e:
			errorLog()
			datas = None		
		try:
			if datas and (len(datas) == 16):
				s="%s\t%s\t%s\t%s\n%s\t%s\t%s\t%s\n%s\t%s\t%s\t%s\n" % (datas[0], datas[4], datas[8], datas[12], 
						datas[1], datas[5], datas[9], datas[13],
						datas[2], datas[6], datas[10], datas[14])
				if not os.path.isdir(WeatherDir):
					os.makedirs(WeatherDir)    
				open(WeatherFile, "w+").write(s.encode("utf-8"))
				return s.split("\n")
		except Exception as e:
			errorLog()
	try:
		datas = open(WeatherFile, "r").read().decode("utf-8").split("\n")
	except:
		datas= [u"NULL\n"]
	return datas

def getWeatherCmd(device, wData):
	tft=device.IsTft()
	delta=datetime.datetime.now()-datetime.timedelta(0,3*60*60)  #3小时前
	if devcmds.objects.filter(CmdContent__startswith=tft and "WEATHER MSG=" or "SMS TYPE=IDLELCD", SN=device, 
		CmdCommitTime__gt=delta).count()>0: return None
	if tft:
		return getWeatherCmdTFT(wData)
	return getWeatherCmdMono(wData)

WEATHER_PIC_INDEX={
	u"%s"%(_(u"clear")):"w1.gif",
	u"%s"%(_(u"Partly cloudy")):"w2.gif",
	u"%s"%(_(u"Sun to cloudy")):"w3.gif",
	u"%s"%(_(u"Cloudy to overcast")):"w4.gif",
	u"%s"%(_(u"Little rain")):"w5.gif",
	u"%s"%(_(u"Middle rain")):"w6.gif",
	u"%s"%(_(u"heavy rain")):"w7.gif",
#	u"":"w8.gif",
#	u"":"w9.gif",
#	u"":"w10.gif",
	u"%s"%(_(u"shower")):"w11.gif",
#	u"":"w12.gif",
#	u"":"w13.gif",
#	u"":"w14.gif",
#	u"":"w15.gif",
#	u"":"w16.gif",
#	u"":"w17.gif",
#	u"":"w18.gif",
#	u"":"w19.gif",
#	u"":"w20.gif",
#	u"":"w21.gif",
#	u"":"w22.gif",
#	u"":"w23.gif",
#	u"":"w24.gif",
#	u"":"w25.gif",
}


def getWeatherCmdTFT(wData):
	wd=[]
	for d in wData:
		ds=d.split("\t")
		if len(ds)>2:
			try:
				dpic=WEATHER_PIC_INDEX[ds[1]]
			except:
				dpic="w1.gif"
			ds.append(dpic)
			wd.append("\t".join(ds))
	return u"WEATHER MSG="+(u"\\n".join(wd))

def getWeatherCmdMono(wData):
	today_weather = formatWeather4iClock(wData[0]) 
	tomorrow_weather = formatWeather4iClock(wData[1])
	if today_weather is None or tomorrow_weather is None: return None		
	ss = today_weather + " " * 16 + tomorrow_weather
	return u"SMS TYPE=IDLELCD\tMSG=%s" % (ss)


def checkWeatherForDev(device):
	areaID=device.City
	if not areaID: return None
	datas=getWeather(areaID)
	if datas and (u"NULL\n" not in datas):
		cmd=getWeatherCmd(device, datas)
		if cmd:	
			return appendDevCmd(device, cmd)
	return datas

def formatWeather4iClock(u8):
	u = u8 #.decode("utf-8")
	if u[-1] == '\n': u = u[:-1]
	arr = u.split("\t")	
	ret = ""
	for row in arr:
		ret += gb_16(row)
	return ret

def gb_16(s):
	size = len(s.encode("gb2312"))
	count = (size+15) / 16 * 16 - size	
	return s + " " * count


def index(request):
	from django.http import HttpResponse
	device = iclock.objects.all().filter(SN="888888")[0]
	if get_Weather(device.Address.split("=")[1], device) is None:
		return HttpResponse("False")
	return HttpResponse("True")
