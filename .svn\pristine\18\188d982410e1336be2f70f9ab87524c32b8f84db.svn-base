# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2019-01-11 10:58
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('visitors', '0004_auto_20180824_1615'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='reservation',
            name='visDate',
        ),
        migrations.AddField(
            model_name='reservation',
            name='Mobile',
            field=models.CharField(blank=True, db_column='mobile', max_length=60, null=True, verbose_name='Visitor Fhone'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='levels',
            field=models.CharField(blank=True, max_length=60, null=True, verbose_name='Rights Groups'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='recType',
            field=models.IntegerField(choices=[(0, 'appointment'), (1, 'appointment_success'), (2, 'appointment_fail'), (3, 'invite'), (4, 'invite_success'), (5, 'invite_fail'), (6, 'visite_accept_invite')], default=0, null=True, verbose_name='Checkinout type:'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='userid',
            field=models.CharField(blank=True, max_length=60, null=True, verbose_name='wxvistor userid'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='v_face_path',
            field=models.CharField(blank=True, editable=False, max_length=60, null=True, verbose_name='wxvistor face_path'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='v_userid',
            field=models.CharField(blank=True, editable=False, max_length=60, null=True, verbose_name='wxvistor v_userid'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='viscomeDate',
            field=models.DateTimeField(db_column='viscomedate', null=True, verbose_name='Pre-visit time'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='vislevelDate',
            field=models.DateTimeField(db_column='visleveldate', null=True, verbose_name='Pre-level time'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='remark',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Notes'),
        ),
    ]
