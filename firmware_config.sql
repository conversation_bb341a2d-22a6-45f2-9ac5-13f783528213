-- 固件配置数据导入SQL
-- 清空现有数据
DELETE FROM firmware_config;

-- 插入固件配置数据
INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('专业门禁协议', 'AccessRuleType=1
~LockFunOn=15
~APBFO=1
AuxInFunOn=1
Door1FirstCardOpenDoor=0
BreakAdminType=1
DeviceType=acc', '技术支持需要考虑设备是否支持', 1);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('考勤协议', 'AccessRuleType=0
~LockFunOn=15
~APBFO=0
AuxInFunOn=0
Door1FirstCardOpenDoor=0
BreakAdminType=0
DeviceType=att', '', 2);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('卡号正序', '~CardByteRevert=0', '考勤/门禁新架构设备支持', 3);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('卡号反序', '~CardByteRevert=1', '考勤/门禁新架构设备支持', 4);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('SSR功能', 'IsSupportSSR=1', '技术支持需要考虑设备是否支持', 5);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('字母工号', 'HasSupportABCPin=1
IsSupportABCPin=1', '考勤/门禁新架构设备支持', 6);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('支持T9输入法', 'HzImeOn=1', '', 7);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('10位工号', '~PIN2Width=10', '目前支持一体机', 8);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('18位工号', '~PIN2Width=18', '目前支持一体机', 9);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('支持WEB server功能', 'IsSupportWeb=1', '', 10);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('高级门禁', '~LockFunOn=15', '', 11);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('简单门禁', '~LockFunOn=1', '', 12);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('支持https通讯', 'IsSupportHttps=1
IsSupportSSL=1', '新架构一体机支持', 13);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('关闭指纹功能', 'FingerFunOn=0', '', 14);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('关闭人脸功能', 'FaceFunOn=0', '', 15);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('开启触摸唤醒功能', 'IsSupportTouch=1
TouchWakeUp=1
OpenTouchWakeUp=1', '可见光新版本支持', 16);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('设备号最大支持1-9999', 'DeviceIDStyle=1', '', 17);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('摄像头支持动态二维码功能', 'IsSupportQRcode=1
QRCodeEnable=1
QRCodeDataStream=0
QRCodeDecryptType=1
DynamicQRCodeDec=1
QRCodeDecryptFunList=010', '可见光新版本支持', 18);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('读ID卡后8位卡号', 'IDCardStyle=1', '', 19);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('读ID卡后5位卡号', 'IDCardStyle=2', '', 20);

INSERT INTO firmware_config (feature_name, parameter_value, remark, sort_order) VALUES ('重复确认时间内支持开门', 'RcheckOpenDoor=1', '', 21); 