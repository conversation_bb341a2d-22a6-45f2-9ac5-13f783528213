{% autoescape off %}
{% load iclock_tags %}

[
{% for item in latest_item_list %}
{"id":"{{ item.id }}",
"PIN":"{% if can_change %}{% if item.State == 0 %}<a class='can_edit'  href='#' onclick='javascript:editclick({{item.id}});'>{{ item.employee.PIN }}</a>{% else %}{% if item.State == 6 %}<a class='can_edit'  href='#' onclick='javascript:editclick({{item.id}});'>{{ item.employee.PIN }}</a>{% else %}{{ item.employee.PIN }}{% endif %}{% endif %}{% else %}{{ item.employee.PIN }}{%endif%}",
"EName":"{{ item.employee.EName|default:'' }}",
"DeptID":"{{ item.employee.Dept.DeptID }}",
"DeptName":"{{ item.employee.Dept.DeptName }}",
"ApplyDate":"{{ item.ApplyDate }}",
"starttime":"{{ item.starttime }}",
"endtime":"{{ item.endtime }}",
"location":"{{ item.location }}",
"process":"{{ item|showprocess }}",
"reason":"{{ item.reason }}",
"MODIFYBY":"{{ item.MODIFYBY }}",
"State":"{{ item.get_State_display|get_State_State}}"}
{%if not forloop.last%},{%endif%}
{% endfor %}
]
{% endautoescape %}
