@charset "utf-8";
/* CSS Document*/
.parentFileBox {
	width:auto;
	height:auto;
	overflow:hidden;
	position:relative;
}
.parentFileBox>.fileBoxUl {
	position:relative;
	width:100%;
	height:auto;
	overflow:hidden;
	padding-bottom:5px;
}
.parentFileBox>.fileBoxUl>li {
	float:left;
	border:1px solid #09F;
	border-radius:5px;
	width:170px;
	height:150px;
	margin-top:5px;
	margin-left:5px;
	overflow:hidden;
	position:relative;
	background-color:#099;
}
.parentFileBox>.fileBoxUl>li>.viewThumb {
	position:absolute;
	top:0;
	left:0;
	width:170px;
	height:150px;
	overflow:hidden;
}
.parentFileBox>.fileBoxUl>li>.viewThumb>img {
	width:100%;
	height:100%;
}
.parentFileBox>.fileBoxUl>li>.diyCancel,.parentFileBox>.fileBoxUl>li>.diySuccess {
	position:absolute;
	width:32px;
	height:32px;
	top:2px;
	right:2px;
	cursor:pointer;
	display:none;
}
.parentFileBox>.fileBoxUl>li>.diyCancel {
	background:url(../img/x_alt.png) no-repeat;
}
.parentFileBox>.fileBoxUl>li>.diySuccess {
	background:url(../img/check_alt.png) no-repeat;
	cursor:default;
}
.parentFileBox>.fileBoxUl>li>.diyFileName {
	position:absolute;
	bottom:0px;
	left:0px;
	width:100%;
	height:20px;
	line-height:20px;
	text-align:center;
	color:#fff;
	font-size:12px;
	display:none;
	background:url(../img/bgblack.png);
}
.parentFileBox>.fileBoxUl>li>.diyBar {
	top:0;
	left:0;
	position: absolute;
	width: 170px;
	height: 150px;
	line-height:150px;
	background:url(../img/bgblack.png);
	display:none;
}
.parentFileBox>.fileBoxUl>li>.diyBar>.diyProgressText {
	font-size:14px;
	text-align:center;
	color:#FFF;
	position:relative;
	z-index:99;
}
.parentFileBox>.fileBoxUl>li>.diyBar>.diyProgress {
	position:absolute;
	left:0;
	top:42%;
	height:24px;
	width:100%;
	background-color:#09F;
	filter:alpha(opacity=70);
	-moz-opacity:0.7;
	opacity:0.7;
	z-index:97;
}
.parentFileBox>.diyButton {
	width:100%;
	margin-top:5px;
	margin-bottom:5px;
	height:20px;
	line-height:20px;
	text-align:center;
}
.parentFileBox>.diyButton>a {
	padding:5px 10px 5px 10px;
	background-color:#09C;
	color:#FFF;
	font-size:12px;
	text-decoration:none;
	border-radius:3px;
}
.parentFileBox>.diyButton>a:hover {
	background-color:#0CC;
	color:#F30;
}
.parentFileBox>.fileBoxUl>li:hover {
	-moz-box-shadow: 3px 3px 4px #FF0;
	-webkit-box-shadow: 3px 3px 4px #FF0;
	box-shadow: 3px 3px 4px #FF0;
}
.parentFileBox>.fileBoxUl>.diyUploadHover:hover .diyCancel {
	display:block;
}
.parentFileBox>.fileBoxUl>li:hover .diyFileName {
	display:block;
}
.avi_diy_bg,.txt_diy_bg,.doc_diy_bg,.zip_diy_bg,.csv_diy_bg,.xls_diy_bg,.mp3_diy_bg,.pdf_diy_bg,.rar_diy_bg {
	background-position:center;
	background-repeat:no-repeat;
}
.avi_diy_bg {
	background-image:url(../img/filebg/avi.png);
}
.txt_diy_bg {
	background-image:url(../img/filebg/txt.png);
}
.doc_diy_bg {
	background-image:url(../img/filebg/doc.png);
}
.zip_diy_bg {
	background-image:url(../img/filebg/zip.png);
}
.csv_diy_bg {
	background-image:url(../img/filebg/csv.png);
}
.xls_diy_bg {
	background-image:url(../img/filebg/xls.png);
}
.mp3_diy_bg {
	background-image:url(../img/filebg/mp3.png);
}
.pdf_diy_bg {
	background-image:url(../img/filebg/pdf.png);
}
.rar_diy_bg {
	background-image:url(../img/filebg/rar.png);
}

/* ------------ */
#wrapper {
    width: 980px;
    margin: 0 auto;

    margin: 1em;
    width: auto;
}

#container {
    border: 1px solid #dadada;
    color: #838383;
    font-size: 12px;
    margin-top: 10px;
    background-color: #FFF;
}

.element-invisible {
    position: absolute !important;
    clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
    clip: rect(1px,1px,1px,1px);
}

#updatepic .placeholder {
    border: 3px dashed #e6e6e6;
    min-height: 238px;
    padding-top: 158px;
    text-align: center;
    background: url(./image.png) center 93px no-repeat;
    color: #cccccc;
    font-size: 18px;
    position: relative;
}

#updatepic .placeholder .webuploader-pick {
    font-size: 18px;
    background: #7ac143;
    border-radius: 3px;
    line-height: 44px;
    padding: 0 30px;
    color: #fff;
    display: inline-block;
    margin: 20px auto;
    cursor: pointer;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

#updatepic .placeholder .webuploader-pick-hover {
    background: #7ac143;
}

#uploader .filelist {
    list-style: none;
    margin: 0;
    padding: 0;
}

#uploader .filelist:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    overflow: hidden;
    clear: both;
}

#uploader .filelist li {
    width: 110px;
    height: 110px;
    background: url(./bg.png) no-repeat;
    text-align: center;
    margin: 0 8px 20px 0;
    position: relative;
    display: inline;
    float: left;
    overflow: hidden;
    font-size: 12px;
}

#uploader .filelist li p.log {
    position: relative;
    top: -45px;
}

#uploader .filelist li p.title {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow : ellipsis;
    top: 5px;
    text-indent: 5px;
    text-align: left;
}

#uploader .filelist li p.progress {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    height: 8px;
    overflow: hidden;
    z-index: 50;
}
#uploader .filelist li p.progress span {
    display: none;
    overflow: hidden;
    width: 0;
    height: 100%;
    background: #1483d8 url(./progress.png) repeat-x;

    -webit-transition: width 200ms linear;
    -moz-transition: width 200ms linear;
    -o-transition: width 200ms linear;
    -ms-transition: width 200ms linear;
    transition: width 200ms linear;

    -webkit-animation: progressmove 2s linear infinite;
    -moz-animation: progressmove 2s linear infinite;
    -o-animation: progressmove 2s linear infinite;
    -ms-animation: progressmove 2s linear infinite;
    animation: progressmove 2s linear infinite;

    -webkit-transform: translateZ(0);
}
#uploader .filelist li p.imgWrap {
    position: relative;
    z-index: 2;
    line-height: 110px;
    vertical-align: middle;
    overflow: hidden;
    width: 110px;
    height: 110px;

    -webkit-transform-origin: 50% 50%;
    -moz-transform-origin: 50% 50%;
    -o-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%;

    -webit-transition: 200ms ease-out;
    -moz-transition: 200ms ease-out;
    -o-transition: 200ms ease-out;
    -ms-transition: 200ms ease-out;
    transition: 200ms ease-out;
}

#uploader .filelist li img {
    width: 100%;
}

#uploader .filelist li p.error {
    background: #f43838;
    color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    height: 28px;
    line-height: 28px;
    width: 100%;
    z-index: 100;
}

#uploader .filelist li .success {
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 40px;
    width: 100%;
    z-index: 200;
    background: url(./success.png) no-repeat right bottom;
}

#uploader .filelist div.file-panel {
    position: absolute;
    height: 0;
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr='#80000000', endColorstr='#80000000')\0;
    background: rgba( 0, 0, 0, 0.5 );
    width: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: 300;
}

#uploader .filelist div.file-panel span {
    width: 24px;
    height: 24px;
    display: inline;
    float: right;
    text-indent: -9999px;
    overflow: hidden;
    background: url(./icons.png) no-repeat;
    margin: 5px 1px 1px;
    cursor: pointer;
}

#uploader .filelist div.file-panel span.rotateLeft {
    background-position: 0 -24px;
}
#uploader .filelist div.file-panel span.rotateLeft:hover {
    background-position: 0 0;
}

#uploader .filelist div.file-panel span.rotateRight {
    background-position: -24px -24px;
}
#uploader .filelist div.file-panel span.rotateRight:hover {
    background-position: -24px 0;
}

#uploader .filelist div.file-panel span.cancel {
    background-position: -48px -24px;
}
#uploader .filelist div.file-panel span.cancel:hover {
    background-position: -48px 0;
}
