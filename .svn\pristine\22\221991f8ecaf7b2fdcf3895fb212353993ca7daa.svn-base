{% load i18n %}
{% load iclock_tags %}

<style>
#tbl_monitor th{
	text-align: right;
}
.altclass{
	background: #eeeeee;
}

</style>


<script>
	jqOptions[g_activeTabID]=copyObj(jq_Options);
	tblName[g_activeTabID]='Feedback';
	{% autoescape off %}
		jqOptions[g_activeTabID].colModel={{attModel}}
	{% endautoescape %}

	$(function(){
			jqOptions[g_activeTabID].datatype='local'
			jqOptions[g_activeTabID].height=570
			jqOptions[g_activeTabID].multiselect=true
			jqOptions[g_activeTabID].width='auto'
			jqOptions[g_activeTabID].pager='id_pager_unreplied'
			jqOptions[g_activeTabID].altRows=true
			jqOptions[g_activeTabID].altclass='altclass'
			$("#id_grid_unreplied").jqGrid(jqOptions[g_activeTabID]);
			html="<p style='margin:5px;'>{% trans '1. Display the current software online administrator' %}</p>"
			$("#west_content_"+g_activeTabID).html(html)
			loadData()


            $("#"+g_activeTabID+" #id_reload").click(function(event){
                loadData();
            });

            $("#"+g_activeTabID+" #id_offline").click(function(event){
            	offline()

            });


		})

	function getSearUrl()
	{

		var urlStr=g_urls[g_activeTabID]
		return urlStr
	}

	function loadData(){
		$('#id_grid_unreplied').jqGrid('clearGridData')
		urlstr=getSearUrl()
		$.ajax({
			type: "POST",
			url:urlstr ,
			dataType:"json",
			success: function(data){
				if(data.ret>0){
					for(var i=0;i<data.ret;i++)
					{
						$('#id_grid_unreplied').jqGrid('addRowData',i,data.data[i],'first');
					}
				}
			}
		});
	}

	function offline(){
		//$('#id_grid_unreplied').jqGrid('clearGridData')
		//判断哪些管理员被选中
		var ret=tblSelect()
		if(ret.selectedCount==0){
			//alert('请勾选离线管理员!')
			alert("{%trans 'Please Choose offline administrator'%}")
			return false
		}else{
			if(confirm("{%trans 'Please confirm whether offline these administrators'%}")){
				var userlist
				userlist=ret.ss
				var urlstr
				urlstr=getSearUrl()+"?action=offlineusers"
				$.ajax({
					type: "POST",
					url:urlstr ,
					dataType:"json",
					data:{'userlist':userlist},
					traditional:true,
					success: function(data){
							alert(data.msg)
							loadData()
					}
				});
				}
			}


	}

	function tblSelect(){
		var selCount=0
		var ss=[];
		$("#id_grid_unreplied td input:checked").each(function(i){
			ss.push($(this).parent().next().text());
			selCount=ss.length
		})
		var result={selectedCount: selCount,ss: ss};
		return result;
	}



</script>

<div id="id_top_show_question">

{% block toolbar %}
	<div id="id_toolbar">
		<UL class="toolbar" id="navi">
			<LI id="id_reload"><SPAN class="icon iconfont icon-shuaxin"></SPAN>{% trans "Reload" %}</LI>
			{% if user.is_superuser %}
			<LI id="id_offline"><span class="icon iconfont icon-shanchu"></span>{% trans "Disconnect" %}</LI>
			{% endif %}
		</UL>
	</div>
{% endblock %}
<div>
	<div style='overflow: hidden;'>
		<table id="id_grid_unreplied" >	</table>
		<div id="id_pager_unreplied"></div>
	</div>

</div>



