{% extends "visitors/visitor_register.html" %}
{% load i18n %}
{% load iclock_tags %}
<style>
{% block extrastyle %}
    #modal{
        position: fixed;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display:none;
    }
    #modal .photo{
        width: 20%;
        height: 32%;
        background-color: #fff;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        box-sizing: border-box;
        border-radius: 13px;
    }
    .div_center{
        height: 100%;
        width:22.8%;
        font-size: 3.6rem;
        color: #474B4F;
        float:left;
    }
    .div_photo{
        height: 65%;
        width:100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 0.5px solid #D8E3DE;
        background: #F1F1F1;
        margin-top:7%;
        cursor: pointer;
    }
    .div_face{
        height: 70%;
        width:100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top-left-radius:13px;
        border-top-right-radius:13px;
        background: url(../../media/img/visitor_mode/faceframe.png) repeat scroll center center transparent;
    }
    .phone_img{
        width: 16%;
        height: 100%;
        display: flex;
        border-right: 0.5px solid #D8E3DE;
        justify-content: center;
        align-items: center;
        float: left;
    }
    .div_input{
        width: 80%;
        height: 100%;
        float: left;
    }
    .div_input input{
        background: #F1F1F1;
        border-radius: 0;
        border:0;
        color:#474B4F;
        height: 100%;
        width: 100%;
        font-size:12px;
    }
    .button_{
        height: 40%;
        width: 45%;
        line-height: 25px;
        font-size: 12px;
        cursor: pointer;
        color: #FEFEFE;
        text-align: center;
        background: #7AC143;
        border-radius: 6px;
    }
    .div_content{
        width: 100%;
        height: 16.4%;
        border: 0.5px solid #D8E3DE;
        background: #F1F1F1;
        margin-top:6.8%;
    }
    .div_reason{
        margin-top:1%;
        height: 4.44%;
        width:15%;
        margin-left:34.5%;
    }
    .bottom{
        width: 488px;
        height: 34px;
        margin-left: 34.1%;
        margin-top: 0.5%;
        position: absolute;
    }
    .bottom .div_reason{
        height: 100%;
        width:48.6%;
    }
    .div_next{
        height:5.56%;
        margin-top: 2.65%;
    }
    .div_status{

    }
{% endblock %}
</style>
{% block extrahead %}
<script>
    // 拍照功能
    function get_Photo() {
        modal = document.getElementById("modal")
        face = document.getElementById("face")
        context1.drawImage(video, 0, 0, 300, 150); //将video对象内指定的区域捕捉绘制到画布上指定的区域，实现拍照。
        var img_data = canvas1.toDataURL("image/jpeg");

        if (img_data) {
            time_stamp = moment().format('YYYYMMDDHHmmssSSS');
            $("#id_photourlz").val(time_stamp+'.jpg');
            modal.style.display = 'none'
            face.style.display = 'none'
            canvas1 .style.display = 'block'
        }
    }
    // 获取摄像头权限
    function get_Media() {
        video = document.querySelector('video');
        modal = document.getElementById("modal")
        canvas1 = document.getElementById('canvas_photo');
        context1 = canvas1.getContext('2d');
        var constraints = {
            audio: false,
            video: {width: 1280, height: 720}
        };
        navigator.mediaDevices.getUserMedia(constraints).then(function (stream) {
            successFunc(stream)
            modal.style.display = 'block'
        }).catch(function (err) {
            errorFunc(err)
        });
    }

    // 获取摄像头成功
    function successFunc(stream) {
        //alert('Succeed to get media!');
        if (video.mozSrcObject !== undefined) {
            //Firefox中，video.mozSrcObject最初为null，而不是未定义的，我们可以靠这个来检测Firefox的支持
            video.mozSrcObject = stream;
        } else if ("srcObject" in video) {
            // 旧的浏览器可能没有srcObject
            video.srcObject = stream;
        } else {
            // 防止在新的浏览器里使用它，因为它已经不再支持了
            video.src = window.URL && window.URL.createObjectURL(stream) || stream;
        }

    }
    // 获取摄像头失败
    function errorFunc(e) {
        alert("{% trans 'Camera get failed' %}:" + e);
        return false;
    }
    $(function(){
        // 底部动态显示版权信息
        // var now_date =new Date();
        // var year = now_date.getFullYear();
        // var rights_info = '© '+ year.toString() + ' ZKTeco All Rights Reserved.'
        // $("#right").html(rights_info);
        $("#right").css("color","#474B4F");

        // 进入主页面
        $("#home").click(function(){
		    window.location.href = '/visitors/VisitorMode/';
	    });
        td = moment().format('YYYY-MM-DD HH:mm:ss');
        $("#time").val(td);

        // 手机号验证
        $("#phone").blur(function(){
            var phone = document.getElementById('phone').value;
            if(!(/^1[3456789]\d{9}$/.test(phone))){
                alert("{% trans 'The phone number is incorrect, please fill it in again' %}");
                $("#phone").val('')
            }
        })

        // 拜访人数验证
        $("#number").blur(function(){
            var number = document.getElementById('number').value;
            if(!(/^\d*$/.test(number))) {
                alert("{% trans 'The number of visitors can only be entered as a number, please fill in again' %}");
                $("#number").val('')
            }
        })

        {#// 身份证卡号验证#}
        {#$("#card").blur(function(){#}
	    {#    var SSN = $("#card").val();#}
	    {#    if (SSN!=""){#}
	    {#        $.ajax({#}
        {#            type: "GET",#}
        {#            headers: {"X-CSRFToken": $.cookie('csrftoken')},#}
        {#            url: "/visitors/VisSSNCheck/?SSN=" + SSN,#}
        {#            dataType: "json",#}
        {#            success: function (json) {#}
        {#                if (json.ret === '0') {#}
        {#                    alert("id ok");&ndash;#}
        {#                    document.getElementById('error_label').innerHTML = json.msg;#}
        {#                    document.getElementById('error_div').style.color = 'green';#}
        {#                } else{#}
        {#                    alert("身份证号错误："+ json.msg);#}
        {#                }#}
        {#            }#}
        {#        });#}
        {#    }#}
        {#});#}

        function ReadIDCard()
            {
                var url123="http://127.0.0.1:24008/ISSOnline/ScanReadIdCardInfo?OP-DEV=1&CMD-URL=4&DllType=1"
                    result={ret:-1}
                $.ajax({
                        type: "GET",
                        url:url123,
                        async: false,
                        data:'',
                        dataType:"json",
                        success:function(ret){
                            result=ret;
                       },
                        error:function(request, errorMsg){
                            //alert(gettext('请安装驱动或启动该服务!'));
                            //alert(errorMsg)
                            alert('{% trans 'Please install the driver or start the service!' %}')
                            document.getElementById('error_label').innerHTML = '{% trans 'Please install the driver or start the service!' %}';
                            document.getElementById('error_div').style.color = 'red';
                       }
                    });
                return result;
            }

        // 获取身份信息
        $("#get_visit_info").click(function () {
<!--            $("#visit_msg").html('请刷身份证');-->
            result=ReadIDCard();
            if (result.ret == 0){
                var SSN=$.trim(result.Certificate.IDNumber);
                var name=$.trim(result.Certificate.Name);
                var sex = $.trim(result.Certificate.Sex) === '女' ? 'F' : 'M'
                window.vis_nation=$.trim(result.Certificate.Nation);
                window.vis_address=$.trim(result.Certificate.Address);


                $("#name").val(name);
                $("#card").val(SSN);
                $("#sex").val(sex);
                $("#ID_face").attr("src","data:image/jpeg;base64,"+result.Certificate.Base64Photo);
                queryStr="SSN="+$.trim(result.Certificate.IDNumber)+"&img="+encodeURIComponent(result.Certificate.Base64Photo);
                $.ajax({ type: "POST",
                    url: "/iclock/att/saveSSNPhoto/",
                    dataType:"json",
                    data:queryStr,
                    success: function(retdata){
                        if(retdata.ret==0){
                              //alert(retdata.ret)
                        }
                    },
                    error: function(request, errorMsg){
                         //alert($.validator.format(gettext('Operating failed for {0} : {1}'), options[g_activeTabID].title, errorMsg));
                    }
                });
<!--                $("#visit_msg").html('');-->
            }
        })


        // 点击下一步，进入下一个页面
        $("#next").click(function () {
            var name = $("#name").val();
            var number = $("#number").val();
            var phone = $("#phone").val();
            var card = $("#card").val();
            var sex = $("#sex").val();
            var photourlz = $('#id_photourlz').val();
            var photourl = $("#ID_face").attr('src').startsWith('data:') ? `/iclock/file/photo/${card}.jpg` : ''
            if("undefined" !== typeof canvas1){
                var img_data = canvas1.toDataURL("image/jpeg");
            }
            else{
                var img_data = '';
            }
            reg = new RegExp('(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}[0-9xX]$)', 'g')
            if(name === '' || number === '' || phone === '' || card === ''){
                alert('{% trans 'Name, number of visitors, phone number, and ID number are required fields' %}')
            }
            else if(img_data === ''){
                alert('{% trans 'Please enter facial information' %}')
            } else if (!reg.test(card)) {
                alert('{% trans 'Incorrect format of ID number' %}')
            }
            else{
                $.ajax({
                    type: "POST",
                    headers: {"X-CSRFToken": $.cookie('csrftoken')},
                    url: "/visitors/reg_submit/",
                    dataType: "json",
                    data:{
                      'name': name,
                      'number': number,
                      'phone': phone,
                      'sex': sex,
                      'card': card,
                      'image': img_data,
                      'reason': $("#reason").val(),
                      'time': td,
                      'emp_id': $.cookie('emp_id'),
                      'nation':window.vis_nation,
                      'address':window.vis_address,
                      'photourl': photourl,
                      'photourlz': photourlz
                    },

                    success: function (json) {
                        if (json.ret === '1') {
                            alert(json.msg)
                        } else {
                            $.cookie('visit_SSN',json.data.SSN,{ path: '/', expires: 10 });
                            $.cookie('visit_id', json.data.id, { path: '/', expires: 10 });
                            $.removeCookie('emp_id', {path: '/'})
                            window.location = '/visitors/success_vislogs/?id=' + json.data.id
                        }
                    }
                });
                }
            });
    });
</script>
{% endblock %}



{% block content %}
    <div class="logo_img div_status">
        <img src="../../media/img/visitor_mode/status_2.png" alt="" width="28.44%">
    </div>
    <div class="logo_content">
        <div style="width: 30.7%"><strong>{% trans 'Visitor information' %}：</strong>
            <div id="get_visit_info" style="font-size:12px; color:#7AC143;display: inline-block;">{% trans '(Read ID card information)' %}
<!--                <div id="visit_msg" style="font-size:12px; color:red;display: inline-block;"></div>-->
            </div>
        </div>

    </div>
    <div style="height: 27%;width:65.5%;margin-left:34.5%">
        <div class="div_center">
            <div class="div_content">
                <div class="phone_img" style="color: red;font-size: 15px"><img src="../../media/img/visitor_mode/personnal.png" alt="">*</div>
                <div class="div_input"><label><input id="name" type="text" placeholder="{% trans 'Please enter your name' %}"></label></div>
            </div>
            <div class="div_content">
                <div class="phone_img"><img src="../../media/img/visitor_mode/personnal.png" alt=""></div>
                <div class="div_input">
                    <label>
                        <select id="sex" style="width: 104%;height: 100%;border: 0;color: #474B4F;background: #F1F1F1;">
                            <option value="M">男</option>
                            <option value="F">女</option>
                        </select>
                    </label>
                </div>
            </div>
            <div class="div_content">
                <div class="phone_img" style="color: red;font-size: 15px"><img src="../../media/img/visitor_mode/no.png" alt="">*</div>
                <div class="div_input"><label><input id="card" type="text" placeholder="{% trans 'Please enter your id number' %}"></label></div>
            </div>
            <div class="div_content">
                <div class="phone_img" style="color: red;font-size: 15px"><img src="../../media/img/visitor_mode/phone.png" alt="">*</div>
                <div class="div_input"><label><input id="phone" type="text" placeholder="{% trans 'Please enter your mobile phone number' %}"></label></div>
            </div>
        </div>
        <div class="div_center" style="margin-left:1.09%">
            <div class="div_photo" onclick="get_Media()">
                <canvas id="canvas_photo" style="width: 100%; height: 100%; display:none;"></canvas>
                <img id="face" src="../../media/img/visitor_mode/camera.png" alt="">
            </div>
            <input type="hidden" name="photourlz" id="id_photourlz" value="">
            <div class="div_content">
                <div class="phone_img"><img src="../../media/img/visitor_mode/time.png" alt=""></div>
                <div class="div_input"><label><input class="layui-input" id="time" type="text" placeholder="{% trans 'Visit Time' %}" disabled></label></div>
            </div>
        </div>
        {# 证件照 #}
        <div class="div_ID" style="margin-left:1.09%;display:none;">
            <div class="div_ID_photo" onclick="get_Media()">
                <canvas id="canvas_photo" style="width: 100%; height: 100%; display:none;"></canvas>
                <img id="ID_face" src="../../media/img/visitor_mode/camera.png" alt="">
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="div_content div_reason" style="float: left;margin-left: 0">
            <div class="phone_img" style="width:13%"><img src="../../media/img/visitor_mode/reason.png" alt=""></div>
            <div class="div_input"><label><input class="layui-input" id="reason" type="text" placeholder="{% trans 'Visit reason' %}"></label></div>
        </div>
        <div class="div_content div_reason" style="float: right;margin-left: 0">
            <div class="phone_img" style="width:16%;color: red;font-size: 15px"><img src="../../media/img/visitor_mode/number.png" alt="">*</div>
            <div class="div_input"><label><input class="layui-input" id="number" type="text" placeholder="{% trans 'Visit number' %}"></label></div>
        </div>
    </div>
    <div class="logo_content div_next">
        <div id="next" class="button" style="width:30.72%;height: 100%;margin-top: 3%">{% trans 'next step' %}</div>
    </div>
{% endblock %}
{% block modal %}
    <div id="modal">
        <div class="photo">
            <div class="div_face">
                <video width="100%" height="100%" autoplay="autoplay"></video>
            </div>
            <div class="logo_content" style="height:30%">
                <div class="button_" onclick='get_Photo()'>
                    <img src="../../media/img/visitor_mode/camera_s.png" alt="">
                    抓拍照片
                </div>
            </div>
        </div>
    </div>
{% endblock %}




