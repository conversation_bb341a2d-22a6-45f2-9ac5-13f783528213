{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
<script>
{% block tblHeader %}
jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='DiscontUpgradePackage';
jqOptions[g_activeTabID].sortname='-id';
jqOptions[g_activeTabID].pager="id_pager_DiscontUpgradePackage"
options[g_activeTabID].dlg_width=525;
options[g_activeTabID].dlg_height=400;
options[g_activeTabID].edit_col=1;
dtFields = "{{ dtFields }}";

$(function(){

    smenu="<ul><li  class='subnav_on' onclick=submenuClick('/iclock/data/DiscontUpgradePackage/',this);><a href='#'>{% trans "Maintenance Maintenance" %}</a></li></ul>"

	$("#"+g_activeTabID+" #id_newrec").click(function(event){
		processNewModel();
	});
	$("#"+g_activeTabID+" #id_custom").remove()
	$("#"+g_activeTabID+" #queryButton").hide()
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowDiscontUpgradePackage();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13)
	    searchShowDiscontUpgradePackage();
	});
	$("#"+g_activeTabID+" #searchbar").val('{% trans "OA单号" %}')
});
function process_dialog_DiscontUpgradePackage(htmlObj){

	f=$(htmlObj).find("#id_edit_form").get(0)
	$(f).validate({
				rules: {
					"oa_number":{"required":true,},
                    "serial_number":{"required":true,},
					"parameter_name":{"required":true,}
				}
	});

    // 隐藏固件参数值标签
    $('#id_parameter_value',htmlObj).parent().parent().hide(); // 隐藏整行

    // 找到固件参数文本框
    var firmwareParamInput = $("#id_parameter_name",htmlObj)

    // 固件参数文本框设置为只读并改变文本框的颜色
    firmwareParamInput.attr("readonly", true);
    firmwareParamInput.css("background-color","#E6E6E6");


    // 创建图片标签并添加到文本框后面
    firmwareParamInput.after(
        '<img class="drop_dept" alt="固件参数选择" src="/media/img/sug_down_on.gif" id="id_firmware_config_tree"/>'
    );

    // 为图片添加点击事件
    $("#id_firmware_config_tree",htmlObj).click(function(){
        // 这里可以添加点击后的处理逻辑
        console.log("固件参数下拉图标被点击");
        createQueryDlgbypage_firmware_config_tree('firmware_config_tree')
        var zTree = $.fn.zTree.getZTreeObj("showTree_firmware_config_tree");
        zTree.setting.check.enable = true;
        $('#dlg_dept_title_firmware_config_tree').hide()
        $('#dlg_for_query_firmware_config_tree').dialog({buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
                                  click:function(){save_hide_firmware_config_tree(htmlObj,'firmware_config_tree');}},
                                 {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
                                }] })
    });




}
function strOfData_DiscontUpgradePackage(data)
{
	return stripHtml(data.oa_number);
}

//模糊查询
function searchShowDiscontUpgradePackage(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
	var url=g_urls[g_activeTabID]+"?q="+encodeURI(v)
	savecookie("search_urlstr",url);
	$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function afterPost_DiscontUpgradePackage(flag, obj) {
    $('#id_serial_number',obj).val("");
    $('#id_parameter_name',obj).val("");
    $('#id_oa_number',obj).val("");
    reloadData()
}

function getSelected_firmware(tree_obj)
{
	var zTree = $.fn.zTree.getZTreeObj(tree_obj)
	var parameter_value=[]
	if (zTree!=null)
	{
		if(zTree.setting.check.enable){
			var nodes = zTree.getCheckedNodes(true)
		}else{
			var nodes = zTree.getSelectedNodes()
		}
		for(var i=0;i<nodes.length;i++){
				parameter_value.push(nodes[i].value);
		}
	}
	return parameter_value
}

//用于对参数值进行保存的数据处理函数
function save_hide_firmware_config_tree (obj,page) {
    var values=getSelected_firmware("showTree_"+page);
        if(values.length>0)
        {
            var names=getSelected_deptNames("showTree_"+page);
            var ischecked=0;
            if($("#id_cascadecheck_"+page).prop("checked"))
                ischecked=1;
            $("#id_isContainChild",obj).val(ischecked)
            $("#id_parameter_name",obj).val(formatArrayEx(names));
            $("#id_parameter_value",obj).val(JSON.stringify(values));
        }
        else
        {
            $("#id_parameter_name",obj).val('')
            $("#id_parameter_value",obj).val('');
        }
        dlgdestroy(page)
}

{% endblock %}

</script>

{% block importOp %}
{% endblock %}

{% block exportOp %}
{% endblock %}

{% block allDelete %}
{% if request|reqHasPerm:"disupgrade_pkg_audit" %}
<LI id="id_audit" onclick="batchOp('?action=disupgrade_pkg_audit',itemCanBeDelete,'{% trans '审核并打包' %}');"><SPAN  class="icon iconfont icon-daoru"></SPAN>{% trans '审核并打包' %}</LI>
{% endif %}
{% endblock %}
