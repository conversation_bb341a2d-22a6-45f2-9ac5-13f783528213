{% extends "report_att.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block date_range %}

		<div id="search_Year" style='display:inline;width:315px'><label  style="line-height:18px;padding:1px;">{% trans "Query year:" %}</label>
			<select id="id_year" name="year"></select>
		</div>	

{% endblock %}

{% block date_set_range %}	

var times=parseInt(moment().format("YYYY"))

var html=""
for(var i=0;i<16;i++){
	if(i==15){
		html+="<option selected>"+(times+i-15)+"</option>"
	}else{
		html+="<option>"+(times+i-15)+"</option>"
	}
}
$("#id_year").html(html)

{% endblock %}
{% block getDateUrl %}

function getDateUrl()
{


	var urlStr=g_urls[g_activeTabID]
	var CheckYear=$("#id_year").val();
	var isError=moment(CheckYear, "YYYY").isValid()
	if (isError)
	{
		if(urlStr.indexOf("?")!=-1)
			urlStr+="&y="+CheckYear
		else
			urlStr+="?y="+CheckYear
	
		
	}else{
		alert("{% trans 'Please check if the year format is correct!' %}");
		urlStr='';
	}
	return urlStr

}
{% endblock %}
{% block search %}
<div class="s-info right" id="sear_area">			
	<div class="nui-ipt nui-ipt-hasIconBtn " >
		<input id="searchbar" class="search-input" type="text"  value="{% trans 'personnel number, name' %}" role='defvalue' autocomplete="off" />
		<span id ="queryButton" class="nui-ipt-iconBtn">
			<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
		</span>
		
	</div>
	
	<div class="main-search-btn">
	
		<span><img id="searchButton" src="/media/img/s.gif" title="{% trans "Search" %}" style="cursor:hand;"></span>
	</div>
</div>
{% endblock %}	
