{% extends "order_report_ipos.html" %}
{% load i18n %}
{% load iclock_tags %}


{% block search %}

{% endblock %}
{% block date_range_ext %}

{% endblock %}

{#<script>#}
{% block tree_show %}
    var hcontent = $("#" + g_activeTabID + " #id_content").height();
    var hbar = $("#" + g_activeTabID + " #id_top").length > 0 ? $("#" + g_activeTabID + " #id_top").height() : 0;
    var h = hcontent - hbar;
    $('#report_module_order').css('height', h);
    initInnerorderWindow();
    $("#id_export_order").iMenu();
    ShowDeptData('order_report');
    var zTree = $.fn.zTree.getZTreeObj("showTree_order_report");
    zTree.setting.check.enable = false;
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode) {
        var IsGridExist = $("#id_grid_order").jqGrid('getGridParam', 'records');
        if (typeof (IsGridExist) == 'undefined') {
            //alert('请首先选择报表类型')
            return false
        }
        {#var deptID = treeNode.id;#}
        var deptID = treeNode.id;
        var deptName = treeNode.name;
        //$.cookie("dept_ids",deptID, { expires: 7 });
        var ischecked = 0;
        if ($("#id_cascadecheck_order").prop("checked"))
            ischecked = 1;
        urlStr = getOrderDateUrl(pos_start_date,pos_end_date);
        if (urlStr == '') {
            return;
        }
        if (urlStr.indexOf("?") != -1)
            var urlStr = urlStr + "&deptIDs="+deptID + "&isContainChild=" + ischecked;
        else
            var urlStr = urlStr + "?deptIDs="+deptID + "&isContainChild=" + ischecked;
        savecookie("search_urlstr", urlStr);
        RenderReportGrid(urlStr)
    };
{% endblock %}

{% block getOrderDateUrl %}

function getOrderDateUrl(pos_start_date,pos_end_date)
{

	$("#id_con_error").css("display","none");

	var urlStr=g_urls[g_activeTabID]
    {#var dinding=$("#search_id_dining").val()#}
    var dept =$("#search_id_deptID").val();
    var st=moment().startOf('month').format('YYYY-MM-DD');
    var et=moment().endOf('month').format('YYYY-MM-DD');
    if(pos_start_date){
        st=pos_start_date
    }
    if(pos_end_date){
        et=pos_end_date
    }

	if(urlStr.indexOf("?")!=-1){
		urlStr=urlStr+"&StartDate="+st+"&EndDate="+et;
	}
	else{
		urlStr=urlStr+"?StartDate="+st+"&EndDate="+et;
	}

	return urlStr
}
{% endblock %}

$(function(){
    var hcontent=$("#"+g_activeTabID+" #id_content").height();
	var hbar=$("#"+g_activeTabID+" #id_top").length>0?$("#id_top").height():0;
	var h=hcontent-hbar;
	$('#report_module_order').css('height',h);
});


{#</script>#}
<div id="report_module_order" style="position:relative;width: 99%;overflow-y: hidden;">
{% block Data %}
    <div class="inner-ui-layout-west ui-layout-west">
        <div class="ui-widget-header" style="height: 20px;">
                <span id=id_opt_tree>
                <input type='checkbox' id='id_cascadecheck_order'/>
                <label for="id_cascadecheck_order">{% trans "Include Subordinates" %}</label>
                </span>
        </div>
        <div id='show_dept_tree_' class="inner-west-content">
            <ul id='showTree_order_report' class='ztree' style='margin-left: 0px;height: 100%'></ul>
        </div>
    </div>
    <div class="inner-ui-layout-center ui-layout-center">
        <table id="id_grid_order"></table>
        <div id="id_pager_order"></div>
        <div id="showReportSymbol" style="display:none;">{{ reportSymbol }}</div>
    </div>
{% endblock %}
</div>
