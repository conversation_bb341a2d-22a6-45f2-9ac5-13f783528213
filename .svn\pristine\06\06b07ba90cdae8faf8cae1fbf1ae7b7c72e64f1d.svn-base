# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2018-08-24 16:15
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('visitors', '0003_auto_20180425_0756'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='reason',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'The reason for visiting', 'verbose_name_plural': 'The reason for visiting'},
        ),
        migrations.AlterModelOptions(
            name='reservation',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'reserving visitors', 'verbose_name_plural': 'reserving visitors'},
        ),
        migrations.AlterModelOptions(
            name='visitionlogs',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('search_visitionlogs', 'search visitionlogs'),), 'verbose_name': 'Visitor Registration', 'verbose_name_plural': 'Visitor Registration'},
        ),
        migrations.AlterField(
            model_name='reason',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='reason',
            name='reasonName',
            field=models.CharField(db_column='reasonname', max_length=80, verbose_name='The reason for visiting'),
        ),
        migrations.AlterField(
            model_name='reason',
            name='reasonNo',
            field=models.CharField(db_column='reasonno', max_length=30, verbose_name='Visiting by code'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='InterviewedempName',
            field=models.CharField(blank=True, db_column='interviewedempname', max_length=40, null=True, verbose_name='visiting object'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='SSN',
            field=models.CharField(db_column='ssn', max_length=20, null=True, verbose_name='license number'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='VisitedCompany',
            field=models.CharField(blank=True, db_column='visitedcompany', max_length=60, null=True, verbose_name='visiting department'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='VisitingNum',
            field=models.CharField(blank=True, db_column='visitingnum', max_length=40, null=True, verbose_name='Number of visitors'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='isvisited',
            field=models.BooleanField(default=True, editable=False, verbose_name='has been visited'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='remark',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='visCompany',
            field=models.CharField(blank=True, db_column='viscompany', max_length=60, null=True, verbose_name='visiting unit'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='visDate',
            field=models.DateTimeField(db_column='visdate', null=True, verbose_name='Pre-visit time'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='visReason',
            field=models.CharField(blank=True, db_column='visreason', max_length=80, null=True, verbose_name='The reason for visiting'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='visempName',
            field=models.CharField(db_column='visempname', max_length=40, null=True, verbose_name='Visitor Name'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='CertificateType',
            field=models.CharField(blank=True, choices=[('0', 'One generation ID card'), ('1', 'Second-generation ID card'), ('2', 'Anti-counterfeit identity card'), ('3', 'passport'), ('4', 'driver license'), ('5', 'Driving license'), ('6', 'Social Security Card')], db_column='certificatetype', default='1', max_length=2, null=True, verbose_name='Document category'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='CommonPlate',
            field=models.CharField(blank=True, db_column='commonplate', max_length=20, null=True, verbose_name='Ordinary license plate'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='EnterArticles',
            field=models.CharField(blank=True, db_column='enterarticles', max_length=180, null=True, verbose_name='Entering the carry-on items'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='EnterTime',
            field=models.DateTimeField(blank=True, db_column='entertime', verbose_name='Entry time'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='ExitArticles',
            field=models.CharField(blank=True, db_column='exitarticles', max_length=180, null=True, verbose_name='Leaving carrying items'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='ExitTime',
            field=models.DateTimeField(blank=True, db_column='exittime', null=True, verbose_name='departure time'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='InterviewedempName',
            field=models.CharField(blank=True, db_column='interviewedempname', max_length=40, null=True, verbose_name='visiting object'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='LicenseOrg',
            field=models.CharField(blank=True, db_column='licenseorg', max_length=80, null=True, verbose_name='Issuing authority'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='Mobile',
            field=models.CharField(blank=True, db_column='mobile', max_length=20, null=True, verbose_name='Visitor&#39;s mobile number'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='National',
            field=models.CharField(blank=True, db_column='minzu', default='', max_length=8, null=True, verbose_name='Nationality'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='Photourl',
            field=models.CharField(blank=True, db_column='photourl', max_length=200, null=True, verbose_name='ID Photo'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='Photourlz',
            field=models.CharField(blank=True, db_column='photourlz', max_length=200, null=True, verbose_name='Snapshot photos'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='SSN',
            field=models.CharField(db_column='ssn', max_length=20, verbose_name='document number'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='SpecialPlate',
            field=models.CharField(blank=True, db_column='specialplate', max_length=20, null=True, verbose_name='Special license plate'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='ValidDate',
            field=models.CharField(blank=True, db_column='validdate', max_length=20, null=True, verbose_name='Date of Expiry'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisCompany',
            field=models.CharField(blank=True, db_column='viscompany', max_length=60, null=True, verbose_name='visiting unit'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisPhone',
            field=models.CharField(blank=True, db_column='visphone', max_length=20, null=True, verbose_name='Visitor Mobile Number'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisReason',
            field=models.CharField(blank=True, db_column='visreason', max_length=80, null=True, verbose_name='The reason for visiting'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisState',
            field=models.IntegerField(blank=True, choices=[(0, 'has entered'), (1, 'has left')], db_column='visstate', default=0, editable=False, null=True, verbose_name='Access Status'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisempName',
            field=models.CharField(db_column='visempname', max_length=40, verbose_name='Visitor Name'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisitedCompany',
            field=models.CharField(blank=True, db_column='visitedcompany', max_length=60, null=True, verbose_name='visiting department'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='VisitingNum',
            field=models.CharField(blank=True, db_column='visitingnum', max_length=40, null=True, verbose_name='Number of visitors'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='levels',
            field=models.CharField(blank=True, max_length=60, null=True, verbose_name='Rights Groups'),
        ),
    ]
