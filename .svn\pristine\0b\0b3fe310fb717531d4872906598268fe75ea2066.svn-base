{% extends "base.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block extrastyle %}
<link rel="stylesheet" type="text/css" href="{{ MEDIA_URL }}css/large_att.css" />
<script src="{{ MEDIA_URL }}jslib/echarts-liquidfill.min.js"></script>
<style>
html,body{
    width:100%;
    height:100%;
}

</style>

{% endblock %}
{% block extrahead %}
<script>
array_attendance=new Array();//显示的记录的id列表
lastrecord=0
can_length=0
$(function(){
    AutoResizeHome_()
    attendance_monitor_showTime()
    window.setInterval(attendance_monitor_showTime,1000)
    attendance_monitor_getNewTrans()
});
window.onresize = function(){
    attendance_monitor_getNewTrans()
}
function attendance_monitor_getNewTrans(){
    $.ajax({type: "POST",
			url:"/iclock/attendance_Monitor_/?lastrecord="+lastrecord+'&can_length='+can_length,
			dataType:"json",
			success: function(data){
			    AutoResizeHome_(data)
				logtimer1=setTimeout("attendance_monitor_getNewTrans()", 10000);
			},
			error: function(obj, msg, exc){
				logtimer1=setTimeout("attendance_monitor_getNewTrans()", 10000);
			}
		});
}
function attendance_monitor_showTime(){
    var date = new Date()
    var year = date.getFullYear()
    var month = date.getMonth()+1
    var day = date.getDate()
    var hour = date.getHours()
    var min = date.getMinutes()
    var sec = date.getSeconds()
    var nowtime=year+'-'+checktime(month)+'-'+checktime(day)+'  '+checktime(hour)+':'+checktime(min)+':' +checktime(sec)
    $("#attbox_1_datetime").html(nowtime)

}
function checktime(num){
    if (num<10){
        num='0'+num
    }
    return num
}

function AutoResizeHome_(data='undefined') {
    $('#id_att_box').width(document.body.offsetWidth)
    $('#id_att_box').height(document.body.offsetHeight)
    var content_width=$('#id_att_box').width()//窗口宽度
    var content_height=$('#id_att_box').height()//窗口高度
    var middle_height=(content_height-98)*319/463//中间的总高度
    var chart_height=(content_height-98)*319/463//左右两边的表的高度
    var middle_width=(content_width-96)*300/(271+300+271)//中间的总宽
    if (middle_width<618){//中间宽不能小于618，因为文字和图片最小需要这么宽
        middle_width=618
    }
    var chart_width=(content_width-96-middle_width)/2//左右两边表的宽度，96是div之间的间隔总和4个24px


    var side_height=(content_height-140)*557/(557+244)
    $("#attbox_2").height(side_height)
    $("#attbox_2_left").width(chart_width)
    $("#attbox_2_right").width(chart_width)

    $("#attbox_2_left").height(chart_height)
    $("#attbox_2_right").height(chart_height)

    $("#attbox_2_middle").width(middle_width)

    $("#attbox_2_middle").height(middle_height)

    $("#left_chart1").height((chart_height-15)/2)
    $(".msg_liquidFillchart").height((chart_height-15)/2-54)
    $(".liquidFillchart").height((chart_height-30)/2-100)

    $("#left_chart2").height((chart_height-15)/2)
    $("#right_chart1").height((chart_height-15)/2)
    $("#right_chart2").height((chart_height-15)/2)

    if (middle_height<270){
        $("#middle_chart1").height(0)
    }else{
        $("#middle_chart1").height(middle_height-270)
    }
    $("#right_next").height((content_height-140)*200/(557+200))
    $("#left_back").height((content_height-140)*200/(557+200))

    var bottom_height=content_height-138-middle_height
    $("#attbox_3").height(bottom_height)
    var attendance_msg_width=120/114*bottom_height
    $(".attendance_msg").width(attendance_msg_width)

    var att_record_img_top_height=bottom_height*30/110
    $(".att_record_img_top").height(att_record_img_top_height)

    var att_record_img_height=bottom_height*55/110
    var att_record_img_width=att_record_img_height*7/5
    $(".att_record_img").width(att_record_img_width)
    $(".att_record_img").height(att_record_img_height)
    $(".att_record").width(att_record_img_width)
    var back_next_height=att_record_img_height*55/45
    var back_next_width=att_record_img_width*31/77
    $(".back_next").width(back_next_width)
    $(".back_next").height(back_next_height)
    var attendance_records_width=content_width-back_next_width-back_next_width
    if (attendance_msg_width>10){
        can_length =parseInt((attendance_records_width-10)/(attendance_msg_width-10))//根据长宽高计算可以显示多少条考勤记录
        var left_width=(attendance_records_width-10-(attendance_msg_width-10)*can_length)/2
        $("#attendance_records").css({"margin-left": left_width + "px"});
    }
    arr_length=array_attendance.length
    if (arr_length>can_length){//当当前显示的记录大于可以显示的时，主要是防止页面大小修改导致的显示不下问题，这时左边多的删除掉
        for(var i=0;i<arr_length-can_length;i++){
            var remove_id=array_attendance.shift()
            while ($("#attendance_id_"+remove_id).length > 0)
            {
                $("#attendance_id_"+remove_id).remove()
            }
        }
    }

    if (data=='undefined'){
        online={% autoescape off %}{{device_status_number.online}}{% endautoescape %}
        offline={% autoescape off %}{{device_status_number.offline}}{% endautoescape %}
        trans_hour_list={% autoescape off %}{{trans_hour_list}}{% endautoescape %}
        total_emps={% autoescape off %}{{total_emps}}{% endautoescape %}
        adcount={% autoescape off %}{{adcount}}{% endautoescape %}//已登记比对照片人数
        //noadcount={% autoescape off %}{{noadcount}}{% endautoescape %}//未登记比对照片人数
        cardcount={% autoescape off %}{{cardcount}}{% endautoescape %}//已发卡
        //nocardcount={% autoescape off %}{{nocardcount}}{% endautoescape %}//未发卡
        fpcount={% autoescape off %}{{fpcount}}{% endautoescape %}//已登记指纹人数
        //nofpcount={% autoescape off %}{{nofpcount}}{% endautoescape %}//未登记指纹人数
        adcount_percent={% autoescape off %}{{adcount_percent}}{% endautoescape %}//未登记指纹人数
        cardcount_percent={% autoescape off %}{{cardcount_percent}}{% endautoescape %}//未登记指纹人数
        fpcount_percent={% autoescape off %}{{fpcount_percent}}{% endautoescape %}//未登记指纹人数
        today_workday={% autoescape off %}{{today_workday}}{% endautoescape %}//今日应到
        today_realwork={% autoescape off %}{{today_realwork}}{% endautoescape %}//今日实到
        today_leave={% autoescape off %}{{today_leave}}{% endautoescape %}//今日请假
        today_business={% autoescape off %}{{today_business}}{% endautoescape %}//今日出差
        today_absent={% autoescape off %}{{today_absent}}{% endautoescape %}//今日旷工
        yesterday_early={% autoescape off %}{{yesterday_early}}{% endautoescape %}//昨日早退
        yesterday_late={% autoescape off %}{{yesterday_late}}{% endautoescape %}//昨日迟到
        yesterday_leave={% autoescape off %}{{yesterday_leave}}{% endautoescape %}//昨日早退
        yesterday_absent={% autoescape off %}{{yesterday_absent}}{% endautoescape %}//昨日迟到
        yesterday_long_time={% autoescape off %}{{yesterday_long_time}}{% endautoescape %}
        yesterday_long_name={% autoescape off %}{{yesterday_long_name}}{% endautoescape %}
        lastrecord={% autoescape off %}{{lastrecord}}{% endautoescape %}
        new_attendance_list={% autoescape off %}{{new_attendance_list}}{% endautoescape %}
    }else{
        var device_status_number=data['device_status_number']
        online=device_status_number['online']
        offline=device_status_number['offline']
        trans_hour_list=data['trans_hour_list']
        total_emps=data['total_emps']
        adcount=data['adcount']
        yesterday_long_time=data['yesterday_long_time']
        yesterday_long_name=data['yesterday_long_name']
        noadcount=data['noadcount']
        lastrecord=data['lastrecord']
        cardcount=data['cardcount']

        nocardcount=data['nocardcount']

        fpcount=data['fpcount']

        nofpcount=data['nofpcount']

        adcount_percent=data['adcount_percent']
        cardcount_percent=data['cardcount_percent']
        fpcount_percent=data['fpcount_percent']
        var new_attendance_list=data['new_attendance_list']
        today_workday=data['today_workday']
        today_realwork=data['today_realwork']
        today_leave=data['today_leave']
        today_business=data['today_business']
        today_absent=data['today_absent']
        yesterday_early=data['yesterday_early']
        yesterday_late=data['yesterday_late']
        yesterday_absent=data['yesterday_absent']
        yesterday_leave=data['yesterday_leave']

        $("#adcount").html("{% trans 'registered' %}" + " " +adcount+ " " + "{% trans 'people' %}")
        $("#noadcount").html("{% trans 'unregistered' %}" + " " +noadcount+ " " + "{% trans 'people' %}")
        $("#cardcount").html("{% trans 'registered' %}" + " " +cardcount+ " " + "{% trans 'people' %}")
        $("#nocardcount").html("{% trans 'unregistered' %}" + " " +nocardcount+ " " + "{% trans 'people' %}")
        $("#fpcount").html("{% trans 'registered' %}" + " " +fpcount+ " " + "{% trans 'people' %}")
        $("#nofpcount").html("{% trans 'unregistered' %}" + " " +nofpcount+ " " + "{% trans 'people' %}")
        today_workday_html=''
        for (var i=0;i<today_workday.length;i++){
            today_workday_html+='<div class="should_real_group should_real_num">'+today_workday[i]+'</div>'
        }
        $("#today_workday").html(today_workday_html)
        today_realwork_html=''
        for (var i=0;i<today_realwork.length;i++){
            today_realwork_html+='<div class="should_real_group should_real_num">'+today_realwork[i]+'</div>'
        }
        $("#today_realwork").html(today_realwork_html)
        $("#today_leave").html(today_leave)
        $("#today_business").html(today_business)
        $("#today_absent").html(today_absent)

    }


    yesterday_except(yesterday_early,yesterday_late,yesterday_leave,yesterday_absent)
    data_summary_liquidfill(cardcount_percent,fpcount_percent,adcount_percent)
    today_check_range(trans_hour_list)
    show_card_statics_chart(yesterday_long_name,yesterday_long_time)
    show_device_status_chart(online,offline)
    show_len=can_length
    if (can_length>new_attendance_list.length){
        show_len=new_attendance_list.length
    }
    for(var i=0;i<show_len;i++){
        if (array_attendance.length==can_length){
            var remove_id=array_attendance.shift()
            while ($("#attendance_id_"+remove_id).length > 0)
            {
                $("#attendance_id_"+remove_id).remove()
            }
        }
        if (array_attendance.indexOf(new_attendance_list[i].id)<=-1) {

            array_attendance.push(new_attendance_list[i].id)
            var html_record = '<div class="attendance_msg"  id="attendance_id_' + new_attendance_list[i].id + '">'
                + '<div class="attendance_msg_div" >'
                + '<div class="att_record_img_top"></div>'
                + '<img class="att_record_img" src="/iclock/file/photo/thumbnail/' + new_attendance_list[i].pin + '.jpg"  onerror="this.src=\'/media/img/transaction/noimg.jpg\'"/>'
                + '<p class="att_record att_record_name">' + new_attendance_list[i].name + '</p>'
                + '<p class="att_record att_record_checktime">' + new_attendance_list[i].checktime + '</p>'
                + '</div>'
                + '</div>'
            $("#attendance_records").append(html_record)
            $(".att_record_img").width(att_record_img_width)
            $(".att_record_img").height(att_record_img_height)
            $(".att_record").width(att_record_img_width)
            var attendance_msg_width = 120 / 114 * bottom_height
            $(".attendance_msg").width(attendance_msg_width)
            $(".att_record_img_top").height(att_record_img_top_height)
        }
        lastrecord=new_attendance_list[i].id
    }
}
function show_card_statics_chart(yesterday_long_name,yesterday_long_time){
    var option = {
	    title: {
	        text: gettext('昨日在岗时长排名'),
	        x: 'center',
	        textStyle: {
	            color: 'rgba(96,196,253,1)',
	            fontWeight: 'bold',
	            fontFamily:'Source Han Sans CN Regular',
	            height:15,
	            fontSize:14,
	        },
	    },
	    color: ['#3398DB'],
	    grid: {
	        left: '3%',
	        right: '4%',
	        bottom: '3%',
	        containLabel: true
	    },
	    xAxis : [
	        {
	            minInterval:1,
	            type : 'value',
	            splitLine:{show:false,},
	            axisLabel:{
	                fontSize:10,
	                fontFamily:'Source Han Sans CN Regular',
	                fontWeight:400,
	                color:'rgba(94,151,211,1)',
	            },
	        }
	    ],
	    yAxis : [
	        {

	            type : 'category',
	            data : yesterday_long_name,
	            axisTick: {
	                alignWithLabel: true
	            },
	            splitLine:{show:false,},
	            axisLabel:{
	                fontSize:10,
	                fontFamily:'Source Han Sans CN Regular',
	                fontWeight:400,
	                color:'rgba(94,151,211,1)',
	            },
	        }
	    ],
	    series : [
	        {
	            name:'直接访问',
	            type:'bar',
	            barWidth:'50%',
	            data:yesterday_long_time,
	             itemStyle: {
	                normal: {
	                    color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0.2341,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(48,122,254,1)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(34,193,253,1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }
	                },
	            },
	        }
	    ]
	};
    var myChart = echarts.init(document.getElementById('right_chart2'));
    myChart.resize()
    myChart.setOption(option, true);
}
function show_device_status_chart(){
    var option = {
        title:{
            text: gettext('设备情况统计'),
            x: 'center',
            textStyle: {
                color: 'rgba(80, 179, 248, 1)',
                fontWeight: 'bold',
                fontFamily:'Source Han Sans CN Regular',
                height:14,
                fontSize:14,
            },
        },
        tooltip: {
            trigger: 'item',
            formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
    	series: [{
            name:gettext('device state'),
            type:'pie',
            radius: [35,60],
            avoidLabelOverlap: false,
            label:{
                show:true,
                color:'red',

            },
            labelLine: {
                length:10,
                length2:50,
            },
            data:[{
            	value:online,
            	name:'在线',
            	itemStyle:{
            		color:'rgba(48, 121, 254, 1)'
            	},
                label: {
                    position:'outside',
                    normal: {
                        formatter: ['{b|}','{a|{b}}{c|{d}%}'].join('\n'),
                        rich: {
                            a: {
                                fontSize:14,
                                fontFamily:'Source Han Sans CN Regular',
                                fontWeight:500,
                                color:'rgba(94,151,211,1)',
                            },
                            c: {
                                fontSize:24,
                                fontFamily:'Source Han Sans CN Regular',
                                fontWeight:500,
                                color:'#2E8DEFFF',
                            },
                            b: {
                                color:'blue',
                                backgroundColor: {
                                    image: '/media/img/ipos/img_alarm.png'
                                },
                                height: 50,
                                width:50
                            },
                        },
                    }
                },
            },{
                value:offline,
                name:'离线',
                itemStyle:{
                	color:'rgba(47, 107, 176, 1)'
                },
                label: {
                    normal: {
                        formatter: ['{b|}','{a|{b}}{c|{d}%}'].join('\n'),
                        rich: {
                            a: {
                                fontSize:14,
                                fontFamily:'Source Han Sans CN Regular',
                                fontWeight:500,
                                color:'rgba(94,151,211,1)',
                            },
                            c: {
                                fontSize:24,
                                fontFamily:'Source Han Sans CN Regular',
                                fontWeight:500,
                                color:'#2E8DEFFF',
                            },
                            b: {
                                color:'blue',
                                backgroundColor: {
                                    image: '/media/img/ipos/img_device.png'
                                },
                                height: 50,
                                width:50
                            },
                        },
                    }
                },
            }]
        }]
	};
	var myChart = echarts.init(document.getElementById('right_chart1'));
    myChart.resize()
    myChart.setOption(option, true);
}
function today_check_range(trans_hour_list){
    var option = {
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
            axisLabel:{
                fontSize:12,
                fontFamily:'Source Han Sans CN Regular',
                fontWeight:400,
                color:'rgba(94,151,211,1)',
            },
            axisLine: {
                    show:false,
                },
            axisTick:{
                show:false
            },
        },
        yAxis: {
            minInterval:1,
            type: 'value',
            splitLine: {
                show: true,
                lineStyle:{
                   color: ['rgba(54,153,255,0.12)'],
                   width: 1,
                   type: 'solid'
                }
            },
            axisTick:{
                show:false
            },
            axisLine: {
                show:false,
            },
            axisLabel:{
                fontSize:12,
                fontFamily:'Source Han Sans CN Regular',
                fontWeight:400,
                color:'rgba(94,151,211,1)',
            },
        },

        series: [{
            data:trans_hour_list,
            type: 'line',
            showSymbol:false,
            smooth: true,
            areaStyle: {},
            lineStyle: {
                color:  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0.2341,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(48,122,254,1)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(34,193,253,1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }

            },
            areaStyle: {
                color: '#3699FFFF',
                opacity:'0.28',
            },
        }]
    };
    var myChart = echarts.init(document.getElementById('middle_chart1'));
    myChart.resize()
    myChart.setOption(option, true);
}


function yesterday_except(yesterday_early,yesterday_late,yesterday_leave,yesterday_absent){
    var option = {
	    title: {
            text: gettext("昨日考勤异常"),
            x: 'center',
            textStyle: {
                color: 'rgba(96,196,253,1)',
                fontWeight: 'bold',
                fontFamily:'Source Han Sans CN Regular',
                height:14,
                fontSize:14,
            },
        },

	    tooltip : {
	        trigger: 'item',
	        formatter: "{a} <br/>{b} : {c} ({d}%)"
	    },
	    calculable : true,
	    series : [
	        {
	            name:'',
	            type:'pie',
	            radius : [40, 80],
	            center : ['50%', '50%'],
	            roseType : 'area',
	            data: [
                {value: yesterday_late, name: '{% trans 'Late' %}'},
                {value: yesterday_early, name: '{% trans 'Early' %}'},
                {value: yesterday_leave, name: '{% trans 'Leave' %}'},
                {value: yesterday_absent, name: '{% trans 'Absent' %}'},
            ],
	            label: {
	                formatter:'{b} {d}%',
	                color: 'rgba(46, 141, 239, 1)',
	                fontStyle: 'normal',
	                fontWeight: 400,
	                fontFamily: 'Source Han Sans CN Regular',
	                fontSize: 14,
	            },
	            itemStyle: {
	                color:{
	                    type: 'linear',
	                    x: 1,
	                    y: 0,
	                    x2: 0.2341,
	                    y2: 0.7658,
	                    colorStops: [{
	                        offset: 0, color: 'rgba(54,101,253,1)' // 0% 处的颜色
	                    }, {
	                        offset: 1, color: 'rgba(21,190,254,1)' // 100% 处的颜色
	                    }],
	                    global: false,
	                }
	            }
	        }
	    ]
	};
	var myChart = echarts.init(document.getElementById('left_chart2'));
    myChart.resize()
	myChart.setOption(option, true);
}
function data_summary_liquidfill(cardcount_percent,fpcount_percent,adcount_percent){
    var option1 = {
        title:{
            text:'{% trans 'visible imagery statistics' %}',
            left:'center',
            textStyle: { // 文本样式
                fontSize: 12,
                fontWeight: 'bold',
                color: 'rgba(96,196,253,1)',
                fontFamily:'Source Han Sans CN Regular'
            }
        },
        series: [{
          type: 'liquidFill',
          name: '', // 系列名称，用于tooltip的显示，legend 的图例筛选
          radius: '62%', // 水球图的半径
          center: ['50%', '60%'], // 水球图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          // 水填充图的形状 circle 默认圆形  rect 圆角矩形  triangle 三角形
          // diamond 菱形  pin 水滴状 arrow 箭头状  还可以是svg的path
          shape: 'circle',
          phase: 0, // 波的相位弧度 不设置  默认自动
          direction: 'right', // 波浪移动的速度  两个参数  left 从右往左 right 从左往右
          outline: {
            show: true,
            borderDistance: 0, // 边框线与图表的距离 数字
            itemStyle: {
              opacity: 1, // 边框的透明度   默认为 1
              borderWidth: 1, // 边框的宽度
              shadowBlur: 1, // 边框的阴影范围 一旦设置了内外都有阴影
              shadowColor: '#fff', // 边框的阴影颜色,
              borderColor: 'rgba(44, 141, 254, 1)' // 边框颜色
            }
          },
          // 图形样式
          itemStyle: {
            color:  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0.2341,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(34, 193, 253, 1)' // 0% 处的颜色
                    }, {
                        offset:1, color: 'rgba(48, 122, 254, 1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                },
            opacity: 1, // 波浪的透明度
            shadowBlur: 10 // 波浪的阴影范围
          },
          backgroundStyle: {
            color: 'rgba(17, 34, 63, 1)', // 水球未到的背景颜色
            opacity: 1
          },
          // 图形的高亮样式
          emphasis: {
            itemStyle: {
              opacity: 1 // 鼠标经过波浪颜色的透明度
            }
          },
          // 图形上的文本标签
          label: {
            show:false,
          },
          data: [adcount_percent] // 系列中的数据内容数组
        }]
    };
    var myChart = echarts.init(document.getElementById('face_chart'));
	myChart.resize()
	myChart.setOption(option1);
	var option2 = {
        title:{
            text:'{% trans 'Card statistics' %}',
            left:'center',
            textStyle: { // 文本样式
                fontSize: 12,
                fontWeight: 'bold',
                color: 'rgba(96,196,253,1)',
                fontFamily:'Source Han Sans CN Regular'
            }
        },
        series: [{
          type: 'liquidFill',
          name: '{% trans 'National employment rate' %}', // 系列名称，用于tooltip的显示，legend 的图例筛选
          radius: '62%', // 水球图的半径
          center: ['50%', '60%'], // 水球图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          // 水填充图的形状 circle 默认圆形  rect 圆角矩形  triangle 三角形
          // diamond 菱形  pin 水滴状 arrow 箭头状  还可以是svg的path
          shape: 'circle',
          phase: 0, // 波的相位弧度 不设置  默认自动
          direction: 'right', // 波浪移动的速度  两个参数  left 从右往左 right 从左往右
          outline: {
            show: true,
            borderDistance: 0, // 边框线与图表的距离 数字
            itemStyle: {
              opacity: 1, // 边框的透明度   默认为 1
              borderWidth: 1, // 边框的宽度
              shadowBlur: 1, // 边框的阴影范围 一旦设置了内外都有阴影
              shadowColor: '#fff', // 边框的阴影颜色,
              borderColor: 'rgba(44, 141, 254, 1)' // 边框颜色
            }
          },
          // 图形样式
          itemStyle: {
            color:  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0.2341,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(34, 193, 253, 1)' // 0% 处的颜色
                    }, {
                        offset:1, color: 'rgba(48, 122, 254, 1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                },
            opacity: 1, // 波浪的透明度
            shadowBlur: 10 // 波浪的阴影范围
          },
          backgroundStyle: {
            color: 'rgba(17, 34, 63, 1)', // 水球未到的背景颜色
            opacity: 1
          },
          // 图形的高亮样式
          emphasis: {
            itemStyle: {
              opacity: 1 // 鼠标经过波浪颜色的透明度
            }
          },
          // 图形上的文本标签
          label: {
            show:false,
          },
          data: [cardcount_percent] // 系列中的数据内容数组
        }]
    };
    var myChart = echarts.init(document.getElementById('card_chart'));
	myChart.resize()
	myChart.setOption(option2);
	var option3 = {
        title:{
            text:'{% trans 'Fingerprint statistics' %}',
            left:'center',
            textStyle: { // 文本样式
                fontSize: 12,
                fontWeight: 'bold',
                color: 'rgba(96,196,253,1)',
                fontFamily:'Source Han Sans CN Regular'
            }
        },
        series: [{
          type: 'liquidFill',
          name: '{% trans 'National employment rate' %}', // 系列名称，用于tooltip的显示，legend 的图例筛选
          radius: '62%', // 水球图的半径
          center: ['50%', '60%'], // 水球图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          // 水填充图的形状 circle 默认圆形  rect 圆角矩形  triangle 三角形
          // diamond 菱形  pin 水滴状 arrow 箭头状  还可以是svg的path
          shape: 'circle',
          phase: 0, // 波的相位弧度 不设置  默认自动
          direction: 'right', // 波浪移动的速度  两个参数  left 从右往左 right 从左往右
          outline: {
            show: true,
            borderDistance: 0, // 边框线与图表的距离 数字
            itemStyle: {
              opacity: 1, // 边框的透明度   默认为 1
              borderWidth: 1, // 边框的宽度
              shadowBlur: 1, // 边框的阴影范围 一旦设置了内外都有阴影
              shadowColor: '#fff', // 边框的阴影颜色,
              borderColor: 'rgba(44, 141, 254, 1)' // 边框颜色
            }
          },
          // 图形样式
          itemStyle: {
            color:  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0.2341,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(34, 193, 253, 1)' // 0% 处的颜色
                    }, {
                        offset:1, color: 'rgba(48, 122, 254, 1)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                },
            opacity: 1, // 波浪的透明度
            shadowBlur: 10 // 波浪的阴影范围
          },
          backgroundStyle: {
            color: 'rgba(17, 34, 63, 1)', // 水球未到的背景颜色
            opacity: 1
          },
          // 图形的高亮样式
          emphasis: {
            itemStyle: {
              opacity: 1 // 鼠标经过波浪颜色的透明度
            }
          },
          // 图形上的文本标签
          label: {
            show:false,
          },
          data: [fpcount_percent] // 系列中的数据内容数组
        }]
    };
    var myChart = echarts.init(document.getElementById('finger_chart'));
	myChart.resize()
	myChart.setOption(option3);

}


</script>
{% endblock %}

{% block content %}
<div id="id_att_box" style="overflow:hidden">
    <div id="attbox_1">
        <div id="attbox_1_title">考勤精细化管理平台</div>
        <div id="attbox_1_datetime">2019－12－14   11:41:39</div>
    </div>

    <div id="attbox_2">
        <div id="attbox_2_left">
            <div id="left_chart1">
                <div id="title_liquidFillchart">数据汇总</div>
                <div class="msg_liquidFillchart">
                    <div id="face_chart" class="liquidFillchart"></div>
                    <div class="logo_detail_liquidFillchart">
                        <div class="liquidFillchart_selected_logo"></div>
                        <div class="liquidFillchart_detail" id="adcount"></div>
                    </div>
                    <div class="logo_detail_liquidFillchart">
                        <div class="liquidFillchart_unselected_logo"></div>
                        <div class="liquidFillchart_detail" id="noadcount"></div>
                    </div>
                </div>
                <div class="msg_liquidFillchart">
                    <div id="card_chart" class="liquidFillchart"></div>
                    <div class="logo_detail_liquidFillchart">
                        <div class="liquidFillchart_selected_logo"></div>
                        <div class="liquidFillchart_detail" id="cardcount"></div>
                    </div>
                    <div class="logo_detail_liquidFillchart">
                        <div class="liquidFillchart_unselected_logo"></div>
                        <div class="liquidFillchart_detail" id="nocardcount"></div>
                    </div>
                </div>
                <div class="msg_liquidFillchart">
                    <div id="finger_chart" class="liquidFillchart"></div>
                    <div class="logo_detail_liquidFillchart">
                        <div class="liquidFillchart_selected_logo"></div>
                        <div class="liquidFillchart_detail" id="fpcount"></div>
                    </div>
                    <div class="logo_detail_liquidFillchart">
                        <div class="liquidFillchart_unselected_logo"></div>
                        <div class="liquidFillchart_detail" id="nofpcount"></div>
                    </div>
                </div>
            </div>
            <div id="left_chart2"></div>
        </div>
        <div id="attbox_2_middle">
            <div class="title_today_attendance">
                <img src="/media/img/ued/img_tip_left.png" align="center"/>
                <span class="title_today_attendance_words">今日考勤</span>
                <img src="/media/img/ued/img_tip_right.png" align="center"/>
            </div>
            <div id="should_real" style="height:100px;">
                <div class="should_real_box">
                    <div class="should_real_msg">应到</div>
                    <div class="should_real_nums" id="today_workday">
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                        <!--<div class="should_real_group should_real_comma">,</div>-->
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                    </div>
                </div>
                <div class="should_real_box">
                    <div class="should_real_msg">实到</div>
                    <div class="should_real_nums" id="today_realwork">
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                        <!--<div class="should_real_group should_real_comma">,</div>-->
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                        <div class="should_real_group should_real_num">0</div>
                    </div>
                </div>
            </div>
            <div class="attendance_excepts">
                <div style="width:50%;float:left">
                    <div class="attendance_except">
                        <img src="/media/img/ued/ic_leave.png">
                    </div>
                    <div class="attendance_except">
                        <p class="attendance_except_num" id="today_leave">0</p>
                        <p class="attendance_except_msg">{% trans 'Leave' %}</p>
                    </div>
                </div>
                <div style="width:50%;float:left">
                    <div class="attendance_except">
                        <img src="/media/img/ued/ic_absenteeism.png">
                    </div>
                    <div class="attendance_except">
                        <p class="attendance_except_num" id="today_absent">0</p>
                        <p class="attendance_except_msg">{% trans 'Absent' %}</p>
                    </div>
                </div>
            </div>
            <div class="attendance_excepts">
                <div style="width:50%;float:left">
                    <div class="attendance_except">
                        <img src="/media/img/ued/ic_out.png">
                    </div>
                    <div class="attendance_except">
                        <p class="attendance_except_num">0</p>
                        <p class="attendance_except_msg">外出</p>
                    </div>
                </div>
                <div style="width:50%;float:left">
                    <div class="attendance_except">
                        <img src="/media/img/ued/ic_evection.png">
                    </div>
                    <div class="attendance_except">
                        <p class="attendance_except_num" id="today_business">0</p>
                        <p class="attendance_except_msg">出差</p>
                    </div>
                </div>
            </div>
            <div class="title_today_attendance">
                <img src="/media/img/ued/img_tip_left.png" align="center"/>
                <span class="title_today_attendance_words">今日打卡时间范围人次数</span>
                <img src="/media/img/ued/img_tip_right.png" align="center"/>
            </div>
            <div id="middle_chart1">

            </div>
        </div>
        <div id="attbox_2_right">
            <div id="right_chart1"></div>
            <div id="right_chart2"></div>
        </div>
    </div>
    <div id="attbox_3">
        <div id="left_back">
            <img class="back_next" src="/media/img/ued/ic_back.png">
        </div>
        <div id="attendance_records" style="">

        </div>
        <div id="right_next">
            <img class="back_next" src="/media/img/ued/ic_next.png">
        </div>
    </div>
</div>

{% endblock %}

