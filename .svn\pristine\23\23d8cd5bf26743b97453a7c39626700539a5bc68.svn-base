{% load i18n %}
{% load iclock_tags %}


<script>
totalRecCnt_emp={{ item_count }};
//jq_Options.rowNum={{ limit }}
var isHasDept=1
var tabpanel;  
var jcTabs = [];
 
  dataExportsFormats=1;
  datas=[];
  fieldnames=[];
  t_items=[]
function getTitems()
{ 
    {% if user|HasPerm:"iclock.iaccRecordDetails_iaccempitemdefine"%}t_items.push({id:'iacc_RecordDetails',title:'{% trans "record detail" %}',html:getHtml('iacc_RecordDetails'),closable: false}){% endif %}
    {% if user|HasPerm:"iclock.iaccSummaryRecord_iaccempitemdefine"%}t_items.push({id:'iacc_SummaryRecord',title:'{% trans "record summary" %}',html:getHtml('iacc_SummaryRecord'),closable: false}){% endif %}
    {% if user|HasPerm:"iclock.iaccEmpUserRights_iaccempitemdefine"%}t_items.push({id:'iacc_EmpUserRights',title:'{% trans "User Permission Details" %}',html:getHtml('iacc_EmpUserRights'),closable: false}){% endif %}
    {% if user|HasPerm:"iclock.iaccEmpDevice_iaccempitemdefine"%}t_items.push({id:'iacc_EmpDevice',title:'{% trans "user equipment" %}',html:getHtml('iacc_EmpDevice'),closable: false}){% endif %}
	return 1;
}
function getSidx(index){
    var sidx=""
    if (index=='iacc_RecordDetails'){
        if($("#o_Asc").prop("checked")){
            if($("#o_DeptID").prop("checked")){
                sidx=sidx+"UserID__DeptID__DeptName,"
            }
            if($("#o_PIN").prop("checked")){
                sidx=sidx+"UserID__PIN,"
            }
            if($("#o_EName").prop("checked")){
                sidx=sidx+"UserID__EName,"
            }
            if($("#o_TTime").prop("checked")){
                sidx=sidx+"TTime,"
            }
        }else{
            if($("#o_DeptID").prop("checked")){
                sidx=sidx+"-UserID__DeptID__DeptName,"
            }
             if($("#o_PIN").prop("checked")){
                sidx=sidx+"-UserID__PIN,"
            }
             if($("#o_EName").prop("checked")){
                sidx=sidx+"-UserID__EName,"
            }
             if($("#o_TTime").prop("checked")){
                sidx=sidx+"-TTime,"
            }
         }
         if(sidx.length>0){
            sidx=sidx.substring(0,sidx.length-1);
         }else{
            sidx="UserID__PIN,TTime"
         }
    }else if(index=="iacc_SummaryRecord"||index=="iacc_EmpUserRights"||index==" iacc_EmpDevice"){
        if($("#o_Asc").prop("checked")){
            if($("#o_DeptID").prop("checked")){
                sidx=sidx+"DeptID__DeptName,"
            }
            if($("#o_PIN").prop("checked")){
                sidx=sidx+"UserID__PIN,"
            }
            if($("#o_EName").prop("checked")){
                sidx=sidx+"UserID__EName,"
            }
        }else{
            if($("#o_DeptID").prop("checked")){
                sidx=sidx+"-DeptID__DeptName,"
            }
            if($("#o_PIN").prop("checked")){
                sidx=sidx+"-UserID__PIN,"
            }
            if($("#o_EName").prop("checked")){
                sidx=sidx+"-UserID__EName,"
            }
        
        }
        if(sidx.length>0){
           sidx=sidx.substring(0,sidx.length-1);
        }else{
            sidx="UserID__PIN"
        }
    }else{
        sidx=""
    }
    return sidx
}
function department_tree(){
    var title='{% trans "Departmental staff conditions" %}';
    var urlStr=g_urls[g_activeTabID];
    createDlgdeptfor('transaction')
    $('#dlg_for_query_transaction').dialog({
        title:title,
        buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',click:function(){getSelectData();}},
            {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
            }]
    })	
   }
function getSelectData(){
var dept_ids=getSelected_dept("showTree_transaction")
$.cookie("dept_ids",dept_ids, { expires: 7 });
var dept_names=getSelected_deptNames("showTree_transaction");
$("#department").val(dept_names);
var emp = getSelected_emp_ex('transactions')
$.cookie("emp",emp, { expires: 7 });

dlgdestroy('transactions')
}
   
function getHtml(tabid)
{
	return "<div id="+"id_"+tabid+">  <table id="+"id_"+tabid+"_grid"+"></table> "+"<div id="+"id_"+tabid+"_pager"+"></div>  </div>"
} 
$(function(){
    getTitems();
    tabpanel = new TabPanel({  
        renderTo:'report_tabs',  
        width:'100%',  
        height:'100%',  
        active : 0,
        //maxLength : 10,  
        items : t_items
    });  
    $("#id_export").click(function(){
        var tabitem=tabpanel.getActiveTab();
        var index=tabitem.id;
        if (index=='undefined' || index=='null') return;
        var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
        var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
        var emp="";
        var deptIDs=$.cookie("dept_ids");
        if (deptIDs!=null){
            emp=getSelected_emp();
        }
        var totalEmp=parseInt($("#Employee").attr("alt"));
        if(ComeTime==null ||ComeTime=="" ||deptIDs=="" ){
            $("#id_con_error").css("display","block");
            $("#id_con_error").html("<span class='Report_errorlist'>{% trans 'Please set conditions!' %}</span>");
        }else if(totalEmp<=0){
            $("#id_con_error").css("display","block");
            $("#id_con_error").html("<span class='Report_errorlist'>"+$("#department").val()+"&nbsp;&nbsp;{% trans 'no employee' %}</span>");
        }
        clickexport(grid_disabledfields);
    })

    delCookie("dept_ids");//删除cookie
    $('.tabpanel_mover li').click(function(){
		var tabitem=tabpanel.getActiveTab();
		var index=tabitem.id;
		if (index=='undefined' || index=='null') return;
		var page_style=index;
        var emp="";
        var deptIDs=$.cookie("dept_ids");
        if (deptIDs!=null){
        	emp=getSelected_emp();
        }
        var ischecked=0;
        if($("#id_cascadecheck").prop("checked"))
        	ischecked=1;
		var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
		var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
		var isError=validate_form_iaccessempreports();
		var totalEmp=parseInt($("#Employee").attr("alt"));
		var jqOptions=copyObj(jq_Options)
		var height=$(".tabpanel_content").height()-150;
		jqOptions.height=height;
		jqOptions.pager='#id_'+page_style+'_pager';
        jqOptions.rowNum={{limit}}
        jqOptions.rowList=[jqOptions.rowNum,jqOptions.rowNum*2]
		var idx=''
		//hideEmployee ();
		//hideDeptment ();
        if(isError){
            $("#id_con_error").css("display","block");
            $("#id_con_error").html("<span class='Report_errorlist'>{% trans 'Please check that you select one or more employees or select department and the date is validate!' %}</span>");
            return false;
        }else{
            $.cookie("emp",emp, { expires: 7 });
            $.cookie("ComeTime",ComeTime, { expires: 7 });
            $.cookie("EndDate",EndDate, { expires: 7 });
            $.cookie("deptIDs",deptIDs, { expires: 7 });
            $("#id_con_error").css("display","none");
        }
        if(ComeTime==null ||ComeTime=="" ||deptIDs=="" ){
            $("#id_con_error").css("display","block");
            $("#id_con_error").html("<span class='Report_errorlist'>{% trans 'Please set conditions!' %}</span>");
        }else if(totalEmp<=0){
            $("#id_con_error").css("display","block");
            $("#id_con_error").html("<span class='Report_errorlist'>"+$("#department").val()+"&nbsp;&nbsp;{% trans 'no employee' %}</span>");
        }else{
            $("#id_con_error").css("display","none");
            if(index=='iacc_RecordDetails'){//记录明细表
                idx=getSidx(index)
                $("#id_export_con").val("iacc_RecordDetails");
                $("#id_show_units").html(unit['unit']);
                var queryStr="startDate="+ComeTime+"&endDate="+EndDate+"&deptIDs="+$.cookie("deptIDs")+"&isContainChild="+ischecked+"&UserIDs="+emp+"&sidx="+idx;
                var urlStr="/iclock/iacc/getIacc_RecordDetailsReport/?page=1&"+queryStr
                savecookie("search_urlstr",urlStr);                
                $.cookie("queryStr",queryStr, { expires: 7 });
                $("#id_"+page_style+'_grid').jqGrid('GridUnload')
		//$.jgrid.gridUnload("#id_"+page_style+'_grid')
                $.ajax({
                    type:"GET",
                    url:"/iclock/iacc/getIacc_RecordDetailsReport/?page=1&stamp="+new Date().toUTCString(),
                    dataType:"json",
                    //data:queryStr,
                    success:function(json){
                        tblName="iacc_RecordDetails";
                        grid_disabledfields=json['disabledcols']
                        jqOptions.colModel=json['colModel']
                        get_grid_fields(jqOptions)
                        hiddenfields(jqOptions)
        //                    jqOptions.sortname="";
        //                    jqOptions.sortorder="desc";
                        jqOptions.url=urlStr
                        $("#id_"+page_style+'_grid').jqGrid(jqOptions);
                    }
                }); 
            }else if(index=='iacc_SummaryRecord'){//记录汇总表
                idx=getSidx(index)
                $("#id_export_con").val("iacc_SummaryRecord");
                $("#id_show_units").html(unit['unit']);
                var queryStr="startDate="+ComeTime+"&endDate="+EndDate+"&deptIDs="+$.cookie("deptIDs")+"&isContainChild="+ischecked+"&UserIDs="+emp+"&sidx="+idx;
                var urlStr="/iclock/iacc/getIacc_SummaryRecordReport/?page=1&"+queryStr
                savecookie("search_urlstr",urlStr);                
                $.cookie("queryStr",queryStr, { expires: 7 });
                $("#id_"+page_style+'_grid').jqGrid('GridUnload')
		//$.jgrid.gridUnload("#id_"+page_style+'_grid')
                $.ajax({
                    type:"GET",
                    url:"/iclock/iacc/getIacc_SummaryRecordReport/?page=1&stamp="+new Date().toUTCString(),
                    dataType:"json",
                    //data:queryStr,
                    success:function(json){
                        tblName="iacc_SummaryRecord";
                        grid_disabledfields=json['disabledcols']
                        jqOptions.colModel=json['colModel']
                        get_grid_fields(jqOptions)
                        hiddenfields(jqOptions)
        //                    jqOptions.sortname="";
        //                    jqOptions.sortorder="desc";
                        jqOptions.url=urlStr
                        $("#id_"+page_style+'_grid').jqGrid(jqOptions);
                    }
                }); 
            }else if(index=='iacc_EmpUserRights'){//用户权限明细
                idx=getSidx(index)
                $("#id_export_con").val("iacc_EmpUserRights");
                $("#id_show_units").html(unit['unit']);
                var queryStr="startDate="+ComeTime+"&endDate="+EndDate+"&deptIDs="+$.cookie("deptIDs")+"&isContainChild="+ischecked+"&UserIDs="+emp+"&sidx="+idx;
                var urlStr="/iclock/iacc/getIacc_EmpUserRightsReport/?page=1&"+queryStr
                savecookie("search_urlstr",urlStr);                
                $.cookie("queryStr",queryStr, { expires: 7 });
                $("#id_"+page_style+'_grid').jqGrid('GridUnload')
		//$.jgrid.gridUnload("#id_"+page_style+'_grid')
                $.ajax({
                    type:"GET",
                    url:"/iclock/iacc/getIacc_EmpUserRightsReport/?page=1&stamp="+new Date().toUTCString(),
                    dataType:"json",
                    //data:queryStr,
                    success:function(json){
                        tblName="iacc_EmpUserRights";
                        grid_disabledfields=json['disabledcols']
                        jqOptions.colModel=json['colModel']
                        get_grid_fields(jqOptions)
                        hiddenfields(jqOptions)
        //                    jqOptions.sortname="";
        //                    jqOptions.sortorder="desc";
                        jqOptions.url=urlStr
                        $("#id_"+page_style+'_grid').jqGrid(jqOptions);
                    }
                }); 
            }else if(index=='iacc_EmpDevice'){//用户设备
                idx=getSidx(index)
                $("#id_export_con").val("iacc_EmpDevice");
                $("#id_show_units").html(unit['unit']);
                var queryStr="startDate="+ComeTime+"&endDate="+EndDate+"&deptIDs="+$.cookie("deptIDs")+"&isContainChild="+ischecked+"&UserIDs="+emp+"&sidx="+idx;
                var urlStr="/iclock/iacc/getIacc_EmpDeviceReport/?page=1&"+queryStr
                savecookie("search_urlstr",urlStr);                
                $.cookie("queryStr",queryStr, { expires: 7 });
                $("#id_"+page_style+'_grid').jqGrid('GridUnload')
		//$.jgrid.gridUnload("#id_"+page_style+'_grid')
                $.ajax({
                    type:"GET",
                    url:"/iclock/iacc/getIacc_EmpDeviceReport/?page=1&stamp="+new Date().toUTCString(),
                    dataType:"json",
                    //data:queryStr,
                    success:function(json){
                        tblName="iacc_EmpDevice";
                        grid_disabledfields=json['disabledcols']
                        jqOptions.colModel=json['colModel']
                        get_grid_fields(jqOptions)
                        hiddenfields(jqOptions)
        //                    jqOptions.sortname="";
        //                    jqOptions.sortorder="desc";
                        jqOptions.url=urlStr
                        $("#id_"+page_style+'_grid').jqGrid(jqOptions);
                    }
                }); 
            }
        }
    });

  	$("#department").val(($.cookie("department")==null?"":$.cookie("department")));
  	$("#Employee").val(($.cookie("Employee")==null?"":$.cookie("Employee")));
  	$("#"+g_activeTabID+" #id_ComeTime").val(($.cookie("ComeTime")==null?"":$.cookie("ComeTime")));
  	$("#"+g_activeTabID+" #id_EndTime").val(($.cookie("EndDate")==null?"":$.cookie("EndDate")));
  	$("#o_DeptID").val(($.cookie("DeptID")==null?"":$.cookie("DeptID")));
  	$("#o_PIN").val(($.cookie("PIN")==null?"":$.cookie("PIN")));
  	$("#o_EName").val(($.cookie("EName")==null?"":$.cookie("EName")));
  	$("#o_TTime").val(($.cookie("TTime")==null?"":$.cookie("TTime")));
  
  	department=[];
  	$("#"+g_activeTabID+" #id_ComeTime").datepicker(datepickerOptions);
  	$("#"+g_activeTabID+" #id_EndTime").datepicker(datepickerOptions);
    var currDate=new Date();
    td=currDate.getFullYear()
    	+"-"
    	+(currDate.getMonth()+1<10?"0"+(currDate.getMonth()+1):currDate.getMonth()+1)
    	+"-"
    	+currDate.getDate()
    if($.cookie("ComeTime")){
    	$("#"+g_activeTabID+" #id_ComeTime").val($.cookie("ComeTime"))
    	$("#"+g_activeTabID+" #id_EndTime").val($.cookie("EndDate"))
    }else{
    	$("#"+g_activeTabID+" #id_ComeTime").val(td)
    	$("#"+g_activeTabID+" #id_EndTime").val(td)
    }
	
	//自定义字段
	$("#id_custom").click(function(){
		ShowCustomField();
	});
	//重定义自定义字段提交
	$("#btn_DefineField_rp").click(function(){
		var sFields=getunSelected_Fields();
		var queryStr="tblName="+tblName+"&Fields="+sFields
		$.post("/iclock/att/saveFields/", 
			queryStr,
			function (ret, textStatus) {
				if(ret.ret==0){
					hideFields_define();
					$("#tabs ul").find(".ui-tabs-selected a").trigger("click")
				}
			},
        "json");
	});
    unit={unit:100}
    
	$("#id_form_export").submit(function(){
		var emp=getSelected_emp();
		var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
		var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
		//var deptIDs=$("#hidden_depts").val()
        var deptIDs=$.cookie("dept_ids")
		var tblName=$("#id_export_con").val();
		var isError=validate_form_iaccessempreports();
		//hideEmployee ();
		//hideDeptment ();
		if(!isError&& tblName!=""){
			$("#id_deptIDs").val(deptIDs);
			$("#id_UserIDs").val(emp);
		}else{
			$("#id_con_error").css("display","block");
			$("#id_con_error").html("<span class='Report_errorlist'>{% trans 'Please check that you select one or more employees or select department and the date is validate and click report!' %}</span>");
			return false;
		}
	});
	$("#id_print").click(function(){
		var emp=getSelected_emp();
		var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
		var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
		//var deptIDs=$("#hidden_depts").val()
        var deptIDs=$.cookie("dept_ids")  
		var tblName=$("#id_export_con").val(); 
		var isError=validate_form_iaccessempreports();
		//hideEmployee ();
		//hideDeptment ();
        var ischecked=0;
        if($("#id_cascadecheck").prop("checked"))
        	ischecked=1;
		if(!isError&& tblName!=""){
			var queryStr="ComeTime="+ComeTime+"&EndTime="+EndDate+"&deptIDs="+deptIDs+"&isContainChild="+ischecked+"&UserIDs="+emp;
	        if($.cookie("op")!="" && $.cookie("op")!=null)
	            queryStr+=$.cookie("op");
	        if($.cookie("oe")!="" && $.cookie("oe")!=null)
	            queryStr+=$.cookie("oe");
			window.open("/iclock/att/print/"+tblName+"/?"+queryStr);
			$("#id_con_error").css("display","none");
		}else{
			$("#id_con_error").css("display","block");
			$("#id_con_error").html("<span class='Report_errorlist'>{% trans 'Please check that you select one or more employees or select department and the date is validate and click report!' %}</span>");
		}
	});
        
    $("#o_DeptID").click(function(){
    $("#o_none").attr("checked",false);
    if($(this).prop("checked"))
        $.cookie("od","&o=UserID__DeptID__DeptID", { expires: 7 });
    else
        $.cookie("od","&o=-UserID__DeptID__DeptID", { expires: 7 });
    });
    $("#o_PIN").click(function(){
    $("#o_none").attr("checked",false);
    if($(this).prop("checked"))
        $.cookie("op","&o=UserID__PIN", { expires: 7 });
    else
        $.cookie("op","&o=-UserID__PIN", { expires: 7 });
    });
    
    $("#o_EName").click(function(){
    $("#o_none").attr("checked",false);
    if($(this).prop("checked"))
        $.cookie("oe","&o=UserID__EName", { expires: 7 });
    else
        $.cookie("oe","&o=-UserID__EName", { expires: 7 });
    });
    
    $("#o_TTime").click(function(){
    $("#o_none").attr("checked",false);
    if($(this).prop("checked")){
        $.cookie("ot","&o=TTime", { expires: 7 });
    }
    else{
        $.cookie("ot","&o=-TTime", { expires: 7 });
    }
    });
    $("#o_none").click(function(){
    $.cookie("od","", { expires: 0 });
    $.cookie("op","", { expires: 0 });
    $.cookie("oe","", { expires: 0 });
    $.cookie("ot","", { expires: 0 });
    $("#o_DeptID").attr("checked",false);
    $("#o_PIN").attr("checked",false);
    $("#o_EName").attr("checked",false);
    $("#o_TTime").attr("checked",false);
    });
    
    $.cookie("url","", { expires: 0 });
    $.cookie("pin","", { expires: 0 });
    $.cookie("od","", { expires: 0 });
    $.cookie("op","", { expires: 0 });
    $.cookie("oe","", { expires: 0 });
    $.cookie("ot","", { expires: 0 });
    //$("#"+g_activeTabID+" #id_ComeTime").val("");
    //$("#"+g_activeTabID+" #id_EndTime").val("");
    $("#tbl").html("<thead><tr><th abbr='UserID__PIN'>{% trans 'PIN' %}</th><th abbr='UserID__EName'>{% trans 'EName' %}</th><th abbr='UserID__DeptID__DeptName'>{% trans 'department name' %}</th>{{ cl.FieldName.TTime }}{{ cl.FieldName.State }}{{ cl.FieldName.Verify }}{{ cl.FieldName.WorkCode }}{{ cl.FieldName.Reserved }}{{ cl.FieldName.SN }}<th>{% trans 'Picture' %}</th></tr></thead>"+$("#tbl").html());
    $("#"+g_activeTabID+" #id_ComeTime").focus(
    function() { this.select(); }
    );
    $("#Employee").focus(
    function() { this.select(); }
    );
    $("#"+g_activeTabID+" #id_EndTime").focus(
    function() { this.select(); }
    );
    
});

function validate_form_iaccessempreports(){   //验证表单的合法性(人员(可不选，在计算项目中计算选中部门的人员，其他的就默认值0)、开始时间、结束时间)
	var t_emp=getSelected_emp();
	var t_ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
	var cTime=t_ComeTime.split("-");
	var t_EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
	var eTime=t_EndDate.split("-");
	//var deptIDs=$("#hidden_depts").val()
    var deptIDs=$.cookie("dept_ids")
	cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
	edate=new Date(parseInt(eTime[0],10),parseInt(eTime[1],10)-1,parseInt(eTime[2],10));
	var days=(edate.valueOf()-cdate.valueOf())/(1000*3600*24)+1;
	if(t_ComeTime=="" || t_EndDate=="" || cdate>edate ||deptIDs==""||deptIDs==null||!valiDate(t_ComeTime)||!valiDate(t_EndDate))//||days>31 
		return 1;
	else
		return 0
}
$("#id_custom").click(function(){//调整自定义显示自动高度，放在$function外面，保证data_list.html中$("#id_custom").click()方法先运行
	var top =  $(".customlink").position().top;
	var left = $(".customlink").position().left;
	$("#show_field_selected").css({position: 'absolute',display:"block",top: top+74,left: left});
})

</script>
<div class="module1" style="position:relative;height:100%;">

<form id="id_form_export" action="/iclock/att/exportReport/" method="POST">

<table id="changelist" style="margin-bottom: 2px;">
<tr>
<td width="70px" colspan="1"><span style="font-size: 15px; padding-right: 20px;"><b><img src="/media/img/blogmarks.png" /></b></span></td>
<td width="660px"><span style="display: none; " id="id_con_error" ></span></td>
<td align="right"width="70px"><div id="id_line"><img src="../media/img/home.png" title="{% trans 'Add to home page' %}" onclick="javascript:saveHome();" /></div></td>
 
</tr>

<tr>
    <td colspan="3" width="720px">
       <span style="width:295px">
            <span>
                <div>
                <span style="float:left;"><label style="line-height:21px;padding:1px;" class="required">{% trans "department" %}</label></span><span style="float:left;"><input type="text" readOnly="readOnly"  name="department"  id="department"/></span>
                <span style="float:left;"><img  class="drop_dept" title="{% trans 'open department tree' %}" src="/media/img/sug_down_on.gif" id="id_drop_dept" onclick="javascript: department_tree();"/></span>
                </div>

                <!--div id="show_deptment">
                <div class="title"><span id="id_opt_trees"><input type="checkbox" id="id_cascadecheck"/>{% trans 'Includes subordinate departments' %}</span><span onclick="hideDeptment ();" class="close" title="{% trans "return" %}"/><span onclick="save_hideDeptment ();" class="save_Deptment" title="{% trans "Submit" %}"/></div>
                <div id="id_dept"></div>
                </div>
            </span>
            <span>
                <div><span style="float:left;"><label style="line-height:21px;padding:1px;" class="required">{% trans 'employee' %}</label></span><span style="float:left;"><input type="text" readOnly="readOnly" name="Employee" id="Employee"/>
                </span>
                <span style="float:left;"><img  title="{% trans 'open employee list' %}" src="/media/img/sug_down_on.gif" id="id_drop_emp"/></span>
                </div>
                <div id="show_emp">
                <div class="title"><span onclick="hideEmployee ();" class="close" title="{% trans "return" %}"/><span onclick="save_hideEmployee ();" class="save_Deptment" title="{% trans "Submit" %}"/></div>
                <div id="id_emp"></div>
                </div>
            </span--!>
        </span>
    
        <span style="width:385px">
            <span>
                <label style="line-height:21px;padding:1px;" class="required">{% trans "Begin Date" %}</label>
                <input type="None" size="11" class="vDateOnlyField" id="id_ComeTime" maxlength="10" name="ComeTime"/>
                <label style="line-height:21px;padding:1px;" class="required">{% trans "End Date" %}</label>
                <input type="None" size="11" class="vDateOnlyField" id="id_EndTime" maxlength="10" name="EndTime"/>
                
                <span style="padding: 3px; display: none; float: left;"><input type="hidden" id="id_export_con" value="" name="tblName"/>
                <input type="hidden" id="id_deptIDs" value="" name="deptIDs"/>
                <input type="hidden" id="id_UserIDs" value="" name="UserIDs"/>
                <input type="hidden" id="id_l" value="1000000" name="l"/>
                </span>
            </span>
            
        </span>
       
    </td>

</tr>
<tr id="toolbar">
    <td id="divPage" width="800px" colspan="3">
        <div id="id_order" style='display:inline;float:left;width:355px'><label class="required">{% trans "Order" %}</label>
        <input type="checkbox" name="DeptID" id="o_DeptID" style="padding-left:1px;"/>{% trans "department" %}
        <input type="checkbox" name="PIN" id="o_PIN" style="padding-left:1px;"/>{% trans "PIN" %}
        <input type="checkbox" name="EName" id="o_EName" style="padding-left:1px;"/>{% trans "EName" %}
        <input type="checkbox" name="TTime" id="o_TTime" style="padding-left:1px;" checked/>{% trans "time" %}
        <input type="radio" name="order_type" value="Asc" id="o_Asc" style="padding-left:1px;"/>{% trans "Ascending Order" %}
        <input type="radio" name="order_type" value="Desc" id="o_Desc" style="padding-left:1px;" checked/>{% trans "Descending Order" %}
        </div>

        <div id="id_toolbar" style='display:inline;width:340px'>
    		<ul id="nav" class="nav">
    			<li id="id_calcReport" style="border:1px solid #77B7DE;display:none;"><span class="searchlink"></span><a href="#" title="{% trans "Only need to count statistics once before viewing all the reports." %}">{% trans 'Calculate' %}</a></li>
    			<li id="id_reload" style="border:1px solid #77B7DE;display:none;"><span class="reloadlink"></span><a href="#">{% trans "Reload" %}</a></li>
    			<li id="id_export" style="border:1px solid #77B7DE;"><span class="exportlink"></span><a href="#">{% trans "Export" %}</a></li>
    			<li id="id_print" style="border:1px solid #77B7DE;"><span class="printlink"></span><a href="#">{% trans "Print" %}</a></li>
    			<li id="id_custom" style="border:1px solid #77B7DE;"><span class="customlink"></span><a href="#" title="{% trans 'User define fields' %}" >{% trans "Preferences" %}</a></li>
    		</ul>
    	</div>
    </td>
</tr>
</table>
</form>
	<div id="RightPane"  valign="top" style="width: 100%;height:100%;"><!-- Tabs pane -->
		<div id="report_tabs" style="width:100%;height:100%;">	</div>
	
	</div>
</div>

<div id="show_field_selected">
	<div class="title"><span class='close' onclick='hideFields_define();'></span></div>
	<div id="id_fields_selected"></div>
	<div class="btn">
	<input type="button" value='{% trans "Submit" %}' id="btn_DefineField_rp"  class="btnOKClass">
	<input type="button" value='{% trans "Return" %}' id="btnShowCancel" onclick="hideFields_define();" class="btnCancelClass">
	</div>
</div>
  





