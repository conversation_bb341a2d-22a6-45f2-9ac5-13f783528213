{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <style>
       .mui-segmented-control .mui-control-item {
            line-height: 40px;
            text-align: center;
        }

        .mui-table-view-cell > a:not(.mui-btn) {
            margin: -18px -15px;
        }

        .mui-control-content {
            background-color: white;
            min-height: 215px;
        }

        .mui-control-content .mui-loading {
            margin-top: 50px;
        }

        html, #slider, .mui-content {
            width: 100%;
            height: 100%;
        }

        body {
            width: 100%;
            height: 100%;
        }

        .mui-slider-group {
            height: calc(100% - 45px);
        }

        .mui-content {
            background-color: #ececec;
        }

        .result_list {
            height: 40px;
            line-height: 40px;
            text-align: center;
            font-size: 16px;
            color: #4CD964;
        }

        .result-card {
            margin: 40px;
        //  border: 1 px solid #6d6d72;
            border-radius: 6px;
            background: white;
            margin-top: 40%;
        }
    </style>
{% endblock %}

{% block content %}
	<header class="mui-bar mui-bar-nav">
        <a href="/iapp/ipos_main/" class=" mui-icon mui-icon-left-nav mui-pull-left"></a>
        <h1 class="mui-title" style='font-size: 16px'>{% trans 'Redeem Voucher' %}</h1>
    </header>
    <div class="mui-content" style="font-family: Microsoft YaHei;">
        <div class="result-card">
            <div class="result_list" style="font-size: 14px; color: #6d6d72;">{% trans 'Confirm whether WeChat payment has been completed' %}</div>
            <div id='order_paid' class="result_list" onclick="orderCheck(this)" var={{ out_trade_no }} style=" line-height: 64px; height: 64px; border-bottom: 1px solid
                 #c8c7cc;border-top: 1px solid #c8c7cc; font-size: 22px;">{% trans 'Payment completed' %}
        </div>
        <div class="result_list" onclick="repay(this)" var={{ out_trade_no }} style="font-size: 16px; color: #6d6d72;
        ">{% trans 'Pay again if encountering problems' %}
    </div>
{% endblock %}
{% block extrjs %}
    <script type="text/javascript">
        mui.init();
        function orderCheck(a) {
            var out_trade_no = a.getAttribute("var");
            window.location.href = '/iapp/ipos/wechatpayCheck/?out_trade_no=' + out_trade_no;
        }
        function repay(a) {
            var out_trade_no = a.getAttribute("var");
            mui.post('/iapp/ipos/repay/?out_trade_no=' + out_trade_no,{
            },function(data){
                var url = data.url
                // console.log('url',url)
                if (url == null){
                    window.location.href = "/iapp/ipos/self_recharge/"
                }
                else{
                    window.location.href = url
                }
                },'json'
            );
        }
    </script>
{% endblock %}
