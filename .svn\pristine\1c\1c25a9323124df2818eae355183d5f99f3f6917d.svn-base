{% load i18n %}
{% load iclock_tags %}
<div class='alert' style='height: 20px;margin-left: 5px;margin-top: 5px;'>{% trans 'Custom menu items of each module can be set up to 5, only valid for the current logged-in user, does not affect other users to use, all users can freely configure the menu items, save please refresh the page to take effect.' %}</div>

<div id='id_option' style='padding: 20px;'>
    <form id='id_edit_form_calc' method='post'>
        <div class='ui-widget-header' style='width:700px;height: 28px;margin-left: 100px;margin-top: 10px;'><h3 style="padding-top: 8px;padding-left: 10px"><a>{% trans 'Custom Menus Settings' %}</a></h3></div>
        <table id="tab" style="margin-left: 100px;margin-top: 20px;">
            {% for key,value in menu_list.items %}
                    <td valign="top"  >
                        <table id='{{ key }}'>
                            <th style="text-align: center;">{{ key }}</th>
                        {% for key_1,value_1 in value.items %}
                            <tr>
                                <td>
                                    <input type='checkbox' maxlength='30' id='id_{{ key_1 }}' name={{ key_1 }} />{{ value_1 }}
                                </td>
                            </tr>
                        {% endfor %}
                        </table>
                    </td>
            {% endfor %}
        </table>
    </form>
</div>
<div><ul class='errorlist'><li id='option_calc_id_error' style='display:none;'></li></ul></div>
	{% if user|HasPerm:"iclock.sys_common_setting" %}
    <div style="margin-left: 100px;margin-top: 20px;">
        <input id='id_submit' type='button' class='m-btn  zkgreen rnd' value='{% trans "Save" %}'></input><br>
    </div>
	{% endif %}


<script>
{% autoescape off %}
    var selfset={{ selfset }}
    var menu_list="{{ menu_list }}"
{% endautoescape %}

var menu_jsonstr = menu_list.replace(/'/g,'"').replace(/u/g,'')
var menu = eval('(' + menu_jsonstr + ')');
var SaveOptions=function(obj,keys){
    $("#option_calc_id_error").html('')
    var formStr=formToRequestString(obj.get(0));
    var url='/iclock/isys/options/'
    $.post(url+'?action='+keys,
        formStr,
        function (ret, textStatus) {
            $("#"+g_activeTabID+" #option").remove()
                if (keys=='status')
                reloadData();
                $("#option_calc_id_error").css("display","block");
                $("#option_calc_id_error").html(ret.message);
        },
        "json");
}

function limit_max() {
    var sign = '';
    for(var tabName in menu){
        var selectnumb  = get_checked_item(tabName);
        if(selectnumb.length>5){
            alert(tabName+'{% trans 'Module,You can set up up to 5.' %}');
            sign = 1
        }

    }
        return sign
}

function get_checked_item (tabName) {

        var target_table = $('table').filter(function (_index) {
            if (this.id === 'tab') {
                return false;
            }
            var all_found = $(this).find('th');
            for (var i = 0, n = all_found.length; i < n; i++) {
                var elem = all_found[i];
                if (elem.innerText === tabName) {
                    return true;
                }
            }
            return false;
        });
        var all_check_box = $('td input[type=checkbox]', target_table);
        var result = [];
        for (var i = 0, n = all_check_box.length; i < n; i++) {
            var elem = all_check_box[i];
            if ($(elem).prop('checked')) {
                result.push(elem);
            }
        }
        return result;
}

$(function(){
    $("#"+g_activeTabID+" #id_start_date").datepicker(datepickerOptions)
    for(var i in selfset.selfset_module){
         $("#"+g_activeTabID+" #id_"+ selfset.selfset_module[i]).attr("checked","checked");
        }

    $("#"+g_activeTabID+" #id_submit").click(function(){
        if(!limit_max()){
            $.blockUI({title:'',theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Updating system configuration...' %}</br></h1>'});
                var queryStr=$("#"+g_activeTabID+" #id_edit_form_calc").formSerialize();

                $.ajax({
                    type: "POST",
                    url:"/base/isys/option_selfset_menu/",
                    data:queryStr,
                    dataType:"json",
                    success:function(retdata){
                    $.unblockUI();
                        var message=retdata.message
                        $("#option_calc_id_error").css("display","block");
                        $("#option_calc_id_error").html(message);
                            }
                        });}
        });



});

</script>

