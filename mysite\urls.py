#coding=utf-8
#from django.conf.urls.defaults import *
from django.conf.urls import include
from django.urls import re_path as url
from django.conf import settings
#from django.contrib.auth.views import login, logout, password_change
import os, time,copy
from django.utils._os import safe_join
#from settings import MEDIA_ROOT,ADDITION_FILE_ROOT,WORK_PATH
from django.template import loader, Context, RequestContext
from django.template.response import TemplateResponse
from django.http import QueryDict, HttpResponse, HttpResponseRedirect, HttpResponseNotModified
from django.shortcuts import render
from mysite.utils import *
from django.contrib import admin
from django.contrib.auth.views import *
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from mysite.base.models import GetParamValue, SetParamValue
#from iclock.nomodelview import wap,waplogin,waprecordlist
#from iclock.templatetags.iclock_tags import createmenu
#from iclock.templatetags.iclock_tags import *
from mysite.core.menu import *
from django import template
from . import core, iclock
from django.views.static import serve
import sys
from mysite.core.zkmimi import *
from mysite.wxapp.views import check_user_openid, get_reuqest_user
from mysite.visitors.models import staffwxid
from django.contrib.auth import login
from django.middleware.csrf import get_token

#admin.autodiscover()
#import mysite.authurls

def vitualLogin(request):
    from django.contrib.auth import authenticate
    username=request.GET.get('username','')
    password=request.GET.get('password','')
    user = authenticate(username=username, password=password)
    if user:
        if user.is_active:
            from django.contrib.auth import login
            login(request, user)
        else:
            return False
    else:
        return False
    return True
    # if user:
    #     return True
    # else:
    #     return False

# 单点登陆
def ilogin(request):
    if request.GET:
        ret = vitualLogin(request)
        if not ret:
            return render(request,'welcome_sys.html',{'content': u"%s" % _(u'Login failed')})

        response = HttpResponseRedirect('/iclock/imanager')
        response.set_cookie('csrftoken', get_token(request))
        return response



ADDITION_FILE_ROOT = safe_unicode(addition_file_root(), 'GB18030')
try:
    if settings.PRODUCTCODE==14:
        settings.SITETITLE=_(u'Time Refined Management Platform')
    else:
        settings.SITETITLE=_(u'Time & Security Refinement Management Platform')#_('ZKEcoPro Precise time and security management platform')
    siteTitle=settings.SITETITLE
except Exception as e:
    siteTitle=_('Precise time and security management platform')
SESSION_KEY = '_auth_user_id'
def checkBoxRegister():
    boxfile='/usr/share/plymouth/themes/zkteco/zkteco.script'
    if os.path.exists(boxfile): #linux盒子独有文件
        fn = os.path.join(ADDITION_FILE_ROOT, 'license', 'actbox.dat')
        if not os.path.exists(fn):   #没有激活文件
            return True
    return False

def index(request):
    #response=HttpResponseRedirect(settings.LOGIN_URL)
    #return response
    #if not request.user or request.user.is_anonymous:
        #url=settings.LOGIN_URL
    #else:
    url=settings.LOGIN_REDIRECT_URL
#    if os.name == 'posix' and checkBoxRegister():
#        url = '/minisvr/'
    return HttpResponseRedirect(url)
#	return render("index.html", RequestContext(request, {}),);
@login_required
def icenter(request, template_name='base_site.html'):
    # return HttpResponseRedirect(LOGIN_REDIRECT_URL)
    #print request.POST
    if request.GET:  #处理AppScan安全扫描，SSL请求中的查询参数问题，无其他用途
        return None
    j=0
    #settings.ENABLED_MOD_TMP=copy.deepcopy(settings.ENABLED_MOD)
    #settings.G_MODULE=SALE_MODULE[0]
    #for m in SALE_MODULE:    #初始显示第一个模块的功能
    #	if settings.G_MODULE!=m:
    #		settings.ENABLED_MOD_TMP.remove(m)
    #	j=j+1
    getISVALIDDONGLE(reload=0)
    home_url='/iclock/homepage/showHomepage/'#GetParamValue('opt_basic_homeurl','/iclock/homepage/showHomepage/',str(request.user.id))
    Title=GetParamValue('opt_basic_sitetitle',u"%s"%siteTitle)
    if settings.DEMO==1:
        Title=siteTitle
#	ui=GetParamValue('opt_basic_ui','ui0',str(request.user.id))
#	uix=request.POST.get('skins','ui0')
#	if uix!=ui:
#		ui=uix
#		SetParamValue('opt_basic_ui',ui,request.user.id)


    #settings.SALE_MODULE=['att','acc']#模拟测试

    mod_name=''

    if len(settings.SALE_MODULE)==1:
        mod_name=settings.SALE_MODULE[0]
    elif len(settings.SALE_MODULE)>1:
#        if 'adms' in settings.SALE_MODULE and 'att' in settings.SALE_MODULE:
 #           settings.SALE_MODULE.remove('adms')
        mod_name=settings.SALE_MODULE[0]
    if settings.PRODUCTCODE==15:
        mod_name='ipos'
    m=MenuList(request,mod_name)
    menu_t=m.getmodulesMenu()
    #获取每个模块的首页
    home_url=m.get_homeurl()

    tabs_style=GetParamValue('opt_users_mul_page','1',request.user.id)
    submenu_t=m.getAllMenus()
    # template_name='base_site.html'
    # LANGUAGE_CODE=settings.LANGUAGE_CODE
    # if settings.LANGUAGE_CODE=='zh-Hans':
    #     LANGUAGE_CODE="zh-cn"
    context={"home_url":home_url,
         'tabs_style':tabs_style,
        'iclock_url_rel': 'iclock',
        'title':Title,
        'ui':'ui0',
        'UIES':settings.UIES,
        'menu_title':menu_t,#模块部分
        'submenu_title':submenu_t,#
        'mod_name':mod_name,
        'debug_warn':_(u'Please turn off debug mode in time') if settings.DEBUG==1 else '',
      #  'LANGUAGE_CODE':LANGUAGE_CODE,
        }
    return render(request, template_name, context)




    #response=render("base_site10.html", RequestContext(request, {"home_url":home_url,
    #									 'iclock_url_rel': 'iclock',
    #									 'title':Title,
    #									 'ui':'ui0',
    #									 'UIES':settings.UIES,
    #									 'menu_title':menu_t,
    #									 'submenu_title':submenu_t,
    #									 'mod_name':mod_name,
    #									 'modules_menu':modules_t
    #									 }),)
    #return response
@require_http_methods(["POST"])
def my_i18n(request):
    from django.views.i18n import set_language
    r=set_language(request)
    req_lang = request.POST.get('language')
    set_cookie(r, 'django_language', req_lang, 365*24*60*60)
    # DefaultLangProxy.set_language(req_lang)
    return r
#  return render()
@login_required
def setModule(request):
    _mod=request.GET.get('mod','att')
    settings.G_MODULE=_mod

    #settings.ENABLED_MOD_TMP=copy.deepcopy(settings.ENABLED_MOD)
    #for m in settings.SALE_MODULE:
    #	if m!=_mod:
    #		settings.ENABLED_MOD_TMP.remove(m)
    #m_text=createmenu(request)
    #response=render("left_menu.html", RequestContext(request, {"menu":m_text}))
    #return response

def image(request):
    raw=request.body
#	print raw
    #time.sleep(100)
    return render(request,"index.html", {})

def AdRegFace(request):
    url= request.get_full_path()
    seach = url.split('?')
    if len(seach)>1:
        response = HttpResponseRedirect('/iapp/facereg/facereg/'+seach[1])
    else:
        response = HttpResponseRedirect('/iapp/facereg/facereg/')
    return response

def AdWxRegFace(request):
    response = HttpResponseRedirect('/iapp/facereg/wxfacereg/')
    return response

# 保存tinymce富文本上传的图片。
def image_upload(request):
    import io
    import os
    import time
    output = io.BytesIO()
    f = request.FILES['file']
    entry_date = request.POST.get('entry_date')
    entry_date = time.strptime(entry_date, "%Y-%m-%d %H:%M:%S")
    entry_date = int(time.mktime(entry_date)*10)
    file_name = f.name
    fname = os.path.join(addition_file_root(),'tinymce',str(entry_date),file_name)
    size = f.size
    MAX_PHOTO_WIDTH = 2*1080*1080
    try:
        os.makedirs(os.path.split(fname)[0])
    except:
        pass
    for chunk in f.chunks():
        output.write(chunk)
    import PIL.Image as Image
    try:
        output.seek(0)
        im = Image.open(output)
    except IOError as e:
        #		print "error to open", imgUrlOrg, e.message
        return getJSResponse("result=-1; message='Not a valid image file';")
    # print f.name
    size = f.size
    if im.size[0] > MAX_PHOTO_WIDTH:
        width = MAX_PHOTO_WIDTH
        height = int(im.size[1] * MAX_PHOTO_WIDTH / im.size[0])
        try:
            im = im.resize((width, height), Image.LANCZOS)
        except Exception as e:
            print ("6666666666==", e)
    try:
        im.save(fname);
    except IOError:
        print ("saveUploadImage==")
        im.convert('RGB').save(fname)
    result = {}
    result['location'] = getStoredFileURL('tinymce/%s'%str(entry_date), None, file_name)
    return getJSResponse(result)

def wxcheckSignature(request):
    import hashlib
    from mysite.core.wxsignature import get_openid_by_access_token
    state = request.GET.get('state', '')
    request.session["wxoa"] = '1'
    if 'emp_pin' in request.session:
        del request.session["emp_pin"]
    if 'employee' in request.session:
        del request.session["employee"]
    code = request.GET.get('code', '')
    # print("wxcheckSignature code=", code)
    if code:
        get_openid_result = get_openid_by_access_token(code) # 获取openid
        # print('get_openid_result----',get_openid_result)
        if get_openid_result['result'] == 0:
            try:
                openid = get_openid_result['openid']
                if state.startswith('wap_') or state.startswith('my_info'):
                    if check_user_openid(openid):
                        sf = staffwxid.objects.get(wxid=openid)
                        user = get_reuqest_user(request, sf.pin)
                        ret = login(request, user)
                        user = request.user
                        if user.is_anonymous:
                            return _(u"Anonymous user")
                        if user.logintype != 1:
                            user.employee = None
                            if 'employee' in request.session:
                                del request.session["employee"]
                        else:
                            request.session['employee'] = user.employee
                        if state.startswith('wap_'):
                            if state.split('_')[1] == 'main':
                                redirect_url = "/iapp/main/"
                            elif state.split('_')[1] == 'wxapp':
                                redirect_url = "/wxapp/%s/?single_page=1" % (
                                    state[len('wap_wxapp_'):])
                            elif state.split('_')[1] == 'detail':
                                redirect_url = "/%s/%s?pk=%s" % (
                                    state.split('_')[3], state.split('_')[4], state.split('_')[2])
                            else:  # (其他默认访问wap地址模块为state.split('_')[1])
                                redirect_url = "/iapp/" + state.split('_')[1] + "/%s/?single_page=1" % (
                                    state[len(state.split('_')[0] + '_' + state.split('_')[1] + '_'):])
                        elif state.startswith('my_info'):
                            redirect_url = "/wxapp/my_info/"
                        else:
                            cc = {
                                "message": _(u"The configuration address is incorrect.")
                            }
                            return render(request, 'iapp/404_wap.html', cc)

                    else:
                        if state.startswith('wap_'):
                            if state.split('_')[1] == 'main':
                                redirect_url = "/wxapp/iapp_bind_openid?openid=%s" % (openid)
                            elif state.split('_')[1] == 'detail':
                                redirect_url = "/%s/%s?pk=%s" % (
                                    state.split('_')[3], state.split('_')[4], state.split('_')[2])
                                redirect_url = "/wxapp/bind_openid?target_page=%s&mode=%s&openid=%s&id=%s" % (
                                    state.split('_')[4],
                                    state.split('_')[3],
                                    openid,
                                    state.split('_')[2])
                            else:
                                redirect_url = "/wxapp/bind_openid?target_page=%s&&mode=%s&&openid=%s" % (
                                    state[len(state.split('_')[0] + '_' + state.split('_')[1] + '_'):],
                                    state.split('_')[1],
                                    openid)
                        elif state.startswith('my_info'):
                            redirect_url = "/wxapp/bind_openid?target_page=%s&&mode=%s&&openid=%s" % (
                                state, 'wxapp', openid)
                        else:
                            cc = {
                                "message": _(u"The configuration address is incorrect.")
                            }
                            return render(request, 'iapp/404_wap.html', cc)
                if state.find('stranger_') >= 0:  # 外部人员访客
                    if state.split('_')[1] == 'detail':
                        redirect_url = "/%s/%s?pk=%s&single_page=1&cancle_back=1&code=%s" % (
                        state.split('_')[3], state.split('_')[4], state.split('_')[2], code)
                    else:
                        redirect_url = "/wxapp/%s?single_page=1&code=%s" % (
                        state.split('_')[1], code)
                return HttpResponseRedirect(redirect_url)
            except Exception as e:
                cc = {
                    "message": str(e)
                }
                return render(request, 'iapp/404_wap.html', cc)
        else:
            cc = {
                "message": get_openid_result['message']
            }
            return render(request, 'iapp/404_wap.html', cc)
    else:
        cc = {
            "message": _(u"Code does not exist!")
        }
        return render(request, 'iapp/404_wap.html', cc)





    # signature=request.GET.get('signature','')
    # if signature:
    #     timestamp=request.GET.get('timestamp','')
    #     nonce=request.GET.get('nonce','')
    #     echostr=request.GET.get('echostr','')
    #     str="%s%s%s"%("zkecopro",timestamp,nonce)
    #     sha = hashlib.sha1(str)
    #     encrypts = sha.hexdigest()
    #     print(encrypts, signature)
    #     return HttpResponse(echostr)

def photo_decription(request, path, document_root):
    """
    照片解密，若jpg是经过加密处理的，则解密后返回给客户端（URL方式访问图片）
    """
    from django.utils._os import safe_join
    import posixpath
    path = posixpath.normpath(unquote(path)).lstrip('/')
    path_prefix = path.split('/')[0]
    if settings.MULTI_TENANT:
        path_prefix = path.split('/')[1]
    if path_prefix in ['photo', 'merchandise', 'food']:  # 照片、头像、比对照片、商品资料,餐品维护中的图片获取
        try:
            image_data = cache.get('%s_DECRIPTION_PHOTO_%s'%(settings.UNIT, path))
            if image_data:
                return HttpResponse(image_data, content_type="image/jpeg")
            fullpath = safe_join(document_root, path)
            if os.path.exists(fullpath):  # 没找到jpg文件，但有crypt文件，解密数据返回
                img = open(fullpath, 'r')
                data = img.read()
                if str(data[:10]) == "CRYPT_IMG:":
                    image_data = aes_crypt(data[10:], False)
                    cache.set('%s_DECRIPTION_PHOTO_%s'%(settings.UNIT, path), image_data, 60*60*2)  #缓存2小时
                    return HttpResponse(image_data, content_type="image/jpeg")
        except:
            pass
    return None

@login_required
def serve_ex(request, path, document_root, show_indexes):
    """用于URL访问文件的登录校验"""
    ret = photo_decription(request, path, document_root)
    if ret:
        return ret
    return serve(request, path, document_root, show_indexes)

def device_pic_serve(request, path, document_root=None, show_indexes=False):
    """设备服务端文件时使用，当前暂用于取服务器的比对照片"""
    ret = photo_decription(request, path, document_root)
    if ret:
        return ret
    return serve(request, path, document_root, show_indexes)

@csrf_protect
@login_required
def fileserve(request, path, document_root=None, show_indexes=False):
    sn = request.GET.get('SN', '')
    purl = request.GET.get('url', '')
    if purl:
        ##兼容固件升级, http://************:8099/iclock/file/?SN=CLMY203560015&url=/iclock/file/firmware/admin/firm_675524214
        firm = purl.replace('iclock/file/', '')
        path = firm
    else:
        path_prefix = path.split('/')[0]
        #对于自定义登录背景，logo不进行登录校验
        if path == 'photo/logo_custom.png' or path == 'photo/login_bgself.jpg':
            return serve(request, path, document_root, show_indexes)
        if settings.MULTI_TENANT:
            path_prefix = path.split('/')[1]
        if path_prefix in ['reports', 'photo', 'merchandise', 'food']:  #需要进行登录验证的资源
            return serve_ex(request, path, document_root, show_indexes)
    # 尝试安全地连接路径
    full_path = safe_join(document_root, path)
    # 检查最终路径是否在基础路径内
    if not full_path.startswith(document_root):
        raise ValueError(_(u'No permission'))  #不允许的文件路径
    return serve(request, path, document_root, show_indexes)

def ping_cutpic(request=None):
    '''
    模拟抠图
    用来判断抠图是否卡死,卡死时可以重启相关Apache进程。 见schedulers 中的 job_check
    '''
    from mysite.utils import cutpic_zkcropface
    # 随便找个图片用来测试
    test_pic = os.path.join(settings.APP_HOME, 'apache','icons', 'blank.png')
    test_dispath = os.path.join(settings.APP_HOME, 'apache','icons', '_blank.png')
    ret = cutpic_zkcropface(test_pic, test_dispath)
    response = head_response()
    resp = 'OK'
    response["Content-Length"] = len(resp)
    response.write(resp)
    return response


from django.views.generic import TemplateView
from mysite.base.views import download_file_new, upgrade_file_response
urlpatterns =[
    url(r'iclock/i18n/setlang/', my_i18n),
    url(r'iclock/iModule/setModule/$', setModule),
 #   (r'iclock/rosetta-i18n/',include('rosetta.urls')),
    # 用于设备获取比对照片的Url
    # url(r'iclock/picfile/(?P<path>.*)$', device_pic_serve, {'document_root': ADDITION_FILE_ROOT, 'show_indexes': True}),
    url(r'iclock/file/(?P<path>.*)', fileserve,{'document_root': ADDITION_FILE_ROOT, 'show_indexes': True}),


    # 远程升级客户端固件，以分包拉取的方式获取文件
    url(r'upgrade/file/(?P<path>.*)$', upgrade_file_response, {'document_root': ADDITION_FILE_ROOT}),

    url(r'iclock/tmp/(?P<path>.*)$', serve,{'document_root': tmpDir(), 'show_indexes': True}),
    # url(r'iclock/ccccc/(?P<path>.*)$', serve, {'document_root': "c:/", 'show_indexes': True}),
    url(r'iclock/media/(?P<path>.*)$', serve,{'document_root': settings.MEDIA_ROOT, 'show_indexes': False}),
    url(r'iapp/mobile/(?P<path>.*)$', serve,{'document_root': settings.WORK_PATH+'/iapp', 'show_indexes': False}),
    url(r'api/doc/(?P<path>.*)', serve,{'document_root': settings.WORK_PATH+'/api/doc', 'show_indexes': False}),
    url(r'accounts/', include('mysite.accounts.urls')),
    url(r'iclock/staff/', include('mysite.iclock.staff_portal')),
    url(r'iclock/backup/', include('mysite.iclock.staff_portal')),
    url(r'iclock/ilogin', ilogin),   #单点登陆实例
    url(r'iclock/imanager$', icenter),
    url(r'^iclock/getMenus/$', core.menu.getMenus),
#url(r'^iclock/backup/mysql/$',iclock.backup.mysqlBackup),
    url(r'iclock/', include('mysite.iclock.urls')),
    url(r'meeting/', include('mysite.meeting.urls')),
    url(r'base/', include('mysite.base.urls')),
    url(r'acc/', include('mysite.acc.urls')),
    url(r'visitors/', include('mysite.visitors.urls')),
    url(r'ipos/', include('mysite.ipos.urls')),
    url(r'asset/', include('mysite.asset.urls')),
    url(r'locker/', include('mysite.asset.urls')),
    url(r'payroll/', include('mysite.payroll.urls')),  # 薪资
#	url(r'patrol/', include('mysite.patrol.urls')),
    url(r'api/', include('mysite.api.urls')),
    url(r'iapp/', include('mysite.iapp.urls')),
	url(r'wxapp/', include('mysite.wxapp.urls')),
    url(r'selfservice/', include('mysite.selfservice.urls')),
    #	url(r'sms/', include('mysite.sms.urls')),   #for mobile
##    (r'^admin/', include('django.contrib.admin.urls')),
    url(r'^media/(?P<path>.*)$', serve,{'document_root': settings.MEDIA_ROOT, 'show_indexes': True}),
#    #(r'^options/', 'iclock.setoption.index'),
#    #(r'^image', image),
#	#(r'^admin/(.*)', admin.site.root),
#	#(r'^photologue/', include('django_apps.photologue.urls')),
#	#(r'^(?i)wap$',wap),
##	(r'^(?i)wap/$',wap),
#	#(r'^waplogin/$',waplogin),
#	#(r'^waprecordlist/$',waprecordlist),
    url(r'adreg/', AdRegFace),
#    url(r'minisvr/', include('mysite.minisvr')),
    url(r'^$', index),
    url(r'^release/(?P<version>.*)', download_file_new),
    # 公众号设置-功能设置-里面的JS接口安全域名的配置和网页授权域名的配置。域名格式：域名/wxcheckSignature；将文件放到files/wxapp下
    url(r'wxcheckSignature/(?P<path>.*)$', serve,{'document_root': ADDITION_FILE_ROOT+'/wxapp', 'show_indexes': True}),
    #ID离线消费白名单更新
    url(r'^file/(?P<path>.*)$', serve,{'document_root': ADDITION_FILE_ROOT+'/ipos/whitelist', 'show_indexes': True}),
    url(r'wxcheckSignature', wxcheckSignature),
    url(r'wxfacereg/', AdWxRegFace),
    url(r'image/image_upload', image_upload),# 保存tinymce富文本上传的图片。
    # 远程升级客户端固件
    # "GET /iclock/file?SN=6587210100006&url=/iclock/file/firmware/admin/firm_678204609 HTTP/1.1"
    url(r'iclock/file(?P<path>.*)$', fileserve, {'document_root': ADDITION_FILE_ROOT, 'show_indexes': True}),
    url(r'iclock/ping_cutpic', ping_cutpic),
]

if 'rosetta' in settings.INSTALLED_APPS:
    urlpatterns += [
        url(r'^rosetta/', include('rosetta.urls'))
    ]
    urlpatterns += [
        url(r'^admin/', admin.site.urls)
    ]

if hasattr(settings,'SUPPORT_QYWX') and settings.SUPPORT_QYWX == 1:
    urlpatterns.insert(0, url(r'wxapp/', include('mysite.wxapp.urls')))

if settings.DEMO == 1:
    urlpatterns += [
        url(r'soft/release/(?P<path>.*)$', serve, {'document_root': settings.RELEASE_PATH_EX, 'show_indexes': True})
    ]

# if getattr(settings, 'BIOPHOTO_FORMAT', 1) == 1:
#     # 比对照片下发格式为URL时， 用于设备获取比对照片的Url
#     urlpatterns.insert(1,
#         url(r'iclock/picfile/(?P<path>.*)$', device_pic_serve, {'document_root': ADDITION_FILE_ROOT, 'show_indexes': True})
#     )
urlpatterns.insert(1,
    url(r'iclock/picfile/(?P<path>.*)$', device_pic_serve, {'document_root': ADDITION_FILE_ROOT, 'show_indexes': True})
)

if settings.DEBUG:
    print("%s %s\n----------" % (settings.PRODUCTCODE == 14 and 'E-ZKTime' or 'E-ZKEcoPro', settings.VERSION))