{% load i18n %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table{
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid black;
            padding: 2px 5px;
            font-weight: normal;
            font-size: 16px;
        }
        table, tr, td, th, tbody, thead, tfoot {
            page-break-inside: avoid !important;
            /*word-break: keep-all !important;*/
            /*white-space: nowrap !important;*/
        }
        h2 {
            text-align: center;
        }
        th{
            height:25px;
        }
    </style>
</head>
<body>
    <h2 align="center">{{ title }}</h2>
    {% block main_table %}
    <table style="width: 100%;">
		<tr>
            <th width="14%" >{% trans "Document No" %}</th>
            <th width="20%" >{{ item.document_number }}</th>
            <th width="14%" >{% trans "Apply Time" %}</th>
            <th width="19%" >{{ item.apply_time }}</th>
            <th width="14%" >{% trans "Applicant" %}</th>
            <th width="19%" >{% if item.UserID %}{% if not item.UserID.EName %}{{ item.UserID.PIN }}{% else %}{{ item.UserID.EName }}{% endif %}{% else %}{{ item.operator|trim }}{% endif %}</th>
{#            <th width="19%" >{% if not item.UserID.EName %}{{ item.UserID.PIN }}{% else %}{{ item.UserID.EName }}{% endif %}</th>#}
        </tr>
		<tr>
            <th width="14%" >{% trans "Repair Description" %}</th>
            <th colspan="5" >{{ item.remark }}</th>
        </tr>
    </table>
    <h4 align="left">{% trans "Asset Detail" %}</h4>
    <table style="margin-top: -10px;">
        <tr>
            {% for header in headers %}
                <th>{{ header }}</th>
            {% endfor %}
        </tr>
        {% for row in datas %}
            <tr>
                <th>{{ forloop.counter }}</th>
                {% for coloum in row %}
                    <th>{{ coloum|trim }}</th>
                {% endfor %}
            </tr>
        {% endfor %}
</table>
    {% endblock %}

</tr>
</table>

<div style="margin-top: 20px">
    <div style="width:100px; float:left; display:inline;">{% trans "Signature of applicant" %}</div>
    <div style="width:250px; float:right; display:inline;">{% trans "Signature Time" %}</div>
</div>
</body>
</html>