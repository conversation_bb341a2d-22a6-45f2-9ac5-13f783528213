# -*- coding: utf-8 -*-
# Generated by Django 1.11.5 on 2017-10-01 14:53
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MyUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(help_text='Required. 30 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=30, unique=True, validators=[django.core.validators.RegexValidator('^[\\w.@+-]+$', 'Enter a valid username.', b'invalid')], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=30, verbose_name='\u59d3\u540d')),
                ('last_name', models.CharField(blank=True, editable=False, max_length=30, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('is_alldept', models.BooleanField(default=False, help_text='\u6388\u6743\u662f\u5426\u7ba1\u7406\u6240\u6709\u90e8\u95e8\u3002', verbose_name='\u6388\u6743\u6240\u6709\u90e8\u95e8')),
                ('AutheTimeDept', models.IntegerField(db_column='authetimedept',blank=True, default=0, help_text='\u7684\u65f6\u6bb5\u548c\u73ed\u6b21\u3002\u5982\u679c\u6240\u6709\u5355\u4f4d\u4f7f\u7528\u540c\u4e00\u5957\u65f6\u6bb5\u548c\u73ed\u6b21\u5ffd\u7565\u6b64\u9879', null=True, verbose_name='\u6388\u6743\u4f7f\u7528')),
                ('Tele', models.CharField(blank=True, db_column='ophone', max_length=30, null=True, verbose_name='\u8054\u7cfb\u7535\u8bdd')),
                ('is_public', models.BooleanField(default=False, editable=False, help_text='\u516c\u5f00\u8d85\u7ea7\u7ba1\u7406\u5458\u59d3\u540d\u3001\u7535\u8bdd\u3001EMAIL\uff0c\u65b9\u4fbf\u8054\u7cfb\u3002', verbose_name='\u662f\u5426\u516c\u5f00\u4fe1\u606f')),
                ('logintype', models.IntegerField(blank=True, default=0, null=True)),
                ('loginid', models.IntegerField(blank=True, default=0, null=True)),
                ('logincount', models.IntegerField(blank=True, default=0, null=True, verbose_name='\u767b\u5f55\u6b21\u6570')),
                ('DelTag', models.IntegerField(db_column='deltag',blank=True, default=0, editable=False, null=True, verbose_name='\u6ce8\u9500\u6807\u8bb0')),
                ('emp_pin', models.CharField(blank=True, max_length=30, null=True, verbose_name='\u5de5\u53f7')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'db_table': 'auth_user',
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'permissions': (('browse_myuser', 'Can browse user'),),
            },
        ),
    ]
