# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-01-14 09:25
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0026_splittimecard'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderList',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('foodname', models.CharField(max_length=20, verbose_name='Variety of dishes')),
                ('price', models.DecimalField(db_column='price', decimal_places=2, max_digits=6, verbose_name='Unit price (yuan)')),
                ('quantity', models.IntegerField(blank=True, editable=False, verbose_name='amount')),
                ('ordercode', models.ForeignKey(blank=True, db_column='orderrecordcode', null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.OrderRecord', verbose_name='Order')),
            ],
            options={
                'verbose_name': 'App order record detail',
                'verbose_name_plural': 'App order record detail',
            },
        ),
        migrations.AddField(
            model_name='aliwxfulllog',
            name='payment_code',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='支付码'),
        ),
        migrations.AlterField(
            model_name='healthydiet',
            name='LikeSum',
            field=models.CharField(db_column='likesum', max_length=40, null=True, verbose_name='Like people number'),
        ),
    ]
