{% autoescape off %}
{% load i18n %}

[
{% for item in latest_item_list %}
{"id":"{{ item.id }}",
"PIN":"{{ item.PIN }}",
"EName":"{{ item.EName|trim |default:""}}",
"Gender":"{{ item.get_Gender_display|default:""}}",
"DeptID":"{{ item.Dept.DeptID }}",
"Card":"{{ item.Card|default:""}}",
"DeptName":"{{  item.Dept.DeptName  }}",
"storage_detail":"{% if can_change %}<a class='can_edit'  href='#' onclick='javascript:addStorage({{item.id}});'>{% trans "Add locker cabinet cells" %}</a>{% else %}{% trans "Add locker cabinet cells" %}{% endif %}"

}
{%if not forloop.last%},{%endif%}
{% endfor %}
]
{% endautoescape %}
