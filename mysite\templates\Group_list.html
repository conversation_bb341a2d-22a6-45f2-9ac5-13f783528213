{% extends "data_list.html" %}
{% load iclock_tags %}
{% block tblHeader %}
{% load i18n %}


//hd='({% trans 'After the submission of the operation need to be about half a minute or so of device in the entry into force' %})'
//hasImport={% if user|HasPerm:"iclock.import_Group" %}true{% else %}false{% endif %}
var att_mod={% if 'att'|enabled_module %}true{% else %}false{% endif %}
var iaccess_mod={% if 'acc'|enabled_module %}true{% else %}false{% endif %}
var meeting_mod={% if 'meeting'|enabled_module %}true{% else %}false{% endif %}
var visitors_mod={% if 'visitors'|enabled_module %}true{% else %}false{% endif %}
var ipos_mod={% if 'ipos'|enabled_module %}true{% else %}false{% endif %}
var asset_mod={% if 'asset'|enabled_module %}true{% else %}false{% endif %}

var device_mod=false
var    ic_permission=[]
var    ic_ids=[];
var    ic_selected_ids=[];

var ic_ownerpermissions={{ permissions }};
options[g_activeTabID].dlg_width=650;
options[g_activeTabID].dlg_height=560;
//var jqOptions=copyObj(jq_Options)
jqOptions[g_activeTabID].sortname='id';
tblName[g_activeTabID]='group'
jqOptions[g_activeTabID].pager='#id_pager_'+tblName[g_activeTabID]
jqOptions[g_activeTabID].colModel=[
	{name:'id',width:220,sortable:true,align:'left',label:'{% trans "GroupID" %}'},
	{name:'name',width:220,sortable:true,align:'left',label:'{% trans "Group name" %}'},
	{name:'Creator',width:220,sortable:false,align:'left',label:'{% trans "creator" %}'}
	];
$(function(){

	var info='<div class="west_info"><p>{% trans "1. Management group is similar to role function" %}</p><p>{% trans "2. Prioritize certain permissions into management groups to facilitate configuring the same permissions for different users." %}</p><p>{% trans "3. The management group is not allowed to be deleted after being used" %}</p><p>{% trans "4. Management group name cannot be repeated" %}</p><p>{% trans "5. The authority to browse departments and personnel is automatically given" %}</p><p>{% trans "6. The export permissions of leave and make-up in the conference module are synchronized with the attendance module" %}</p></div>'
    renderLeftInformation(info);
    smenu="<ul><li  class='subnav_on' onclick=submenuClick('/iclock/data/group/',this);><a href='#'>{% trans "management group" %}</a></li></ul>"
    //$('#menu_div').html(smenu)
	savecookie("search_urlstr",g_urls[g_activeTabID])


    $("#"+g_activeTabID+" #queryButton").hide()
    $("#"+g_activeTabID+" #searchButton").click(function(){
        searchShowGroup();
    });
    $("#"+g_activeTabID+" #searchbar").keypress(function(event){
        if(event.keyCode==13){
         searchShowGroup();
        }
    });
    $("#"+g_activeTabID+" #searchbar").val("{% trans "group name" %}")
	$("#"+g_activeTabID+" #id_newrec").click(function(event){
		processNewModel();
	});
	$("#"+g_activeTabID+" #id_custom").hide()
});
function strOfData_group(data)
{
	return stripHtml(data.id)+' '+data.name;
}
function afterPost_group(flag, obj) {
    $('form').resetForm()
    reloadData()
}
function process_dialog_group(obj)
{
	f=$(obj).find("#id_edit_form").get(0)
	$(f).validate({
			rules: {
					"name":{string:true,"maxlength":50}
				}
			});

}
function process_dialog_again_group(obj)
{
	//return;
    if (att_mod||iaccess_mod||ipos_mod||meeting_mod||visitors_mod||asset_mod)
    device_mod=true
    $("#tabs_group").tabs()
    disable_mod=[]
    if (!att_mod && !iaccess_mod && !ipos_mod && !meeting_mod && visitors_mod && !asset_mod)    // 仅访客模式下设备权限不展示
    disable_mod.push(1)
    if(!att_mod)
    disable_mod.push(2)
    if(!iaccess_mod)
    disable_mod.push(3)
    if(!meeting_mod)
    disable_mod.push(4)
//    if(!device_mod)
//    disable_mod.push(1)
    if(!visitors_mod)
    disable_mod.push(5)
    if(!ipos_mod)
    disable_mod.push(6)
    {% if ''|isZKTime == '1' %}       //zktime10授权文件下取消 其他无关模块 是否展示的功能
        disable_mod=[]
    {% endif %}
    {% if ''|isECOpos == '1' %}       //zkpos授权文件下取消 其他无关模块 是否展示的功能
        disable_mod=[]
    {% endif %}
    {% if ''|isZKAdms == '1' %}       //zkadms授权文件下取消 其他无关模块 是否展示的功能
        disable_mod=[]
    {% endif %}
    $("#tabs_group").tabs( "option", "disabled", disable_mod)
    ic_permission=[]
    ic_ids=[];
    ic_selected_ids=[];
    {% if ''|isZKTime == '1' %}

        ic_allowed_perm=[
            {id:"department",name:"{% trans ' department ' %}",'mod':'employees'},
            {id:"employee",name:"{% trans ' employee ' %}",'mod':'employees'},
            {id:"attcalclog",name:"{% trans 'System Options' %}",'mod':'system'},
            {id:"announcement",name:"{% trans 'Announcement-table' %}",'mod':'system'},
            {id:"userroles",name:"{% trans 'userRoles' %}",'mod':'employees'},

            {id:"ademployee",name:"{% trans 'Mobile Register Photo Review' %}",'mod':'employees'},
            {id:"empzone",name:"{% trans 'Employee Area Setting' %}",'mod':'employees'},


            {id:"group",name:"{% trans 'group' %}",'mod':'system'},
            {id:"myuser",name:"{% trans 'User' %}",'mod':'system'},
            {id:"authterminal",name:"{% trans 'AuthTerminal' %}",'mod':'system'},//终端授权管理
            {id:"adminlog",name:"{% trans 'Admin Action Log' %}",'mod':'system'},
            {id:"interfacelog",name:"{% trans 'API Interface Log' %}",'mod':'system'},
            {id:"devcmds",name:"{% trans ' log of command to device ' %}",'mod':'system'},
            {id:"employeelog",name:"{% trans 'Employee Login Log' %}",'mod':'system'},
            {id:"devlog",name:"{% trans 'Other logs' %}",'mod':'system'},
            {id:"adsetting",name:"{% trans 'Ad Settings' %}",'mod':'system'},



            {% if "msg"|enabled_module %}
                {id:"iclockmsg",name:"{% trans ' information subscription ' %}",'mod':'employees'},

            	{id:"messages",name:"{% trans ' public information ' %}",'mod':'system'},
            {% endif %}
            {id:"itemdefine",name:"{% trans 'Reports' %}",'mod':'query'},
            {id:"iclock",name:"{% trans ' device ' %}",'mod':'device'},
            //{id:"fptemp",name:"{% trans 'Fingerprint Information Management' %}",'mod':'device'},
            {id:"biodata",name:"{% trans 'Feature Template Management' %}",'mod':'employees'},

            {id:"transactions",name:"{% trans 'Attendance Record' %}",'mod':'device'},
            //{id:"devlog",name:"{% trans ' log of data from device ' %}",'mod':'device'},
            {id:"oplog",name:"{% trans 'Device Operation Log' %}",'mod':'device'},
            {id:"errorlog",name:"{% trans 'Commands Error Log' %}",'mod':'device'},//命令错误日志

            //{id:"attdataproofcmd",name:"{% trans 'attdata proof cmd' %}",'mod':'device'},
            {id:"empofdevice",name:"{% trans 'View people on the device' %}",'mod':'device'},

            {id:"schclass",name:"{% trans ' shift time-table ' %}",'mod':'schedule'},
            {id:"num_run",name:"{% trans ' shift ' %}",'mod':'schedule'},
            {id:"user_of_run",name:"{% trans ' empoyee shift ' %}",'mod':'schedule'},
            {id:"user_speday",name:"{% trans ' special leave ' %}",'mod':'schedule'},
            {id:"user_overtime",name:"{% trans 'User OverTime' %}",'mod':'schedule'},
            {id:"outwork",name:"{% trans 'outwork' %}",'mod':'schedule'},
            {id:"applocation",name:"{% trans 'applocation' %}",'mod':'schedule'},
            {id:"checkexact",name:"{% trans "Replacement processing" %}",'mod':'schedule'},

            //{id:"accounts",name:"{% trans ' Posting ' %}",'mod':'att'},
            //{id:"itemdefine",name:"{% trans 'Reports' %}",'mod':'att'},
            {id:"holidays",name:"{% trans ' holidays ' %}",'mod':'schedule'},
            {id:"leaveclass",name:"{% trans ' Leave Class ' %}",'mod':'schedule'},
            {id:"userproperty",name:"{% trans 'Staff extended attributes' %}",'mod':'employees'},
            {id:"attparam",name:"{% trans ' attendance rule ' %}",'mod':'schedule'},
            {id:"days_off",name:"{% trans 'Rehabilitation' %}",'mod':'employees'},
            {id:"iclockdept",name:"{% trans 'data query' %}",'mod':'query'},
            {id:"annual_settings",name:"{% trans 'Annual Vacation Management' %}",'mod':'schedule'},
            {id:"attendancegroup",name:"{% trans 'Attendance group' %}",'mod':'schedule'},
            {id:"zone",name:"{% trans 'area' %}",'mod':'device'},
            {id:"epidemicinvestigation",name:"{% trans 'data query' %}",'mod':'epidemic'}

        ]
    {% endif %}
    {% if ''|isECOpos == '1' %}

        ic_allowed_perm=[
            {id:"iclock",name:"{% trans ' device ' %}",'mod':'device'},
            {id:"empofdevice",name:"{% trans 'View people on the device' %}",'mod':'device'},
            {id:"oplog",name:"{% trans 'Device Operation Log' %}",'mod':'device'},
            {id:"errorlog",name:"{% trans 'Commands Error Log' %}",'mod':'device'},//命令错误日志
            {id:"empofdevice",name:"{% trans 'View people on the device' %}",'mod':'device'},
            {id:"department",name:"{% trans ' department ' %}",'mod':'employees'},
            {id:"employee",name:"{% trans ' employee ' %}",'mod':'employees'},
            {id:"attcalclog",name:"{% trans 'System Options' %}",'mod':'system'},
            {id:"announcement",name:"{% trans 'Announcement-table' %}",'mod':'system'},
            {id:"userroles",name:"{% trans 'userRoles' %}",'mod':'employees'},

            {id:"ademployee",name:"{% trans 'Mobile Register Photo Review' %}",'mod':'employees'},



            {id:"group",name:"{% trans 'group' %}",'mod':'system'},
            {id:"myuser",name:"{% trans 'User' %}",'mod':'system'},
            {id:"authterminal",name:"{% trans 'AuthTerminal' %}",'mod':'system'},//终端授权管理
            {id:"adminlog",name:"{% trans 'Admin Action Log' %}",'mod':'system'},
            {id:"interfacelog",name:"{% trans 'API Interface Log' %}",'mod':'system'},
            {id:"devcmds",name:"{% trans ' log of command to device ' %}",'mod':'system'},
            {id:"employeelog",name:"{% trans 'Employee Login Log' %}",'mod':'system'},
            {id:"devlog",name:"{% trans 'Other logs' %}",'mod':'system'},
            {id:"adsetting",name:"{% trans 'Ad Settings' %}",'mod':'system'},



            {% if "msg"|enabled_module %}
                {id:"iclockmsg",name:"{% trans ' information subscription ' %}",'mod':'employees'},

            	{id:"messages",name:"{% trans ' public information ' %}",'mod':'system'},
            {% endif %}
            {id:"itemdefine",name:"{% trans 'Reports' %}",'mod':'query'},
            {id:"biodata",name:"{% trans 'Feature Template Management' %}",'mod':'employees'},

            {id:"splittime",name:"{% trans 'segmentation' %}",'mod':'information'},
        	{id:"batchtime",name:"{% trans 'Consumption time period' %}",'mod':'information'},
			{id:"meal",name:"{% trans 'meal information' %}",'mod':'information'},
			{id:"merchandise",name:"{% trans 'Product Information' %}",'mod':'information'},
            {id:"merchandiseclassify",name:"{% trans 'Merchandise classify' %}",'mod':'ipos'},
        	{id:"keyvalue",name:"{% trans 'key value data' %}",'mod':'information'},
            {id:"iclockdept",name:"{% trans 'data query' %}",'mod':'query'},



        ]
    {% endif %}
    {% if ''|isZKAdms == '1' %}

        ic_allowed_perm=[
            {id:"department",name:"{% trans ' department ' %}",'mod':'employees'},
            {id:"employee",name:"{% trans ' employee ' %}",'mod':'employees'},
            {id:"user_speday",name:"{% trans ' special leave ' %}",'mod':'employees'},
            {id:"biodata",name:"{% trans 'Feature Template Management' %}",'mod':'employees'},
            {id:"userroles",name:"{% trans 'userRoles' %}",'mod':'employees'},
            {id:"empzone",name:"{% trans 'Employee Area Setting' %}",'mod':'employees'},

            {id:"iclock",name:"{% trans ' device ' %}",'mod':'adms'},
            {id:"iclockdept",name:"{% trans 'data query' %}",'mod':'adms'},
            {id:"acc_maintenance",name:"{% trans 'acc_maintenance' %}",'mod':'adms'},
            {id:"transactions",name:"{% trans 'Attendance Record' %}",'mod':'adms'},
            {id:"records",name:"{% trans 'Access Control Record' %}",'mod':'adms'},
            {id:"timezones",name:"{% trans 'Access Control Period' %}",'mod':'adms'},
            {id:"level",name:"{% trans 'Acc Rights Groups' %}",'mod':'adms'},

            {id:"interlock",name:"{% trans 'interlocking' %}",'mod':'adms'},
            {id:"linkage",name:"{% trans 'Linkage' %}",'mod':'adms'},
            {id:"antipassback",name:"{% trans 'anti-submarine' %}",'mod':'adms'},
            {id:"firstopen",name:"{% trans 'The first person is always open' %}",'mod':'adms'},
            {id:"combopen",name:"{% trans 'Multiple Openers' %}",'mod':'adms'},
            {id:"combopen_door",name:"{% trans 'Multiple people open the door' %}",'mod':'adms'},

            {id:"oplog",name:"{% trans 'Device Operation Log' %}",'mod':'adms'},
            {id:"errorlog",name:"{% trans 'Commands Error Log' %}",'mod':'device'},//命令错误日志
            {id:"devcmds",name:"{% trans ' log of command to device ' %}",'mod':'adms'},
            {id:"empofdevice",name:"{% trans 'View people on the device' %}",'mod':'adms'},

            {id:"attcalclog",name:"{% trans 'System Options' %}",'mod':'system'},
            {id:"group",name:"{% trans 'group' %}",'mod':'system'},
            {id:"myuser",name:"{% trans 'User' %}",'mod':'system'},
            {id:"authterminal",name:"{% trans 'AuthTerminal' %}",'mod':'system'},//终端授权管理
            {id:"adminlog",name:"{% trans 'Admin Action Log' %}",'mod':'system'},
            {id:"interfacelog",name:"{% trans 'API Interface Log' %}",'mod':'system'},
            {id:"devlog",name:"{% trans 'Other logs' %}",'mod':'system'},

            {% if "msg"|enabled_module %}
                {id:"iclockmsg",name:"{% trans ' information subscription ' %}",'mod':'employees'},
            	{id:"messages",name:"{% trans ' public information ' %}",'mod':'system'},
            {% endif %}
            ]
    {% endif %}
    {% if ''|isZKTime == '0' %}
    {% if ''|isZKAdms == '0' %}
    {% if ''|isECOpos == '0' %}
        ic_allowed_perm=[
        {id:"department",name:"{% trans 'department' %}",'mod':'basic'},
        {id:"employee",name:"{% trans ' employee ' %}",'mod':'basic'},
		{id:"userroles",name:"{% trans 'Job' %}",'mod':'basic'},
		{id:"ademployee",name:"{% trans 'Mobile Register Photo Review' %}",'mod':'basic'},
        {id:"empzone",name:"{% trans 'Employee Area Setting' %}",'mod':'basic '},

        {id:"attcalclog",name:"{% trans 'System Options' %}",'mod':'system'},
		{id:"announcement",name:"{% trans 'Announcement management' %}",'mod':'system'},
        {id:"group",name:"{% trans 'Management Group' %}",'mod':'system'},
		{id:"myuser",name:"{% trans 'User' %}",'mod':'system'},
        {id:"authterminal",name:"{% trans 'AuthTerminal' %}",'mod':'system'},//终端授权管理
		{id:"adminlog",name:"{% trans 'Admin Action Log' %}",'mod':'system'},
        {id:"interfacelog",name:"{% trans 'API Interface Log' %}",'mod':'system'},
		{id:"devcmds",name:"{% trans ' log of command to device ' %}",'mod':'system'},
		{id:"employeelog",name:"{% trans 'Employee Login Log' %}",'mod':'system'},
		{id:"adsetting",name:"{% trans 'Ad Settings' %}",'mod':'system'},

        // 这个里面的id值是模型名称的小写
        {id:"firmwareconfig",name:"{% trans '固件参数配置' %}",'mod':'firmware'},
        {id:"upgradepackage",name:"{% trans '连续序列号升级包' %}",'mod':'firmware'},
        {id:"discontupgradepackage",name:"{% trans '不连续序列号升级包' %}",'mod':'firmware'},
        {id:"upgradepackagetable",name:"{% trans '升级包制作明细表' %}",'mod':'firmware'},


		{% if "msg"|enabled_module %}
		{id:"iclockmsg",name:"{% trans ' information subscription ' %}",'mod':'basic'},
		{id:"messages",name:"{% trans ' public information ' %}",'mod':'basic'},
		{% endif %}
	//	{id:"itemdefine",name:"{% trans 'Reports' %}",'mod':'basic'},

        {id:"iclock",name:"{% trans ' device ' %}",'mod':'device'},
		//{id:"fptemp",name:"{% trans 'Fingerprint Information Management' %}",'mod':'device'},
		{id:"biodata",name:"{% trans 'Feature Template Management' %}",'mod':'device'},
		{id:"transactions",name:"{% trans 'Attendance Record' %}",'mod':'device'},
		//{id:"devlog",name:"{% trans ' log of data from device ' %}",'mod':'device'},
		{id:"oplog",name:"{% trans 'Device Operation Log' %}",'mod':'device'},
        {id:"errorlog",name:"{% trans 'Commands Error Log' %}",'mod':'device'},//命令错误日志
        {id:"devlog",name:"{% trans 'Device Upload Data Log' %}",'mod':'device'},
		//{id:"attdataproofcmd",name:"{% trans 'attdata proof cmd' %}",'mod':'device'},
        {id:"empofdevice",name:"{% trans 'View people on the device' %}",'mod':'device'},

		{id:"schclass",name:"{% trans ' shift time-table ' %}",'mod':'att'},
		{id:"num_run",name:"{% trans 'shift' %}",'mod':'att'},
		{id:"user_of_run",name:"{% trans 'Shift management' %}",'mod':'att'},
		{id:"user_speday",name:"{% trans 'Leave application' %}",'mod':'basic'},
		{id:"user_overtime",name:"{% trans 'Overtime application' %}",'mod':'att'},
		{id:"outwork",name:"{% trans 'User Egress Apply' %}",'mod':'att'},
        {id:"applocation",name:"{% trans 'Sign-in location setting' %}",'mod':'att'},
		{id:"checkexact",name:"{% trans "Application for renewal" %}",'mod':'device'},
		//{id:"accounts",name:"{% trans ' Posting ' %}",'mod':'att'},
		//{id:"itemdefine",name:"{% trans 'Reports' %}",'mod':'att'},
		{id:"holidays",name:"{% trans 'Holiday settings' %}",'mod':'basic'},
		{id:"leaveclass",name:"{% trans 'Holiday class settings' %}",'mod':'basic'},
		{id:"userproperty",name:"{% trans 'Staff extended attributes' %}",'mod':'basic'},
		{id:"attparam",name:"{% trans ' attendance rule ' %}",'mod':'att'},
        {id:"days_off",name:"{% trans 'Transfer application' %}",'mod':'att'},
        {id:"iclockdept",name:"{% trans 'data query' %}",'mod':'basic'},

//		{id:"acgroup",name:"{% trans 'ACGroup-table' %}",'mod':'acc'},
		//{id:"acunlockcomb",name:"{% trans 'ACUnlockComb-table' %}",'mod':'acc'},
//		{id:"useracprivilege",name:"{% trans 'UserACPrivilege-table' %}",'mod':'acc'},
        {id:"zone",name:"{% trans 'area' %}",'mod':'system'},
        {id:"accdoor",name:"{% trans 'door management' %}",'mod':'acc'},
        {id:"timezones",name:"{% trans 'Access Control Period' %}",'mod':'acc'},
        {id:"level",name:"{% trans 'UserACPrivilege-table' %}",'mod':'acc'},
        {id:"accevent",name:"{% trans 'Event Type' %}",'mod':'acc'},
        //{id:"level_emp",name:"{% trans 'Authority group personnel' %}",'mod':'acc'},
        {id:"interlock",name:"{% trans 'interlocking' %}",'mod':'acc'},
        {id:"linkage",name:"{% trans 'Linkage' %}",'mod':'acc'},
        {id:"antipassback",name:"{% trans 'anti-submarine' %}",'mod':'acc'},
        {id:"firstopen",name:"{% trans 'The first person is always open' %}",'mod':'acc'},
        {id:"combopen",name:"{% trans 'Multiple Openers' %}",'mod':'acc'},
        {id:"combopen_door",name:"{% trans 'Multiple people open the door' %}",'mod':'acc'},

        {id:"records",name:"{% trans 'data query' %}",'mod':'acc'},
		{id:"mapmanage",name:"{% trans 'digital map' %}",'mod':'acc'},
//		{id:"iaccdevitemdefine",name:"{% trans 'Device and Record Report' %}",'mod':'acc'},
		{id:"IaccEmpItemDefine",name:"{% trans 'Personnel and Record Report' %}",'mod':'acc'},
		{id:"iaccessoplog",name:"{% trans 'Access Control Equipment Operation Log' %}",'mod':'acc'},

		//{id:"user_contract",name:"{% trans 'User Contract' %}",'mod':'basic'},
		{id:"annual_settings",name:"{% trans 'Annual Vacation Management' %}",'mod':'att'},
		{id:"meetlocation",name:"{% trans 'meeting room' %}",'mod':'meeting'},
		{id:"participants_tpl",name:"{% trans 'Participant Template' %}",'mod':'meeting'},
        {id:"participants_details",name:"{% trans 'Template Personnel Data' %}",'mod':'meeting'},
		{id:"meet_order",name:"{% trans 'Meeting appointment' %}",'mod':'meeting'},
        {id:"meet",name:"{% trans 'Meeting Schedule' %}",'mod':'meeting'},
		{id:"meetmessage",name:"{% trans 'Meeting announcement' %}",'mod':'meeting'},
        {id:"meet_devices",name:"{% trans 'Conference room equipment' %}",'mod':'meeting'},
		{id:"minute",name:"{% trans 'Meeting Minutes' %}",'mod':'meeting'},
        {id:"meet_details",name:"{% trans 'Participants' %}",'mod':'meeting'},

        {id:"reason",name:"{% trans 'The reason for visiting' %}",'mod':'visitors'},
        {id:"reservation",name:"{% trans 'Visitor Appointment' %}",'mod':'visitors'},
        {id:"visitionlogs",name:"{% trans 'Visitor Registration' %}",'mod':'visitors'},
        {id:"blacklist",name:"{% trans 'Visitor Blacklist' %}",'mod':'visitors'},
        {id:"interviewee",name:"{% trans 'Interviewee Management' %}",'mod':'visitors'},

        {id:"splittime",name:"{% trans 'segmentation' %}",'mod':'ipos'},
        {id:"batchtime",name:"{% trans 'Consumption time period setting' %}",'mod':'ipos'},
        {id:"dininghall",name:"{% trans 'Restaurant settings' %}",'mod':'ipos'},
        {id:"bookdinner",name:"{% trans 'Book meal' %}",'mod':'ipos'},
        {id:"meal",name:"{% trans 'meal information' %}",'mod':'ipos'},
        {id:"merchandise",name:"{% trans 'Product Information' %}",'mod':'ipos'},
        {id:"merchandiseclassify",name:"{% trans 'Merchandise classify' %}",'mod':'ipos'},
        {id:"keyvalue",name:"{% trans 'key value data' %}",'mod':'ipos'},
        {id:"iccard",name:"{% trans 'Card Information' %}",'mod':'ipos'},
        {id:"allowance",name:"{% trans 'subsidy' %}",'mod':'ipos'},
        {% if not "version"|get_params:request == '10.0' %}
            {id:"handconsume",name:"{% trans 'Manual supplement consumption' %}",'mod':'ipos'},
        {% endif %}
        {id:"issuecard",name:"{% trans 'Card Management' %}",'mod':'ipos'},
        //{id:"icconsumerlist",name:"{% trans 'Consumer details' %}",'mod':'ipos'},
        {id:"iclockdininghall",name:"{% trans 'data query' %}",'mod':'ipos'},
        {id:"cardcashsz",name:"{% trans 'Consumer details' %}",'mod':'ipos'},
        {id:"food",name:"{% trans 'Maintenance of dishes' %}",'mod':'ipos'},
        {id:"foodschedule",name:"{% trans 'Manage Food Schedule' %}",'mod':'ipos'},
        {id:"orderrecord",name:"{% trans 'Order management' %}",'mod':'ipos'},
        {id:"ordermealmessage",name:"{% trans 'order meal message' %}",'mod':'ipos'},
{#        {id:"foodclassify",name:"{% trans 'Cuisine management' %}",'mod':'ipos'},#}
{#        {id:"cookbook",name:"{% trans 'Dish management' %}",'mod':'ipos'},#}
{#        {id:"adminmanagement",name:"{% trans 'Administrative management' %}",'mod':'ipos'},#}
{#        {id:"healthydiet",name:"{% trans 'Healthy diet' %}",'mod':'ipos'},#}
{#        {id:"apppic",name:"{% trans 'Home page settings' %}",'mod':'ipos'},#}
{#        {id:"messagecenter",name:"{% trans 'Active ad' %}",'mod':'ipos'},#}
        {id:"epidemicinvestigation",name:"{% trans 'data query' %}",'mod':'epidemic'},

//        {id:"assetdevice",name:"{% trans 'device' %}",'mod':'asset'},
        {id:"assetclass",name:"{% trans 'Asset Category' %}",'mod':'asset'},
        {id:"asset",name:"{% trans 'Asset' %}",'mod':'asset'},
        {id:"recipient",name:"{% trans 'Asset Recipient' %}",'mod':'asset'},
        {id:"borrowsheet",name:"{% trans 'Asset Borrow' %}",'mod':'asset'},
        {id:"bringout",name:"{% trans 'Asset Bring Out' %}",'mod':'asset'},
        {id:"retreat",name:"{% trans 'Asset Retreat' %}",'mod':'asset'},
        {id:"allocation",name:"{% trans 'Asset Allocation' %}",'mod':'asset'},
        {id:"repairsheet",name:"{% trans 'Asset Repair' %}",'mod':'asset'},
        {id:"retiresheet",name:"{% trans 'Asset Retire' %}",'mod':'asset'},
        {id:"inventorysheet",name:"{% trans 'Asset Inventory' %}",'mod':'asset'},
        {id:"assetchangelog",name:"{% trans 'data query' %}",'mod':'asset'},
		{id:"attendancegroup",name:"{% trans 'Attendance groups' %}",'mod':'att'},
        {id:"lockercabinet",name:"{% trans 'Locker setting' %}",'mod':'asset'},
        {id:"lockeropeninglog",name:"{% trans 'Locker opening details' %}",'mod':'asset'},
      {% if ''|is_asset_device == '1' %}
        {id:"assetdevice",name:"{% trans 'Device Management' %}",'mod':'asset'},   //资产-设备管理
      {% endif %}
      {id:"item",name:"{% trans 'Salary item' %}",'mod':'payroll'},
      {id:"salarytemplate",name:"{% trans 'Salary template' %}",'mod':'payroll'},
      {id:"employeeitem",name:"{% trans 'Employee salary setting' %}",'mod':'payroll'},
      {id:"salaryset",name:"{% trans 'Salary set' %}",'mod':'payroll'},
      {id:"salarysetdata",name:"{% trans 'Salary set data' %}",'mod':'payroll'},
      {id:"salarysetresult",name:"{% trans 'Salary slip' %}",'mod':'payroll'},
      {id:"operatelog",name:"{% trans 'Salary operation log' %}",'mod':'payroll'},
      {id:"salarysetemployee",name:"{% trans 'data query' %}",'mod':'payroll'},
		]
        {% endif %}
	{% endif %}
	{% endif %}
	getInitData(obj);
        var setting = {
            check: {enable: true,chkboxType:{ "Y": "s", "N": "s" },chkStyle: "checkbox"},

            data: {simpleData: {enable: true,idKey: "id", pIdKey: "pid",  rootPId: 0}
                }
        };
	{% if ''|isZKTime == '0' %}
	{% if ''|isZKAdms == '0' %}
	{% if ''|isECOpos == '0' %}

	    treedata=getPermissionTreeEx('basic',"{% trans 'basic permissions' %}");
		$.fn.zTree.init($("#tabs-basic #basic-permissions",obj), setting, treedata);

	    treedata=getPermissionTreeEx('device',"{% trans 'Device Operation Permissions' %}");
		$.fn.zTree.init($("#tabs-device #device-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('att',"{% trans 'Attendance Permissions' %}");
		$.fn.zTree.init($("#tabs-att #att-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('acc',"{% trans 'Access Control Authority' %}");
		$.fn.zTree.init($("#tabs-acc #acc-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('meeting',"{% trans 'Conference Permissions' %}");
		$.fn.zTree.init($("#tabs-meeting #meeting-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('visitors',"{% trans 'Guest Permissions' %}");
		$.fn.zTree.init($("#tabs-visitors #visitors-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('ipos',"{% trans 'Consumption Authority' %}");
		$.fn.zTree.init($("#tabs-ipos #ipos-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('epidemic',"{% trans 'Antiepidemic Permissions' %}");
		$.fn.zTree.init($("#tabs-epidemic #epidemic-permissions",obj), setting, treedata);

    treedata=getPermissionTreeEx('asset',"{% trans 'Asset Permissions' %}");
		$.fn.zTree.init($("#tabs-asset #asset-permissions",obj), setting, treedata);

    treedata=getPermissionTreeEx('payroll',"{% trans 'Payroll Permissions' %}");
		$.fn.zTree.init($("#tabs-payroll #payroll-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('system',"{% trans 'system' %}");
		$.fn.zTree.init($("#system-permissions",obj), setting, treedata);

        treedata=getPermissionTreeEx('firmware',"{% trans '设备特性模块权限' %}");
		$.fn.zTree.init($("#firmware-permissions",obj), setting, treedata);
	{% endif %}
	{% endif %}
	{% endif %}

	{% if ''|isZKTime == '1' %}

		treedata=getPermissionTreeEx('employees',"{% trans 'Personnel permissions' %}");
		$.fn.zTree.init($("#tabs-employees #employees-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('device',"{% trans 'Device Operation Permissions' %}");
		$.fn.zTree.init($("#tabs-device #device-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('schedule',"{% trans 'Schedule' %}");
		$.fn.zTree.init($("#tabs-schedule #schedule-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('query',"{% trans 'Report' %}");
		$.fn.zTree.init($("#tabs-query #query-permissions",obj), setting, treedata);

        treedata=getPermissionTreeEx('epidemic',"{% trans 'Antiepidemic Permissions' %}");
		$.fn.zTree.init($("#tabs-epidemic #epidemic-permissions",obj), setting, treedata);

        treedata=getPermissionTreeEx('system',"{% trans 'system' %}");
		$.fn.zTree.init($("#system-permissions",obj), setting, treedata);
	{% endif %}
	{% if ''|isECOpos == '1' %}

		treedata=getPermissionTreeEx('employees',"{% trans 'Personnel permissions' %}");
		$.fn.zTree.init($("#tabs-employees #employees-permissions",obj), setting, treedata);
		treedata=getPermissionTreeEx('information',"{% trans 'information' %}");
		$.fn.zTree.init($("#tabs-information #information-permissions",obj), setting, treedata);
		treedata=getPermissionTreeEx('device',"{% trans 'Device Operation Permissions' %}");
		$.fn.zTree.init($("#tabs-device #device-permissions",obj), setting, treedata);
		treedata=getPermissionTreeEx('query',"{% trans 'Report' %}");
		$.fn.zTree.init($("#tabs-query #query-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('system',"{% trans 'system' %}");
		$.fn.zTree.init($("#system-permissions",obj), setting, treedata);
	{% endif %}
    {% if ''|isZKAdms == '1' %}

		treedata=getPermissionTreeEx('employees',"{% trans 'Personnel permissions' %}");
		$.fn.zTree.init($("#tabs-employees #employees-permissions",obj), setting, treedata);

		treedata=getPermissionTreeEx('adms',"{% trans 'data' %}");
		$.fn.zTree.init($("#tabs-adms #adms-permissions",obj), setting, treedata);

        treedata=getPermissionTreeEx('system',"{% trans 'system' %}");
		$.fn.zTree.init($("#system-permissions",obj), setting, treedata);
	{% endif %}

	f=$(obj).find("#id_edit_form").get(0)
	$(f).validate({
		rules: {
				"name": {"required":true}
			}
		});
}



function getOptions_html_Group(obj)
{
	var html="";
        var selected_ids=[]
        var perm_names=["basic-permissions","employees-permissions","device-permissions","att-permissions","acc-permissions","meeting-permissions","visitors-permissions","ipos-permissions","ihr-permissions","finance-permissions","system-permissions","firmware-permissions","auth-permissions","schedule-permissions","query-permissions","information-permissions","epidemic-permissions","adms-permissions","asset-permissions","payroll-permissions"]
        for(var i in perm_names)
        {
            selected_ids=$.merge(selected_ids,getSelected_dept(perm_names[i]));

        }

	for(var i in selected_ids)
	{
		if (selected_ids[i]>-1)
			html+="<option value='"+selected_ids[i]+"' selected>"+selected_ids[i]+"</option>"
	}
 	return html;
}

function getInitData(obj)
{

        //不需要显示配置的权限
        //var filterNames='change_transaction,add_devcmds,change_devcmds,add_devlog,change_devlog,'+
        //'add_oplog,change_oplog,change_fptemp,change_facetemp,'+
        //'add_attshifts,change_attshifts,delete_attshifts,add_itemdefine,change_itemdefine,delete_itemdefine'



	var gp_Obj=$("#id_permissions",obj).get(0).options;
	for(var i=0;i<gp_Obj.length;i++)
		if(gp_Obj[i].selected)
			ic_selected_ids.push(gp_Obj[i].value);
        var ic_allowed_perm_str=""
	for(var i=0;i<(ic_allowed_perm.length);i++)
           ic_allowed_perm_str+=ic_allowed_perm[i].id +"|"
	for(var i=0;i<gp_Obj.length;i++)
	{
		var texts=(gp_Obj[i].innerHTML).split("|");
		if($.trim(texts[0])=="iclock" || $.trim(texts[0])=="auth"|| $.trim(texts[0])=="accounts"|| $.trim(texts[0])=="acc"|| $.trim(texts[0])=="meeting"|| $.trim(texts[0])=="ipos"|| $.trim(texts[0])=="visitors" || $.trim(texts[0])=="asset" || $.trim(texts[0])=="payroll" || $.trim(texts[0])=="base")
		{
        		if(ic_allowed_perm_str.indexOf($.trim(texts[1])+"|")>=0){
			//alert($.trim(texts[2]))
                                        //if (filterNames.indexOf($.trim(texts[2]))==-1)
                                        //{
						ic_permission.push(texts);
						ic_ids.push(gp_Obj[i].value);
					//}
				}
		}
	}
}
function getPermissionTreeEx(perms,text){
        var tree=[{
                        id:-10000,
                        name:text,
                        pid:0,
                        open:true
        }];
        for(var i in ic_allowed_perm){
            if (ic_allowed_perm[i].mod.indexOf(perms)!=-1) {
	            var sub_tree=getTreeStringEx(ic_allowed_perm,i)
                if (sub_tree.length>1)
                    tree=$.merge(tree,sub_tree);
	    }
	}
        return tree;
}
//权限翻译
var general_perm={
				"add_":	"{% trans 'Add' %}",
				"browse_":	"{% trans 'Browse' %}",
				"change_":	"{% trans 'Modify' %}",
				"delete_":	"{% trans 'Delete' %}",
                "export_": "{% trans 'export data' %}"
};

// 自定义权限设置的地方
var third_perm={
    "upgradepackage": {
        "upgrade_pkg_audit":"{% trans '审核并打包' %}"
    },
    "discontupgradepackage": {
        "disupgrade_pkg_audit":"{% trans '审核并打包' %}"
    },
    "applocation": {
        "setzone":"{% trans 'Setting the home area' %}"
    },
    "attendancegroup":{
        'set_attendance_group_employee': "{% trans 'Set attendance group employee' %}",
        'browse_attendance_group_employee': "{% trans 'Browse attendance group employee' %}",
	},
    "annual_settings":{
        'annual_leave_standard_export': "{% trans 'Annual leave standard export' %}",
        'annual_leave_details_export': "{% trans 'Annual leave details export' %}"
    },
    "ademployee":{
        "adrotatepic_ademployee":"{% trans 'Pictures Rotating' %}"
    },
	"employee": {

		"restoreEmpLeave_employee":"{% trans 'Restore Emp leave' %}",
		'empLeave_employee': "{% trans 'Employee leave' %}",

		"setBlack":"{% trans 'Add to blacklist' %}",
        'restoreEmpBlack': "{% trans 'Remove from blacklist' %}",
        {% if ''|isECOpos == '0' %}
		'zoneSetting': "{% trans 'Setting the home area' %}",
        {% endif%}
		"toDepart_employee": "{% trans "Change employee department" %}",
		//"enroll_employee": "{% trans "Enroll employee&#39;s fingerprint" %}",
                "edit_privilege": "{% trans "Edit device permissions" %}",
                "edit_MVerifyPass": "{% trans "Edit Device Password" %}",
                "edit_Card": "{% trans "Editor card number" %}",
		"Upload_pictures":"{% trans 'Upload pictures' %}",
    {% if ''|is_wechat_offcial_account == '1' %}
         'unbind_employee':		"{% trans 'Offical account personnel untying' %}"//公众号人员解绑
    {% endif %}
//		"import employee": "{% trans "Import employee" %}",
//		"restore Emp leave":"{% trans 'Restore Emp leave' %}"
	},

	"iclock": {
        {% if ''|isECOpos == '0' %}
		'cleardata_iclock':			"{% trans 'Clear data in device' %}",
		'clearlog_iclock':	"{% trans 'Clear transactions in device' %}",
//		'Copy data between device':		"{% trans 'Copy data between device' %}",//备份登记数据到其他设备
		'Set power suspend':			"{% trans 'Set power suspend' %}",
		'info_iclock':	"{% trans 'Refresh device information' %}",
		'download_attphoto':	"{% trans 'Download attendance photos' %}",
		'loaddata_iclock':				"{% trans 'Upload new data' %}",
		'pause_iclock':					"{% trans 'Pause device' %}",
		'reboot_iclock':				"{% trans 'Reboot device' %}",
		'reloaddata_iclock':			"{% trans 'Upload data again' %}",
		'reloadlogdata_iclock':	"{% trans 'Upload transactions again' %}",
                'browselogPic':	"{% trans 'View Attendance Record Photo' %}",
        'deptEmpDelFromDev_iclock':"{% trans 'Remove people or feature templates from device' %}",

//		'Reset Password in device':		"{% trans 'Reset Password in device' %}",//重新设置人员在设备上的考勤密码
//		'Restore employee to device':	"{% trans 'Restore employee to device' %}",//恢复设备上的人员数据
		'resume_iclock':		"{% trans 'Resume a resumed device' %}",
//		'Upgrade firmware':				"{% trans 'Upgrade firmware' %}",//升级固件
		'deptEmptoDev_iclock':				"{% trans 'Transfer employee of department to the device' %}",
//		'Upload_Iclock_Photo':			"{% trans 'Upload device image' %}",
		'deptEmptoDelete_iclock':		"{% trans 'Delete employee from the device order by department' %}",//按部门人员从设备中删除人员
//		'delFingerFromDev_iclock':		"{% trans 'Only Delete fingers of employee from the device order by department' %}",
//		'delFaceFromDev_iclock':		"{% trans 'Only Delete face of employee from the device order by department' %}",
//-		'clearpic_iclock':		"{% trans 'Clearing live photos on the device' %}",

//		'Delete fingers of employee the device':		"{% trans 'Only Delete fingers of employee from the device order by department' %}",//按部门人员从设备中删除人员指纹
//		'Delete face of employee the device':		"{% trans 'Only Delete face of employee from the device order by department' %}",//按部门人员从设备中删除人员面部
//		'attdataProof_iclock':		"{% trans 'Attendance data proofreading' %}",//考勤数据校对
//		'devoption_iclock':		"{% trans 'Set options of device' %}",
//-		"toDevPic_iclock":"{% trans 'Transfer employee PIC to the device' %}",
		'toDevWithin_iclock': "{% trans 'Transfer to the device templately' %}",
//		"delDevPic_iclock":"{% trans 'Delete employee PIC from the device' %}",
		'AutoToDev_employee':"{% trans 'Auto transfer employee to the device' %}",
		    'to_dev_employee':"{% trans 'Transfer employee to the device' %}", // 传送人员到设备
		    'to_dev_belong_att_employee':"{% trans 'Transfer personnel to belong attendance equipment' %}", // 传送人员到归属考勤设备
		    'to_dev_belong_acc_employee':"{% trans 'Transfer personnel to home access control equipment' %}", // 传送人员到归属门禁设备
		    'to_dev_employee_pic':"{% trans 'Transfer employee PIC to the device' %}", // 下发人员照片到设备
		    'to_dev_employee_temp':"{% trans 'Transfer to the device templately' %}", // 临时调拨人员到设备
		    'del_emp_from_dev':"{% trans 'Deleted from the device employee' %}", // 从设备中删除人员
		    'del_emp_from_acc_dev':"{% trans 'Remove from home access control device' %}", // 从归属门禁设备删除
        'set_device_asp':"{% trans 'Set ownership' %}",
        'sync_locker_data':"{% trans 'Sync locker data' %}",


        {% if ''|isZKTime == '0' %}

            'unlock_iclock':			"{% trans 'Output unlock signal' %}",
            'unalarm_iclock':		"{% trans 'Terminate alarm signal' %}",
            'synctime_iclok':"{% trans 'Synchronize device time' %}",
            {% if "acc"|enabled_module %}
                'Upload_AC_Options':			"{% trans 'Upload AC Options' %}",
                'Upload_User_AC_Options':		"{% trans 'Upload User AC Options' %}",
            {%endif%}
		    {% if "ipos"|enabled_module %}
                'Upload_pos_all_data':			"{% trans 'Synchronize all data to device (consumption)' %}",
                'Upload_pos_Merchandise':		"{% trans 'Upload product information to device' %}",
                'Upload_pos_Meal':		"{% trans 'Uploading Meal Information to Device' %}",
                'consumer_record_detection':		"{% trans 'Consumer Record Detection' %}",
            {%endif%}
        {% endif %}
        {% endif %}
        {% if ''|isECOpos == '1' %}
            'Upload_pos_all_data':			"{% trans 'Synchronize all data to device (consumption)' %}",
            'Upload_pos_Merchandise':		"{% trans 'Upload product information to device' %}",
            'Upload_pos_Meal':		"{% trans 'Uploading Meal Information to Device' %}",
        {% endif %}
//		'mvToDev_iclock': "{% trans 'Move employee to a new device' %}"
		},
	"transactions":
	{
//		'Audited Transaction':					"{% trans 'Audit Transaction' %}",审核
//		'Clear_Obsolete_Data':					"{% trans 'Clear Obsolete Data' %}",
//		'Forget_transaction':	"{% trans 'Forgetting to Clock in/out' %}"
//		'Init database':			"{% trans 'Init database' %}"
		'monitor_oplog':			"{% trans 'Transaction Monitor' %}",
		'meeting_transaction':			"{% trans 'Participation Record' %}",
		'upload_utransaction':		"{% trans 'Upload u disk transaction file' %}",
        'monitor_rollbook': "{% trans 'Real Time roll call' %}",
	},
	"user_of_run":
	{
		'Employee_shift_details':			"{% trans 'Employee shift details' %}"
	},
	"user_speday":
	{
		'leaveAudit_user_speday':			"{% trans 'Audit Sepcial Leave' %}",
    {% if ''|isZKAdms == '0' %}
                'setprocess':			"{% trans 'Multi-level approval process settings' %}"
	{% endif %}
    },

        "checkexact":
	{
                'TransAudit_checkexact':			"{% trans 'audit' %}"
        },

        "outwork":
	{
                'TransAudit_outWork':			"{% trans 'audit' %}"
        },

	"user_overtime":
	{
		'overtimeAudit_user_overtime':			"{% trans 'Audit OverTime' %}"
	},
    "reservation":{
        'Audit_reservation': "{% trans 'audit' %}"
    },
    "assetchangelog":
    {
        'Audit_report': "{% trans 'report' %}"
    },
    "asset":
    {
        'Audit_recipient': "{% trans 'Recipient' %}",
        'Audit_borrow': "{% trans 'Borrow' %}",
        'Audit_bringout': "{% trans 'Bring out' %}",
        'Audit_retreat': "{% trans 'Draws back' %}",
        'Audit_allocate': "{% trans 'Allocation' %}",
        'Audit_repair': "{% trans 'Repair' %}",
        'Audit_retire': "{% trans 'Scrap' %}",
        'Audit_return': "{% trans 'Revert' %}",
        'Audit_store': "{% trans 'Replenish stock' %}"
    },
    "recipient":
    {
        'Audit_recipient_asset': "{% trans 'audit' %}"
    },
	"borrowsheet":
    {
        'Audit_borrow_asset': "{% trans 'audit' %}",
        'Audit_return_asset': "{% trans 'Revert' %}"
    },
    "bringout":
    {
        'Audit_bringout_asset': "{% trans 'audit' %}"
    },
    "retreat":
    {
        'Audit_retreat_asset': "{% trans 'audit' %}"
    },
    "allocation":
    {
        'Audit_allocate_asset': "{% trans 'audit' %}"
    },
    "repairsheet":
    {
        'Audit_repair_asset': "{% trans 'audit' %}"
    },
    "retiresheet":
    {
        'Audit_retire_asset': "{% trans 'audit' %}"
    },
    "inventorysheet":
    {
        'Audit_InventorySheet': "{% trans 'audit' %}"
    },
    "lockercabinet":
    {
        'addemp_storage': "{% trans 'Add emps' %}",
        'delemp_storage': "{% trans 'Clear people' %}"
    },
	//"itemdefine": {
	//	},
	"num_run":
			{
				'addShiftTimeTable_num_run':			"{% trans 'Add time-table' %}",
				'deleteAllShiftTimeTbl_num_run':		"{% trans 'Delete time-table' %}"
			},
	"user_temp_sch":
			{
				'user_temp_sch_add':		"{% trans 'increase' %}",
				'user_temp_sch_modify':		"{% trans 'Modify' %}",
				'user_temp_sch_delete':		"{% trans 'delete' %}"
				/*
				'Data_Management':			"{% trans 'Data Management' %}",
				'Init database':			"{% trans 'Init database' %}",
				'modify_basic_set':			"{% trans 'basic settings' %}",
				'modify_Email_set':			"{% trans 'Modify Email Settings' %}",
				'modify_recordstatus_set':		"{% trans 'Modify Record Status' %}",
				'Clear_Obsolete_Data':		"{% trans 'Edit Clear Expired Data' %}",
				'Backup_Database':			"{% trans 'Backup Database' %}",
				'import_department_data':	"{% trans 'import department data' %}",
				'import_employee_data':	"{% trans 'import employee data' %}",
				'Import_Finger_data':		"{% trans 'Import Finger data' %}",
				'U_Disk_Data_Manager':		"{% trans 'U_Disk Data Manager' %}",
				'Database_Options':			"{% trans 'Database Options' %}",
				'System_Options':			"{% trans 'System Options' %}",
				'preferences_user':			"{% trans 'Preferences' %}",
				'setprocess':			"{% trans 'Multi-level approval' %}"
				*/
			},

 	"iclockdept":{
//		'IclockDept_transactions': "{% trans 'Attendance Record' %}",
//		'IclockDept_monitor': "{% trans 'Real-time recording monitoring' %}",
		'IclockDept_calcreports': "{% trans 'statistical report' %}",
		'IclockDept_reports': "{% trans 'Attendance Report' %}",
		'attendance_report_export': "{% trans 'Attendance Report export' %}",
	},
    "epidemicinvestigation":{
		'monitor_oplog_epidemic': "{% trans 'Real Time Monitoring' %}",
		'epidemic': "{% trans 'Outbreak investigation records' %}",
		'emp_temperature': "{% trans 'Personal temperature schedule' %}",
		'department_temperature_report': "{% trans 'Departmental temperature summary' %}",
		'detail_temperature': "{% trans 'Details personal temperature schedule' %}",
	},

	"level":
			{
				'addemps_level':			"{% trans 'Add emps' %}",
				'delallemps_level':		"{% trans 'Delete allemps' %}"
			},
	"firstopen":
			{
				'addemps_firstopen':			"{% trans 'Add emps' %}",
				'delallemps_firstopen':		"{% trans 'Delete allemps' %}"
			},
	"combopen":
			{
				'addemps_combopen':			"{% trans 'Add emps' %}",
				'delallemps_combopen':		"{% trans 'Delete allemps' %}"
			},

        "UserACPrivilege-table":{
                    'setdevice': "{% trans 'Distribution Device' %}",
                    'Employee_to_device': "{% trans 'Employee to device' %}"
        },
	"mapmanage":{
		'MapManage_SetMap': "{% trans 'Setting the map' %}",
		'MapManage_SaveStyle': "{% trans 'Save Style' %}",
		'MapManage_RemoveMap': "{% trans 'Clear Map' %}",
		'Map_Monitor': "{% trans 'Electronic map monitoring' %}"
	},
	"records":{
		'acc_records': "{% trans 'Access Control Record' %}",

        {% if ''|isZKAdms == '0' %}
	        'monitor_oplog': "{% trans 'real time monitoring' %}",
        {% endif %}
        'incoming_and_control': "{% trans 'Incoming And Control' %}",
        'acc_reports': "{% trans 'Access Control Report' %}",
        'access_control_record_export': "{%  trans 'Access Control Record export' %}",
        'access_reports_export': "{%  trans 'Access reports export' %}"
	},

	"iaccdevitemdefine":{
		'iaccMonitor_iaccdevitemdefine': "{% trans 'Monitoring Record Table' %}",
		'iaccAlarm_iaccdevitemdefine': "{% trans 'Alarm Record Table' %}",
		'iaccUserRights_iaccdevitemdefine': "{% trans 'User Permissions Table' %}"
	},
	"iaccempitemdefine":{
		'iaccRecordDetails_iaccempitemdefine': "{% trans 'record detail' %}",
		'iaccSummaryRecord_iaccempitemdefine': "{% trans 'record summary' %}",
		'iaccEmpUserRights_iaccempitemdefine': "{% trans 'User Permission Details' %}",
		'iaccEmpDevice_iaccempitemdefine': "{% trans 'user equipment' %}"
	},
        "visitionlogs":{
                'search_visitionlogs':"{% trans 'Visitor Record Query' %}",
                'visitor_record_export':"{% trans 'Visitor Record export' %}",
                'visitor_pattern':"{% trans 'Visitor Pattern'%}"
        },
	"issuecard":{
		'issuecard_issuecard': "{% trans 'issuing card' %}",
		'issuecard_oplosecard': "{% trans 'loss' %}",
		'issuecard_oprevertcard': "{% trans 'Solutions Hanging' %}",
		'issuecard_cancelmanagecard': "{% trans 'Logout Management Card' %}",
		'issuecard_nocardretirecard': "{% trans 'No Card Retirement' %}",
		'issuecard_supplement': "{% trans 'Recharge' %}",
		'issuecard_reimburse': "{% trans 'Refund' %}",
		'issuecard_retreatcard': "{% trans 'return card' %}",
		'issuecard_updatecard': "{% trans 'Card data modification' %}",
		'issuecard_initcard': "{% trans 'card initialization' %}",
    'issuecard_batch_supplement': "{% trans 'bulk recharge' %}",
    'overdue_card_renewal': "{% trans 'Overdue card renewal' %}"
	},
    "allowance":{
        'allowanceAudit_allowance':"{% trans 'audit' %}"
    },
    "foodschedule":{
        'temporary_food_schedule':"{% trans 'temporary food schedule' %}"
    },
    "orderrecord":{
        'Audit_OrderRecord_Refund':"{% trans 'Refund' %}",
        'approve_taken_meal':"{% trans 'Take meals' %}"
    },

	"iclockdininghall":{
		'iclockdininghall_cardcashsz': "{% trans 'Card cash receipts and payments' %}",
		'iclockdininghall_reports': "{% trans 'report' %}",
    'consumption_order_reports': "{% trans 'Order report' %}",
		'cardcashsz_export': "{% trans 'Card cash receipts and payments export' %}",
		'consumption_report_export': "{% trans 'consumption report export' %}",
{#        'iclockdininghall_orderlist':"{% trans 'Order management' %}",#}
{#        'iclockdininghall_foodcommend':"{% trans 'Evaluation of dishes' %}"#}
	},

  "cardcashsz":{
		'iclockdininghall_icconsumerlist': "{% trans 'Browse' %}",
		'icconsumerlist_amount_correction': "{% trans 'correction' %}",
		'consumer_details_export': "{% trans 'Consumer details export' %}"
	},

        "meet_order":{
		'orderAudit_meet_order': "{% trans 'Meeting appointment review' %}"
	},
	"meet":{
		'meeting_reports': "{% trans 'Conference Report' %}",
		'meeting_monitor': "{% trans 'Meeting Real Time' %}",
        'meet_participants_sync': "{% trans 'Synchronize participants to the device' %}",
        'participation_record_export': "{% trans 'Participation Record export' %}",
        'conference_report_export': "{% trans 'Conference Report export' %}"
	},
    "meetmessage":
           {
        'send_Meet_Email': "{% trans 'Send the notice of participation' %}"
    },
    "minute":
           {
        'send_Minute_Email': "{% trans 'send meeting minutes' %}"
    },
    "group":{
        'group_export': "{% trans 'group export' %}"
    },
    "announcement":
           {
        'send_Announcement_Email': "{% trans 'Send Announcement Notice' %}"
    },
    "authterminal":
           {
        'terminalStop_authterminal': "{% trans 'deactivate' %}"
    },

	"attcalclog":{
        'sys_personal_setting': "{% trans 'Personalization' %}",
		'sys_basic_setting': "{% trans 'Device Parameter Settings' %}",
		'sys_email_setting': "{% trans 'Email Settings' %}",
    {% if ''|isZKAdms == '0' %}
		'sys_state_setting': "{% trans 'record display status' %}",
    {#    'sys_sms_setting': "{% trans 'SMS Parameter Settings' %}", #}
        'sys_visitors_setting': "{% trans 'Visitor parameter settings' %}",
        {% if "show_app_settings"|get_params:request == '1' %}
            'sys_iapp_setting': "{% trans 'APP address parameter configuration' %}",
        {% endif %}
    {% endif %}
        'sys_api_setting': "{% trans 'Data docking settings' %}",
		'sys_calc_setting': "{% trans 'Statistics Task Settings' %}",
		'sys_del_setting': "{% trans 'Clear Data Task' %}",
        {% if 'ipos'|enabled_module %}
		'sys_pos_setting': "{% trans 'Consumption Parameter Settings' %}",
        {% endif %}
	//	'sys_sap_setting': "{% trans 'SAP docking settings' %}",
        {% if 'acc'|enabled_module %}
		'sys_acc_setting': "{% trans 'Access Control Settings' %}",
        {% endif %}
		'sys_autoexport_setting': "{% trans 'AutoExport' %}",

        {% if "antiepidemic"|get_params:request == '1' %}
            'sys_antiepidemic_setting': "{% trans 'Antiepidemic Setting' %}",
        {% endif %}
        {% if not "version"|get_params:request == '10.0' %}
            'sys_backup_setting': "{% trans 'Backup Task Settings' %}",
        {% endif %}
	//	'sys_app_setting': "{% trans 'APP Settings' %}"
        {% if ''|isZKTime == '1' %}
            'sys_common_setting': "{% trans 'Common Functions Settings' %}",
        {% endif %}
        'group_export': "{% trans 'group export' %}"
  },

  "salaryset":{
    "set_employee_to_salaryset":"{% trans 'add people' %}",
    "view_employee_salaryset":"{% trans 'view people' %}",
    "calculate_employee_salaryset":"{% trans 'Calculate salary' %}",
    "audit_employee_salaryset":"{% trans 'Salary audit' %}"
  },
  "salarysetdata":{
    "get_attendance_data_salarysetdata":"{% trans 'Get attendance data' %}",
    "import_attendance_data_salarysetdata":"{% trans 'Import attendance data' %}",
    "edit_salary_data_salarysetdata":"{% trans 'Salary import' %}",
  },
  "item":{
    "add_item":"{% trans 'Add salary item' %}",
  },
  "salarytemplate":{
    "set_employee_to_salarytemplate":"{% trans 'add people' %}",
    "view_employee_salarytemplate":"{% trans 'view people' %}",
  },
  "employeeitem":{
    "add_employeeitem":"{% trans 'Fixed salary' %}",
    "change_employeeitem":"{% trans 'Adjusted salary' %}",
    "set_salary_template_salary_employeeitem":"{% trans 'Set salary template' %}",
    "browser_employeeitemlog":"{% trans 'Salary adjustment log' %}",
  },
  "salarysetresult":{
    "send_payslip_salarysetresult":"{% trans 'Send payslip' %}",
  },
  "salarysetemployee":{
      "payroll_report":"{% trans 'Reports' %}",
  }
}


function getTransText(table, permName)
{

	for(n in general_perm)
	{
		if(permName.indexOf(n)!=-1 && permName != 'add_employeeitem' && permName != 'change_employeeitem' && permName != 'add_biodata' && permName != 'add_outwork' && permName != 'change_outwork' && permName != 'add_ademployee')
                {
                    return general_perm[n];
                }
	}
	if(table in third_perm)
	{
		var permss=third_perm[table];
		for(n in permss) if(permName==n) return permss[n];
	}
	return ""//permName;
}

function hidePermission(permission){
    //前端隐藏非必要的权限
    {% if ''|is_new_log_mode %}
      var hide_permissions=['add_authterminal','change_authterminal','export_biodata', 'delete_adminlog', 'delete_employeelog', 'delete_interfacelog']
    {% else %}
      var hide_permissions=['add_authterminal','change_authterminal','export_biodata']
    {% endif %};

    if(hide_permissions.indexOf(permission)!=-1){
        return true
    }
    return false
}


function getTreeStringEx(perms,index)
{
        var tree_sub=[{
                        id:(index+1)*(-1),
                        name:perms[index].name,
                        pid:-10000,
                        open:true
        }];

        for(var i in ic_permission)
        {
		id=ic_ids[i]*1;
		if (!isExist_permissions(id))continue
                if($.trim(ic_permission[i][1])==perms[index].id)
                {
                        if(hidePermission(ic_permission[i][2])) continue
                        var ttext=getTransText($.trim(ic_permission[i][1]), $.trim(ic_permission[i][2]));
                        var tree_sub_sub={
                                        id:id,
                                        name:ttext,
                                        checked:isSelected(ic_ids[i])?true:false,
                                        pid:(index+1)*(-1)

                                    };
                        if (ttext!="")
                        tree_sub.push(tree_sub_sub);
                }
        }

        return tree_sub;
}

function isSelected(selected_id)
{	var flag=0;
	for(var i=0;i<ic_selected_ids.length;i++){
		if(ic_selected_ids[i]==selected_id)
			{
				flag=1;
				break;
			}
		else
			flag=0;
	}
	return flag;
}

function isExist_permissions(id)
{
	for(var i=0;i<ic_ownerpermissions.length;i++){
		if (id==ic_ownerpermissions[i]) return true
	}
	if(ic_ownerpermissions.length>0)
		return false
	else
		return true

}

function beforePost_group(obj,actionName){
	$("#id_permissions",obj).html(getOptions_html_Group(obj));
}

//模糊查询
function searchShowGroup(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
	var url="/iclock/data/group/?name__icontains="+encodeURI(v)
	savecookie("search_urlstr",url);
	$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}


{% endblock %}

{% block can_export %}
var can_export={% if user|HasPerm:"iclock.group_export" %}true{% else %}false{% endif %}
{% endblock %}
