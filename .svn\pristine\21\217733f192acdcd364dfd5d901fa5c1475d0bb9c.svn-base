# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-12-30 09:28
from __future__ import unicode_literals

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0031_auto_20191230_0928'),
        ('ipos', '0024_auto_20191029_1438'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminManagement',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Title', models.CharField(db_column='title', max_length=80, verbose_name='headline')),
                ('article_type', models.CharField(blank=True, choices=[('1', 'Management system'), ('2', 'Food safety'), ('3', 'Restaurant system'), ('4', 'Administrative push')], default='1', max_length=3, verbose_name='Article type')),
                ('Content', models.TextField(db_column='content', null=True, verbose_name='Content')),
                ('Author', models.<PERSON>r<PERSON>ield(db_column='author', max_length=30, verbose_name='Author')),
                ('Pubdate', models.DateTimeField(db_column='pubdate', default=datetime.datetime.now, verbose_name='Pubdate')),
                ('Entrydate', models.DateTimeField(db_column='entrydate', default=datetime.datetime.now, null=True, verbose_name='write time')),
            ],
            options={
                'verbose_name': 'Administrative management',
                'verbose_name_plural': 'Administrative management',
            },
        ),
        migrations.CreateModel(
            name='AppPic',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('map_name', models.CharField(default='', max_length=30, null=True, unique=True, verbose_name='map name')),
                ('map_path', models.CharField(blank=True, default='', max_length=90, null=True, verbose_name='map path')),
                ('width', models.FloatField(blank=True, default=0, editable=False, null=True, verbose_name='width')),
                ('height', models.FloatField(blank=True, default=0, editable=False, null=True, verbose_name='height')),
                ('mulriple', models.IntegerField(blank=True, default=5, editable=False, null=True, verbose_name='multiple')),
            ],
            options={
                'default_permissions': ('browse', 'add', 'change', 'delete'),
            },
        ),
        migrations.CreateModel(
            name='CookBook',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Dish number')),
                ('itemname', models.CharField(max_length=20, verbose_name='Variety of dishes')),
                ('is_push', models.BooleanField(default=False, verbose_name='Push or not')),
                ('is_push_book', models.BooleanField(default=False, verbose_name='Allowed book or not')),
                ('Price', models.DecimalField(db_column='price', decimal_places=2, max_digits=6, verbose_name='Unit price (yuan)')),
                ('WeekDay', models.CharField(db_column='weekday', max_length=250, null=True, verbose_name='Meal time')),
                ('meal', models.CharField(max_length=250, verbose_name='meal')),
                ('itemIntroduce', models.TextField(blank=True, db_column='itemintroduce', default='', max_length=50, null=True, verbose_name='Introduction of dishes')),
            ],
            options={
                'verbose_name': 'App menu',
                'verbose_name_plural': 'App menu',
            },
        ),
        migrations.CreateModel(
            name='FoodClassify',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Dish classify id')),
                ('itemname', models.CharField(max_length=20, verbose_name='Dish classify name')),
            ],
            options={
                'verbose_name': 'Variety of dishes',
                'verbose_name_plural': 'Variety of dishes',
            },
        ),
        migrations.CreateModel(
            name='FoodCommend',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Commend', models.TextField(db_column='commend', default=0, null=True, verbose_name='Patrons recommendation')),
                ('FoodCommend', models.IntegerField(db_column='foodcommend', default=0, null=True, verbose_name='Reviews of dishes')),
                ('ServerCommend', models.IntegerField(db_column='servercommend', default=0, null=True, verbose_name='Restaurant service')),
                ('HealthCommend', models.IntegerField(db_column='healthcommend', default=0, null=True, verbose_name='Food Hygiene')),
                ('DelTag', models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True)),
                ('CookBookCode', models.ForeignKey(blank=True, db_column='cookbookcode', null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.CookBook', verbose_name='Variety of dishes')),
            ],
            options={
                'verbose_name': 'App reviews of dishes',
                'verbose_name_plural': 'App reviews of dishes',
            },
        ),
        migrations.CreateModel(
            name='HealthyDiet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Article id')),
                ('Title', models.CharField(db_column='title', max_length=100, verbose_name='title')),
                ('Content', models.TextField(db_column='content', null=True, verbose_name='Content')),
                ('LikeSum', models.TextField(db_column='likesum', editable=False, null=True, verbose_name='Like people number')),
                ('Author', models.CharField(db_column='author', max_length=30, verbose_name='Author')),
                ('Pubdate', models.DateTimeField(db_column='pubdate', default=datetime.datetime.now, verbose_name='Pubdate')),
                ('Entrydate', models.DateTimeField(db_column='entrydate', default=datetime.datetime.now, null=True, verbose_name='write time')),
            ],
            options={
                'verbose_name': 'Healthy diet',
                'verbose_name_plural': 'Healthy diet',
            },
        ),
        migrations.CreateModel(
            name='MerchandiseClassify',
            fields=[
                ('Mercid', models.AutoField(db_column='mercid', editable=False, primary_key=True, serialize=False)),
                ('Mercode', models.CharField(db_column='mercode', help_text='The maximum length is no more than 40 characters. Modifying the commodity category number will not change the category of the commodity.', max_length=40, verbose_name='Merchandise classify id')),
                ('MercName', models.CharField(db_column='mercname', max_length=40, verbose_name='Merchandise classify name')),
                ('MercParent', models.IntegerField(blank=True, db_column='supmercid', default=0, verbose_name='Superior class')),
                ('DelTag', models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag')),
                ('opstamp', models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Refresh time')),
            ],
            options={
                'verbose_name': 'Merchandise classify',
                'verbose_name_plural': 'Merchandise classify',
                'db_table': 'merchandiseclassify',
            },
        ),
        migrations.CreateModel(
            name='MerchandiseList',
            fields=[
                ('id', models.AutoField(db_column='id', editable=False, primary_key=True, serialize=False)),
                ('MercOrdercode', models.CharField(db_column='mercordercode', max_length=40, verbose_name='order number')),
                ('Mercode', models.CharField(db_column='merccode', max_length=40, verbose_name='Product Number')),
                ('MercName', models.CharField(db_column='mercname', max_length=40, verbose_name='product name')),
                ('MercPrice', models.DecimalField(db_column='mercprice', decimal_places=2, max_digits=6, verbose_name='Unit price (yuan)')),
                ('checktime', models.DateTimeField(blank=True, null=True, verbose_name='Operation time')),
                ('is_emp', models.BooleanField(default=False, verbose_name='Is it external consumption')),
                ('quantity', models.IntegerField(blank=True, editable=False, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Commodity consumption list',
                'verbose_name_plural': 'Commodity consumption list',
                'default_permissions': 'browse',
            },
        ),
        migrations.CreateModel(
            name='MessageCenter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=30, verbose_name='message number')),
                ('Title', models.CharField(db_column='title', max_length=80, verbose_name='headline')),
                ('message_type', models.CharField(blank=True, choices=[('4', 'Nutrition and health'), ('5', 'Delicacy'), ('6', 'Cooking Skill'), ('7', 'Recent Activity')], default='3', max_length=3, verbose_name='Message type')),
                ('Content', models.TextField(db_column='content', null=True, verbose_name='Content')),
                ('Author', models.CharField(db_column='author', max_length=30, verbose_name='Author')),
                ('Pubdate', models.DateTimeField(db_column='pubdate', default=datetime.datetime.now, verbose_name='Pubdate')),
                ('Entrydate', models.DateTimeField(db_column='entrydate', default=datetime.datetime.now, editable=False, null=True, verbose_name='write time')),
            ],
            options={
                'verbose_name': 'app Message Center',
                'verbose_name_plural': 'app Message Center',
            },
        ),
        migrations.CreateModel(
            name='MobileComsumptionLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('posmoney', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='Amount of consumption')),
                ('posoptime', models.DateTimeField(blank=True, null=True, verbose_name='consumption time')),
                ('sn', models.CharField(blank=True, max_length=20, null=True, verbose_name='serial number')),
                ('paysource', models.IntegerField(blank=True, choices=[(1, 'Alipay consumption'), (2, 'WeChat consumption')], editable=False, null=True, verbose_name='means of transaction')),
                ('tradeno', models.CharField(blank=True, max_length=100, null=True, verbose_name='order number')),
                ('create_time', models.DateTimeField(blank=True, null=True, verbose_name='create time')),
                ('trade_state', models.IntegerField(blank=True, choices=[(1, 'paying'), (2, 'paid'), (3, 'failure to pay'), (4, 'overdue')], null=True, verbose_name='trade state')),
                ('reserve_field', models.CharField(blank=True, max_length=200, null=True, verbose_name='reserve field')),
            ],
            options={
                'verbose_name': 'Mobile consumption log',
                'verbose_name_plural': 'Mobile consumption log',
                'default_permissions': ('browse',),
            },
        ),
        migrations.CreateModel(
            name='OrderRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=30, verbose_name='OrderID')),
                ('MenuList', models.TextField(db_column='menulist', verbose_name='Order Information')),
                ('OrderDate', models.DateField(db_column='orderdate', verbose_name='Order Date')),
                ('PlaceTime', models.DateTimeField(db_column='placetime', verbose_name='Place time')),
                ('CommendDate', models.DateField(db_column='commenddate', null=True, verbose_name='Ticking time')),
                ('state', models.IntegerField(blank=True, choices=[(1, 'Ongoing'), (2, 'Called off'), (3, 'Done'), (4, 'Timeout'), (5, 'Appraised')], null=True, verbose_name='Order state')),
                ('money', models.CharField(max_length=30, null=True, verbose_name='Total')),
                ('meal', models.IntegerField(blank=True, choices=[(1, 'breakfast'), (2, 'lunch'), (3, 'dinner')], null=True, verbose_name='meal')),
                ('usefulTag', models.BooleanField(db_column='usefultag', default=False, verbose_name='Push or not')),
                ('UserID', models.ForeignKey(blank=True, db_column='userid_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel')),
            ],
            options={
                'verbose_name': 'App order record',
                'verbose_name_plural': 'App order record',
            },
        ),
        migrations.AddField(
            model_name='cardcashsz',
            name='MercOrdercode',
            field=models.CharField(db_column='mercordercode', max_length=40, null=True, verbose_name='Order number'),
        ),
        migrations.AddField(
            model_name='cardcashsz',
            name='PhotoPath',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='Consumption photos'),
        ),
        migrations.AddField(
            model_name='cardcashsz',
            name='is_bookfood',
            field=models.BooleanField(default=False, verbose_name='Is it ordering consumption'),
        ),
        migrations.AddField(
            model_name='cardcashsz',
            name='pay_pin',
            field=models.CharField(blank=True, default='', max_length=24, null=True, verbose_name='payer'),
        ),
        migrations.AddField(
            model_name='merchandise',
            name='opstamp',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Refresh time'),
        ),
        migrations.AddField(
            model_name='merchandise',
            name='restriction_type',
            field=models.IntegerField(blank=True, choices=[(1, 'Limited purchase for internal personnel only'), (2, 'Limited purchase for external personnel only'), (0, 'Unlimited purchase')], default=0, null=True, verbose_name='Restriction type'),
        ),
        migrations.AddField(
            model_name='orderrecord',
            name='ipos_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.CardCashSZ', verbose_name='Expenses record'),
        ),
        migrations.AlterUniqueTogether(
            name='mobilecomsumptionlog',
            unique_together=set([('tradeno', 'sn')]),
        ),
        migrations.AlterUniqueTogether(
            name='messagecenter',
            unique_together=set([('code',)]),
        ),
        migrations.AlterUniqueTogether(
            name='merchandiselist',
            unique_together=set([('id',)]),
        ),
        migrations.AlterUniqueTogether(
            name='merchandiseclassify',
            unique_together=set([('Mercode',)]),
        ),
        migrations.AlterUniqueTogether(
            name='healthydiet',
            unique_together=set([('code',)]),
        ),
        migrations.AddField(
            model_name='foodcommend',
            name='OrderCode',
            field=models.ForeignKey(blank=True, db_column='orderrecordcode', null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.OrderRecord', verbose_name='Order'),
        ),
        migrations.AddField(
            model_name='foodcommend',
            name='UserID',
            field=models.ForeignKey(blank=True, db_column='userid_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterUniqueTogether(
            name='foodclassify',
            unique_together=set([('code',)]),
        ),
        migrations.AddField(
            model_name='cookbook',
            name='Classify',
            field=models.ForeignKey(blank=True, db_column='foodclassify_code', null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.FoodClassify', verbose_name='Dish classify'),
        ),
        migrations.AddField(
            model_name='merchandise',
            name='MercID',
            field=models.ForeignKey(db_column='mercid', null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.MerchandiseClassify', verbose_name='Merchandise classify'),
        ),
        migrations.AlterUniqueTogether(
            name='orderrecord',
            unique_together=set([('code',)]),
        ),
        migrations.AlterUniqueTogether(
            name='cookbook',
            unique_together=set([('code',)]),
        ),
    ]
