{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
{% block tblHeader %}
hd='({% trans 'After the submission of the operation need to be about half a minute or so of device in the entry into force' %})'
hasImport={% if user|HasPerm:"ipos.import_ICcard" %}true{% else %}false{% endif %}
//jqOptions=copyObj(jq_Options)
jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='ICcard';
jqOptions[g_activeTabID].pager = "id_pager_"+tblName[g_activeTabID];
jqOptions[g_activeTabID].sortname='code,name';
options[g_activeTabID].dlg_width=880;
options[g_activeTabID].dlg_height=520;

function strOfData_ICcard(data)
{
	return stripHtml(data.code)+" "+stripHtml(data.name);
}
function afterPost_ICcard(flag, obj) {
    var number = $('#id_code',obj).val()
    $('form').resetForm()
    $('#id_code',obj).val(auto_increase_number(number));
    reloadData()
}
function getConsumptions(){
    options_html=""
    for(var i=0;i<c_states.length;i++){
        options_html+="<option value='"+c_states[i].symbol+"'>"+c_states[i].pName+"</option>"
    }
    return options_html;
}

$(function(){
        consumption={% autoescape off %} {{ consumption }}{% endautoescape %}
        c_states= consumption.states;
        var info='<div><p>{% trans '1. Card data is used to limit the consumption of cards when issuing cards' %}</p><br>'
                + '<p>{% trans '2. By default, the system uses the default [employee card] card when issuing cards. The card class does not have a consumption limit by default. It can be modified but cannot be deleted. Users can add card classes by themselves. New card classes can be added. Modify and delete' %}</p><br>'
                + '<p>{% trans '3. For example, when consuming, the number of actual consumption of the card is checked (consumption on any device will be superimposed, such as on a, b, C consumer machine, then the number of consumption of this card is 3). If this number does not exceed the maximum allowable consumption of the card type in the specified equipment, it can be consumed, otherwise consumption is not allowed.' %}</p><br>'
                + '</div>'
        renderLeftInformation(info);
        smenu="<ul><li  class='subnav_on' onclick=submenuClick('/ipos/data/ICcard/',this);><a href='#'>{% trans 'Card Information' %}</a></li></ul>"
        //$("#menu_div").html(smenu)

	$("#"+g_activeTabID+" #queryButton").hide()
        $("#"+g_activeTabID+" #id_newrec").click(function(event){
                processNewModel();
	});
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowDep_ICcard();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13)
	    searchShowDep_ICcard();
	});
	$("#"+g_activeTabID+" #id_export").css('display','none');
	$("#"+g_activeTabID+" #id_third").html("");

});


function process_dialog_ICcard(obj,flag)
{
    $(obj).dialog({resizable:false,modal:true});
    if(flag=='edit'){
        options[g_activeTabID].dlg_width=1150;
        $("#id_code",obj).attr('readonly','True')
    }
    var value=$("#id_"+flag,obj).val();
    $("#id_remark",obj).parent().parent().after(
        "<th><label for='id_posmeal'>{% trans 'Can eat' %}:</label></th>"
        +"<td>"
        +"<div id='dlg_for_query_posmeal' style='overflow:hidden;'>"
        +"<div id='dlg_dept_posmeal' class='dlgdiv'>"
        +"<div id='dlg_dept_body_posmeal' style='overflow:auto;'>"
        +"<ul id='showTree_posmeal' class='ztree' style='height:123px;overflow:auto;'></ul>"
        +"</div></div></div>"
        +"<div style='display:none;'><input id='id_posmeal' name='posmeal' type='hidden' value=''/></div>"
        +"</td>"
        +"<th><label for='id_use_mechine'>{% trans 'available device' %}:</label></th>"
        +"<td>"
        +"<div id='dlg_for_query_use_mechine' style='overflow:hidden;'>"
        +"<div id='dlg_dept_use_mechine' class='dlgdiv'>"
        +"<div id='dlg_dept_body_use_mechine' style='overflow:auto;'>"
        +"<input type='checkbox' id='id_CheckAll' name='CheckAll' value='1' />{% trans 'select all' %}"
        +"<ul id='showTree_use_mechine' class='ztree' style='height:123px;overflow:auto;'>"
        +"</ul>"
        +"</div></div></div>"
        +"<div style='display:none;'><input id='id_use_mechine' name='use_mechine' type='hidden' value=''/></div>"
        +"</td>"
    );
    $("#id_CheckAll",obj).change(function(){
        if($("#id_CheckAll",obj).is(":checked")){
            var treeObj = $.fn.zTree.getZTreeObj("showTree_use_mechine");
            treeObj.checkAllNodes(true);
        }else{
            var treeObj = $.fn.zTree.getZTreeObj("showTree_use_mechine");
            treeObj.checkAllNodes(false);
        }
    })
    $('#id_edit_form',obj).validate({
        rules:{'name':{string:true,maxlength:24},
				'date_max_money':{required:true,max:100000,min:0,digits:true},
                'code':{required:true,digits:true,max:254,min:1},
                'date_max_count':{required:true,min:0,digits:true},
                'per_max_money':{required:true,max:100000,min:0,digits:true},
                'meal_max_money':{required:true,max:100000,min:0,digits:true},
                'meal_max_count':{required:true,min:0,digits:true},
                'less_money':{required:true,min:0,digits:true},
                'max_money':{required:true,'max':100000,min:0,digits:true},
                'use_date':{required:true,'max':99999,digits:true,maxlength:10,min:0},
                'discount':{required:true,max:100,min:0,digits:true}
                }
    });
    $("#id_check_book_dinner",obj).change(function(){
        if($("#id_check_book_dinner",obj).is(":checked")){
            var treeObj = $.fn.zTree.getZTreeObj("showTree_posmeal");
            treeObj.checkAllNodes(true);
        }
    });
}

function process_high_sign(obj,i,code){
        mhtml = "<div>"
                +"<div class='left' style='width:300px;height: 330px;border: 1px solid #dddddd;'>"
                +"<div class='alert alert-info' style='height: 20px;margin-left: 0px;margin-top: 0px;'>{% trans 'advanced setting' %}("+i +")</div>"
                +"<div><form id='dlg_dev_iccard' method='post'>"
                    +"<table>"
                        +"<tr><th><label for='id_check_dev'>{% trans 'Whether to enable' %}:</label></th><td><input type='checkbox' name='check_dev' id='id_check_dev'></td></tr>"
                        +"<tr><th><font color='red'>*</font><label for='id_discount_h'>{% trans 'discount' %}(%):</label></th>"
                            +"<td style='vertical-align:top;'><input type='number' name='discount_h' value='0' id='id_discount_h'><span><i class='icon iconfont icon-xiaoxi' title='{% trans "Discount 80% is 20% off" %}'></i></span></td></tr>"
                        +"<tr><th><label for='id_pos_time_h'><font color='red'>*</font>{% trans 'Consumption time period' %}:</label></th>"
                            +"<td><select style='vertical-align:top;' name='pos_time_h' id='id_pos_time_h'>"+getConsumptions()+"</select></td></tr>"
                        +"<tr><th><font color='red'>*</font><label for='id_date_max_money_h'>{% trans 'Maximum amount of daily consumption (yuan)' %}:</label></th>"
                        +"<td style='vertical-align:top;'><input type='number' name='date_max_money_h' value='0' step='1' required='' id='id_date_max_money_h'></td></tr>"
                        +"<tr><th><font color='red'>*</font><label for='id_date_max_count_h'>{% trans 'Maximum number of daily consumption' %}:</label></th>"
                        +"<td style='vertical-align:top;'><input type='number' name='date_max_count_h' value='0' step='1' required='' id='id_date_max_count_h'></td></tr>"
                        +"<tr><th><font color='red'>*</font><label for='id_meal_max_money_h'>{% trans 'Maximum amount of meal consumption (yuan)' %}:</label></th>"
                        +"<td style='vertical-align:top;'><input type='number' name='meal_max_money_h' value='0' step='1' required='' id='id_meal_max_money_h'></td></tr>"
                        +"<tr><th><font color='red'>*</font><label for='id_meal_max_count_h'>{% trans 'Maximum number of meals consumed' %}:</label></th>"
                        +"<td style='vertical-align:top;'><input type='number' name='meal_max_count_h' value='0' step='1' required='' id='id_meal_max_count_h'></td></tr>"
                        +"<tr><th><font color='red'>*</font><label for='id_use_date_h'>{% trans 'Effective days of use' %}:</label></th>"
                        +"<td style='vertical-align:top;'><input type='number' name='use_date_h' value='0' required='' id='id_use_date_h'></td></tr>"
                        +"<tr><th><label for='id_remark_h'>{% trans 'Remarks' %}:</label></th>"
                        +"<td style='vertical-align:top;'><input type='text' name='remark_h' id='id_remark_h'></td></tr>"
                        +"</tr>"
                        +'<tr><td colspan="2"><span id="id_error_dev" style="display:none;"></span></td></tr>'
                        +"<tr align='right'><td color><input id='id_add_dev' type='button' class='m-btn  zkgreen rnd' value='{% trans "Submit" %}'/>"
                        +"</td></tr>"
                    +"</table></form>"
                +"</div>"
            +"</div>"
                +"</div>"

    $("#device_sign",obj).html(mhtml)

    $('#dlg_dev_iccard',obj).validate({
        rules:{'date_max_money_h':{required:true,max:100000,min:0},
                'date_max_count_h':{required:true,min:0},
                'meal_max_money_h':{required:true,max:100000,min:0},
                'meal_max_count_h':{required:true,min:0},
                'use_date_h':{required:true,min:0},
                'discount_h':{required:true,max:100,min:0,digits:true}
                }
        });



    var url="/ipos/getData/?func=ICcard&code="+code+"&dev_sn="+i;
    $.ajax({
        type: "POST",
        url:url,
        dataType:"json",
        success:function(data){
            if(data.isvalid==1)
            {
                $("#id_check_dev").prop("checked","checked");
                $("#id_discount_h").val(data.discount)
                $("#id_pos_time_h").val(data.pos_time)
                $("#id_date_max_money_h").val(data.date_max_money)
                $("#id_date_max_count_h").val(data.date_max_count)
                $("#id_meal_max_money_h").val(data.meal_max_money)
                $("#id_meal_max_count_h").val(data.meal_max_count)
                $("#id_use_date_h").val(data.use_date)
                $("#id_remark_h").val(data.remark)
            }
        }})

    $("#id_add_dev").button({icons: {primary: "ui-icon-check"}});
    $("#id_add_dev").click(function(){
        date_max_money_h = Number($('#id_date_max_money_h').val());
        // console.log(date_max_money_h);
        meal_max_money_h = Number($('#id_meal_max_money_h').val());
        date_max_count_h = Number($('#id_date_max_count_h').val());
        meal_max_count_h = Number($('#id_meal_max_count_h').val());
        // console.log(date_max_money_h,meal_max_money_h);
        if ((date_max_money_h != 0)&&(date_max_money_h < meal_max_money_h)){
            $('#id_error_dev').html('<ul class="errorlist"><li>{% trans 'The maximum amount of meal consumption cannot be greater than the maximum amount of daily consumption' %}</li></ul>').show();
            return false;
        }
        if ((date_max_count_h != 0)&&(date_max_count_h < meal_max_count_h)){
            $('#id_error_dev').html('<ul class="errorlist"><li>{% trans 'The maximum number of meals consumed cannot be greater than the maximum number of daily consumption' %}</li></ul>').show();
            return false;
        }
        var queryStr=$("#dlg_dev_iccard").formSerialize()+"&code="+code+"&dev_sn="+i;
        $.ajax({
            type: "POST",
            url:"/ipos/dev_card/",
            data:queryStr,
            dataType:"json",
            success:function(retdata){
                var message=retdata.message
                if (retdata.ret==0)
                    $("#id_error_dev").html("<ul class='oklist'><li>"+message+"</li></ul>").show();
                else
                    $("#id_error_dev").html("<ul class='errorlist'><li>"+message+"</li></ul>").show();
            }
        });
    });
}



function process_dialog_again_ICcard(obj,flag){
    var code=$("#id_code",obj).val()
    ShowTreeData("posmeal",code)
    ShowTreeData("use_mechine",code)
    var zTree = $.fn.zTree.getZTreeObj("showTree_use_mechine",code);
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode){
        var devsn=treeNode.id;
        var devname=treeNode.name;
        var dev_sns=getSelected_dept("showTree_use_mechine")
        if(flag=='edit'){
            if(dev_sns.indexOf(devsn)!='-1' && devsn!=''){
                $("#device_sign").show()
                process_high_sign(obj,devsn,code)
            }else{
                $("#device_sign").hide();
            }
        }


    }
}

function ShowTreeData(page,code){
        var setting = {
            check: {enable: true,chkStyle: "checkbox",chkboxType: { "Y": "s", "N": "ps" }},
	    async: {
			    enable: true,
			    url: "/ipos/getData/?func="+page+"&code="+code,
			    autoParam: ["id"]
		    }
	};
	$.fn.zTree.init($("#showTree_"+page), setting,null);	
}

function searchShowDep_ICcard(){
    var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
    var url="/ipos/data/ICcard/?q="+encodeURI(v)
	savecookie("search_urlstr",url);
    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}


function beforePost_ICcard(obj,actionName)
{
        var treeids=getSelected_dept("showTree_posmeal");
        if(treeids.length>0)
        {
                $("#id_posmeal",obj).val(treeids);
        }
        else
        {
                $("#id_posmeal",obj).val('');
        }
        var treeids=getSelected_dept("showTree_use_mechine");
        if(treeids.length>0)
        {
                $("#id_use_mechine",obj).val(treeids);
        }
        else
        {
                $("#id_use_mechine",obj).val('');
        }
        date_max_money = Number($('#id_date_max_money').val());
        meal_max_money = Number($('#id_meal_max_money').val());
        per_max_money = Number($('#id_per_max_money').val());
        date_max_count = Number($('#id_date_max_count').val());
        meal_max_count = Number($('#id_meal_max_count').val());
        less_money = Number($('#id_less_money').val());
        max_money = Number($('#id_max_money').val());
        if ((date_max_money != 0)&&(date_max_money < meal_max_money)){
            $('#id_error',obj).html('<ul class="errorlist"><li>{% trans 'The maximum amount of meal consumption cannot be greater than the maximum amount of daily consumption' %}</li></ul>');
            return false;
        }
        if ((date_max_money != 0)&&(date_max_money < per_max_money)){
            $('#id_error',obj).html('<ul class="errorlist"><li>{% trans 'The maximum amount of secondary consumption cannot be greater than the maximum amount of daily consumption' %}</li></ul>');
            return false;
        }
        if ((meal_max_money != 0)&&(meal_max_money < per_max_money)){
            $('#id_error',obj).html('<ul class="errorlist"><li>{% trans 'The maximum amount of secondary consumption cannot be greater than the maximum amount of food consumption' %}</li></ul>');
            return false;
        }
        if ((date_max_count != 0)&&(date_max_count < meal_max_count)){
            $('#id_error',obj).html('<ul class="errorlist"><li>{% trans 'The maximum number of meals consumed cannot be greater than the maximum number of daily consumption' %}</li></ul>');
            return false;
        }
        if ((max_money != 0)&&(max_money < less_money)){
            $('#id_error',obj).html('<ul class="errorlist"><li>{% trans 'The minimum card balance cannot be greater than the maximum card balance' %}</li></ul>');
            return false;
        }
}

{% endblock %}
