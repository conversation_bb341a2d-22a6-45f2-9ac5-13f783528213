{% autoescape off %}
{% load iclock_tags %}

[
{% for item in latest_item_list %}
{"id":"{{ item.id }}",
"PIN":"{% if can_change %}{% if item.State == 0 %}<a class='can_edit'  href='#' onclick='javascript:editclick({{item.id}});'>{{ item.employee.PIN }}</a>{% else %}{% if item.State == 6 %}<a class='can_edit'  href='#' onclick='javascript:editclick({{item.id}});'>{{ item.employee.PIN }}</a>{% else %}{{ item.employee.PIN }}{% endif %}{% endif %}{% else %}{{ item.employee.PIN }}{%endif%}",
"EName":"{{ item.employee.EName|default:'' }}",
"DeptName":"{{ item.employee.Dept.DeptName }}",
"CHECKTIME":"{{ item.CHECKTIME }}",
"CHECKTYPE":"{{ item.CHECKTYPE|getRecordState }}" ,
"process":"{{ item|showprocessmsg }}",
"YUYIN":"{{ item.YUYIN|filteryuanyin }}",
"MODIFYBY":"{{ item.MODIFYBY }}",
"State":"{{ item|get_state_msg}}",
"ApplyDate":"{{ item.ApplyDate }}",
"Device":"{{ item.Device|default:"" }}",
"SaveStamp":"{{ item.SaveStamp|default:"" }}"}
{%if not forloop.last%},{%endif%}
{% endfor %}
]
{% endautoescape %}
