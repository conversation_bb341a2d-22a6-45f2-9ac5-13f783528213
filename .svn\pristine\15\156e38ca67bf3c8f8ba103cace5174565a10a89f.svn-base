# -*- coding: utf-8 -*-
from mysite.wxapp.dataparams import *

class Enum_(tuple):
     __getattr__ = tuple.index

recType= Enum_(['reservationing','reservationsuccess','reservationfail','inviteing','invitesuccess','invitefail','v_invited'])

def oauth(method):
    @functools.wraps(method)
    def warpper(*args, **kwargs):
        request=args[0]
        code = request.GET.get('code', None)
        host= hosturl#'https://www.zkecopro.cn'
        if code:
            try:
                user_info = wechat_client.oauth.get_user_info(code)
            except Exception as e:
                #print e
                #raise e
                return HttpResponse(status=403)
            else:
                request.session['user_info'] = user_info
        else:
            user_info=request.session.get('user_info',None)
            if  not user_info:
                url = wechat_client.oauth.authorize_url(host+request.get_full_path())
                return HttpResponseRedirect(url)

        return method(*args, **kwargs)
    return warpper


def facedetectandcut(fs,userid):
    count = 1
    targetPath = os.path.join(settings.FILEPATH, 'files', 'vistor', 'face', 'thumbnail')
    thumbnailpath = os.path.join(settings.FILEPATH, 'files', 'vistor', 'face')
    if not os.path.exists(targetPath):
        os.makedirs(targetPath)
    if not os.path.exists(thumbnailpath):
        os.makedirs(thumbnailpath)
    savedface=[]
    v_face_path= []
    counts=[]
    for f in fs:
        listStr = [userid, str(count)]
        fileName = ''.join(listStr)
        imgData = base64.b64decode(f.split(',')[1])
        with open(targetPath+os.sep+'%s.jpg' % fileName,'wb') as pic:
            pic.write(imgData)
        with open(thumbnailpath+os.sep+'%s.jpg' % fileName,'wb') as pic:
            pic.write(imgData)
        savedface.append(targetPath+os.sep+'%s.jpg' % fileName)
        savedface.append(thumbnailpath+os.sep+'%s.jpg' % fileName)
        v_face_path.append('%s.jpg' % fileName)
    if counts:
        [os.remove(i)  for i in savedface]
        return {'ret': 1 ,"counts": ','.join(counts)}
    return {'ret': 0, 'v_face_path':','.join(v_face_path) }



def datelocaltime(come_date):
    try :
         come_date = datetime.datetime.strptime(come_date, "%Y-%m-%dT%H:%M:%S")
    except:
         try:
             come_date = datetime.datetime.strptime(come_date, "%Y-%m-%dT%H:%M:%S.%f")
         except:
             come_date = datetime.datetime.strptime(come_date, "%Y-%m-%dT%H:%M")
    return come_date
