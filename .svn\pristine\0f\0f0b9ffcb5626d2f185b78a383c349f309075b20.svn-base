var base64EncodeChars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",base64DecodeChars=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,-1,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,-1,-1,-1,-1,-1];function base64encode(e){var b,a,f,d,c,g;f=e.length;a=0;for(b="";a<f;){d=e.charCodeAt(a++)&255;if(a==f){b+=base64EncodeChars.charAt(d>>2);b+=base64EncodeChars.charAt((d&3)<<4);b+="\x3d\x3d";break}c=e.charCodeAt(a++);if(a==f){b+=base64EncodeChars.charAt(d>>2);b+=base64EncodeChars.charAt((d&3)<<4|(c&240)>>4);b+=base64EncodeChars.charAt((c&15)<<2);b+="\x3d";break}g=e.charCodeAt(a++);b+=base64EncodeChars.charAt(d>>2);b+=base64EncodeChars.charAt((d&3)<<4|(c&240)>>4);b+=base64EncodeChars.charAt((c&15)<<2|(g&192)>>6);b+=base64EncodeChars.charAt(g&63)}return b}function base64decode(e){var b,a,f,d,c;d=e.length;f=0;for(c="";f<d;){do b=base64DecodeChars[e.charCodeAt(f++)&255];while(f<d&&-1==b);if(-1==b)break;do a=base64DecodeChars[e.charCodeAt(f++)&255];while(f<d&&-1==a);if(-1==a)break;c+=String.fromCharCode(b<<2|(a&48)>>4);do{b=e.charCodeAt(f++)&255;if(61==b)return c;b=base64DecodeChars[b]}while(f<d&&-1==b);if(-1==b)break;c+=String.fromCharCode((a&15)<<4|(b&60)>>2);do{a=e.charCodeAt(f++)&255;if(61==a)return c;a=base64DecodeChars[a]}while(f<d&&-1==a);if(-1==a)break;c+=String.fromCharCode((b&3)<<6|a)}return c}function utf16to8(e){var b,a,f,d;b="";f=e.length;for(a=0;a<f;a++)d=e.charCodeAt(a),1<=d&&127>=d?b+=e.charAt(a):(2047<d?(b+=String.fromCharCode(224|d>>12&15),b+=String.fromCharCode(128|d>>6&63)):b+=String.fromCharCode(192|d>>6&31),b+=String.fromCharCode(128|d>>0&63));return b}function utf8to16(e){var b,a,f,d,c,g;b="";f=e.length;for(a=0;a<f;)switch(d=e.charCodeAt(a++),d>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:b+=e.charAt(a-1);break;case 12:case 13:c=e.charCodeAt(a++);b+=String.fromCharCode((d&31)<<6|c&63);break;case 14:c=e.charCodeAt(a++),g=e.charCodeAt(a++),b+=String.fromCharCode((d&15)<<12|(c&63)<<6|(g&63)<<0)}return b}function CharToHex(e){if("FFFFFFFFFFFF"==e)return e;e=e.toString();var b,a,f;b="";for(a=0;a<e.length;)f=e.charCodeAt(a++).toString(16),1==(f+"").length&&(b+="0"),b+=f;return b.toUpperCase()}function zk_encrypt(e,b){b=b.toString();e=base64encode(e);var a=Array(256);"undefined"!=typeof zk_key&&(b=zk_key+"-"+b);for(var f=Array(e.length),d=0,c=0;256>c;c++)a[c]=c;for(c=0;256>c;c++){var d=(d+a[c]+b.charCodeAt(c%b.length))%256,g=a[c];a[c]=a[d];a[d]=g}for(c=0;c<e.length;c++)f[c]=e.charCodeAt(c);for(var h=c=d=0;h<f.length;h++)c=(c+1)%256,d=(d+a[c])%256,g=a[c],a[c]=a[d],a[d]=g,f[h]=String.fromCharCode(f[h]^a[(a[c]+a[d])%256]);return CharToHex(f.join(""))};