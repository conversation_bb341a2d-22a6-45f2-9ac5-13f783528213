# -*- coding: utf-8 -*-
# Generated by Django 1.11.10 on 2018-04-02 12:34
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0012_auto_20180310_1337'),
    ]

    operations = [
        migrations.AlterField(
            model_name='attrecabnormite',
            name='Verify',
            field=models.IntegerField(choices=[(0, 'Password'), (1, 'Fingerprint'), (2, 'Card'), (3, 'Card'), (4, 'Card'), (5, 'Add'), (9, 'Other'), (11, '\u5361+\u5bc6\u7801'), (15, 'FACE'), (16, '\u5176\u4ed6')], db_column='verifycode', default=0, verbose_name='verification'),
        ),
        migrations.AlterField(
            model_name='transactions',
            name='Verify',
            field=models.IntegerField(choices=[(0, 'Password'), (1, 'Fingerprint'), (2, 'Card'), (3, 'Card'), (4, 'Card'), (5, 'Add'), (9, 'Other'), (11, '\u5361+\u5bc6\u7801'), (15, 'FACE'), (16, '\u5176\u4ed6')], db_column='verifycode', default=0, null=True, verbose_name='verification'),
        ),
    ]
