#coding=utf-8
from django.utils.translation import gettext_lazy as _
from mysite.iclock.models import *
from mysite.base.models import *

from django.db.models.fields import <PERSON>Field
from django.contrib.auth.models import Permission, Group
from mysite.iclock.datautils import GetModel, hasPerm
from mysite.iclock.templatetags.iclock_tags import HasPerm
import datetime
from django.conf import settings

STANDARD_MODULES = [
    {'id': 'person', 'caption': _(u'Personnel')},
    {'id': 'adms', 'caption': _(u'ADMS')},
    {'id': 'system', 'caption': _(u'System')},
]

menus = [
        # 人事
        {'caption': {'title': _(u"Institutional Information"), 'event': '', 'permissions': '', '_mod': 'person', 'id': 'id_maintenance_menu', 'img': ''},
            'submenu': [
                {'title': _(u'Department'), 'event': '/iclock/data/department/', 'permissions': 'iclock.browse_department', '_mod': 'person', 'img': ''},
            ]},
        {'caption': {'title': _(u"Personnel Information"), 'event': '', 'permissions': '', '_mod': 'person', 'id': 'id_maintenance_menu', 'img': ''},
            'submenu': [
                {'title': _(u'personnel'), 'event': '/iclock/data/employee/', 'permissions': 'iclock.browse_employee', '_mod': 'person', 'img': ''},
                {'title': _(u'Employee Area Setting'), 'event': '/acc/data/empZone/', 'permissions': 'acc.browse_empzone', '_mod': 'person', 'img': ''},
            ]},
        {'caption': {'title': _(u"Basic Information"), 'event': '', 'permissions': '', '_mod': 'person', 'id': 'id_maintenance_menu', 'img': ''},
            'submenu': [
                {'title': _(u'Job'), 'event': '/iclock/data/userRoles/', 'permissions': 'iclock.browse_userroles', '_mod': 'person', 'img': ''},
                {'title': _(u'Feature Template'), 'event': '/iclock/data/BioData/', 'permissions':'iclock.browse_biodata', '_mod': 'person', 'img': ''},
            ]},
        {'caption': {'title': _(u"Daily matters"), 'event': '', 'permissions': '', '_mod': 'person', 'id': 'id_maintenance_menu', 'img': ''},
            'submenu': [
                # {'title': _("Employee Leave"), 'event': '/iclock/data/USER_SPEDAY/', 'permissions': 'iclock.browse_user_speday', '_mod': '', 'img': ''},
                {'title': _(u'Personnel Resignation'), 'event': '/iclock/data/empleavelog/', 'permissions': 'iclock.empLeave_employee', '_mod': 'person', 'img': ''},
                {'title': _(u'Mobile Register Photo Review'), 'event': '/iclock/data/ademployee/', 'permissions': 'iclock.browse_ademployee', '_mod': 'person', 'img': ''},
            ]},

        # 数据
        {'caption': {'title': _(u"Device Management"), 'event': "", 'permissions': '', '_mod': 'adms', 'id': 'id_adms_menu', 'img': ''},
            'submenu': [

                {'title': _(u"Acc Device Management"), 'event': '/iclock/data/iclock/','permissions': 'iclock.browse_iclock', '_mod': 'adms','img': 'media/images/button/connect_to_network.png', 'sign': 1},
                {'title': _(u"Att Device Management"), 'event': '/iclock/data/iclock/','permissions': 'iclock.browse_iclock', '_mod': 'adms','img': 'media/images/button/connect_to_network.png', 'sign': 2},
            ]},
        {'caption': {'title': _(u"Common Functions"), 'event': "", 'permissions': '', '_mod': 'adms', 'id': 'id_adms_menu', 'img': ''},
            'submenu': [
                {'title': _(u'Server Commands'), 'event': '/iclock/data/devcmds/', 'permissions': 'iclock.browse_devcmds', '_mod': 'adms', 'img': 'media/images/button/arrow_down_48.png'},
            ]},
        {'caption': {'title': _(u"acc_maintenance"), 'event': "", 'permissions': '', '_mod': 'adms', 'id': 'id_adms_menu','img': ''},
            'submenu': [
                {'title': _(u"Access Control Period"), 'event': '/acc/data/timezones/','permissions': 'acc.browse_timezones', '_mod': 'adms', 'img': ''},
                {'title': _(u'Access Control Authority'), 'event': '/acc/isys/acc/', 'permissions': 'acc.browse_level','_mod': 'adms', 'img': ''},
                {'title': _(u'Access Control Rules'), 'event': '/acc/isys/accessRules/', 'permissions': '','_mod': 'adms', 'img': ''},
            ]},

        {'caption': {'title': _("Search/Print"), 'event': '', 'permissions': '', '_mod': 'adms', 'id': 'id_adms_menu', 'img': ''},
            'submenu': [
                {'title': _("Transaction"), 'event': '/iclock/data/transactions/', 'permissions': 'iclock.browse_transactions', '_mod': 'adms', 'img': ''},
                {'title':_(u"Access Control Record"),'event':'/acc/data/records/','permissions':'acc.acc_records', '_mod': 'adms', 'img': ''},
            ]},

        # 系统
        {'caption': {'title': _(u"Rights Profile"), 'event': '', 'permissions': '', '_mod': 'system', 'id': 'id_group_menu', 'img': ''},
            'submenu': [
                {'title': _(u"Management Group"), 'event': '/iclock/data/group/', 'permissions': 'auth.browse_group', '_mod': 'system', 'img': ''},
                {'title': _(u"User"), 'event': '/iclock/data/user/', 'permissions': 'accounts.browse_myuser', '_mod': 'system', 'img': ''},
                {'title': _(u"AuthTerminal"), 'event': '/iclock/data/AuthTerminal/', 'permissions': 'iclock.browse_authterminal','_mod': 'system', 'img': ''},
                # {'title':_(u"Online Users"),'event':'/base/show_onlineusers/','permissions':'','_mod':'system','img':''},
            ]},
        {'caption': {'title': _(u"System Configuration"), 'event': '', 'permissions': '', '_mod': 'system', 'id': 'id_group_menu', 'img': ''},
            'submenu': [
                {'title': _(u'ZKECO Upgrade'), 'event': '/base/database_upgrade/', 'permissions': 'iclock.browse_database_upgrade', '_mod': 'system','img': ''},
                {'title': _(u"System Options"),'event': '/base/isys/options/', 'permissions': '', '_mod': '', 'img': '', 'id': 'options'},
            ]},

        {'caption': {'title': _(u"Public Configuration"), 'event': '', 'permissions': '', '_mod': 'system', 'id': 'id_group_menu',  'img': ''},
            'submenu': [
                 {'title': _(u'Area'), 'event': '/acc/data/zone/', 'permissions': 'acc.browse_zone', '_mod': 'system', 'img': ''},
                {'title': _("Holidays"), 'event': '/iclock/data/holidays/', 'permissions': 'iclock.browse_holidays','_mod': 'system'},

            ]},

        {'caption': {'title': _("System"), 'event': '', 'permissions': '', '_mod':'system', 'id': 'id_log_menu','img':''},
            'submenu': [
                {'title': _(u'Server Commands'), 'event': '/iclock/data/devcmds/', 'permissions': 'iclock.browse_devcmds', '_mod': 'system-adms', 'img': ''},
                {'title': _(u'Admin Action Log'), 'event': '/iclock/data/adminLog/', 'permissions': 'iclock.browse_adminlog', '_mod': 'system-adms', 'img': ''},
                {'title': _(u"Backup Record"), 'event': '/base/show_db_backup/','permissions': 'iclock.sys_backup_setting', '_mod': 'system-adms', 'img': ''},
                {'title': _(u'Middle table'), 'event': '/iclock/data/middletable/','permissions': 'iclock.sys_api_setting', '_mod': 'system-adms', 'img': ''},

            ]},
        {'caption': {'title': _(u"Device Log"), 'event': '', 'permissions': '', '_mod': 'system', 'id': 'id_devlog_menu', 'img': ''},
            'submenu': [
                {'title': _(u'Commands Error Log'), 'event': '/iclock/data/errorlog/', 'permissions': 'iclock.browse_devcmds', '_mod': 'system-adms', 'img': ''},
                {'title': _(u'Device Upload Data Log'), 'event': '/iclock/data/devlog/', 'permissions': 'iclock.browse_devlog', '_mod': 'system-adms;system-att;system-acc;system-meeting;system-ipos;patrol', 'img': ''},
                {'title': _(u'Device Operation Log'), 'event': '/iclock/data/oplog/', 'permissions': 'iclock.browse_devlog', '_mod': 'system-adms;system-att;system-acc;system-meeting;system-ipos;patrol', 'img': ''},
            ]},


    ]
