{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <fieldset class="layui-elem-field">
        <legend>{% trans 'Annual Holiday' %}</legend>
        <div class="layui-field-box">
            <table class="layui-table">
                <tbody>
                <tr>
                    <th>{% trans 'years'%}</th>
                    <td id="Ayear"></td>
                </tr>
                <tr>
                    <th>{% trans 'Entry Time'%}</th>
                    <td id="Hiredday"></td>
                </tr>
                <tr>
                    <th>{% trans 'working age' %}</th>
                    <td id="gongling"></td>
                </tr>
                <tr>
                    <th>{% trans 'Can take annual leave'%}</th>
                    <td id="canused"></td>
                </tr>
                <tr>
                    <th>{% trans 'already annual leave'%}</th>
                    <td id="Hasused"></td>
                </tr>
                <tr>
                    <th id="DayOrHour"></th>
                    <td id="days"></td>
                </tr>
                <tr>
                    <th>{% trans 'Annual leave clear date'%}</th>
                    <td id="Ann_restart"></td>
                </tr>
                </tbody>
            </table>
        </div>
    </fieldset>
{% endblock %}
{% block extrjs %}
    <script>
        {% autoescape off %}
            var user_info={{ data }}
        {% endautoescape %}
        $(function () {
            $('#Ayear')[0].innerText = user_info.Ayear
            $('#Hiredday')[0].innerText = user_info.Hiredday
            $('#gongling')[0].innerText = user_info.gongling
            $('#canused')[0].innerText = user_info.canused
            $('#Hasused')[0].innerText = user_info.Hasused
            if(user_info.unit == 1){
                $('#DayOrHour')[0].innerText = "{% trans 'Annual holiday hours'%}"
            }
            else{
                $('#DayOrHour')[0].innerText = "{% trans 'Annual holiday days'%}"

            }
            $('#days')[0].innerText = user_info.days
            $('#Ann_restart')[0].innerText = user_info.Ann_restart
        })
    </script>
{% endblock %}