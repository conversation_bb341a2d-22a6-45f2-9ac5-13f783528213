#!/usr/bin/env python
#coding=utf-8

import datetime
from django.urls import re_path as url
from django.core.paginator import Paginator
from django.http import JsonResponse
from mysite.asset.models import InventorySheet

def get_data_info(request):
    info_type = request.GET.get('info_type')
    st = request.GET.get('st', '')
    et = request.GET.get('et', '')
    if st:
        st = datetime.datetime.strptime(st, '%Y-%m-%d')
    if et:
        et = datetime.datetime.strptime(et, "%Y-%m-%d")
        et = et + datetime.timedelta(days=0, hours=23, minutes=59, seconds=59)
    offset = int(request.GET.get('page', 1))
    limit = int(request.GET.get('limit', 10))
    c_list = []

    if info_type == 'inventory':  # 资产盘点
        uname = request.employee['name']
        check_status = request.GET.get('status')
        if check_status:
            check_status = check_status.split(',')
        else:
            check_status = (2,)
        inventory_list = InventorySheet.objects.filter(check_status__in=check_status, status=1, executor__contains=uname).order_by('-creat_time')
        if st:
            inventory_list = inventory_list.filter(creat_time__gte=st)
        if et:
            inventory_list = inventory_list.filter(creat_time__lte=et)
        p = Paginator(inventory_list, limit, allow_empty_first_page=True)
        iCount = p.count
        if iCount < (int(offset) - 1) * int(limit):
            offset = 1
        pp = p.page(offset)
        for inventory in pp.object_list:
            data_list = {}
            data_list['id'] = inventory.id
            data_list['name'] = inventory.name
            data_list['responsible_person'] = inventory.responsible_person
            data_list['creat_time'] = inventory.creat_time.strftime('%Y-%m-%d %H:%M:%S')
            data_list['executor'] = inventory.executor
            data_list['remark'] = inventory.remark
            data_list['status'] = inventory.get_status_display()
            data_list['inventory_model'] = inventory.get_inventory_model_display() or ''
            c_list.append(data_list)
        return JsonResponse({'code':0, 'data': c_list, 'count': len(inventory_list)})

urlpatterns = [
    url(r'^get_data_info/$', get_data_info), #获取数据
]