# Generated by Django 4.2.16 on 2024-09-25 16:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0070_ademployee_remarks_announcement_ann_type'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='iclock',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'permissions': (('pause_iclock', 'Pause device'), ('resume_iclock', 'Resume a resumed device'), ('reloaddata_iclock', 'Upload data again'), ('reloadlogdata_iclock', 'Upload transactions again'), ('info_iclock', 'Refresh device information'), ('reboot_iclock', 'Reboot device'), ('loaddata_iclock', 'Upload new data'), ('cleardata_iclock', 'Clear data in device'), ('clearlog_iclock', 'Clear transactions in device'), ('devoption_iclock', 'Set options of device'), ('unlock_iclock', 'Output unlock signal'), ('unalarm_iclock', 'Terminate alarm signal'), ('synctime_iclok', 'Synchronize device time'), ('attdataProof_iclock', 'Attendance data proofreading'), ('toDevWithin_iclock', 'Transfer to the device templately'), ('mvToDev_iclock', 'Move employee to a new device'), ('AutoToDev_employee', 'Auto transfer employee to the device'), ('to_dev_employee', 'Transfer employee to the device'), ('to_dev_belong_att_employee', 'Transfer personnel to belong attendance equipment'), ('to_dev_belong_acc_employee', 'Transfer personnel to home access control equipment'), ('to_dev_employee_pic', 'Transfer employee PIC to the device'), ('to_dev_employee_temp', 'Transfer to the device templately'), ('del_emp_from_dev', 'Deleted from the device employee'), ('del_emp_from_acc_dev', 'Remove from home access control device'), ('Upload_AC_Options', 'Upload AC Options'), ('Upload_User_AC_Options', 'Upload User AC Options'), ('deptEmptoDev_iclock', 'Transfer employee of department to the device'), ('deptEmptoDelete_iclock', 'Delete employee from the device'), ('browselogPic', 'browse logPic'), ('deptEmpDelFromDev_iclock', 'Remove people or feature templates from device'), ('Upload_pos_all_data', 'Upload All Data'), ('Upload_pos_Merchandise', 'Upload Merchandise'), ('Upload_pos_Meal', 'Upload Meal'), ('Upload_Iclock_Photo', 'Upload Iclock Photo'), ('delDevPic_iclock', 'delDevPic employee'), ('set_device_asp', 'set divice ascription'), ('consumer_record_detection', 'Consumer Record Detection'), ('sync_locker_data', 'Sync locker data'), ('download_attphoto', 'Download attendance photos')), 'verbose_name': 'device', 'verbose_name_plural': 'device'},
        ),
        migrations.AlterField(
            model_name='device_options',
            name='SN',
            field=models.ForeignKey(db_column='sn_id', db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock'),
        ),
    ]
