{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <div class="x-body">
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'StartDate' %}" name="start" id="start" autocomplete="off"></div>
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'End Date' %}" name="end" id="end" autocomplete="off"></div>
        <a class="layui-btn search_btn" data-type="reload" id="search_menu">{% trans 'Inquire' %}</a>
        <div style="padding-top: 20px;">
            <button class="layui-btn" onclick="x_admin_show('{% trans 'Add' %}{% trans 'Book meal' %}','/selfservice/ipos/bookdinner/_new_/',650,500)"><i
                class="layui-icon"></i>{% trans 'Add' %}
            </button>
            <button class="layui-btn layui-btn" onclick="delAll()"><i class="layui-icon"></i>{% trans 'Delete' %}</button>
            <a class="layui-btn layui-btn-small" style="margin-top:3px;float:right"
            href="javascript:location.replace(location.href);" title="{% trans 'Reload' %}">
            <i class="icon iconfont" style="line-height:30px">&#xe6aa;</i></a>
        </div>
        <table class="layui-hide" id="demo" lay-filter="demo"></table>
    </div>
{% endblock %}
{% block extrjs %}
    <script>
        layui.use(['table', 'laydate'], function () {
            var table = layui.table;
            var laydate = layui.laydate;
            var form = layui.form;
            var startDate = laydate.render({
                elem: '#start',
                type: 'date',
                done: function (value, date) {
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1, //关键
                        date: date.date,
                        hours: 0,
                        minutes: 0,
                        seconds: 0
                    };
                }
            });
            var endDate = laydate.render({
                elem: '#end',
                type: 'date',
                done: function (value, date) {
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date,
                        hours: 0,
                        minutes: 0,
                        seconds: 0
                    }
                }
            });

            var ins1 = table.render({
                elem: '#demo'
                , url: '/selfservice/ipos/get_data_info/?info_type=bookdinner'       // 数据接口
                , cellMinWidth: 50 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                , id: 'id'
                , page: true
                , cols: [[
                    {type: 'checkbox'}
                    , {field: 'pin', width: 110, title: "{% trans 'BadgeNumber' %}"}
                    , {field: 'username', width: 90, title: "{% trans 'Name' %}"}
                    , {field: 'deptname', width: 110, title: "{% trans 'Department' %}"}
                    , {field: 'card', width: 140, title: "{% trans 'Id Card' %}"}
                    , {field: 'book_time', width: 140, title: "{% trans 'bookdinner date' %}", sort: true}
                    , {field: 'book_meal', width: 110, title: "{% trans 'meal' %}"}
                    //, {field: 'book_num', width: 110, title: "{% trans 'copies' %}"}
                    , {field: 'applydate', width: 180, title:  "{% trans 'Apply Time' %}"}
                ]]
            });

            var $ = layui.$, active = {
                reload: function () {
                    var start = $("input[name='start']").val();
                    var end = $("input[name='end']").val();
                    table.reload('id', {
                        where: {
                            st: start,
                            et: end
                        }
                    });
                }
            };

            $('#search_menu').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
            table.on('sort(demo)', function(obj){ //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                  console.log(obj.field); //当前排序的字段名
                  console.log(obj.type); //当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
                  //尽管我们的 table 自带排序功能，但并没有请求服务端。
                  //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
                table.reload('id', {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。 layui 2.1.1 新增参数
                    ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                      field: obj.field //排序字段   在接口作为参数字段  field order
                      ,order: obj.type //排序方式   在接口作为参数字段  field order
                    },
                  });
            });
        });

        function delAll() {
            var checkStatus = layui.table.checkStatus('id');
            if (checkStatus.data.length ==0) {
                return layer.msg("{% trans 'Select the record that you want to delete' %}", {icon: 5, time: 2000});
            }
            var ids = checkStatus.data[0].id;
            for (var i = 1; i < checkStatus.data.length; i++) {
                ids += "," + checkStatus.data[i].id;
            }
            layer.confirm("{% trans 'Are you sure you want to delete?' %}", function (index) {
                $.ajax({
                    url: "/selfservice/ipos/get_data_info/?info_type=bookdinner&type=2&data_id=" + ids,
                    type: "POST",
                    dataType: "json",
                    success: function (data) {
                        if (data.code == 1) {//删除这一行
                            layer.msg("{% trans 'successfully deleted' %}", {icon: 6});
                            layui.table.reload('id');
                        }
                        else if(data.code == 2){
                            layer.msg(data.msg, {icon: 5});
                        }else {
                            layer.msg("{% trans 'Delete fail!' %}", {icon: 5});
                        }
                    }
                });
            });
        }
    </script>

{% endblock %}
