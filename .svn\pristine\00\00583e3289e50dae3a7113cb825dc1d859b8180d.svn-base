# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2018-07-02 08:22
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0011_auto_20180615_1519'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='aliwxfulllog',
            options={'default_permissions': ('browse',), 'verbose_name': '\u7f51\u7edc\u5145\u503c\u8868', 'verbose_name_plural': '\u7f51\u7edc\u5145\u503c\u8868'},
        ),
        migrations.AlterUniqueTogether(
            name='aliwxfulllog',
            unique_together=set([('tradeno',)]),
        ),
        migrations.AlterUniqueTogether(
            name='cardcashsz',
            unique_together=set([('sn', 'hide_column', 'sys_card_no', 'money', 'checktime', 'wallet_type', 'money_B', 'cardserial')]),
        ),
    ]
