{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
<script>
{% block tblHeader %}
hd='({% trans 'After the submission of the operation need to be about half a minute or so of device in the entry into force' %})'
hasImport={% if user|HasPerm:"ipos.import_ordermealmessage" %}true{% else %}false{% endif %}
//jqOptions=copyObj(jq_Options)
jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='OrderMealMessage';
jqOptions[g_activeTabID].pager = "id_pager_"+tblName[g_activeTabID]
jqOptions[g_activeTabID].sortname='Pubdate';
options[g_activeTabID].dlg_width=1000;
options[g_activeTabID].dlg_height=560;


function ShowDeptTree_employee(page,tag,obj)
{
	var d_url="/ipos/getData_dining/?func=diningtree";

	var setting = {
            check: {enable: true,chkStyle: "checkbox",chkboxType: { "Y": "", "N": "" }},
	    async: {
			    enable: true,
			    url: d_url,
			    autoParam: ["id"]
		    }
	};
	$.fn.zTree.init($("#showTree_"+page), setting,null);
	if(tag){
		var zTree = $.fn.zTree.getZTreeObj("showTree_"+page);
		zTree.setting.check.enable = false;
		zTree.setting.callback.onClick = function onClick(e, treeId, treeNode){
            var id=treeNode.id;
            $("#department",obj).val(treeNode.name);
            $("#id_dining",obj).val(id);
            $("input[type='hidden'][name='dining']",obj).val(id);
            hideDeptment();
		}
	}
}

function searchshow_message(){
    var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v="";
    var error = validate_form_msg();
    var st = $("#id_ComeTime_Message").val();
    var et = $("#id_EndTime_Message").val();
	if (!error){
        urltime = "&Pubdate__gte="+st+"&Pubdate__lte="+et;
    }else{
	    alert('{% trans "Start time cannot be greater than end time" %}');
    }
    var url="/ipos/data/OrderMealMessage/?q="+encodeURI(v)+urltime;
	savecookie("search_urlstr",url);
    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function beforePost_OrderMealMessage(obj,actionName){
    var tinymce_content = tinyMCE.activeEditor.getContent();
    $("#id_Content",obj).val(tinymce_content);
    if(tinymce_content==""){
        $("#id_error",obj).css("display","block");
        $("#id_error",obj).html("<ul class='errorlist'><li>{% trans 'Content is a required field' %}</li></ul>");
        return false;
    }
}

function process_dialog_OrderMealMessage(obj,flag){
    var tpickerOptions=copyObj(datetimepickerOptions);
    tpickerOptions.showSecond=true
    tpickerOptions.timeFormat='HH:mm:ss'
    $("#id_Pubdate",obj).datetimepicker(tpickerOptions);
    $("#id_Content",obj).after("<div id='tinydiv'></div>");
    $("#id_Author",obj).val('{{ request.user.username }}');

    if(flag=='edit'){
        $('#id_code',obj).attr('readonly','True').css('background','#E3E3E3');
    }
    f=$(obj).find("#id_edit_form").get(0);
    $(f).validate({
        rules: {
                code: {required:true},
                Title: {required:true},
                dining: {required:true},
            }
        });
}

function afterPost_OrderMealMessage(flag, obj){
    $('#id_code',obj).val(auto_increase_number($('#id_code',obj).val()));
    $('#id_Title',obj).val('');
    tinyMCE.activeEditor.setContent(' ');
}

function process_dialog_again_OrderMealMessage(obj){
    $("#id_Content",obj).hide();
    if(typeof(tinyMCE) !== 'undefined') {
        //解决tinymce只初始化一次的问题
        var length = tinyMCE.editors.length;
        for (var i=length; i>0; i--) {
          tinyMCE.editors[i-1].remove();
        };
    }
    entry_date = moment().format("YYYY-MM-DD HH:mm:ss");
    tinymce.init({
        selector:'#tinydiv',
        language:'zh_CN',//注意大小写
        width: 800,
        height: 300,
        menubar: 'edit insert view format table',
        toolbar1: 'undo redo fontsizeselect formatselect forecolor backcolor bold italic alignleft aligncenter alignright alignjustify underline strikethrough alignnone',
        toolbar2: 'outdent indent axupimgs fullscreen',
        plugins: 'image axupimgs fullscreen',
        images_upload_handler: function (blobInfo, succFun, failFun) {
            var xhr, formData;
            var file = blobInfo.blob();//转化为易于理解的file对象
            max_file_size = 2*1080*1080;
            if (file.size > max_file_size){
                failFun("{% trans 'Picture cannot exceed 2M size' %}");
                return;
            };
            xhr = new XMLHttpRequest();
            xhr.withCredentials = false;
            xhr.open('POST', '/image/image_upload');
            xhr.onload = function() {
                var json;
                if (xhr.status != 200) {
                    failFun('HTTP Error: ' + xhr.status);
                    return;
                }
                json = JSON.parse(xhr.responseText);
                if (!json || typeof json.location != 'string') {
                    failFun('Invalid JSON: ' + xhr.responseText);
                    return;
                }
                succFun(json.location);
            };
            formData = new FormData();
            formData.append('entry_date', entry_date);
            formData.append('file', file, file.name );//此处与源文档不一样
            xhr.send(formData);
        },
        paste_data_images: false,
        automatic_uploads:false,
    }).then( resolve=>{
        // init完成后，回调
        tinymce.editors[0].setContent($("#id_Content",obj).text())
  });
}

$(function(){
    entry_date = moment().format("YYYY-MM-DD HH:mm:ss");
    $("#id_ComeTime_Message").val(moment().startOf('month').format('YYYY-MM-DD'));
    $("#id_ComeTime_Message").datepicker(datepickerOptions);
    $("#id_EndTime_Message").val(moment().endOf('month').format('YYYY-MM-DD'));
    $("#id_EndTime_Message").datepicker(datepickerOptions);
	$("#"+g_activeTabID+" #queryButton").hide()

    $("#"+g_activeTabID+" #id_newrec").click(function(event){
        /*
        var treeObj = $.fn.zTree.getZTreeObj("showTree_"+g_activeTabID);
        var nodes = treeObj.getSelectedNodes();
        if(nodes.length==0){alert('{% trans "Please first expand the restaurant tree on the left and select the restaurant" %}');return;}
        */
        processNewModel();
	});
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchshow_message();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13)
	    searchshow_message();
	});
    //$("#"+g_activeTabID+" #id_import").iMenu();
	//$("#"+g_activeTabID+" #id_third").html("");
});

function strOfData_Food(data)
{
	return stripHtml(data.code)+" "+stripHtml(data.name);
}

function validate_form_msg(){   //验证表单的合法性(人员(可不选，在计算项目中计算选中部门的人员，其他的就默认值0)、开始时间、结束时间)
        var t_ComeTime=$("#id_ComeTime_Message").val();
        var cTime=t_ComeTime.split("-");
        var t_EndDate=$("#id_EndTime_Message").val();
        var eTime=t_EndDate.split("-");
        cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
        edate=new Date(parseInt(eTime[0],10),parseInt(eTime[1],10)-1,parseInt(eTime[2],10));
        var days=(edate.valueOf()-cdate.valueOf())/(1000*3600*24)+1;
        if(cdate>edate || t_ComeTime=="" || t_EndDate==""||!valiDate(t_ComeTime)||!valiDate(t_EndDate)){
                return 1;
        }else{
                return 0
        }
}

{% endblock %}
</script>

{% block otherQuery %}
    <div class="s-info left" id="time_area">
        <label  >{% trans 'Begin Date' %}</label>
        <input type='text' name='ComeTime_msg' maxlength='10' id='id_ComeTime_Message' style='width:80px !important;'>
        <label >{% trans 'End Date' %}</label>
        <input type='text' name='EndTime_msg' maxlength='10' id='id_EndTime_Message' style='width:80px !important;'>
    </div>
{% endblock %}

{% block sear_area %}
<div class="s-info left" id="sear_area" style="min-width:200px;">
    <div class="nui-ipt nui-ipt-hasIconBtn " >
        <input id="searchbar" class="search-input" type="text"  value="{{ cl.searchHint }}" role='defvalue' autocomplete="off" style="width: 154px;"/>
        <span id ="queryButton" class="nui-ipt-iconBtn">
            <b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
        </span>
    </div>
    <span id="searchButton" style="margin-left: 15px"><a class="m-btn  zkgreen rnd mini">{% trans 'Query' %}</a></span>
</div>

{% endblock %}

{% block importOp %}

{% endblock %}

{% block aDelete %}
    {% if request|reqHasPerm:"delete" %}
        <LI id="aDelete" onclick="batchOp('?action=del',itemCanBeDelete,'{% trans "Delete" %}');"><SPAN
                class="icon iconfont icon-shanchu"></SPAN>{% trans "Delete" %}</LI>
    {% endif %}
{% endblock %}

