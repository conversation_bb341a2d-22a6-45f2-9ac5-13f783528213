{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <script src="{{ MEDIA_URL }}js/mui.picker.min.js"></script>
    <script src="{{ MEDIA_URL }}js/mui.view.js "></script>
    <link href="{{ MEDIA_URL }}css/mui.picker.min.css" rel="stylesheet"/>
    <style type="text/css">
        h5 {
            margin: 5px 7px;
        }
        .mui-btn {
            background-color: #7ac143;
            border:0px;
            font-size: 16px;
            padding: 8px;
            margin: 3px;
        }
    
    </style>

{% endblock %}
{% block content %}
    <header class="mui-bar mui-bar-nav">
        <a id="back_a" href="/iapp/att/my_info/" class="mui-icon mui-icon-left-nav mui-pull-left"></a>
        <h1 class="mui-title">{% trans 'Change password' %}</h1>
    </header>
    <div class="mui-content">
        <h5>{% trans 'Original password: ' %}</h5>
        <div class="mui-input-row mui-password">
            <input id='oldpwd' type="password" />
        </div>
        <h5>{% trans 'New password:' %}</h5>
        <div class="mui-input-row mui-password">
            <input id='newpwd' type="password" />
        </div>
        <h5>{% trans 'New password confirmation:' %}</h5>
        <div class="mui-input-row mui-password">
            <input id='newpwd2' type="password" />
        </div>
        <div class="mui-content-padded">
            <button id='saveBtn' class="mui-btn mui-btn-block mui-btn-primary">{% trans 'Submit' %}</button>
        </div>
    </div>
{% endblock %}
{% block extrjs %}
    <script type="text/javascript">
        var single_page = sessionStorage.getItem("single_page");
        (function($){
            if (single_page == '1'){
                document.getElementById('back_a').setAttribute('href','/wxapp/my_info/');
            }
            $.init();

            //提交
            document.getElementById("saveBtn").addEventListener('tap',function(){
                oldpwd = $('#oldpwd')[0].value
                newpwd = $('#newpwd')[0].value
                newpwd2 = $('#newpwd2')[0].value
                if (newpwd!=newpwd2){
                    mui.toast('{% trans 'The new password entered twice is different!' %}')
                    return false
                }
                //点击响应逻辑
                $.ajax('/iclock/accounts/password_change/',{
                    data:{
                        old_password: oldpwd,
                        new_password1: newpwd,
                        new_password2: newpwd2,
                    },
                    dataType:'json',
                    type:'post',
                    timeout:10000,
                    headers:{'Content-Type':'application/x-www-form-urlencoded'},
                    success:function(data){
                        $.toast(data.message);  //自动消失提示框
                        if (data.ret==0){
                            if (is_wxoa()){
                                alert('{% trans 'Password modification successful, please re-enter!' %}');
                                wx.closeWindow();
                            }
                            else {
                                sessionStorage.clear();
                                mui.alert('{% trans 'Please log in again with the new password later' %}', '{% trans 'Password modification successful' %}', function() {
                                     mui.openWindow({
                                         id: 'logout',
                                         url: '/iapp/logout/',
                                     });
                                });
                            }
                        }
                    },
                    error:function(xhr,type,errorThrown){
                        //异常处理；
                        // console.log(type);
                    }
                });

            });

        })(mui);
    </script>
{% endblock %}