#!/usr/bin/env python
#coding=utf-8
from django.template import Context, RequestContext, Template, TemplateDoesNotExist
from django.utils.translation import gettext_lazy as _
from mysite.utils import *
from mysite.cab import listFile
import os
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from mysite.iclock.models import create<PERSON>hum<PERSON><PERSON>, format<PERSON><PERSON>,employee
from django.contrib.auth.decorators import login_required,permission_required
from mysite.iclock.iutils import userIClockList 
#imgUrlOrg=getUploadFileName(device.SN, pin, fname)
from django.core.paginator import Paginator
from mysite.iclock.datautils import hasPerm
def listPic(path, pattern):
	picPath=os.path.split(getUploadFileName(path,"",""))[0]
	flist=listFile(picPath, pattern)
	flist.sort(reverse=True)
	return flist

def listDir(request, path):
	sn_list=None
	valid_sn=None
	if not request.user.is_superuser:
		sn_list=AuthedIClockList(request.user)
	if not path[0]:
		valid_sn=sn_list
	elif (sn_list!=None) and (path[0] not in sn_list):
		return []
	path="/".join(path)
	path=getUploadFileName(path,"","")
	for root,dirs,files in os.walk(path):
		if "thumbnail" in dirs:
			dirs.remove("thumbnail")
		if valid_sn==None: return dirs
		return [i for i in dirs if i in valid_sn]
	return []

def get_pictures_of_device(request):
	Result={}
	datas=[]
	d={}
	d['id']=1
	
	ttime=request.GET.get('ttime')
	sn=request.GET.get('key')
	mon=ttime[:6]
	cmap_path="%s/%s/" %(sn,mon)
	flist=listPic(cmap_path,'*.jpg')
	if len(ttime)==8:
		templist=[]
		for t in flist:
			if ttime in t:
				templist.append(t)
		flist=templist
	offset = int(request.POST.get('page', 1))

	limit= int(request.POST.get('rows', 30))
	
	p=Paginator(flist, limit,allow_empty_first_page=True)
	item_count=p.count
	if item_count<(offset-1)*limit:
		offset=1
	page_count=p.num_pages
	pp=p.page(offset)
	j=0
	for t in pp:
		fname=os.path.split(t)[1]
		s_name=os.path.splitext(fname)[0]
		lname=s_name.split('-')
		tm=lname[0]
		pin=''
		stime='%s-%s %s:%s:%s'%(tm[4:6],tm[6:8],tm[8:10],tm[10:12],tm[12:14])
		if len(lname)==2:#通过照片
			pin=lname[1]
			try:
				obj=employee.objByPIN(pin)
			except:
				obj=None
			if obj:
				pin=pin+'  '+obj.EName or ''
			file_url = getStoredFileURL('upload/%s/%s'%(sn,mon), None, fname)
			d['p%d'%j]=u"<div><img src=%s style='height:160px;'/></div><div>%s<br>%s</div>" %(file_url,pin,stime)
		else:
			file_url = getStoredFileURL('upload/%s/%s'%(sn,mon), None, fname)
			d['p%d'%j]=u"<div style='border:1px solid red;'><img src=%s  style='height:160px;'/></div><div>%s<br>%s</div>" %(file_url,'',stime)
						
		j+=1
		if j==6:
			datas.append(d.copy())
			d={}
			j=0
			d['id']=item_count/6
	if d:
		datas.append(d.copy())
	
	Result['page']=offset
	Result['records']=item_count
	Result['total']=page_count
	Result['rows']=datas
	return Result

def get_pictures_of_employee(request,cc):

	datas=[]
	j=0
	d={'id':j}
	can_edit=hasPerm(request.user, employee, 'change')
	for t in cc['latest_item_list']:
		fname=t.PIN+'.jpg'
		pin=u'%s %s'%(t.PIN,t.EName or '')
		if can_edit:
			pin="""<a class='can_edit'  href='#' onclick='javascript:editclick(%s);'>%s</a>"""%(t.id,pin)
		
		
		
		cmapfile=getStoredFileName('photo', None, "%s"%fname)



		if os.path.exists(cmapfile):
			file_url = getStoredFileURL('photo', None, fname)
			d['p%d'%j]=u"<div><img src=%s  style='height:180px;'/></div><div>%s</div>" %(file_url,pin)
			#以下为泰康保险定制
			#if hasattr(settings,'SHOWEMPPHOTO') and settings.SHOWEMPPHOTO==0:
			#	if BioData.objects.filter(UserID=t.id,bio_type=bioFace).count()==0:
			#		d['p%d'%j]=u"<div><img src=/media/img/transaction/user_default.gif style='height:180px;'/></div><div>%s</div>"%(pin)



		else:
			d['p%d'%j]=u"<div><img src=/media/img/transaction/user_default.gif style='height:180px;'/></div><div>%s</div>"%(pin)
		
		
		
						
		j+=1
		if j==6:
			datas.append(d.copy())
			d={}
			j=0
			d['id']=j
	if d:
		datas.append(d.copy())
	return dumps1(datas)

def get_pictures_of_storage(request, cc):
	datas = []
	j = 0
	d = {'id': j}

	for t in cc['latest_item_list']:
		code = t.code
		if t.status == 1:
			pin = u'%s %s' % (t.user.PIN, t.user.EName or '')
			dept = u'%s' % t.user.DeptID.DeptName
			state = _(u"In use")
			d['p%d' % j] = u"<div style='height:100px;background:#7cdcff;color:#000000;position:relative;margin-right:10px;margin-bottom:10px'><div style='position:absolute;top:0;right:0;font-size:17px;font-weight:bold;'>%s</div><div style='text-align:center;margin-bottom:20px'>%s</div><div style='text-align:center;'>%s</div><div style='text-align:center;'>%s</div></div>" \
							 % (state, code, pin, dept)
		else:
			pin = ''
			dept = ''
			state = _(u"Leave unused")
			d['p%d' % j] = u"<div style='height:100px;background:#54da93;color:#000000;position:relative;margin-right:10px;margin-bottom:10px'><div style='position:absolute;top:0;right:0;font-size:17px;font-weight:bold;'>%s</div><div style='text-align:center;margin-bottom:20px'>%s</div><div style='text-align:center;'>%s</div><div style='text-align:center;'>%s</div></div>" \
							 % (state, code, pin, dept)
		j += 1
		if j == 8:
			datas.append(d.copy())
			d = {}
			j = 0
			d['id'] = j
	if d:
		datas.append(d.copy())
	return dumps1(datas)


@login_required
def index(request, path):
	if request.method=='POST':
		Result={}
		if path=='devpictures':
			Result=get_pictures_of_device(request)
		else:
			pass
		
		
		#rs="{"+""""page":"""+str(Result['page'])+","+""""total":"""+str(Result['page_count'])+","+""""records":"""+str(Result['item_count'])+","+""""rows":"""+dumps(Result['datas'])+"""}"""
		return getJSResponse(Result)
