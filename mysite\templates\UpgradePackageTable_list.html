{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
<script>
{% block tblHeader %}
jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='UpgradePackageTable';
jqOptions[g_activeTabID].sortname='-id';
jqOptions[g_activeTabID].pager="id_pager_UpgradePackageTable"
options[g_activeTabID].dlg_width=525;
options[g_activeTabID].dlg_height=400;
options[g_activeTabID].edit_col=1;
dtFields = "{{ dtFields }}";
$(function(){

    smenu="<ul><li  class='subnav_on' onclick=submenuClick('/iclock/data/UpgradePackageTable/',this);><a href='#'>{% trans "Maintenance Maintenance" %}</a></li></ul>"

	$("#"+g_activeTabID+" #id_newrec").click(function(event){
		processNewModel();
	});
	$("#"+g_activeTabID+" #id_custom").remove()
	$("#"+g_activeTabID+" #queryButton").hide()
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowUpgradePackageTable();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13)
	    searchShowUpgradePackageTable();
	});
	$("#"+g_activeTabID+" #searchbar").val('{% trans "OA单号,设备序列号" %}')
});
function process_dialog_UpgradePackageTable(htmlObj){

	f=$(htmlObj).find("#id_edit_form").get(0)
	$(f).validate({
				rules: {
					"serial_number":{"required":true,},
					"parameter_name":{"required":true,},
					"oa_number":{"required":true,}
				}
	});
}
function strOfData_UpgradePackageTable(data)
{
	return stripHtml(data.oa_number);
}

//模糊查询
function searchShowUpgradePackageTable(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
	var url=g_urls[g_activeTabID]+"?q="+encodeURI(v)
	savecookie("search_urlstr",url);
	$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function afterPost_UpgradePackageTable(flag, obj) {
    $('#id_user',obj).val("");
    $('#id_oa_number',obj).val("");
    $('#id_serial_number',obj).val("");
    $('#id_parameter_name',obj).val("");

    reloadData()
}

{% endblock %}

</script>

{% block newrec %}
{% endblock %}

{% block aDelete %}
{% endblock %}

{% block importOp %}
{% endblock %}

