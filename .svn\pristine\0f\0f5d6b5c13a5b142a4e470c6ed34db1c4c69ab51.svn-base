#!/usr/bin/python
# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _
from django.db.models import Q
import time

from rest_framework import exceptions
from rest_framework import serializers

from mysite.utils import *
# from mysite.base.models import GetParamValue

from mysite.ipos.models import *


class CardCashSZSerializer(serializers.HyperlinkedModelSerializer):
    meal_name = serializers.CharField(source='meal.name', default=_(u"other"), allow_null=True)
    dining_name = serializers.CharField(source='dining.name', default='', allow_null=True)
    log_flag = serializers.CharField(source='get_log_flag_display', default='', allow_null=True)
    pos_model = serializers.CharField(source='get_pos_model_display', default='', allow_null=True)
    wallet_type = serializers.CharField(source='get_wallet_type_display', default='', allow_null=True)
    class Meta:
        model = CardCashSZ
        fields = ('money', 'money_B', 'blance', 'allow_balance', 'checktime', 'meal_name', 'dining_name', 'log_flag',
                  'card', 'sn', 'pos_model', 'wallet_type')

class AllowanceSerializer(serializers.HyperlinkedModelSerializer):
    is_pass = serializers.CharField(source='get_is_pass_display', default='', allow_null=True)
    is_ok = serializers.CharField(source='get_is_ok_display', default='', allow_null=True)
    class Meta:
        model = Allowance
        fields = ('money', 'is_pass', 'is_ok' , 'allow_date', 'receive_date', 'valid_date')

class ICConsumerListSerializer(serializers.HyperlinkedModelSerializer):
    blance = serializers.CharField(source='balance', default='', allow_null=True)
    checktime = serializers.CharField(source='pos_time', default='', allow_null=True)
    meal_name = serializers.CharField(source='meal.name', default=_(u"other"), allow_null=True)
    dining_name = serializers.CharField(source='dining.name', default='', allow_null=True)
    log_flag = serializers.CharField(source='get_log_flag_display', default='', allow_null=True)
    sn = serializers.CharField(source='dev_sn', default='', allow_null=True)
    pos_model = serializers.CharField(source='get_pos_model_display', default='', allow_null=True)
    wallet_type = serializers.CharField(source='get_wallet_type_display', default='', allow_null=True)
    class Meta:
        model = ICConsumerList
        fields = ('money', 'money_B', 'blance', 'allow_balance', 'checktime', 'meal_name', 'dining_name', 'log_flag',
                  'card', 'sn', 'pos_model', 'wallet_type')