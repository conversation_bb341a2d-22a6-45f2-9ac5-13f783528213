{% load i18n %}
{% load iclock_tags %}

<div class='alert' style='height: 20px;margin-left: 5px;margin-top: 5px;'>{% trans 'Warning: Data cannot be restored after it is initialized.When deleting the card issuance, you need to delete the other tables together.' %}</div>

<div id='id_option1' style='padding: 20px;'>
        <form id='id_options_inits' method='post'>
            <div class='ui-widget-header' style='width:700px;height: 28px;margin-left: 100px;margin-top: 10px;'><h3 style="padding-top: 8px;padding-left: 10px;"><a>{% trans 'Consumer information initialization' %}</a></h3></div>
            <table style="margin-left: 100px;margin-top: 20px;">
			<tr>
				<td style='padding-bottom: 5px'><label for='id_subsidy' ></label><input  type='checkbox' maxlength='30'  id='id_subsidy_rec' name='subsidy'/> {% trans 'Subsidy Form' %}</td>
			</tr>			

			<tr>
				<td style='padding-bottom: 5px'><label for='id_consumer' ></label><input  type='checkbox' maxlength='30'  id='id_consumer' name='consumer'/> {% trans 'Consumer Schedule' %}</td>
			</tr>			
			
			<tr>
				<td style='padding-bottom: 5px'><label for='id_card' ></label><input  type='checkbox' maxlength='30'  id='id_card' name='card' onclick="cardOnclick(this)"/> {% trans 'Card Form' %}</td>
			</tr>
			
			<tr>
				<td style='padding-bottom: 5px'><label for='id_card_cash' ></label><input  type='checkbox' maxlength='30'  id='id_card_cash' name='card_cash'/> {% trans 'Card cash receipts and payments' %}</td>
			</tr>
            
        </table></form>
	<div><ul class='oklist'><li id='id_ok_option_inits' style='display:none;'></li></ul></div>
	<div><ul class='errorlist'><li id='id_error_option_inits' style='display:none;'></li></ul></div>
	<div style="margin-left: 100px;margin-top: 30px;"><input id='id_inits' type='button'  class='m-btn  zkgreen rnd' value='{% trans "Init database" %}'/></div>
	</div>
	
<script>

function createOptionDlg(){
	var html="<div id='option_inits' class='required' align='center' style='top:10px;'><form id='dlg_option_inits'><table>"
			+"<tr><td><label>{% trans 'password' %}</label></td>"
			+"<td><input name='password' id='id_password' type='password'></td></tr>"
			+"<td><input name='id_hidden' id='id_hidden' type='text' style='display:None'></td></tr>"
			+"</table></form></div>"
	var title=""
	var obj=$(html)
	
	title="{% trans "Please enter your password:" %}"		
	$(html).dialog({title:title,resizable:false,
					modal:true,
					width:260,
					height:150,
					buttons:[{id:'id_check',text:'{% trans "save and return" %}',click:function(){SaveOptions()}},
							 {text:'{% trans "Return" %}',click:function(){$(this).dialog("destroy"); }}],
					close:function(){$(this).dialog("destroy");}});
	
	$('#id_password').on('keypress',function(event){
		if(event.keyCode == 13)
		{   
		    SaveOptions()
		}
	});
}

SaveOptions = function()
{
    var data = {
       "pwd": $("#id_password").val(),
       "subsidy": $('#id_subsidy_rec').prop('checked') ? 'on' : '',
       "consumer": $('#id_consumer').prop('checked') ? 'on' : '',
       "card": $('#id_card').prop('checked') ? 'on' : '',
       "card_cash": $('#id_card_cash').prop('checked') ? 'on' : '',
   }
   $.blockUI({title:'',theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'In operation...' %}</br></h1>'});
        			$.ajax({
        				type: "POST",
        				url:"/base/isys/option_inits/",
        				data: data,
        				dataType:"json",
        				success:function(retdata){
						        ret = retdata.ret	
                                var message=retdata.message		
        						
								if (ret ==0)
								{  	
								    $("#option_inits").remove()
									$("#id_ok_option_inits").html(message).show();
									$("#id_error_option_inits").css('display','none');
								}
                                else
                                {
								    $("#id_error_option_inits").html(message).show(); 
                                    $("#id_ok_option_inits").css('display','none'); 									
								}	
								$.unblockUI();							
        					}
        				});
						

}


function cardOnclick(checkbox){

	if ( checkbox.checked == true){ 
		$("#id_subsidy_rec").prop("checked","checked");
		$("#id_consumer").prop("checked","checked");
		$("#id_card_cash").prop("checked","checked");
	}
	else
	{   
	    $("#id_subsidy_rec").removeAttr("checked");
		$("#id_consumer").removeAttr("checked");
		$("#id_card_cash").removeAttr("checked");
	}
}

$(function()
{
    $("#"+g_activeTabID+" #id_inits").click(function(){
	             	createOptionDlg() 
         		
        		});
});

</script>
 

