{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <div class="x-body">
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'StartDate' %}" name="start" id="start" autocomplete="off"></div>
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'End Date' %}" name="end" id="end" autocomplete="off"></div>
        <a class="layui-btn search_btn" data-type="reload" id="search_menu">{% trans 'Inquire' %}</a>
        <button class="layui-btn" onclick="x_admin_show('{% trans 'self-service register' %}','/selfservice/epidemic/temperature/_new_/',500,300)"><i
                class="layui-icon"></i>{% trans 'self-service register' %}
        </button>
        <a class="layui-btn layui-btn-small" style="margin-top:3px;float:right"
        href="javascript:location.replace(location.href);" title="{% trans 'Reload' %}">
        <i class="icon iconfont" style="line-height:30px">&#xe6aa;</i></a>
        <table class="layui-hide" id="epidemic_query" lay-filter="epidemic_query"></table>
    </div>
{% endblock %}
{% block extrjs %}
    <script>
        layui.use(['table', 'laydate'], function () {
            var table = layui.table;
            var laydate = layui.laydate;
            var startDate = laydate.render({
                elem: '#start',
                type: 'date',
                done: function (value, date) {
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1, //关键
                        date: date.date
                    };
                }
            });
            var endDate = laydate.render({
                elem: '#end',
                type: 'date',
                done: function (value, date) {
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    }
                }
            });
            cols = [[
                {type: 'checkbox'}
                , {field: 'PIN', width: 110, title: "{% trans 'BadgeNumber' %}"}
                , {field: 'EName', width: 100, title: "{% trans 'Name' %}"}
                , {field: 'temperature', width: 100, title: "{% trans 'Temperature' %}"}
                , {field: 'mask_flag', width: 100, title: "{% trans 'Mask Flag' %}"}
                , {field: 'TTime', width: 180, title: "{% trans 'Test Time' %}"}
                , {field: 'data_source', width: 180, title: "{% trans 'Data Source' %}"}
            ]]

            table.render({
                elem: '#epidemic_query'
                , url: '/selfservice/epidemic/get_data_info/?info_type=epidemic_query'       // 数据接口
                , cellMinWidth: 50 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                , id: 'id'
                , page: true
                , cols: cols
            });
            var $ = layui.$, active = {
                reload: function () {
                    var start = $("input[name='start']").val();
                    var end = $("input[name='end']").val();
                    table.reload('id', {
                        where: {
                            st: start,
                            et: end
                        }
                    });
                }
            };

            $('#search_menu').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
            table.on('sort(epidemic_query)', function(obj){ //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                  // console.log(obj.field); //当前排序的字段名
                  // console.log(obj.type); //当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
                  //尽管我们的 table 自带排序功能，但并没有请求服务端。
                  //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
                table.reload('id', {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。 layui 2.1.1 新增参数
                    ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                      field: obj.field //排序字段   在接口作为参数字段  field order
                      ,order: obj.type //排序方式   在接口作为参数字段  field order
                    },
                  });
            });
        });
    </script>
{% endblock %}