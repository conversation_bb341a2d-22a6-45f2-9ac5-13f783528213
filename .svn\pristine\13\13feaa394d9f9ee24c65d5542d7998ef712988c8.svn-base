{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block extrastyle %}
    <script src="{{ MEDIA_URL }}js/mui.picker.min.js"></script>
    <script src="{{ MEDIA_URL }}js/mui.view.js "></script>
    <link href="{{ MEDIA_URL }}css/mui.picker.min.css" rel="stylesheet"/>
    <style type="text/css">
        .mui-segmented-control .mui-control-item {
            line-height: 40px;
            text-align: center;
        }

        .mui-table-view-cell > a:not(.mui-btn) {
            margin: -18px -15px;
        }

        .mui-control-content {
            min-height: 215px;
        }

        .mui-control-content .mui-loading {
            margin-top: 50px;
        }

        html, #slider, .mui-content {
            width: 100%;
            height: 100%;
        }

        body {
            width: 100%;
            height: 100%;
        }

        .mui-slider-group {
            height: calc(100% - 45px);
        }
    </style>
{% endblock %}
{% block content %}
    <header class="mui-bar mui-bar-nav">
        <a href="/iapp/ipos_main/" class=" mui-icon mui-icon-left-nav mui-pull-left"></a>
        <h1 class="mui-title" style='font-size: 16px'>{% trans 'Subsidy' %}</h1>
    </header>
    <div class="mui-content" style="height: 100%;">
        <div id="slider" class="mui-slider" style="height: 100%;">
            <div id="sliderSegmentedControl"
                 class="mui-slider-indicator mui-segmented-control mui-segmented-control-inverted">
                <a class="mui-control-item" href="#item1mobile">{% trans 'Claimed' %}</a>
                <a class="mui-control-item" href="#item2mobile">{% trans 'Unclaimed' %}</a>
            </div>
            <div id="sliderProgressBar" class="mui-slider-progress-bar mui-col-xs-6"></div>
            <div class="mui-slider-group" style="height: 100%;">
                <div id="item1mobile" class="mui-slider-item mui-control-content">
                    <div id="scroll1" class="mui-scroll-wrapper">
                        <div class="mui-scroll">
                            <div class="mui-loading">
                                <div class="mui-spinner">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="item2mobile" class="mui-slider-item mui-control-content">
                    <div id="scroll2" class="mui-scroll-wrapper">
                        <div class="mui-scroll">
                            <div class="mui-loading">
                                <div class="mui-spinner">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extrjs %}
    <script>
        mui.init({
            swipeBack: false,
        });
        (function ($) {
            $('.mui-scroll-wrapper').scroll({
                indicators: true //是否显示滚动条
            });
            //初始加载审批中的请假数据
            loadData1('item1mobile')
            document.getElementById('slider').addEventListener('slide', function (e) {
                if (e.detail.slideNumber === 0) {
                    loadData1('item1mobile')
                } else if (e.detail.slideNumber === 1) {
                    loadData1('item2mobile')
                }
            });
        })(mui);

        function loadData1(itemName) {
            if (itemName == 'item1mobile') {
                is_ok = 1
            } else {
                is_ok = 0
            }
            mui.ajax('/iapp/ipos/get_data_info/?info_type=allowance&is_ok=' + is_ok + '', {
                data: {},
                dataType: 'json',
                type: 'post',
                success: function (data) {
                    if (data.length > 0) {
                        var html = "<ul id='id" + itemName + "' class='mui-table-view'>";
                        if (is_ok == 1) {
                          {% if "ipos_wallets"|get_wallet_type %}//双钱包
                             for (var i in data) {
                                html += "<li class='mui-table-view-cell'>"
                                + "<div class='mui-media-body'>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_money' style='font-size:13px'><label>{% trans 'Cash operation amount: ' %}</label>" + data[i].money + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_money_B' style='font-size:13px'><label>{% trans 'Subsidy operation amount: ' %}</label>" + data[i].money_B + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_wallet_type' style='font-size:13px'><label>{% trans 'Subsidy type: ' %}</label>" + data[i].allow_type + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Card number: ' %}</label>" + data[i].card + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_balance' style='font-size:13px'><label>{% trans 'Cash balance: ' %}</label>" + data[i].balance + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_allow_balance' style='font-size:13px'><label>{% trans 'Subsidy balance: ' %}</label>" + data[i].allow_balance + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_checktime' style='font-size:13px'> <label>{% trans 'Subsidy collection time: ' %}</label> " + data[i].receive_date + "</span></p>"
                                + "</div>"
                                + "</li>"
                             }
                          {% else %}
                            for (var i in data) {
                                html += "<li class='mui-table-view-cell'>"
                                + "<div class='mui-media-body'>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_money' style='font-size:13px'><label>{% trans 'Subsidy amount: ' %}</label>" + data[i].money + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_wallet_type' style='font-size:13px'><label>{% trans 'Subsidy type: ' %}</label>" + data[i].allow_type + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Card number: ' %}</label>" + data[i].card + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_balance' style='font-size:13px'><label>{% trans 'Balance: ' %}</label>" + data[i].balance + "</span></p>"
                                + "<p class='mui-ellipsis' align='left'><span id='id_checktime' style='font-size:13px'> <label>{% trans 'Subsidy collection time: ' %}</label> " + data[i].receive_date + "</span></p>"
                                + "</div>"
                                + "</li>"
                             }
                          {% endif %}
                        } else {
                            {% if "POS_IC"|filter_config_option %}
                                for (var i in data) {
                                    html += "<li class='mui-table-view-cell'>"
                                        + "<div class='mui-media-body'>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Subsidy amount: ' %}</label>" + data[i].money + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Card number: ' %}</label>" + data[i].card + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Any review: ' %}</label>" + data[i].is_pass + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Subsidy time:' %}</label>" + data[i].checktime + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Subsidy Ineffective Date: ' %}</label>" + data[i].valid_date + "</span></p>"
                                        + "</div>"
                                        + "</li>"
                                }
                            {% else %}
                                for (var i in data) {
                                    html += "<li class='mui-table-view-cell'>"
                                        + "<div class='mui-media-body'>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Subsidy amount: ' %}</label>" + data[i].money + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Card number: ' %}</label>" + data[i].card + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Any review: ' %}</label>" + data[i].is_pass + "</span></p>"
                                        + "<p class='mui-ellipsis' align='left'><span style='font-size:13px'><label>{% trans 'Subsidy time:' %}</label>" + data[i].checktime + "</span></p>"
                                        + "</div>"
                                        + "</li>"
                                }
                            {% endif %}
                        }
                        html += "</ul>"
                    }
                    var item = document.getElementById(itemName);
                    if (data.length < 1) {
                        var html = "<h4 align='center' class='mui-text-center mui-h5 center-box-msg' style = 'position: absolute;left: -webkit-calc(50% - 75px);top: -webkit-calc(50% - 20px); width: 160px;'>{% trans 'No subsidy information!' %}</h4>"
                            + "<svg class='iconcenter-box center-box' aria-hidden='true'> <use xlink:href='#icon-nothing'></use> </svg>"
                        item.querySelector('.mui-scroll').setAttribute("style", "height: 100%;position: relative;");
                    }
                    if (item.querySelector('.mui-loading')) {
                        setTimeout(function () {
                            item.querySelector('.mui-scroll').innerHTML = html;
                        }, 100);
                    }
                }
            });
        }
    </script>
{% endblock %}
