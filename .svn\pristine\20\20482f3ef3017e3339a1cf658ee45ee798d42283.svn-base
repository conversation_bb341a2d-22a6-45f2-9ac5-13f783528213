#!/usr/bin/python
# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _
from django.db.models import Q
import time

from rest_framework import exceptions
from rest_framework import serializers

from mysite.iclock.datamisc import save_image_to_folder

from mysite.iclock.dataview import send_wxmessage_for_standard
from mysite.utils import *
from mysite.base.models import GetParamValue
from mysite.iclock.nomodelview import gettimediff,getprocess_EX,getemployeeprocess
from mysite.iclock.templatetags.iclock_tags import showprocess, showprocessmsg
from mysite.iapp.rest.utils import get_State_display, constrast_face
from mysite.iapp.rest.auth import get_auth_user

from mysite.iclock.models import transactions, DeptAdmin, createThumbnail
from mysite.iclock.models import employee, devicePIN, AppMessage, ademployee
from mysite.iclock.models import department
from mysite.iclock.models import USER_SPEDAY
from mysite.iclock.models import checkexact, outWork, UserEgress
from mysite.iclock.models import USER_OVERTIME
from mysite.iclock.models import USER_SPEDAY_DETAILS
from mysite.iclock.models import LeaveClass, Announcement,attShifts
from mysite.iclock.models import userRole, userRolesDell
from mysite.accounts.models import MyUser
from mysite.iapp.rest.views_visitor import wx_get_photo_url
from mysite.iclock.models import AuthTerminal
from mysite.iclock.dataview import create_approver_message

class TransactionsSerializer(serializers.HyperlinkedModelSerializer):
    """
        经度、纬度为write_only字段，非模型字段，使用后pop掉
        write_only,客户端post过来的数据，后台服务器处理后不会再经过序列化后返回给客户端
    """
    PIN = serializers.CharField(source='UserID.PIN')
    EName = serializers.ReadOnlyField(source='UserID.EName')
    longitude = serializers.CharField(required=False)
    latitude = serializers.CharField(required=False)
    location = serializers.CharField(required=False)
    coordinate_system = serializers.CharField(required=False)
    photoUpload = serializers.CharField(required=False,allow_blank=True)
    deviceid = serializers.CharField(required=False)

    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(TransactionsSerializer, self).to_representation(instance)
        if isinstance(instance.punch_location, dict):
            # 提交。
            a = instance.punch_location
        else:
            # 取数据,数据库取出来的是单引号要替换成双引号变成前端的json格式。
            if instance.punch_location is not None:
                a = json.loads(instance.punch_location.replace("\'","\""))
                ret['location'] = a['location']
            else:
                # 没有punch_location说明是内勤签到
                ret['location'] = ((instance.SN.Alias or '') + '(' + (instance.SN.SN or '') + ')'
                                   ) if instance.SN else ''
        return ret

    def is_valid(self, raise_exception=True):
        if int(GetParamValue('iapp_enable_app_sign', '0', 'app')) != 1:
            raise exceptions.APIException(detail='Card signing failed, APP Sign not enabled!', code='1002')
        emp = get_auth_user(self.context['request'])
        # is_valid 规范写法，应该是抛出 serializers.ValidationError
        # 是否允许移动端打卡校验
        if str(emp.clock_in_mobile) == '1':  #禁用移动端打卡
            raise exceptions.APIException(
                detail=_("Card signing failed, mobile check-in not enabled, please contact the administrator!"),
                code=1003)
        # 签到失败
        # 签到位置校验
        longitude = self.initial_data.get('longitude')
        latitude = self.initial_data.get('latitude')
        coordinate_system = self.initial_data.get('coordinate_system', 'gcj02')
        if (longitude and latitude):
            from mysite.iapp.rest.utils import valid_range, get_setting_location
            locations = get_setting_location(emp)
            if not valid_range(longitude, latitude, locations, coordinate_system):
                raise exceptions.APIException(detail=_('Beyond the check-in area'), code=1001)
        # 签到限制（同一个手机，同一天，只能用于同一个账号打卡签到）
        app_check_device = str(GetParamValue('iapp_app_check_device', '0', 'app'))
        try:
            deviceid = self.initial_data['deviceid']
        except:
            deviceid = self.initial_data['wxOpenId']
        if app_check_device == '1':
            flag = cache.get('APP_SIGNIN_DEVICE_{}_{}'.format(datetime.date.today(), deviceid))
            if flag and flag != emp.PIN:
                raise exceptions.APIException(detail=_("This device has been bound to other account."), code=1007)
            cache.set('APP_SIGNIN_DEVICE_{}_{}'.format(datetime.date.today(), deviceid), emp.PIN, 60*60*24)
        # 后台面部比对
        contrast_photos = self.initial_data.get('photoUpload')
        if int(GetParamValue('iapp_app_face_verificate', '0', 'app')) == 1:
            ret = constrast_face(contrast_photos, _user)
            if ret:
                raise exceptions.APIException(detail=ret['detail'], code=ret['code'])

        valid = super(TransactionsSerializer, self).is_valid(raise_exception=raise_exception)
        return valid

    def create(self, validated_data):
        _user = validated_data.pop('UserID')  #u'UserID': {u'PIN': u'100001'}
        emp = get_auth_user(self.context['request'])
        validated_data['UserID'] = emp

        longitude = 'longitude' in validated_data and validated_data.pop('longitude') or ''
        latitude = 'latitude' in validated_data and validated_data.pop('latitude') or ''
        location = 'location' in validated_data and validated_data.pop('location') or ''
        coordinate_system = 'coordinate_system' in validated_data and validated_data.pop('coordinate_system') or ''
        contrast_photos = 'photoUpload' in validated_data and validated_data.pop('photoUpload') or ''
        deviceid = 'deviceid' in validated_data and validated_data.pop('deviceid') or ''
        validated_data['Verify'] = 60  #验证方式,60,app定位签到
        validated_data['TTime'] = datetime.datetime.now()  #app签卡时间,以服务端为准
        punch_location = {
                'longitude': longitude,
                'latitude': latitude,
                'coordinate_system': coordinate_system,
                'location': location,
                'deviceid': deviceid
            }
        validated_data['punch_location'] = punch_location
        translation_obj = transactions.objects.create(**validated_data)
        set_att_flag_cache(validated_data['TTime'], emp.PIN)  #用于考勤消费联动
        return translation_obj


    class Meta:
        model = transactions
        read_only_fields = ('TTime',)
        fields = ('url','PIN','EName','TTime','State','Verify','longitude','latitude','location','coordinate_system', 'photoUpload', 'deviceid')


class EmployeesSerializer(serializers.HyperlinkedModelSerializer):
    DeptNumber = serializers.CharField(source='DeptID.DeptNumber')
    DeptName = serializers.ReadOnlyField(source='DeptID.DeptName')
    photoUpload = serializers.CharField(write_only=True, required=False)
    ContrastPhotoUpload = serializers.CharField(write_only=True, required=False)
    photo = serializers.SerializerMethodField()
    boundUserName = serializers.SerializerMethodField()  #用户名称
    contrast_photos = serializers.SerializerMethodField()  #对比照片

    def is_valid(self, raise_exception=True):
        # is_valid 规范写法，应该是抛出 serializers.ValidationError
        pin = self.initial_data.get('PIN')  # 人员编号
        contrast_photos = self.initial_data.get('ContrastPhotoUpload')  # 对比照片
        if contrast_photos and contrast_photos != 'null':
            # app上传人员对比照片
            from mysite.base.views import save_contrast_pic_to_path
            now_time = datetime.datetime.now()
            timestamp = now_time.strftime('%Y%m%d%H%M%S%f')
            emp = employee.objByPIN(pin)
            try:
                ademployee_obj = ademployee.objects.create(adpin=emp.PIN, adname=emp.EName,
                                                           adphoto='ad_%s_%s.jpg' % (emp.PIN, timestamp),
                                                           addept=emp.DeptID.DeptName,
                                                           addeptnum=emp.DeptID.DeptNumber, audit_type=1,
                                                           upload_time=now_time)
            except Exception as e:
                # 存在待审核记录，不能重复提交
                raise exceptions.APIException(detail=str(e), code=-1)
            if int(GetParamValue('opt_basic_adautoaduit', '0')) == 1:
                is_audit = False  # 是否需要比对照片审核(移动端上传比对照片需审核)
                ademployee_obj.audit = 1
                ademployee_obj.remarks = str(_('Automatic audit'))
                ademployee_obj.save()
            else:
                is_audit = True  # 是否需要比对照片审核(移动端上传比对照片需审核)
            save_result = save_contrast_pic_to_path(devicePIN(pin), contrast_photos, 2, is_audit=is_audit, timestamp=timestamp)
            if save_result['ret'] < 0:
                ademployee_obj.delete()  # 保存对比照片失败，删除对比照片审核记录。
                raise exceptions.APIException(detail=save_result['message'], code=save_result['ret'])
        valid = super(EmployeesSerializer, self).is_valid(raise_exception=raise_exception)
        return valid

    def get_boundUserName(self, obj):
        """
        获取绑定用户名称
        """
        try:
            MyUserObj = MyUser.objects.get(id=obj.boundid)
            data = MyUserObj.username
        except:
            data = ''
        return data

    def get_photo(self, obj):
        """
        获取用户照片，当前查询结果集也会有这个数据，待完善
        """
        try:
            photo_path = getStoredFileName("photo/app/thumbnail", None, obj.PIN+".jpg")
            type = self._context['request'].query_params.get('datatype')
            if type == 'wxipos':
                import random
                photo_path = getStoredFileName("photo/thumbnail", None, obj.PIN + ".jpg")
                if os.path.exists(photo_path):
                    data = getStoredFileURL("photo/thumbnail", None, obj.PIN+".jpg", auth=False)+'?time=%s' % str(random.random()).split('.')[1]
                    return data
                return ''
            else:
               with open(photo_path,"rb") as f:
                   data = f.read()
                   if data[:10] == b"CRYPT_IMG:":    #解密
                       data = aes_crypt(data[10:], False)
                   ls_f = base64.b64encode(data)
                   if sys.version[0] == '3':
                       base64_data = ls_f.decode("utf-8")
                   data = 'data:image/jpg;base64,%s'%base64_data
        except:
            data = ''
        return data

    def get_contrast_photos(self, obj):
        """
        获取用户对比照片
        """
        # 获取对比照片
        PIN = obj.pin()  # 获取人员去0的pin
        photo_path = getStoredFileName('photo', None, "ad_%s.jpg"%PIN)
        try:
           with open(photo_path,"rb") as f:
               data = f.read()
               if data[:10] == b"CRYPT_IMG:":    #解密
                   data = aes_crypt(data[10:], False)
               ls_f = base64.b64encode(data)
               if sys.version[0] == '3':
                   base64_data = ls_f.decode("utf-8")
               data = 'data:image/jpg;base64,%s'%base64_data
        except:
            data = ''
        return data

    def create(self, validated_data):
        """
        重写create方法，用于更新外键
        """
        dept = validated_data.pop('DeptID')
        DeptID = department.objByNumber(dept['DeptNumber'])
        if not DeptID:
            raise exceptions.APIException(_('DeptNumber is invalid'))
        photo = validated_data.pop('photoUpload')  # 用户照片、头像
        PIN = validated_data.get('PIN')
        if photo:
            # app 我的团队-人员登记-比对照片保存（目前app已经屏蔽相关代码）
            from mysite.base.views import save_contrast_pic_to_path
            save_result = save_contrast_pic_to_path(PIN, photo, 3)
            if save_result['ret'] < 0:
                return getJSResponse(save_result)
        return employee.objects.create(DeptID=DeptID, **validated_data)

    def update(self, instance, validated_data):
        """
        重写update方法
        """
        pin = validated_data.pop('PIN')  #PIN不可编辑
        PIN = employee.objByPIN(pin).pin()  #获取人员去0的pin
        photo = validated_data.pop('photoUpload')  #用户照片、头像
        type = self._context['request'].POST.get('datatype')
        validated_data.pop('ContrastPhotoUpload')  #对比照片
        if photo and photo != 'null':
            if type == 'wxipos':
                # 小程序保存头像的时候同时把头像当作对比照片
                from mysite.base.views import save_contrast_pic_to_path
                save_contrast_pic_to_path(PIN, photo, 2)
            else:
                # 保存app头像
                from mysite.iapp.rest.utils import appBase64ToImage
                appBase64ToImage(photo, '%s.jpg' % PIN)

        dept = validated_data.pop('DeptID')
        DeptID = department.objByNumber(dept['DeptNumber'])
        if not DeptID:
            raise exceptions.APIException(_('DeptNumber is invalid'))
        setattr(instance, 'DeptID', DeptID)  #存在有效部门才更新
        return super(EmployeesSerializer,self).update(instance, validated_data)

    class Meta:
        model = employee
        read_only_fields = ('photo','boundid','boundUserName')
        fields = ('url','PIN','EName','DeptNumber','DeptName','Gender','Card','Birthday','Mobile','email','photo','photoUpload','ContrastPhotoUpload','boundid','boundUserName','contrast_photos')


class AppMessageSerializer(serializers.HyperlinkedModelSerializer):
    userid = serializers.ReadOnlyField(source='UserID.id')
    photoUrl = serializers.SerializerMethodField()

    def get_photoUrl(self, obj):
        """
        获取用户app文件夹照片地址
        """
        try:
            ins = self._context['request'].GET
            apiUrl = ins.get('apiUrl', '')
            if int(obj['msg_type']) in [1,4,5,6,7]:
                content = json.loads(obj['content'])
                user_id = MyUser.objects.get(username = content['Author']).id
                emp = employee.objects.filter(boundid = user_id)
                PIN = emp[0].PIN
            else:
                PIN = ins.get('PIN', '')
            pUrl = wx_get_photo_url(PIN,apiUrl,False)
            return pUrl
        except Exception as e:
            print('e----',e)
            return None

    def create(self, validated_data):
        """
        重写create方法
        """

        return employee.objects.create( **validated_data)

    def update(self, instance, validated_data):
        """
        重写update方法
        """
        instance.msg_type = validated_data.get('msg_type', instance.msg_type)
        instance.title = validated_data.get('title', instance.title)
        instance.content = validated_data.get('content', instance.content)
        instance.createtime = validated_data.get('createtime', instance.createtime)
        instance.save()
        # return instance
        return super(AppMessageSerializer,self).update(instance, validated_data)

    class Meta:
        model = AppMessage
        read_only_fields = ()
        fields = ('id', 'userid', 'msg_type', 'title', 'content', 'createtime', 'is_send', 'is_read', 'photoUrl')


class DepartmentSerializer(serializers.HyperlinkedModelSerializer):


    class Meta:
        model = department
        read_only_fields = ()
        fields = ('DeptID', 'DeptNumber', 'DeptName', 'parent', 'DelTag')


def pending_approval_notification(emp,roleid,checktype,reason,type):
    """
    待审批申请通知
    type : 1 休假申请 , 2 补签申请 , 3 加班申请 , 4 出差申请
    """
    deptadmins = DeptAdmin.objects.filter(dept=emp.DeptID)
    if type == 1:
        title = _(u"Leave application")
    elif type == 2:
        title = _(u"Application for renewal")
    elif type == 3:
        title = _(u"Overtime application")
    elif type == 4:
        title = _(u"Business trip application")
    for deptadmin in deptadmins:
        try:
            role = userRole.objects.get(userid=deptadmin.user)
            if role.roleid.roleid == roleid:
                u = employee.objects.get(boundid=deptadmin.user.id)
                if not u:
                    continue
                if not emp.EName:
                    name = emp.PIN
                else:
                    name = emp.EName
                datas = {
                    "thing1": {"value": title},
                    "phrase7": {"value": checktype},
                    "name5": {"value": name},
                    "thing25": {"value": reason}
                }
                template_id = "Ax1if4lG_caeHCBPYi2f7RT23nj9ZB7_uiDKX0hFwOA"
                send_wxmessage_for_standard(u, template_id, datas, 1)
        except:
            continue
    return

class UserSpedaySerializer(serializers.HyperlinkedModelSerializer):
    PIN = serializers.CharField(source='UserID.PIN',required=False)
    EName = serializers.ReadOnlyField(source='UserID.EName')
    # attachment = serializers.SerializerMethodField(read_only=False)
    attachment = serializers.CharField(required=False)
    Place = serializers.CharField(required=False,allow_blank=True)
    mobile = serializers.CharField(required=False,allow_blank=True)
    successor = serializers.CharField(required=False,allow_blank=True)
    remarks = serializers.CharField(required=False,allow_blank=True)
    roleid = serializers.CharField(required=False,allow_blank=True)
    photoUrl = serializers.SerializerMethodField()

    # def get_attachment(self, obj):
    #     # 获取附件名称
    #     try:
    #         obj_detail = obj.USER_SPEDAY_DETAILS()
    #         file = obj_detail.file
    #     except:
    #         file = ''
    #     return file
    def get_photoUrl(self, obj):
        """
        获取用户app文件夹照片地址
        """
        try:
            ins = self._context['request'].GET
            apiUrl = ins.get('apiUrl', '')
            PIN = obj.UserID.PIN
            pUrl = wx_get_photo_url(PIN,apiUrl,False)
            return pUrl
        except Exception as e:
            print('e----',e)
            return None
    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(UserSpedaySerializer, self).to_representation(instance)
        obj = instance.USER_SPEDAY_DETAILS()
        if obj:
            # ret['process'] = showprocess(instance)
            # 多级审批函数修改（适配职务/人员）
            ret['process'] = showprocessmsg(instance)
            ret['State_display'] = get_State_display(instance)
            ret['attachment'] = obj.file
            ret['Place'] = obj.Place
            ret['mobile'] = obj.mobile
            ret['successor'] = obj.successor
            ret['remarks'] = obj.remarks
        return ret

    def create(self, validated_data):
        st = validated_data['StartSpecDay']
        et = validated_data['EndSpecDay']
        minunit = gettimediff(et,st)

        _user = validated_data.pop('UserID')  #u'UserID': {u'PIN': u'100001'}
        emp = get_auth_user(self.context['request'])

        tmp = USER_SPEDAY.objects.filter(UserID=emp, StartSpecDay__lte=et, EndSpecDay__gte=st).exclude(State=3).count()
        if tmp:
            raise exceptions.APIException(detail=_('There are overlaps in the leave time.'),code=1002)
        dateid = validated_data['DateID']
        reason = validated_data['YUANYING']
        roleid = 0
        employeelist = ''
        process = ''
        pid = 0
        processtype = 1
        if GetParamValue('opt_basic_Auto_audit', '0') == '0':  # 是否支持自动审核，如支持自动将状态改为已接受2
            state = 0
            proc, pid = getemployeeprocess(emp.id, dateid, minunit)
            processtype = 1
            employeelist = ''
            if not proc:  # 优先多级人员审批
                proc, pid = getprocess_EX(emp.id, dateid, minunit)
                processtype = 0
                if len(proc) == 0:
                    roleid = 0
                    process = ''
                else:
                    roleid = proc[0]
                    process = ','.join(str(x) for x in proc)
                    process = ',' + process + ','
            else:
                roleid = 0
                employeelist = proc[0]
                process = ','.join(str(x) for x in proc)
                process = ',' + process + ','
        else:
            state = 2
        validated_data['State'] = state

        Place = 'Place' in validated_data and validated_data.pop('Place') or ''
        mobile = 'mobile' in validated_data and validated_data.pop('mobile') or ''
        successor = 'successor' in validated_data and validated_data.pop('successor') or ''
        remarks = 'remarks' in validated_data and validated_data.pop('remarks') or ''

        validated_data['roleid'] = roleid
        validated_data['process'] = process
        validated_data['processid'] = pid
        validated_data['UserID'] = emp
        validated_data['ApplyDate'] = datetime.datetime.now()
        validated_data['employeelist'] = employeelist
        validated_data['processtype'] = processtype
        uspe_obj = USER_SPEDAY.objects.create(**validated_data)


        # 保存上传照片附件
        filename = ''
        if 'PhotoData' in self.initial_data and self.initial_data['PhotoData'] != '':
            # 小程序请假申请上传图片附件
            
            photodata = self.initial_data['PhotoData']
            # 以时间戳方式命名附件
            timestamp = int(time.time())
            filename = '%s_%s.jpg' % (str(uspe_obj.id), timestamp)
            filpath = getStoredFileName('userSpredyFile', None)
            fname = os.path.join(filpath, filename)
            save_image_to_folder(data=photodata, data_format=2, name=filename, relative_path='userSpredyFile', is_encrypt=False)
        USER_SPEDAY_DETAILS(USER_SPEDAY_ID=uspe_obj,remarks=remarks,Place=Place,mobile=mobile,successor=successor,file=filename).save()
        if dateid == 1:
            # 公出
            create_approver_message("businessLeave", uspe_obj, '', 0)  # 发送通知给审批人
        else:
            # 请假
            create_approver_message("vacation", uspe_obj, '', 0)  # 发送通知给审批人
        if uspe_obj.UserID == emp:
            # 添加发送休假申请通知
            leaveclass = LeaveClass.objects.get(LeaveID=dateid)
            if dateid == 1:
                type = 4 #出差
            else:
                type = 1 #其他假类
            pending_approval_notification(emp, roleid, leaveclass.LeaveName, reason, type)

        return uspe_obj


    class Meta:
        model = USER_SPEDAY
        fields = ('id','url','PIN','EName','StartSpecDay','EndSpecDay','DateID',\
            'YUANYING','ApplyDate','State','roleid','clearance','attachment','Place','mobile','successor','remarks','photoUrl','employeelist','processtype')



class CheckexactSerializer(serializers.HyperlinkedModelSerializer):
    PIN = serializers.CharField(source='UserID.PIN')
    EName = serializers.ReadOnlyField(source='UserID.EName')
    CHECKTYPE = serializers.CharField()
    photoUrl = serializers.SerializerMethodField()

    def get_photoUrl(self, obj):
        """
        获取用户app文件夹照片地址
        """
        try:
            ins = self._context['request'].GET
            apiUrl = ins.get('apiUrl', '')
            PIN = obj.UserID.PIN
            pUrl = wx_get_photo_url(PIN,apiUrl,False)
            return pUrl
        except Exception as e:
            print('e----',e)
            return None

    def create(self, validated_data):
        _user = validated_data.pop('UserID')  #u'UserID': {u'PIN': u'100001'}
        emp = get_auth_user(self.context['request'])
        uid = emp.id
        uid = emp.id
        proc, pid = getemployeeprocess(uid, 10001, 1)
        processtype = 1
        employeelist = ''
        if not proc:  # 优先多级人员审批
            proc, pid = getprocess_EX(uid, 10001, 1)
            processtype = 0
            if len(proc) == 0:
                roleid = 0
                process = ''
            else:
                roleid = proc[0]
                process = ','.join(str(x) for x in proc)
                process = ',' + process + ','
        else:
            roleid = 0
            employeelist = proc[0]
            process = ','.join(str(x) for x in proc)
            process = ',' + process + ','
        checktime = validated_data['CHECKTIME']
        reason = validated_data['YUYIN']
        checktypeid = validated_data['CHECKTYPE']
        if checktypeid == 'I':
            checktype = _(u"Check Clock In")
        elif checktypeid == 'O':
            checktype = _(u"Check Clock Out")
        else:
            checktype = checktypeid
        if checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktime).exclude(State=3).count()==0:
            from mysite.iclock.nomodelview import limit_checkforget
            checktimes = datetime.datetime.now()
            # 判断是否超出申请补签次数限制
            if limit_checkforget(None, uid, checktimes):
                raise exceptions.APIException(
                    _("Number of times exceeding the upper limit of supplementary signature!"))
            # 判断是否补签时间大于当前时间
            if checktime > checktimes:
                raise exceptions.APIException(
                    _("Save failed! The patch time is greater than the current time"))
            validated_data['UserID'] = emp
            validated_data['roleid'] = roleid
            validated_data['process'] = process
            validated_data['processid'] = pid
            validated_data['ApplyDate'] = checktimes
            validated_data['MODIFYBY'] = 'employee'
            validated_data['processtype'] = processtype
            validated_data['employeelist'] = employeelist
            ret = checkexact.objects.create(**validated_data)
            if ret.UserID == emp:
                #添加发送补签申请通知
                pending_approval_notification(emp, roleid, checktype, reason,2)
            # return checkexact.objects.create(**validated_data)
            create_approver_message("checkexact", ret, '', 0)  # 发送通知给审批人
            return ret
        else:
            raise exceptions.APIException(_('Data duplication'))


    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(CheckexactSerializer, self).to_representation(instance)
        # ret['process'] = showprocess(instance)
        # 多级审批函数修改（适配职务/人员）
        ret['process'] = showprocessmsg(instance)
        ret['State_display'] = get_State_display(instance)
        return ret

    class Meta:
        model = checkexact
        fields = ('url','id','PIN','EName','CHECKTIME','CHECKTYPE','YUYIN','State','ApplyDate','photoUrl','processtype','employeelist')


class OutWorkSerializer(serializers.HyperlinkedModelSerializer):
    PIN = serializers.CharField(source='UserID.PIN')
    EName = serializers.ReadOnlyField(source='UserID.EName')
    coordinate_system = serializers.CharField(required=False, allow_blank=True)
    photoUpload = serializers.CharField(required=False,allow_blank=True)
    photoUrl = serializers.SerializerMethodField()

    def get_photoUrl(self, obj):
        """
        获取用户app文件夹照片地址
        """
        try:
            ins = self._context['request'].GET
            apiUrl = ins.get('apiUrl', '')
            PIN = obj.UserID.PIN
            pUrl = wx_get_photo_url(PIN,apiUrl,False)
            return pUrl
        except Exception as e:
            print('e----',e)
            return None

    def create(self, validated_data):
        enable_app_outwork_sign = int(GetParamValue('iapp_enable_app_outwork_sign', '0', 'app'))
        app_location_photo = int(GetParamValue('iapp_app_location_photo', '0', 'app'))
        if enable_app_outwork_sign != 1 and app_location_photo != 1:
            # 兼容旧版本已经设置开启签到上传照片，自动允许启用APP外勤上报位置。
            raise exceptions.APIException(detail='Card signing failed, APP Outwork Sign not enabled!', code='1002')
        _user = validated_data.pop('UserID')  #u'UserID': {u'PIN': u'100001'}
        emp = get_auth_user(self.context['request'])

        coordinate_system = 'coordinate_system' in validated_data and validated_data.pop('coordinate_system') or ''#ios与安卓计算时使用的方法不同
        checktime = datetime.datetime.now()
        contrast_photos = 'photoUpload' in validated_data and validated_data.pop('photoUpload') or ''
        if int(GetParamValue('iapp_app_face_verificate', '0', 'app')) == 1:
            ret = constrast_face(contrast_photos, _user)
            if ret:
                raise exceptions.APIException(detail=ret['detail'], code=ret['code'])
        if app_location_photo == 1:
            if contrast_photos and contrast_photos != 'null':
                from mysite.iapp.rest.utils import appAttBase64ToImage
                photo = contrast_photos.replace('data:image/jpeg;base64,', '')
                appAttBase64ToImage(photo, '{}-{}.jpg'.format(_user['PIN'],checktime.strftime("%Y%m%d%H%M%S")))
        if settings.OUTWORK_TYPE == 1:
            in_locations = False
            #在外勤申请表中有过相应审批通过记录，上报位置直接存入外勤签到表，并同时插入原始记录表
            useregress_objs = UserEgress.objects.filter(UserID=emp, starttime__lt=checktime, endtime__gt=checktime, State=2)
            longitude = 'longitude' in validated_data and validated_data.pop('longitude') or ''
            latitude = 'latitude' in validated_data and validated_data.pop('latitude') or ''

            from mysite.iapp.rest.geo_utils import gcj02_to_bd09, wgs84_to_bd09, haversine
            if coordinate_system == 'wgs84':
                lng1, lat1 = wgs84_to_bd09(float(longitude), float(latitude))
            else:
                lng1, lat1 = gcj02_to_bd09(float(longitude), float(latitude))  # 火星坐标转百度坐标
            # 多个签卡区域，只要一个符合，即可签到
            for l in useregress_objs:
                lng0 = float(l.longitude)  # 中心点经度
                lat0 = float(l.latitude)  # 中心点纬度
                valid_range = 1000  # 默认有效签到半径，后续可添加自定义修改功能
                d = haversine(lng0, lat0, lng1, lat1)  # 计算两坐标点距离
                if int(d) <= int(valid_range):
                    in_locations = True
                    break

            if in_locations:
                validated_data['UserID'] = emp
                validated_data['CHECKTIME'] = checktime
                validated_data['MODIFYBY'] = 'employee'
                validated_data['State'] = 2
                validated_data['roleid'] = 0
                validated_data['process'] = ","
                validated_data['processid'] = 0
                validated_data['sign_type'] = 1  # 第二种外勤信息标志位
                transactions.objects.create(UserID=emp, TTime=checktime,Verify=61, purpose=9)
                return outWork.objects.create(**validated_data)
            raise exceptions.APIException(detail=_('Beyond the check-in area'),code=1001)

        uid = emp.id
        proc, pid = getemployeeprocess(uid,10002,1)
        processtype = 1
        employeelist = ''
        if not proc:  # 优先多级人员审批
            proc, pid = getprocess_EX(uid,10002,1)
            processtype = 0
            if len(proc) == 0:
                roleid = 0
                process = ''
            else:
                roleid = proc[0]
                process = ','.join(str(x) for x in proc)
                process = ',' + process + ','
        else:
            roleid = 0
            employeelist = proc[0]
            process = ','.join(str(x) for x in proc)
            process = ',' + process + ','
        if outWork.objects.filter(UserID__id=uid,CHECKTIME=checktime).exclude(State=3).count()==0:
            validated_data['UserID'] = emp
            validated_data['CHECKTIME'] = checktime
            validated_data['MODIFYBY'] = 'employee'
            validated_data['roleid'] = roleid
            validated_data['process'] = process
            validated_data['processid'] = pid
            validated_data['employeelist'] = employeelist
            validated_data['processtype'] = processtype
            outwork_obj = outWork.objects.create(**validated_data)
            from mysite.iclock.dataview import create_approver_message
            create_approver_message("outwork", outwork_obj, '', 0)  # 发送通知给审批人
            return outwork_obj
        else:
            raise exceptions.APIException(_('Data duplication'))


    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(OutWorkSerializer, self).to_representation(instance)
        # ret['process'] = showprocess(instance)
        # 多级审批函数修改（适配职务/人员）
        ret['process'] = showprocessmsg(instance)
        ret['State_display'] = get_State_display(instance)
        return ret

    class Meta:
        model = outWork
        fields = ('url','id','PIN','EName','CHECKTIME', 'longitude', 'latitude','coordinate_system','location', 'State', 'photoUpload', 'photoUrl','employeelist','processtype')


class UserEgressSerializer(serializers.HyperlinkedModelSerializer):
    PIN = serializers.CharField(source='UserID.PIN')
    EName = serializers.ReadOnlyField(source='UserID.EName')

    def create(self, validated_data):
        _user = validated_data.pop('UserID')  #u'UserID': {u'PIN': u'100001'}
        emp = get_auth_user(self.context['request'])
        uid = emp.id

        st = validated_data['starttime']
        et = validated_data['endtime']

        proc,pid=getprocess_EX(uid,10003,1)
        if len(proc)==0:
            roleid=0
            process=''
        else:
            roleid=proc[0]
            process=','.join(str(x) for x in proc)
            process=','+process+','

        tmp = UserEgress.objects.filter(UserID=emp).exclude(Q(starttime__gt=et) | Q(endtime__lt=st))
        if tmp:
            raise exceptions.APIException(detail=_('There are overlaps in the leave time.'), code=1002)

        validated_data['UserID'] = emp
        validated_data['ApplyDate'] = datetime.datetime.now()
        validated_data['MODIFYBY'] = 'employee'
        validated_data['roleid'] = roleid
        validated_data['process'] = process
        validated_data['processid'] = pid
        return UserEgress.objects.create(**validated_data)

    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(UserEgressSerializer, self).to_representation(instance)
        # ret['process'] = showprocess(instance)
        # 多级审批函数修改（适配职务/人员）
        ret['process'] = showprocessmsg(instance)
        ret['State_display'] = get_State_display(instance)
        return ret

    class Meta:
        model = UserEgress
        fields = ('url','id','PIN','EName','starttime','endtime','ApplyDate', 'longitude', 'latitude','location', 'State', 'reason')


class OverTimeSerializer(serializers.HyperlinkedModelSerializer):
    PIN = serializers.CharField(source='UserID.PIN')
    EName = serializers.ReadOnlyField(source='UserID.EName')
    photoUrl = serializers.SerializerMethodField()

    def get_photoUrl(self, obj):
        """
        获取用户app文件夹照片地址
        """
        try:
            ins = self._context['request'].GET
            apiUrl = ins.get('apiUrl', '')
            PIN = obj.UserID.PIN
            pUrl = wx_get_photo_url(PIN,apiUrl,False)
            return pUrl
        except Exception as e:
            print('e----',e)
            return None

    def create(self, validated_data):
        minunit = float(validated_data['AsMinute'])
        min = float(GetParamValue('MinsWorkDay'))
        minunit = minunit / min
        _user = validated_data.pop('UserID')  #u'UserID': {u'PIN': u'100001'}
        reason = validated_data['YUANYING']
        emp = get_auth_user(self.context['request'])
        uid = emp.id
        proc, pid = getemployeeprocess(uid, 10000, minunit)
        processtype = 1
        employeelist = ''
        if not proc:  # 优先多级人员审批
            proc, pid = getprocess_EX(uid, 10000, minunit)
            processtype = 0
            if len(proc) == 0:
                roleid = 0
                process = ''
            else:
                roleid = proc[0]
                process = ','.join(str(x) for x in proc)
                process = ',' + process + ','
        else:
            roleid = 0
            employeelist = proc[0]
            process = ','.join(str(x) for x in proc)
            process = ',' + process + ','
        et = validated_data['EndOTDay']
        st = validated_data['StartOTDay']
        tmp = USER_OVERTIME.objects.filter(UserID=emp).exclude(Q(StartOTDay__gt=et) | Q(EndOTDay__lt=st)
                                                               ).exclude(State=3)
        if tmp:
            raise exceptions.APIException(detail=_('Coincides with the existing overtime'), code=1002)

        validated_data['ApplyDate'] = datetime.datetime.now()
        validated_data['UserID'] = emp
        validated_data['roleid'] = roleid
        validated_data['process'] = process
        validated_data['processid'] = pid
        validated_data['processtype'] = processtype
        validated_data['employeelist'] = employeelist
        ret = USER_OVERTIME.objects.create(**validated_data)
        if ret.UserID == emp:
            # 添加发送加班申请通知
            pending_approval_notification(emp, roleid, _(u"overtime"), reason, 3)
        create_approver_message("overtime", ret, '', 0)  # 发送通知给审批人
        return ret


    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(OverTimeSerializer, self).to_representation(instance)
        # ret['process'] = showprocess(instance)
        # 多级审批函数修改（适配职务/人员）
        ret['process'] = showprocessmsg(instance)
        ret['State_display'] = get_State_display(instance)
        return ret


    class Meta:
        model = USER_OVERTIME
        fields = ('url','id','PIN','EName','StartOTDay','EndOTDay','YUANYING','ApplyDate','State','AsMinute','photoUrl','employeelist','processtype')


class LeaveClassSerializer(serializers.HyperlinkedModelSerializer):

    class Meta:
        model = LeaveClass
        fields = ('LeaveID','LeaveName','MinUnit','Unit','ReportSymbol','LeaveType','clearance', 'must_upload_attachment')

        
class AttShiftsSerializer(serializers.HyperlinkedModelSerializer):
    DeptID = serializers.CharField(source='UserID.DeptID.DeptID')
    EName = serializers.CharField(source='UserID.EName')
    PIN = serializers.CharField(source='UserID.PIN')
    class Meta:
        model = attShifts
        fields = ('DeptID','StartTime','EndTime','AttDate','EName','PIN','RealWorkDay','Late','Early','Absent',
                  'WorkTime', 'ExceptionID')


class AnnouncementSerializer(serializers.HyperlinkedModelSerializer):

    class Meta:
        model = Announcement
        fields = ('url','id','Title','Content','PIN','Author','Pubdate','Entrydate','Channel', 'IsTop', 'admin')
