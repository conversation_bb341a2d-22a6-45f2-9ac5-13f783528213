{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
{% block tblHeader %}
//var jqOptions=copyObj(jq_Options)
jqOptions[g_activeTabID].colModel={{ colModel }}
tblName[g_activeTabID]='Meet';
jqOptions[g_activeTabID].sortname='id';
jqOptions[g_activeTabID].pager='id_pager_'+tblName[g_activeTabID];

function datas(id){
    var r=$("#id_grid_"+tblName[g_activeTabID]).jqGrid("getRowData",id);
    var id=id
    var Name=r.conferenceTitle
    var data=[id,Name];
    return data
}
function beforePost_Meet(obj,actionName)
{
    if($("#id_Starttime",obj).val() > $("#id_Endtime",obj).val())
        {
                $("#id_error",obj).html("{% trans 'The meeting start time cannot be greater than the end time, saving failed' %}").css('color','red').show()
                return false
        }
    if($("#id_lunchtimestr",obj).val() > $("#id_lunchtimeend",obj).val())
        {
                $("#id_error",obj).html("{% trans 'Lunch start time cannot be greater than the end time, save failed' %}").css('color','red').show()
                return false
        }
   if($("#id_Enrolmenttime",obj).val()=='')
       {
               $("#id_error",obj).html("{% trans 'Check-in time cannot be empty' %}").css('color','red').show()
               return false
       }
   if ($("#id_LastSignOfftime", obj).val() == '') {
       $("#id_error", obj).html("{% trans 'The latest check-out time cannot be empty' %}").css('color', 'red').show()
       return false
   }
   return true;

}

function afterPost_Meet(flag,obj){
    number = $('#id_MeetID',obj).val()
    $('form').resetForm()
    $('#id_MeetID',obj).val(auto_increase_number(number))
    ShowMeetData(g_activeTabID,true)
        loadNULLPage('#id_grid_'+tblName[g_activeTabID]);
    var zTree = $.fn.zTree.getZTreeObj("showTree_"+g_activeTabID);
    zTree.setting.check.enable = false;
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode){
        var deptID=treeNode.id;
        var deptName=treeNode.name;
                if (deptID==0)
                    var urlStr="/meeting/data/Meet/"
                else
                    var urlStr="/meeting/data/Meet/?id="+deptID
                
        savecookie("search_urlstr",urlStr);
         $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:urlStr,datatype: "json"}).trigger("reloadGrid");
    }
    reloadData()
}


function ShowMeetData(page,tag,isDiy)
{
    var setting = {
            check: {enable: !tag,chkStyle: "checkbox",chkboxType: { "Y": "", "N": "" }},          
        async: {
                enable: true,
                url: "/meeting/getData/?func=meets",
                autoParam: ["id"]
            }
    };
    $.fn.zTree.init($("#showTree_"+page), setting,null);
}

function strOfData_Meet(data)
{
    return stripHtml(data.MeetID)+" "+data.conferenceTitle;
}

function doAction_Meet(url, action)
{
    var result=getSelected(0,"true");
    selected_data=datas(result.ss);
    var shift_id=selected_data[0]
    if (action == 'meet_add_participants')
        createDlgmeet_Meet();
        if (action == 'meet_show_participants')
                showmeethtml();
}

function showmeethtml(id)
{
        if(typeof id=='undefined')
    {
        var result=getSelected(0,"true");
        if (result.ss.length>1){
            alert("{% trans 'Allow only one meeting to be selected' %}");
            return;
        }else
            selected_data=datas(result.ss);
    }
    else
        {
          var result={ss:id}
          selected_data=datas(id);
                  
         }
     window.open("/meeting/photolocation/?id="+selected_data[0])
}

function createDlgmeet1(id)
{

    createDlgmeet(id)
}


function createDlgmeet_Meet(id)
{
        if(typeof id=='undefined')
    {
        var result=getSelected(0,"true");
        if (result.ss.length>1){
            alert("{% trans 'Allow only one meeting to be selected' %}");
            return;
        }else
            selected_data=datas(result.ss);
    }
    else
        {
          var result={ss:id}
          selected_data=datas(id);
         }

     createQueryDlg_Meet()

}

function searchbydept_Meet(page,selected_data){
    var dept_ids=getSelected_dept("showTree_"+page)
    var ischecked=0;
    if($("#id_cascadecheck_"+page).prop("checked"))
        ischecked=1;
    urlStr="deptIDs="+dept_ids+"&isContainChild="+ischecked
    var emp=getSelected_emp_ex("sel_meet");
    var selectitem=[]
    $.each($(".class_select_empTemplate"),function(){
            if (this.checked==true){
                    selectitem.push(this.value)
            }
    });
    if(emp.length>0){
        urlStr="UserIDs="+emp
    }
    else{
        if(selectitem.length==0){
            alert("{% trans 'Please select a person' %}")
            return
        }
    }
    if(selectitem.length>0)
    {
      tpls=selectitem.join(",")
        urlStr+='&meet_tpl='+tpls
    }
    $.ajax({type: "POST",
                url: "/meeting/employeeformeet/?meetid="+selected_data[0],
                data: urlStr,
                dataType:"json",
                success: function(){alert("{% trans 'Added successfully' %}");$.unblockUI();reloadData('Meet')},
                error: function(request){alert("{% trans 'Save failed' %}")}
                });
    
    
    
    
    
    
}
function createQueryDlg_Meet(id){

       if(typeof id=='undefined')
    {
        var result=getSelected(0,"true");
        if (result.ss.length>1){
            alert("{% trans 'Allow only one meeting to be selected' %}");
            return;
        }else
            selected_data=datas(result.ss);
    }
    else
        {
          var result={ss:id}
          selected_data=datas(id);
         }



    createDlgdeptfor10('meet',1)
    $('#dlg_for_query_meet').dialog({
    buttons:[{id:"btnShowOK",text:"{% trans 'determine' %}",
      click:function(){searchbydept_Meet('meet',selected_data);}},
     {id:"btnShowCancel",text:"{% trans 'Return' %}" ,click:function(){$(this).dialog("destroy"); }
    }] ,width:'1020',height:'550'})
         $.ajax({type: "POST",
                        url: "/meeting/data/participants_tpl/",
                        data:{sidx:'id'},
                        dataType:"json",
                        success: function(json){
                var html='<fieldset style="border:1px solid #e9e9e9;padding: 10px;height: 68px;overflow:auto"><legend><font size="4">{% trans "meeting group" %}</font></legend>'
                                    for(var i=0;i< json['rows'].length;i++){
                                        html+="<span style='padding-top:2px;padding-left:5px;padding-right:5px;height: 8px;display:inline-block;'><input type='checkbox'  value='"+json['rows'][i]['id']+"'  class='class_select_empTemplate'>"+json['rows'][i]['Name']+"</span>"
                                    }
                                html+='</fieldset>'
                                

        
                                $('#dlg_other_meet').css('display','block')
                                $("#dlg_other_meet").css("width","1013px")
                                $("#dlg_other_meet").css("height","100px")
                                $('#dlg_other_meet').html(html)
                                $("#dlg_other_meet").position({
                                    my: "left top",
                                    at: "left bottom",
                                    of: "#dlg_dept_meet"
                                  });
            },
                        error: function(request){
                                alert($.validator.format("{% trans '{0} failed!\n\n{1}' %}", opName, request.statusText)); $.unblockUI();
                                }
                        });
        
}
function Check_meet(id)
{
    createDataDialog('Meet_details', "{% trans 'The list of participants' %}",  1024,'/meeting/data/Meet_details/?MeetID='+id+'&UserID__DelTag=0')




}

function getLog_Meet(id)
{
    createDataDialog_ex("{% trans 'actual participants' %}",  1024,'/meeting/meetDell/?tag=0&MeetID='+id)
}
function getLeaves(id)
{
    createDataDialog_ex("{% trans 'List of Leavers' %}",  1024,'/meeting/meetDell/?tag=1&MeetID='+id)
}
function getAbsent(id)
{
    createDataDialog_ex("{% trans 'List of absentee' %}",  1024,'/meeting/meetDell/?tag=2&MeetID='+id)
}


function createDataDialog_ex(title,  width,actionUrl)
{
    dataDialog(title, width);
    procSimpleData_ex(actionUrl)
}

function procSimpleData_ex(actionUrl)
{
        var jqOptions_mini=copyObj(jq_Options);
        var miniDataUrl=actionUrl
        $.ajax({
            type:"GET",
            url:miniDataUrl,
            dataType:"json",
            data:'',
            success:function(json){
                jqOptions_mini.colModel=json['colModel']
                jqOptions_mini.height=400
                jqOptions_mini.url=miniDataUrl
                jqOptions_mini.pager="#id_pager_mini";
                jqOptions_mini.gridComplete=null;
				jqOptions_mini.multiselect=false
                renderGridData("mini",jqOptions_mini)
            }
        });
}

function del_meet(id)
{
        $.ajax({type: "POST",
                        url: "/meeting/data/Meet_details/?action=del",
                        data: 'K='+id,
                        dataType:"json",
                        success: function(){
                $("#id_grid_mini").jqGrid("delRowData",id);


            },
                        error: function(request){
                                alert($.validator.format("{% trans '{0} failed!\n\n{1}' %}", opName, request.statusText)); $.unblockUI();
                                }
                        });
}
function processDlgMeetStatus(obj,LocationID){
    $('#id_meeting_status',obj).fullCalendar('destroy')
    if ($('#id_meeting_status').hasClass('fc'))
        $('#id_meeting_status').fullCalendar('destroy')

    $('#id_meeting_status',obj).fullCalendar({
                   header: {
                       left: 'prev,next,today',
                       center: 'title',
                       right: 'agendaDay'
                   },
                   buttonText:{
                       prev:"{% trans 'forward' %}",
                       next:"{% trans 'backward' %}",
                       today:"{% trans 'Nowadays' %}",
                       month:"{% trans 'by month' %}",
                       agendaWeek:"{% trans 'by week' %}",
                       agendaDay:"{% trans 'By day' %}"
                   },
                   defaultView: 'agendaDay',
                   titleFormat:'MM-DD',
                   allDayText:"{% trans 'All day' %}",
                   aspectRatio:1,
                   timeFormat: 'H:mm',
                   axisFormat:'H(:mm)',
                   theme:true,
                   selectable: true,
                   unselectCancel:'',
                   eventBackgroundColor:'#ff0000',
                   unselectAuto:false,

                   dayNames:['{% trans "Sunday" %}','{% trans "Monday" %}','{% trans "Tuesday" %}','{% trans "Wednesday" %}','{% trans "Thurday" %}','{% trans "Friday" %}','{% trans "Saturday" %}'],
                   dayNamesShort:['{% trans "Sun" %}','{% trans "Mon" %}','{% trans "Tue" %}','{% trans "Wed" %}','{% trans "Thu" %}','{% trans "Fri" %}','{% trans "Sat" %}'],
                   editable: false,
                   droppable: false,
                   diableResizing:false,
                    events: {
                url: '/meeting/getData/?func=latestmeeting&roomNo='+LocationID
                }






       })

}
function process_dialog_Meet(obj)
{
    $(obj).dialog({resizable:false,modal:true})
    $("#id_Endtime",obj).datetimepicker(datetimepickerOptions);
    $("#id_Starttime",obj).datetimepicker(datetimepickerOptions);
    $("#id_Enrolmenttime",obj).datetimepicker(datetimepickerOptions);
    $("#id_LastEnrolmenttime",obj).datetimepicker(datetimepickerOptions);
    $("#id_EarlySignOfftime",obj).datetimepicker(datetimepickerOptions);
    $("#id_LastSignOfftime",obj).datetimepicker(datetimepickerOptions);
    $("#id_lunchtimestr",obj).datetimepicker(datetimepickerOptions);
    $("#id_lunchtimeend",obj).datetimepicker(datetimepickerOptions);
    
    $("#id_Enrolmenttime",obj).parent().prev().prepend('<font color="red">*</font>');
    $("#id_LastSignOfftime", obj).parent().prev().prepend('<font color="red">*</font>');
    $("#id_MeetContents",obj).css("width","136").css("height","18")
    
        $("#id_LocationID",obj).change(function(){
                $('#id_meeting_status',obj).empty();
                $('#id_meeting_status',obj).removeAttr('class')
                processDlgMeetStatus(obj,$('#id_LocationID',obj).val())
        });
    f=$(obj).find("#id_edit_form").get(0)
    $(f).validate({
            rules: {
                    "id_Endtime": {"required":true},
                    "conferenceTitle":{string:true,"maxlength":20},
                    "id_Starttime": {"required":true},
                    "MeetID": {"alnum":true},
                    "MeetContents":{string:true},
                    "EnrolmentLocation":{string:true},
                    "Contact":{string:true},
                    "Sponsor":{string:true},
                    "ContactPhone":{string:true},
                    "Coorganizer":{string:true}
                }
            });

}

//会议安排模糊查询
function searchShowMeet_Meet(){
    var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
    if (flag!='cansearch'&&flag!='defvalue') return;
    if (flag!='defvalue')
        var v=$("#"+g_activeTabID+" #searchbar")[0].value;
    else
        var v=""
    var url="/meeting/data/Meet/?q="+encodeURI(v)
    savecookie("search_urlstr",url);
    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}


$(function(){

        $("#"+g_activeTabID+" #queryButton").hide()
    
    $("#"+g_activeTabID+" #id_newrec").click(function(event){
            processNewModel();
    });


    $("#"+g_activeTabID+" #searchButton").click(function(){
       searchShowMeet_Meet();
    });
    $("#"+g_activeTabID+" #searchbar").keypress(function(event){
       if(event.keyCode==13)
      searchShowMeet_Meet();
    });

});
extraBatchOp=[
    {caption:'{% trans "About conference related operations" %}',
        submenu:[
        {action:{% if user|HasPerm:"meeting.add_meet_details" %} function(url){doAction_Meet(url, "meet_add_participants")}{% else %}''{% endif %}, title: '{% trans "Adding Participants" %}'},
        {action:{% if user|HasPerm:"meeting.delete_meet_details" %} '"?action=meet_participants_del"'{% else %}''{% endif %}, title: '{% trans "Clear Participants" %}'},
        {action:{% if user|HasPerm:"meeting.meet_participants_sync" %} '"?action=meet_participants_sync"'{% else %}''{% endif %}, title: '{% trans "Synchronize participants to the device" %}'},
        ]}
];



{% endblock %}


{% block loadData %}
    //html="<span id=id_opt_tree><input type='checkbox' id='id_cascadecheck_' checked/>{% trans 'Includes subordinate departments' %}</span>"
    //$("#id_west .ui-widget-header").html(html)
    html="<div id='show_dept_tree_'>"
        +"<ul id='showTree_"+g_activeTabID+"' class='ztree' style='margin-left: 0px;height:100%;'></ul>"
        +"</div>"
    $("#west_content_tab_meeting_Meet").html(html)

//	var h=$("#"+g_activeTabID+" #west_content").height()-20
//	$('#showTree_'+g_activeTabID).css('height',h)

        ShowMeetData(g_activeTabID,true)
        loadNULLPage('#id_grid_'+tblName[g_activeTabID]);
    var zTree = $.fn.zTree.getZTreeObj("showTree_"+g_activeTabID);
    zTree.setting.check.enable = false;
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode){
        var deptID=treeNode.id;
        var deptName=treeNode.name;
                if (deptID==0)
                    var urlStr="/meeting/data/Meet/"
                else
                    var urlStr="/meeting/data/Meet/?id="+deptID
                
        savecookie("search_urlstr",urlStr);
         $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:urlStr,datatype: "json"}).trigger("reloadGrid");
    }


{% endblock %}


{% block extractButton %}
<!--
<LI id="mPause" class="button-mpause"  onclick="batchOp('?action=roomPause',null,'{% trans "not in service" %}');"><SPAN></SPAN>{% trans "not in service" %}</LI>
<LI id="mReset" class="button-mreset"  onclick="batchOp('?action=roomReset',null,'{% trans "Reuse" %}');"><SPAN></SPAN>{% trans "Reuse" %}</LI>
<LI id="mUpload" class="button-mupload"  onclick="batchOp('?action=roomUpload',null,'{% trans "Upload Meeting Room Plan" %}');"><SPAN></SPAN>{% trans "Upload Meeting Room Plan" %}</LI>
-->
    {% if user|HasPerm:'meeting.add_meet_details'%}<LI id="id_add_participants"  onclick='batchOp(function(url){doAction_Meet(url, "meet_add_participants")},undefined,"{% trans 'Adding Participants' %}");'><SPAN class='icon iconfont icon-tianjiarenyuan'></SPAN>{% trans "Adding Participants" %}</LI>{% endif %}
    {% if user|HasPerm:'meeting.delete_meet_details'%}<LI id="id_delete_participants"   onclick="batchOp('?action=meet_participants_del',null,'{% trans "Clear Participants" %}');"><SPAN class='icon iconfont icon-qingchurenyuan'></SPAN>{% trans "Clear Participants" %}</LI>{% endif %}
    <LI id="id_show_participants"  onclick='batchOp(function(url){doAction_Meet(url, "meet_show_participants")},undefined,"{% trans 'Show photos of non-participants' %}");'><SPAN class='icon iconfont icon-tupian'></SPAN>{% trans "Show photos of non-participants" %}</LI>

{% endblock %}
