#!/usr/bin/python
# -*- coding: utf-8 -*-

from django.urls import re_path as url, include
from rest_framework import routers
from mysite.iapp.rest import views
from mysite.iapp.rest import auth
from mysite.iapp.rest import version
from mysite.iapp.rest import visitor_urls

router = routers.DefaultRouter()

router.register(r'transactions', views.TransactionsViewSet)
router.register(r'employees', views.EmployeesViewSet)  #人员
router.register(r'department', views.DepartmentViewSet)  #部门
router.register(r'appmessages', views.AppMessageViewSet)  #消息
router.register(r'announcement', views.AnnouncementViewSet)  #公告
router.register(r'userspedays', views.UserSpedayViewSet)  #请假
router.register(r'checkexacts', views.CheckexactViewSet)  #补签
router.register(r'outwork', views.OutWorkViewSet)  #外勤签到记录
router.register(r'useregress', views.UserEgressViewSet)  #外勤申请记录
router.register(r'overtimes', views.OverTimeViewSet)  #加班
router.register(r'leaveclasses', views.LeaveClassViewSet)  #假类
router.register(r'attshifts', views.AttShiftsViewSet)  #假类

# Wire up our API using automatic URL routing.
# Additionally, we include login URLs for the browseable API.
from mysite.iapp.rest.utils import OpenDoor,Open_close_door,GetStatuRec
urlpatterns = [
    url(r'^', include(router.urls)),
    url(r'^enrollment/', auth.EnrolView.as_view()),  #设备注册，设备首次使用，绑定设备
    url(r'^auth/', auth.AuthView.as_view()),
    url(r'^bind_user/', views.BindUser.as_view()),
    url(r'^unbind_user/', views.UnbindUser.as_view()),
    url(r'^manageUserCount/', views.manageUserCount.as_view()),
    url(r'^version/', version.app_version_response),
    url(r'^get_server_info/', version.get_server_info),
    url(r'^get_version_from_control/', version.get_version_from_control_server),
    url(r'^get_app_captcha/$', version.get_app_captcha),
    url(r'^package/', version.download_file),  #App客户端安装包下载
    url(r'^compositeshift/', views.CompositeShiftView.as_view()),
    url(r'^worktimezone/', views.WorktimezoneView.as_view()),
    url(r'^attshiftsreport/', views.AttShiftsReportView.as_view()),
    url(r'^changepassword/', views.ChangePasswordView.as_view()),
    url(r'^uploadfile/', views.UploadFileView.as_view()),
    url(r'^feedback/', views.FeedBackView.as_view()),
    url(r'^checkLocation/', views.CheckLocationView.as_view()),
    url(r'^testEcharts/', views.testEcharts.as_view()),
    url(r'^OpenDoor/', OpenDoor),
    url(r'^Open_close_door/', Open_close_door),
    url(r'^GetStatuRec/', GetStatuRec),      #获取补签类型
    # url(r'^update_message_state/',views.update_message_state),
    url(r'^ipos/', include('mysite.iapp.rest.ipos.urls')),
    url(r'^asset/', include('mysite.iapp.rest.asset.urls')),
    url(r'^payroll/', include('mysite.iapp.rest.payroll.urls')),
    url(r'^meeting/', include('mysite.iapp.rest.meeting.urls')),
    url(r'^uqrcode/', views.UserQRCode.as_view()),
]

urlpatterns += visitor_urls.urlpatterns