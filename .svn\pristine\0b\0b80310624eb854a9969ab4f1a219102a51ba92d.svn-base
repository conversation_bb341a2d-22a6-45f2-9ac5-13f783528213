# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-07-18 10:13
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0020_auto_20190627_1004'),
    ]

    operations = [
        migrations.AddField(
            model_name='handconsume',
            name='money_B',
            field=models.DecimalField(blank=True, db_column='money_b', decimal_places=2, default=0.0, max_digits=19, null=True, verbose_name='Subsidy operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='pos_model',
            field=models.IntegerField(blank=True, choices=[(1, 'fixed mode'), (2, 'Amount Mode'), (3, 'key value mode'), (4, 'counting mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Accounting Mode'), (8, 'Manual supplement consumption'), (9, 'Device error correction'), (10, 'Manual error correction')], null=True, verbose_name='Consumption Type'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='pos_model',
            field=models.IntegerField(blank=True, choices=[(1, 'fixed mode'), (2, 'Amount Mode'), (3, 'key value mode'), (4, 'counting mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Accounting Mode'), (8, 'Manual supplement consumption'), (9, 'Device error correction'), (10, 'Manual error correction')], null=True, verbose_name='Consumption Type'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='pos_model',
            field=models.IntegerField(blank=True, choices=[(1, 'fixed mode'), (2, 'Amount Mode'), (3, 'key value mode'), (4, 'counting mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Accounting Mode'), (8, 'Manual supplement consumption'), (9, 'Device error correction'), (10, 'Manual error correction')], null=True, verbose_name='Consumption Type'),
        ),
    ]
