#coding=utf-8
from django.shortcuts import render
from django.urls import re_path as url
from django.http import HttpResponseRedirect
from django.core.cache import cache
import requests
from mysite.utils import *
from mysite.settings import *

import hashlib
from binascii import b2a_hex, a2b_hex
from Crypto.Cipher import AES
import base64

def md5(str):
    hl = hashlib.md5()
    hl.update(str)
    return hl.hexdigest()

class AESCrypt():
    def __init__(self, key, iv):
        self.key = key
        self.iv = iv
        self.mode = AES.MODE_CBC
     
    def encrypt(self, text):
        cryptor = AES.new(self.key.encode(), self.mode, self.iv.encode())
        length = AES.block_size #16
        count = len(text)
        if(count % length != 0) :
            add = length - (count % length)
        else:
            add = 0
        text = text + (chr(add) * add)
        cipher = cryptor.encrypt(text.encode())
        data = base64.b64encode(cipher)
        return str(data, encoding="utf-8")
     
    def decrypt(self, text):
        try:
            cryptor = AES.new(self.key.decode('hex'), self.mode, self.iv.decode('hex'))
            rt = cryptor.decrypt(a2b_hex(text))#
        except:
            rt = ''
        res = ''
        for c in rt:
            if ord(c)>16:
                res+=c
        return res

def saveCode(code):
    fn = os.path.join(ADDITION_FILE_ROOT, 'license', 'actbox.dat')
    f = open(fn, "w+")
    try:
        f.write(code)
    except:
        pass
    f.close()

def exec_command(command, flag=True):
    from subprocess import Popen, PIPE
    process = Popen(command, shell=flag, stdout=PIPE, stderr=PIPE)
    stdout, stderr = process.communicate()
    return stdout

def getMiniBoxSN():
    #return "200569106609829"
    try:
        sn = exec_command("dmidecode -s baseboard-serial-number | awk '{printf(\"%s\",$0)}'")
        return sn.replace('/', '')
    except:
        return "".zfill(12)

def get_licenseId():
    from mysite.core.zkmimi import verify
    value=verify()
    if value[0] in [0, 1]:
        return value[1]['id']

def load_regional_data():
    f = os.path.join(ADDITION_FILE_ROOT, 'license', 'region.dat')
    ret = []
    with open(f,'r',encoding='utf-8') as load_f:
        datas = json.load(load_f)
        for data in datas:
            ret.append(data['name'])
    return ret

bioSvr = "https://demo.xmzkteco.com"
def portalLogin(request):
    if request.method=='POST':
        try:
#            t1=datetime.datetime.now()
            context = {}
            context["regional_data"]= load_regional_data()
            context["licenseId"]= get_licenseId()
            lurl = "%s/BiosecurityRegister/portal/distributor.do?login"%bioSvr   #用户登录
            sn = getMiniBoxSN()
            params = {
                "username": request.POST.get('username',""),
                "password": request.POST.get('password',""),
                "devSn": "%s"%sn
            }
            headers = {"Content-Type": "application/json;charset=utf-8" , "Accept": "application/json","Pragma":"no-cache","Cache-Control":"no-store"}
            response = requests.post(lurl, data=dumps1(params), headers=headers,timeout=3)#,verify=False
            print('\n', params, '\n', response.text, '\n')
            res=response.json()
            if res and res['ret'] == 'ok':
                context.update(res['data'])
                context["sn"]=sn

                #CRM系统项目编码和项目名称
                cparams = {
                    "customerId": res['data']['id'],
                    "sn": "%s"%sn
                }
                headers["Access-Token"] = res['data']['accessToken']
                vurl = "%s/BiosecurityRegister/distributor.do?verify"%bioSvr   #设备SN号验证
                response = requests.post(vurl, data=dumps1(cparams), headers=headers, timeout=3)#,verify=False
                print('\n', cparams, '\n', response.text, '\n')
                res=response.json()
                if res and res['ret'] == 'ok':
                    context["projectCode"] = res['data']['projectCode']
                    context["projectName"] = res['data']['projectName']
                    context["status"] = res['data']['status']
                    print(context)
                    cache.set(context['accessToken'], context, timeout=600),
                    return getJSResponse({'ret':'ok', 'data':context})
                else:
                    return getJSResponse(res)
            else:
                return getJSResponse(res)
        except Exception as e:
            print(e)
            pass
    return getJSResponse({'ret': 'fail'})

def getLicId(licenseid, sn):
    try:
        key = md5(sn.encode())
        iv = key[:16]
        cipher = AESCrypt(key, iv)
        return cipher.encrypt(licenseid)
    except:
        return ""

def getMacAddr(sn):
    try:
        from mysite.core.zkmimi import getmac
        key = md5(sn.encode())
        iv = key[:16]
        cipher = AESCrypt(key, iv)
        return cipher.encrypt(getmac())
    except:
        return ""

def getPubKey(licenseid):
    try:
        pubkey = """
                MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt/y6kMNBsQa/wGOHX7mxSWb
                jebEjxgDixOtnf3QU20fTV6MqJ6wwwf8S/sI0c8QKGU9DdsUu7LqvLbEh1Q/yUcJiQRzEtkhtF
                nmCzLv3hsQFs1o6ckdS0mpGGTZAxCc3NKJui6q53CucgKoc73IGUh3CPe0RXEHJS4gGlY4s
                ZPdpTpDoPPr7YqOvbQrJmur6nLDquxrfN5QjP0MiZNt4HQBxMO5bbXdTnQcrj+uQkvxxO
                FFpE+/aWDIJvNmmDFDwWR/P43Ii7QBWemesb9VEK3ddOzo4tnkzFQ5aETOl/kH7wm7V
                VDDqHirf/DiOu1YmjY47CbkDMExD7eKXWftqvwIDAQAB
                """
        key = md5(licenseid.encode())
        iv = key[8:24]
        cipher = AESCrypt(key, iv)
        return cipher.encrypt(pubkey.replace('\n', '').replace(' ' , ''))
    except:
        return ""

def MiniSvrActive(request):
    token=request.GET.get('token','')
    context=cache.get(token)
    if context:
        print('context=', context)
        if request.method=='POST':
            headers = {"Content-Type": "application/json;charset=utf-8" , "Accept": "application/json","Pragma":"no-cache","Cache-Control":"no-store"}
            headers["Access-Token"] = context['accessToken']
            url = "%s/BiosecurityRegister/distributor.do?activate"%bioSvr   #设备激活
            params = {
                "customerId": context['id'],
                "sn": context['sn'],
                "licenseId": getLicId(context['licenseId'], context['sn']),
                "projectCode": context['projectCode'],
                "connectorName": request.POST.get('contacts',""),
                "connectInfo": request.POST.get('contactInfo',""),
                "publicKey": getPubKey(context['licenseId']),
                "macAddr": getMacAddr(context['sn']),
                "productCode": "ZKEcoServer",
                }
            print('\n', context, '\n')
            print('\n', params, '\n')
            response = requests.post(url, data=dumps1(params), headers=headers, timeout=3)#,verify=False
            res=response.json()
            print('BiosecurityRegister response', res)
            if res and res['ret'] == 'ok':
                saveCode(res['data']['activateCode'])
            return getJSResponse(smart_str(dumps(res)))
        else:
            context['MEDIA_URL'] = MEDIA_URL
            return render(request,'minisvr_active.html', context)
    else:
        if request.method=='POST':
            return getJSResponse({"ret": "ovtime", "msg": _(u"The server is disconnected, please log in again.")})
        else:
            return HttpResponseRedirect('/minisvr/')

def portalLogout(request):
    token=request.GET.get('token','')
    cache.delete(token)
    return HttpResponseRedirect('/minisvr/')

def MiniSvr(request):
    context = {'LOGIN_REDIRECT_URL': settings.LOGIN_REDIRECT_URL}    
    return render(request,'minisvr_login.html', context)
      
urlpatterns = [
    url(r'^$', MiniSvr),
    url(r'^Login/$', portalLogin),
    url(r'^Logout/$', portalLogout),
    url(r'^active/$', MiniSvrActive),
]
