#!/usr/bin/env python
#coding=utf-8
import os,sys
from django.utils.translation import gettext_lazy as _
import subprocess
from django.conf import settings
from django.core.cache import cache
import datetime
from mysite.utils import *
#from apscheduler.scheduler import Scheduler
from apscheduler.schedulers.blocking import BlockingScheduler
from mysite.base.models import *
from mysite.iclock.importwm import *
from mysite.iclock.models import *
from mysite.core.zktools import *
from django.db import models, connection,connections
from mysite.tasks import *
from mysite.base.export_tasks import auto_export
from mysite.iclock.models import ItemDefine,middletable
from mysite.iclock.midtable_view import *
import requests
from mysite.core.zkmimi import getISVALIDDONGLE
from mysite.iclock.ad_view import ActiveDirectory
import random

sched=None
jCount={}
jjCount=360
ISDBConn=True

import logging
logging.basicConfig()

def get_process_id(name):
    child = subprocess.Popen(['pgrep', '-f', name], stdout=subprocess.PIPE, shell=False)
    response = child.communicate()[0]
    ret = ["%d"%int(pid) for pid in response.split()]
    print(ret)
    return ret

def restartSvr(svrName):
    import platform
    try:
        if os.name == 'posix':
            pf = platform.linux_distribution()
            if 'CentOS' in pf[0]:#centos
                subprocess.Popen("sudo -S service httpd restart",\
                    stdin=subprocess.PIPE,stderr=subprocess.PIPE,stdout=subprocess.PIPE,universal_newlines=True,shell=True)
            elif 'debian' in pf[0]: #ubuntu
                subprocess.Popen("sudo -S service apache2 restart",\
                    stdin=subprocess.PIPE,stderr=subprocess.PIPE,stdout=subprocess.PIPE,universal_newlines=True,shell=True)
            else:
                subprocess.Popen("sudo -S service apache2 restart",\
                    stdin=subprocess.PIPE,stderr=subprocess.PIPE,stdout=subprocess.PIPE,universal_newlines=True,shell=True)
        else:
            subprocess.call("cmd /C net stop %s & net start %s"%(svrName, svrName))
    except:
        pass

def job_auto_repair_mysql(DBName,user,pwd,port):
    """修复mysql数据库"""
    if os.name.lower()!='nt':return
    exe_file = settings.FILEPATH + '//mysql//bin/mysqlcheck.exe'
    cmd_file = settings.FILEPATH+"/run-repair.cmd"
    if pwd:
        pwd='-p%s'%pwd
    else:
        pwd=''
    if os.path.exists(exe_file):
        repair_cmd= """@echo  off
        cd /d %%~dp0
        %%CD%%\\mysql\\bin\\mysqlcheck.exe --auto-repair --databases %s -o -u%s %s -P%s"""%(DBName,user,pwd,port)
        f=open(cmd_file,"w")
        f.write(repair_cmd)
        f.close()
        t1=datetime.datetime.now()
        subprocess.call("cmd /C run-repair.cmd")
        t2=datetime.datetime.now()
        tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s-%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S")+' repair  ok',(t2-t1).total_seconds()))

def job_check(params):
    global jCount
    global jjCount
    global ISDBConn
    try:
        cursor = connection.cursor()
    except:
        tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"))+' start Timer task Failed,database connect failed')
        ISDBConn=False
        processScheduler()
        return
    ISDBConn=True

    cache.set('schedulers_job_check', datetime.datetime.now().strftime("%Y%m%d%H%M%S"))
    v=cache.get('schedulers_job_check')
    if not v:
        tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s %s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),'ZKEco-cache is wrong'))


    isRestart=cache.get(settings.UNIT+'_job')
    isRestartPull=cache.get(settings.UNIT+'_restart_pull')
    if isRestart and isRestart==1:#重启任务计划，当与任务计划相关的选项参数修改后
        cache.delete(settings.UNIT+'_job')
        processScheduler()
    if isRestartPull and isRestartPull==1:#重启ZKEco-server服务,当新增PULL设备后
        cache.delete(settings.UNIT+'_restart_pull')
        try:
            subprocess.call("cmd /C %s/%s" % (settings.FILEPATH, 'iclockservice.exe -b 1'))
        except:
            pass
        tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s %s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),'restart pull service'))
    if cache.get('_iscalcing_'):return #在统计的时候暂时不检查，以免误判重启服务
    if cache.get('_sync_doors_data_'):return #在同步门禁数据时候暂时不检查，以免误判重启服务
    if cache.get('_synEmptoDev_'):return #在同步门禁数据时候暂时不检查，以免误判重启服务

    jjCount+=1
    if jjCount>360:
        jjCount=0
        tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s %s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),'ZKEco-server is running'))




    servers=params['SERVICES']
    if servers<2:
        url="http://%s:%s/iclock/ping"%(params['HOST'],params['Port0'])
        try:
            if settings.JOB_CHECK_URL_TYPE == 1 and params['TYPE'] == 'apache':
                url = url.replace('ping', 'ping_cutpic')
                print('000000',url)
                response = requests.get(url, data=None, timeout=3)
            else:
                print('000000',url)
                response = requests.get(url, data=None, timeout=10)

            jCount[0]=0
        except Exception as e:
            try:
                jCount[0]+=1
            except:
                jCount[0]=1

            if jCount[0]>1:
                jCount[0]=0
                print ("restart server start....")
                if params['TYPE']!='apache':
                    restartSvr('AttServer')
                else:
                    restartSvr('ZKEco-apache0')
                tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s,%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),' check restart ZKEco-apache0',e))
                print ("restart server end....")
    else:#集群，
        for p in range(servers):
            url="http://%s:%s/iclock/ping"%(params['HOST'],int(params['Port0'])+p)
            try:
                if settings.JOB_CHECK_URL_TYPE == 1:
                    url = url.replace('ping', 'ping_cutpic')
                    print('000000',url)
                    response = requests.get(url, data=None, timeout=3)
                else:
                    print('000000',url)
                    response = requests.get(url, data=None, timeout=10)
                jCount[p]=0
            except Exception as e:
                try:
                    jCount[p]+=1
                except:
                    jCount[p]=1
                if jCount[p]>1:
                    jCount[p]=0
                    print ("restart server start....",p,datetime.datetime.now())
                    restartSvr('ZKEco-apache%s'%p)
                    tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s,%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),' check restart %s'%(int(params['Port0'])+p),e))
                    print ("restart server end....",p,datetime.datetime.now())
    #tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),' jobcheck', e))
        #检测代理服务器，因控制台增加对https的支持，为兼容的需要下面检测代理服务器关闭，在多年的实践下，代理服务一直是稳定的，无需检测
        # url = "http://%s:%s/iclock/ping" % (params['HOST'], params['Port'])
        # try:
        #     response = requests.get(url, data=None, timeout=10)
        #     jCount[servers]=0
        # except Exception as e:
        #     try:
        #         jCount[servers]+=1
        #     except:
        #         jCount[servers]=1
        #     if jCount[servers]>1:
        #         print ("restart ZKEco-proxy start....",datetime.datetime.now())
        #         restartSvr('ZKEco-proxy')
        #         tempFile("job_%s.txt" % (datetime.datetime.now().strftime("%Y%m")), '%s%s,%s' % (
        #         datetime.datetime.now().strftime("%Y%m%d%H%M%S"), ' check restart ZKEco-proxy', e))
        #         print ("restart ZKEco-proxy end....",datetime.datetime.now())




        #url="http://%s:%s/iclock/getrequest"%('127.0.0.1',params['Port1'])
        #try:
        #	response=urllib2.urlopen(url,data=None,timeout=20)
        #	jCount=0
        #except Exception as e:
        #	if 'HTTP Error 500' not in str(e):
        #		jCount+=1
        #	if jCount>0:
        #		jCount=0
        #		tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s,%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),' check restart 1',e))
        #		restartSvr('iclock-apache1')

def job_clear_apache_log():
    if os.name.lower()!='nt':return
    path = os.path.join(settings.APP_HOME,'apache','logs')
    for (root, dirs, files) in os.walk(path):
        for file in files:
            file = path + '/' + file
            try:
                filetype = os.path.splitext(file)[1]
                if filetype == '.log':
                    size = os.path.getsize(file)
                    file_size = 1024 * 1024 * 1  #1M
                    if size >= file_size:
                        f = open(file, "w")
                        file.close()
            except Exception as e:
                pass

def job_test(params):
    tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),' ZKEco-server is running'))



    #tempFile("Calc_%s.txt"%(now.strftime("%Y%m")),'%s/%s-%s/%s sec'%(now.strftime("%Y%m%d"),st.strftime('%Y-%m-%d'),et.strftime('%Y-%m-%d'),t)+' end calc task')
    #try:
    #	adminLog(time=t2, action=u"自动统计", User=None,object=u'明细统计共%s人用时%s'%(sCount,(t2-t1).total_seconds()), model=u"%s %s"%(st.strftime('%Y-%m-%d'),et.strftime('%Y-%m-%d'))).save(force_insert=True)
    #except Exception as e:
    #	print "========1111",e
    #	pass

#同步中间表数据
def sync_midtable_data():
    middata = middletable.objects.filter(flag__exact=0)
    for data in middata:
        if data.table_name.strip() == 'dept':
            DeptSync(data).exec_row()
        elif data.table_name.strip() == 'area':
            AreaSync(data).exec_row()
        elif data.table_name.strip() == 'position':
            PositionSync(data).exec_row()
        elif data.table_name.strip() == 'userinfo':
            EmpSync(data).exec_row()
        else:
            data.msg = _(u"Table name error")
            data.flag = 2
            data.save()

def sync_ad_data(params):
    try:
        ad = ActiveDirectory(host=params['host'], user=params['user'], password=params['password'], search_base=params['search_base'])
        ad.do_sync(params['syncdept'], params['syncemp'])
    except:
        if settings.DEBUG:
            import traceback
            traceback.print_exc()

#sap对接读取人员表
def job_read_emp_data():
    connection.close()
    checkEmpFile()
    checkSpecFile()

#sap对接读取排班表
def job_read_sch_data():
    connection.close()
    checkSchFile()

def job_check_offline():
    connection.close()
    checkDeviceOffline()

def job_photo_del_data():
    connection.close()
    devs=iclock.objects.filter(Q(ProductType=9) | Q(ProductType=None)).exclude(DelTag=1)
    for dev in devs:
        saveCmd(dev.SN, "CLEAR PHOTO")
        tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),dev.SN+' '+"CLEAR PHOTO"))

def job_trans_del_data():
    connection.close()
    devs=iclock.objects.filter(Q(ProductType=9) | Q(ProductType=None)).exclude(DelTag=1)
    for dev in devs:
        if DevIdentity_ex(dev.pushver, '2.3.0'):#仅支持pushver>=2.3.0
            sdate = datetime.datetime(2000, 1, 1, 0, 0).strftime("%Y-%m-%d %H:%M:%S")
            enddate = (datetime.datetime.now() - datetime.timedelta(days=100)).strftime("%Y-%m-%d %H:%M:%S")
            cmdStr = "DATA DELETE ATTLOG StartTime=%s\tEndTime=%s" % (sdate, enddate)
            saveCmd(dev.SN,cmdStr)
            tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"),dev.SN+' '+cmdStr))

def job_check_trans():
    connection.close()
    devs=iclock.objects.filter(Q(ProductType=9) | Q(ProductType=None)).exclude(DelTag=1)
    st=datetime.datetime.now()
    for dObj in devs:
        dObj.LogStamp=0
        dObj.save()
        saveCmd(dObj.SN, "CHECK",cmdTime=st)
        st+=datetime.timedelta(seconds=60)

def job_reload_attlog():
    """
    下发重新上传考勤记录命令，暂用于部分考勤设备
    """
    connection.close()
    devs = iclock.objects.filter(Q(ProductType=9) | Q(ProductType=None)).exclude(DelTag=1)
    # 前一天的记录
    reload_date = datetime.date.today() + datetime.timedelta(days=-1)
    rd = str(reload_date)
    st = rd +' 00:00:00'
    et = rd +' 23:59:59'

    # 计算提交命令时间，分散设备重传时间点，减少服务器压力
    now = datetime.datetime.now()
    # 计算2小时后的时间
    two_hours_later = now + datetime.timedelta(hours=2)

    for dObj in devs:
        if dObj.getDynState() in [1]:  # 对在线的设备进行重采记录
            # 按时间范围重传，这个需要设备支持，那种重传所有数据的，就暂不考虑了
            if DevIdentity(dObj, "push"):
                # 生成当前时间到2小时之间的随机时间
                random_time = now + datetime.timedelta(seconds=random.randint(0, int(two_hours_later.timestamp() - now.timestamp())))
                appendDevCmd(dObj, "DATA QUERY ATTLOG StartTime=%s\tEndTime=%s"%(st,et), cmdTime=random_time)

def job_add_checkinout_export():
    connection.close()
    if settings.DEMO==1:return#DEMO system return
    tasks = ItemDefine.objects.filter(ItemType='auto_export_task').all()
    for task in tasks:
        params = json.loads(task.ItemValue)
        frequency = params["frequency"]
        interval = params["interval"]
        export_day = params["export_day"]
        export_time = params["export_time"]
        hh_mm = export_time.split(':')
        hour = str(int(hh_mm[0]))
        min = str(int(hh_mm[1]))
        if 'enable' in params and params['enable']:
            if frequency == '0':  # 每天(按间隔)
                sched.add_job(auto_export, 'interval', seconds=int(interval), args=[task])
            elif frequency == '3':  # 每天(按时间)
                sched.add_job(auto_export, 'cron', hour=hour, minute=min, second='0', args=[task])
            elif frequency == '1':  # 每周
                if export_day =='7':
                   sched.add_job(auto_export, 'cron', day_of_week='0-6', hour=hour, minute=min, second='0',args=[task])
                else:
                   sched.add_job(auto_export, 'cron', day_of_week=export_day, hour=hour, minute=min, second='0',args=[task])
            else:
                sched.add_job(auto_export, 'cron', day=export_day, hour=hour, minute=min, second='0', args=[task])

def job_add_db_backup():
    from mysite.iclock.nomodelview import backup_db
    connection.close()
    if settings.DEMO == 1:
        return  #DEMO system return
    auto_backup = loads(GetParamValue('auto_backup', '{}', 'tasks'))
    if auto_backup.get('is_', 0) == 1:
        frequency = auto_backup["frequency"]
        day = auto_backup["day"]
        st = auto_backup["st"]
        hh_mm = st.split(':')
        hour = str(int(hh_mm[0]))
        mm = str(int(hh_mm[1]))
        if frequency == '1':  #每周的周几
            sched.add_job(backup_db, 'cron', day_of_week=day, hour=hour, minute=mm, second='0', args=[None,1])
        elif frequency == '0':
            sched.add_job(backup_db, 'cron', day_of_week='0-6', hour=hour, minute=mm, second='0', args=[None,1])  #每天


def job_add_log_backup():
    from mysite.iclock.nomodelview import backup_log
    connection.close()
    if settings.DEMO == 1:
        return  # DEMO system return
    auto_backup_logs = loads(GetParamValue('auto_backup_logs', '{}', 'tasks'))
    if auto_backup_logs and auto_backup_logs.get('is_', 0) == 1:
        frequency = auto_backup_logs["frequency"]
        day = auto_backup_logs["day"]
        st = auto_backup_logs["st"]
        hh_mm = st.split(':')
        hour = str(int(hh_mm[0]))
        mm = str(int(hh_mm[1]))
        if frequency == '1':  # 每周的周几
            sched.add_job(backup_log, 'cron', day_of_week=day, hour=hour, minute=mm, second='0', args=[None, 1])
        elif frequency == '0':
            sched.add_job(backup_log, 'cron', day_of_week='0-6', hour=hour, minute=mm, second='0', args=[None, 1])  # 每天


#   访客过期人员，添加自动签离
def job_add_auto_sign_off():
    connection.close()
    visitors_params = loads(GetParamValue('visitors_param', '{}', 'visitors'))
    if int(visitors_params.get('is_auto_signOff', 0)):
        # 访客登记有参数勾选0点签离的，全部访客登记人员下发删除
        sign_off_for_visit()

        # 访客预约人员根据现在日期大于预离开日期，就下发删除人员
        sign_off_by_leaveTime()


# 访客预约人员根据现在日期大于预离开日期，就下发删除人员
def sign_off_by_leaveTime(ssn_list=None):
    connection.close()
    if ssn_list is None:
        ssn_list = []
    now_data = datetime.date.today()
    reservation_qs = reservation.objects.filter(audit=1).exclude(DelTag=1)
    if ssn_list:
        reservation_qs = reservation_qs.filter(SSN__in=ssn_list)
    for res in reservation_qs:
        vis_leave_time = res.vislevelDate.date()
        if vis_leave_time < now_data:
            res.recType = 11  # 11=已离开
            res.save()


# 访客登记人员下发删除人员
def sign_off_for_visit(ssn_list=None):
    connection.close()
    if ssn_list is None:
        ssn_list = []
    visit_qs = visitionlogs.objects.filter(VisState=0).exclude(DelTag=1)
    if ssn_list:
        visit_qs = visit_qs.filter(SSN__in=ssn_list)
    for visit in visit_qs:
        visit.VisState = 1  # 1=已离开
        visit.ExitTime = datetime.datetime.now()
        visit.save()


#   根据门禁记录，偶数次则下发删除人员
# def jod_auto_sign_of_by_acc():
#     visit_qs = visitionlogs.objects.filter(VisState=0).exclude(DelTag=1)
#     reservation_qs = reservation.objects.filter(audit=1).exclude(DelTag=1)
#     vis_id_list = []
#     for visit in visit_qs:
#         vis_id_list.append('vis' + str(visit.id))   # vis+id记为访客人员pin
#     for res in reservation_qs:
#         vis_id_list.append('vis' + str(res.id))

# 自动删除过期的session
def job_add_clear_session():
    from django.contrib.sessions.models import Session
    connection.close()
    Session.objects.filter(expire_date__lt=datetime.datetime.now()).delete()

def processScheduler():
    global sched
    global ISDBConn
    sched.remove_all_jobs()
    while 1:
        try:
            cursor = connection.cursor()
            break
        except Exception as e:
            print ("processScheduler=",e)
            tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"))+' start Timer task Failed,database connect failed')
            ISDBConn=False
            import time
            time.sleep(10)
    ISDBConn=True

    nt=datetime.datetime.now()
    getISVALIDDONGLE(reload=1)#加载系统支持的销售模块

    try:
        # 预加载数据到缓存，提高查询效率
        preload_data_to_cache()
    except:
        if settings.DEBUG:
            import traceback
            traceback.print_exc()

    cfFileName=os.path.join(settings.APP_HOME,'attsite.ini')
    hDict={}
    #tables = connections['DB'].introspection.table_names()
    if os.path.exists(cfFileName):
        import configparser
        cf = configparser.ConfigParser(strict=False,allow_no_value=True)
        try:
            cf.read(cfFileName,encoding='utf-8')
        except:
            cf.read(cfFileName,encoding='utf-8-sig')
        HOST = cf.get('Options','HOST',fallback='127.0.0.1')
        PORT = cf.getint('Options','Port',fallback=80)
        TYPE = cf.get('Options','Type',fallback='WSGI').lower()
        SERVICES=cf.getint('Options','Services',fallback=0) #只有集群时才需要设置如下3项，该值为2
        PORT0 = cf.getint('Options','Port0',fallback=8010)
        PORT1 = cf.getint('Options','Port1',fallback=8011)
        PORT2 = cf.getint('Options','Port2',fallback=8012)
        PORT3 = cf.getint('Options','Port3',fallback=8013)


        hDict={'Port':PORT,'HOST':HOST,'TYPE':TYPE,'Port0':PORT0,'Port1':PORT1,'Port2':PORT2,'Port3':PORT3,'SERVICES':SERVICES}



    tempFile("job_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s'%(datetime.datetime.now().strftime("%Y%m%d%H%M%S"))+' start Timer task')

    #监控服务任务
#    if hDict.get('TYPE','WSGI') == 'apache':
    if not settings.MULTI_TENANT:  #多租户不检查
        sched.add_job(job_check,'interval', seconds=10,args=[hDict])
    if not settings.DEMO:  # 开启DEMO是不获取公告
        # 定时0点15分从pro官网获取公告
        sched.add_job(get_maintenance_announcement_from_zkecopro, 'cron', day_of_week='0-6', hour='0', minute='15', second='0', args=[])
    # 定时清除apache日志文件
    sched.add_job(job_clear_apache_log, 'cron', hour='3', minute='0', args=[])
    #sched.add_job(job_test,'interval', seconds=30,args=[hDict])

    now=datetime.datetime.now()
    if 'mysql' in settings.DATABASES['default']['ENGINE'] and ('127.0.0.1' in settings.DATABASES['default']['HOST']):
        sched.add_job(job_auto_repair_mysql, 'cron', day_of_week='0-6', hour='5', minute='0', second='0', args=[settings.DATABASES['default']['NAME'],settings.DATABASES['default']['USER'],
                                                                                                                settings.DATABASES['default']['PASSWORD'],settings.DATABASES['default']['PORT']])

    if 'ipos' in settings.SALE_MODULE:
        # 过期卡检查，每天0点检测卡是否过期
        sched.add_job(check_card_overdue, 'cron', day_of_week='0-6', hour='0', minute='15', second='0', args=[])
        # 定时补贴
        sched.add_job(opt_receive_allowance,'cron',day_of_week='0-6', hour='0', minute='1', second='1', args=[])
        settings.CARDTYPE = int(GetParamValue('ipos_cardtype',2,'ipos'))
        if settings.CARDTYPE == 1:
            if settings.OFFLINE_CONSUMPTION == 1:
                sched.add_job(job_create_whitelist_file,'interval', minutes=5, args=[])  #id消费白名单生成(用于ID离线消费)
                sched.add_job(job_analyze_offlinebuylog,'interval', seconds=15, args=[hDict])  #id消费，离线记录解析
            # ID消费，定时检查从支付宝微信超过5分钟并且为待支付的订单是否支付成功。 仅用于wap端员工自助微信、支付宝充值
            if int(GetParamValue("ipos_alipay_payment", 0, "ipos")) == 1 or int(GetParamValue("ipos_wechat_payment", 0, "ipos")) == 1:
                sched.add_job(ali_wechat_check, 'interval', minutes=5, args=[])
        else:
            sched.add_job(job_check_pos_trans_again,'interval',minutes=180,args=[])
            if settings.IC_POSLOG_CHECK:
                #ic消费，补异常记录（未完善）
                sched.add_job(exception_iposlog,'cron',day_of_week='0-6', hour='1', minute='0', second='0', args=[])
            #每天凌晨1点自动检查消费记录和充值记录并且自动补上缺失的充值记录。
            sched.add_job(check_charge_records,'cron',day_of_week='0-6', hour='1', minute='0', second='0', args=[])
            # sched.add_job(check_ic_recharge, 'cron', day_of_week='0-6', hour='22', minute='15', second='0', args=[]) # 每天22点15分检测充值记录，并自动补全异常的充值记录



    # if 'asset' in settings.SALE_MODULE and settings.ASSET_DEVICE:
    #     # 资产每3分钟检查有没有搜索设备的缓存，并且开始搜索到结束超过3分钟清除缓存，并且停止搜索。
    #     sched.add_job(task_stop_search_device, 'interval', minutes=3, args=[])
    #为四川泰康定制
    # AUTO_LOGS=(GetParamValue('opt_basic_Auto_logs','0')=='1')
    # if AUTO_LOGS:
    # 	sched.add_job(job_auto_logs,'cron',day_of_week='0-6', hour='22', minute='0', second='0', args=[])
    if 'acc' in settings.SALE_MODULE:
        sched.add_job(job_check_acc_trans_again,'interval', minutes=180,args=[])


    #收取门禁最新记录
    #自动统计任务
    try:
        calc_data=loads(GetParamValue('auto_calcdata','{}','tasks'))
    except:
        calc_data={}

    try:
        calc_data_five_min = loads(GetParamValue('auto_calcdata_five_min', '{}', 'tasks'))
    except:
        calc_data_five_min = {}
    #指定时间点统计
    if calc_data and calc_data['is_'] == 1:
        list_st=calc_data['st'].split('-')
        start_time=datetime.datetime(int(list_st[0]),int(list_st[1]),int(list_st[2]),0,0,0)
        if start_time<=nt:
            tasks_times = calc_data['stt'].split(';')
            for t in tasks_times:
                tasks_time=t.split(':')
                now=datetime.datetime.now()
                sched.add_job(job_auto_calc_data,'cron',day_of_week='0-6', hour=tasks_time[0], minute=tasks_time[1], second='0', args=[2])
    #按时间间隔统计
    if calc_data_five_min and calc_data_five_min['is_five_min'] == 1:
        sched.add_job(job_auto_calc_data, 'interval', minutes=5, args=[0])

    # 中间表数据自动同步
    try:
        api_data=loads(GetParamValue('api_sycndata','{}','tasks'))
    except:
        api_data={}
    if api_data:
        if api_data.get('apply_') == 1:
            if api_data.get('syncType') == '1':#按时间点同步
                if api_data['TransTimes']:
                    t_times = api_data['TransTimes'].split(';')
                    for i in t_times:
                        point_time = i.split(':')
                        if point_time:
                            try:
                                sched.add_job(sync_midtable_data,'cron',day_of_week='0-6', hour=point_time[0], minute=point_time[1], args=[])
                            except:
                                pass
            elif api_data.get('TransInterval'):#按时间间隔同步
                try:
                    sched.add_job(sync_midtable_data, 'interval', minutes=int(api_data['TransInterval']), args=[])
                except:
                    pass

        if api_data.get('ad_sync',0) == 1:#同步AD域数据到系统
            syncdept = api_data['ad_dept']
            syncemp = api_data['ad_emp']
            host = api_data['ad_host']
            user = api_data['ad_user']
            password = api_data['ad_password']
            search_base = api_data['ad_search_base']
            if api_data.get('ad_synctype') == '1':#按时间点同步
                if api_data['ad_transtimes']:
                    t_times = api_data['ad_transtimes'].split(';')
                    for i in t_times:
                        ad_point_time = i.split(':')
                        if ad_point_time:
                            try:
                                sched.add_job(sync_ad_data,'cron',day_of_week='0-6', hour=ad_point_time[0], minute=ad_point_time[1], args=[{'syncdept':syncdept,'syncemp':syncemp,'host':host,'user':user,'password':password,'search_base':search_base}])
                            except:
                                pass
            elif api_data.get('ad_transinterval'):#按时间间隔同步
                try:
                    sched.add_job(sync_ad_data, 'interval', minutes=int(api_data['ad_transinterval']), args=[{'syncdept':syncdept,'syncemp':syncemp,'host':host,'user':user,'password':password,'search_base':search_base}])
                except:
                    pass
    #自动删除数据任务
    sched.add_job(job_auto_del_data,'cron',day_of_week='0-6', hour='6', minute='0', second='0', args=[])

    # 当启用REDIS_ATTLOG，异步保存记录时，重传记录，作为补充方案（每天凌晨2点，重传前一天的记录）
    if getattr(settings, 'REDIS_ATTLOG', 0):
        sched.add_job(job_reload_attlog,'cron',day_of_week='0-6', hour='1', minute='0', second='0', args=[])

    #每天9点收取设备上的考勤记录,倍耐力定制
    if settings.PIRELLI:
        sched.add_job(job_check_trans,'cron',day_of_week='0-6', hour='9', minute='0', second='0', args=[])
    #sched.add_cron_job(job_check_trans_again,day_of_week='0-6', hour='9', minute='0', second='0', args=[])
    #处理人员、排班及记录任务
    try:
        calc_data=loads(GetParamValue('sap_ftp','{}','sap'))
    except:
        calc_data={}
    if calc_data and calc_data['SAPhost'] and calc_data['SAPuser'] and calc_data['SAPpassword'] and calc_data['hour'] and calc_data['week_hour']:
        emp_hour=calc_data['hour']
        if emp_hour:
            tasks_time=emp_hour.split(':')
            tempFile("Emp_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s'%(emp_hour)+' start read emp')
            sched.add_job(job_read_emp_data,'cron',day_of_week='0-6', hour=tasks_time[0], minute=tasks_time[1], second='0', args=[])
        sch_hour=calc_data['week_hour']
        if sch_hour:
            tasks_time=sch_hour.split(':')
            tempFile("Sch_%s.txt"%(datetime.datetime.now().strftime("%Y%m")),'%s'%(sch_hour)+' start read emp')
            sched.add_job(job_read_sch_data,'cron',day_of_week='0-6', hour=tasks_time[0], minute=tasks_time[1], second='0', args=[])

    #脱机发送邮件任务
    try:
        calc_data=loads(GetParamValue('sap_email','{}','sap'))
    except:
        calc_data={}
    if calc_data and calc_data['emails'] and calc_data['interval'] and int(calc_data['interval'])>29:
        interval=int(calc_data['interval'])
        sched.add_job(job_check_offline,'interval', minutes=interval,args=[])

    #定时删除设备上的考勤照片或考勤记录
    try:
        del_data=loads(GetParamValue('auto_deldata','{}','tasks'))
    except:
        del_data={}
    if del_data and 'st' in del_data.keys():
        del_st=del_data['st']
        if del_st:
            list_st=del_st.split('-')
            del_time=datetime.datetime(int(list_st[0]),int(list_st[1]),int(list_st[2]),0,0,0)

            if del_data and int(del_data['is_'])==1 and del_time<=nt:
                calc_data=loads(GetParamValue('day_deldata','{}','tasks'))
                if calc_data and  'trans_day' in calc_data and  calc_data['trans_day']==datetime.datetime.now().day:
                    sched.add_job(job_trans_del_data,'cron',day=calc_data['trans_day'], hour="9", minute="50", second='0', args=[])
                if calc_data and  'photo_day' in calc_data and  calc_data['photo_day']==datetime.datetime.now().day:
                    sched.add_job(job_photo_del_data,'cron',day=calc_data['photo_day'],hour="9", minute="50", second='0', args=[])
    if (GetParamValue('opt_basic_Auto_iclock','0')=='1') or (GetParamValue('opt_basic_Auto_del_iclock','0')=='1'):
     #   check_sec=int(GetParamValue('opt_basic_check_sec','10'))
      #  if check_sec<10:check_sec=10
        sched.add_job(job_syncData,'interval', seconds=30,args=[])
    if GetParamValue('opt_email_exceptionemail','')=='1' or  GetParamValue('opt_email_exceptionemail','')==1:
        sched.add_job(exception_sendemail,'cron',day_of_week='0-6', hour='7', minute='0', second='0', args=[])
    if GetParamValue('opt_email_later_early_email', '') == '1' or GetParamValue('opt_email_later_early_email', '') == 1:
        sched.add_job(later_early_email, 'cron', day_of_week='0-6', hour='7', minute='0', second='0', args=[])
    if GetParamValue('opt_email_important_email', '') == '1' or GetParamValue('opt_email_important_email','') == 1:
        sched.add_job(important_email, 'cron', day_of_week='0-6', hour='9', minute='30', second='0', args=[])
    # 每分钟检查是否有设备脱机有的话发送邮件提醒
    if GetParamValue('opt_email_dev_offline','')=='1' and GetParamValue('opt_email_dev_offline_eamil',''):
        sched.add_job(send_email_offline_device, 'interval', minutes = 1, args=[])



    #自动导出考勤原始记录任务
    job_add_checkinout_export()
    #自动备份任务
    job_add_db_backup()
    #自动备份日志任务
    job_add_log_backup()

    # 自动删除过期的session
    sched.add_job(job_add_clear_session, 'cron', day_of_week='0-6', hour='0', minute='0', second='0', args=[])

    # 自动签离:0点检查所有过期的预约访客记录和已进入的预约登记记录，并下发删除
    sched.add_job(job_add_auto_sign_off, 'cron',  day_of_week='0-6', hour='0', minute='0', second='0', args=[])

    # 定制：自动每天凌晨删除所有设备
    sched.add_job(del_all_iclock, 'cron', day_of_week='0-6', hour='0', minute='0', second='0', args=[])

    #定时推送防疫报表
    if int(GetParamValue('opt_basic_antiepidemic','0')) == 1 and settings.DEMO != 1:
        anti_data = GetParamValue('antiepidemic_settings_data', '{}', '')
        data = loads(anti_data)
        enable_email_report = data.get('enable_email_report', 0)
        send_time = data.get('send_time', '07:00')
        send_time = send_time.split(':')
        if int(enable_email_report) == 1:
            sched.add_job(job_push_antiepidemic_report, 'cron', day_of_week='0-6', hour=send_time[0], minute=send_time[1], second='0', args=[])

def Run_Tasks(tenant=None):
    global sched
    import time
    #多租户Schema设置
    if tenant:
        settings.PUBLIC_SCHEMA_NAME = tenant.schema_name
    cache.delete(settings.UNIT+'_job')
    sched = BlockingScheduler()
    while 1:
        try:
            processScheduler()
            print('Press Ctrl+{0} to exit'.format('Break' if os.name == 'nt' else 'C'))
            break
        except Exception as e:
            import traceback
            traceback.print_exc()
            pass
        time.sleep(20)

    try:
        sched.start()
    except (KeyboardInterrupt, SystemExit):
        pass

def del_all_iclock():
    # 把所有的设备都进行删除操作 （定时任务每天凌晨执行）
    iclock.objects.all().update(DelTag=1)

