{% extends "report_att.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block sidxblock %}
	function getSidx_employee_finger(){
		return 'DeptID,PIN'
	}
{% endblock %}

{% block date_range %}
    <div id="id_finger" class='left' style='width:650px'><label>{% trans "filter" %}</label>
            <input type="radio" name="ISFINGER" checked value='1' id="r_ALL" />{% trans "All" %}
            <input type="radio" name="ISFINGER" value='2' id="r_YES" />{% trans "has fingerprints" %}
            <input type="radio" name="ISFINGER" value='0' id="r_NO" />{% trans "No fingerprint" %}
            <input type="radio" name="ISFINGER" value='3' id="r3_YES" />{% trans "face" %}
            <input type="radio" name="ISFINGER" value='4' id="r4_NO" />{% trans "no face" %}
            <input type="radio" name="ISFINGER" value='5' id="r5_YES" />{% trans "has card" %}
            <input type="radio" name="ISFINGER" value='6' id="r6_NO" />{% trans "No card" %}
    </div>
{% endblock %}

{% block can_export %}
    var can_export={% if user|HasPerm:"meeting.conference_report_export" %}true{% else %}false{% endif %}
{% endblock %}
{% block date_set_range %}	

{% endblock %}


{% block getDateUrl %}

function getDateUrl()
{
	var urlStr=g_urls[g_activeTabID]
	var val=$('input:radio[name="ISFINGER"]:checked').val();	
	if(urlStr.indexOf("?")!=-1)
		urlStr+="&SEP="+val
	else
		urlStr+="?SEP="+val
	return urlStr

}
{% endblock %}
