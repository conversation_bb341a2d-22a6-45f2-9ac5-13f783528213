{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
{% block tblHeader %}
//options.disableCols=["0"];
selectDept=[]
selectName=""
options[g_activeTabID].dlg_height=620;
options[g_activeTabID].dlg_width=800;
tblName[g_activeTabID]='user';
app_user='{{app}}'
//hd='({% trans 'After the submission of the operation need to be about half a minute or so of device in the entry into force' %})'
//hasImport={% if user|HasPerm:"iclock.import_User" %}true{% else %}false{% endif %}
//jqOptions=copyObj(jq_Options)
jqOptions[g_activeTabID].colModel={{colModel}}
jqOptions[g_activeTabID].sortname='id';
jqOptions[g_activeTabID].pager='id_pager_'+tblName[g_activeTabID]
flag={% if request.user.is_superuser %}true{% else %}false{% endif %}

function getdpts_user(itemid,username){
    createDataDialog('department', "{% trans 'authorized department' %}("+username+")",  1024,'/iclock/simple_data/department/?itemid='+itemid+'&&gettype=2')
}
function getdevicebyuser(itemid,username){
    createDataDialog('devices', "{% trans 'authorized device' %}("+username+")",  1024,'/iclock/simple_data/iclock/?itemid='+itemid)
}
function getdings_user(itemid,username){
    createDataDialog('Dininghall', "{% trans 'authorized restaurant' %}("+username+")",  1024,'/iclock/simple_data/Dininghall/?itemid='+itemid+'&gettype=2')
}
function getzone_user(itemid,username){
    createDataDialog('zone', "{% trans 'authorized area' %}("+username+")",  1024,'/iclock/simple_data/zone/?itemid='+itemid+'&gettype=2')
}
function strOfData_user(data)
{
	return stripHtml(data.username);
}
function afterPost_user(flag, obj) {
    $('form').resetForm()
    $('#id_AuthedDept',obj).parent().parent().parent().show()
    $('#id_AuthedZone',obj).parent().parent().show()
    reloadData()
}
$(function(){

       var info='<div class="west_info"><p>{% trans "1. The user is the administrator who uses the system" %}</p><p>{% trans "2. If you manage all departments, please tick all departments" %}</p><p>{% trans "3. The authorization period is used in the coordination period and the allocation of the shift. For the group unit, it is beneficial for each branch to establish their own scheduling time." %}</p><p>{% trans "4. It is recommended to use the subordinate option when setting up the authorization department. The advantage is that the subordinate unit adjustment does not require reauthorization." %}</p><p></p></div>'
        renderLeftInformation(info);
        smenu="<ul><li  class='subnav_on' onclick=submenuClick('/iclock/data/user/',this);><a href='#'>{% trans "user" %}</a></li></ul>"
        //$('#menu_div').html(smenu)

	savecookie("search_urlstr",g_urls[g_activeTabID])
	$("#"+g_activeTabID+" #id_third").html("");
    $("#"+g_activeTabID+" #id_newrec").click(function(event){
    	processNewModel();
    });
	$("#"+g_activeTabID+" #queryButton").hide()
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowUser();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13){
	     searchShowUser();
	    }
	});
	$("#"+g_activeTabID+" #searchbar").val('{% trans "username, name" %}')
	//if ({{"opt_basic_approval"|get_params}}=='1'){
		{% if user|HasPerm:"iclock.browse_userroles" %}
			var html="<li id='iclock_userRoles' class='Registerlink' onclick="+"menuClick('/iclock/data/userRoles/',this);"+"><SPAN class='icon iconfont icon-shezhiguishu'></SPAN>{% trans 'userRoles' %}</LI>"
			$("#"+g_activeTabID+" #id_custom").after(html)
		{% endif %}
	//}

	if(app_user=='att'||app_user=='adms')
       {
	 //  $('#id_authe_zone').hide();
	}

});
function beforePost_user(obj,actionName){
	var subflag=true;
	var pwd =$("#id_Password",obj).val()
    var reptpwd =$("#id_ResetPassword",obj).val()
    var authDept= $("#department",obj).val()
    var is=$("#id_is_superuser",obj).prop("checked")
    var is_alldept=$("#id_is_alldept",obj).prop("checked")
 	if(pwd.length==0){
		$("#id_error",obj).css("display","block");
		$("#id_error",obj).html("<ul class='errorlist'><li>{% trans 'Password is empty' %}</li></ul>");
		subflag=false;
	}else if(pwd.length<4){
        $("#id_error",obj).css("display","block");
        $("#id_error",obj).html("<ul class='errorlist'><li>{% trans 'Password length less than 4' %}</li></ul>");
        subflag=false;
    }else if(pwd!=reptpwd){
        $("#id_error",obj).css("display","block");
        $("#id_error",obj).html("<ul class='errorlist'><li>{% trans 'Twice password not equal' %}</li></ul>");
        subflag=false;
    }
    else if(authDept=="" && (!is&&!is_alldept)){
        $("#id_error",obj).css("display","block");
        $("#id_error",obj).html("<ul class='errorlist'><li>{% trans 'Please select a department' %}</li></ul>");
        subflag=false;
    }

    if(is||is_alldept)
    {
    //为了满足r授权部门为必输字段的要求
	//deptids=[1]
	authData=[{'deptid':1,'iscascadecheck':1}]
	//$("#id_AuthedDept",obj).html(getOptions_html_User(deptids));
    	$("#id_AuthedDept",obj).val(JSON.stringify(authData));

    }


	return subflag
}



//授权时段部门
function user_deptTree(obj){
		var depName=$("#id_span_parent",obj).html();
		depName=$.trim(depName)
		$('#id_AutheTimeDept',obj).after('<div>'
		+'<span style="float:left;border-top:1 solid #5B80B2;"><input alt="department_time" type="text" style="width:140px !important;" readOnly="readOnly"  id="department_time"  value="'+depName+'"></span>'
		+'<span style="float:left;"><img class="drop_dept" alt="{% trans 'open department tree' %}" src="/media/img/sug_down_on.gif" id="id_drop_dept_time"/></span>'
		+'</div>'
+'{% trans 'The time period and shift'%} '
		);
		$("#id_AutheTimeDept",obj).css("display","none");

		$("#id_drop_dept_time",obj).click(function(){
			createQueryDlgbypage('user_timezone')
			var zTree = $.fn.zTree.getZTreeObj("showTree_user_timezone");
			zTree.setting.check.enable = false;
			$('#dlg_dept_title_user_timezone').hide()
			$('#dlg_for_query_user_timezone').dialog({position: {my: "left top-150", at: "right top",of:"#id_drop_dept_time"},buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
									  click:function(){save_hide_Autued_Deptment(obj,'user_timezone');}},
									 {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}] })

	});

	}

function user_ZoneTree(obj){
		var zoneName=$("#id_span_parent",obj).html();
		zoneName=$.trim(zoneName)
		$('#id_AuthedZone',obj).after('<div>'
		+'<span style="float:left;border-top:1 solid #5B80B2;"><input alt="zone" type="text" style="width:140px !important;" readOnly="readOnly"  id="zone"  value="'+zoneName+'"></span>'
		+'<span style="float:left;"><img class="drop_dept" alt="{% trans 'punch area tree' %}" src="/media/img/sug_down_on.gif" id="id_drop_zone"/></span>'
		+'</div>'
		);
		$("#id_AuthedZone",obj).css("display","none");

		$("#id_drop_zone",obj).click(function(){
			createQueryDlgbypage_zone('user_zone')
			var zTree = $.fn.zTree.getZTreeObj("showTree_user_zone");
			zTree.setting.check.enable = true;
			$('#dlg_dept_title_user_zone').hide()
			$('#dlg_for_query_user_zone').dialog({buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
									  click:function(){save_hide_Autued_Deptment(obj,'user_zone');}},
									 {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}] })

	});

	}



function process_dialog_user(obj,actionName){
	var leng=arguments.length;
    $("#id_is_superuser",obj).after("<span id='checksuper' style='color:red;display:None'>{% trans 'Have no permission to edit this administrator after saving' %}</span>")
	{% if "opt_basic_strong_password"|get_params:request == '1' %}
		$("#id_Password",obj).after("{% trans 'Default 111111111, if a custom password, must contain uppercase letters, lowercase letters and numbers, the length of 8 to 16 characters.' %}")
	{% else %}
		$("#id_Password",obj).after("{% trans 'The length must be great than 4,Default:111111' %}")
	{% endif %}
	$("#id_ResetPassword",obj).after("{% trans 'Must be same as Password' %}")
	$("#id_groups",obj).parent().find('span').html("{% trans 'Users have the rights to manage groups, you can choose multiple. First set the management group, if it is empty, there will be no permissions' %}")

	$("#id_AuthedZone",obj).after("{% trans 'This configuration is for access control modules.' %}")
//	$("#id_last_name",obj).parent().parent().hide();

/*
	if(!flag)
	{
		$("#id_is_superuser",obj).parent().parent().hide();
		$('#id_is_alldept',obj).parent().parent().hide();
		$('#id_is_allzone',obj).parent().parent().hide();
        $('#id_is_all_restaurant',obj).parent().parent().hide();
	}
	else
		$("#id_is_superuser",obj).parent().parent().show();
*/
	is=$("#id_is_superuser",obj).prop("checked")
	is_alldept=$("#id_is_alldept",obj).prop("checked")
	deptTree(obj);
	if(!is&&!is_alldept){
		if(!flag)
		$('#id_is_public',obj).parent().parent().hide();
	}
	else
	{
		if(is){
            $('#id_is_alldept',obj).parent().parent().hide();
		    $('#id_is_allzone',obj).parent().parent().hide();
            $('#id_is_all_restaurant',obj).parent().parent().hide();
        }
		if(is_alldept)
		{
		var dept=$('#id_AuthedDept',obj)
		if(dept.attr('multiple')!=undefined)
			dept.parent().parent().parent().hide();
		else
			dept.parent().parent().hide();

		}
	}


	//if (flag)
		user_deptTree(obj)
	//else
	//	$("#id_AutheTimeDept",obj).parent().parent().hide()
	user_ZoneTree(obj)
	$('#id_is_resetPw',obj).click(function()
	{
		toggleShowPwd(this.checked,obj);
	})
	$('#id_is_superuser',obj).click(function()
	{
		toggleShowSuper(!this.checked,obj);
	})

	$('#id_is_alldept',obj).click(function()
	{
		var dept=$('#id_AuthedDept',obj)
		if(!this.checked)
		{
            dept.parent().parent().parent().show();

		}
		else
		{
            dept.parent().parent().parent().hide();
		}

	})
	$('#id_is_allzone',obj).click(function()
	{
		var zone=$('#id_AuthedZone',obj)
		if(!this.checked)
		{
			zone.parent().parent().show();
		}
		else
		{
			zone.parent().parent().hide();
		}
	})
	if(actionName=="add"){
		$("#id_groups",obj).get(0).selectedIndex=0
		$('#id_is_staff',obj).attr("checked","checked");
		//$('#id_is_resetPw',obj).parent().parent().hide();
		if(app_user=='att'||app_user=='adms')
		{
		 //   $('#zone',obj).parent().parent().parent().parent().hide();
		}
	}
	if(actionName=='edit'){

		$.each($('#id_is_resetPw',obj),function(){
			if($(this).prop("checked"))
				toggleShowPwd(this.checked,obj);
			else
				toggleShowPwd(false,obj);
		});
		if(!flag)
		$('#id_username',obj).attr('readonly','True')//非超级管理员用户名不能修改





	}
    {% if ''|isZKTime == '1' %}
        $("#"+g_activeTabID+" #id_grid_user_frozen tr:gt(0)").click(function(){
            if($(this).attr("id")==1){
                $("#id_edit_form div table",obj).css("width",'403')
            }
        });
    {% endif %}
    {% if ''|large_screen_display == 0 %}
      $('#id_login_to_visualization',obj).parent().parent().hide();
    {% endif %}
    $("#department",obj).attr({name:"_DeptName"})

    f=$(obj).find("#id_edit_form").get(0)
    $(f).validate({
    		rules: {
    				"username": {required:true,"maxlength":30,string:true},
				"Password":{"minlength":4,required:true},
				'first_name':{"maxlength":24,string:true},
				'Tele':{string:true}
    			}
    		});

}


function toggleShowPwd(show,obj)
{
	if(show)
	{
		$('#id_ResetPassword',obj).parent().parent().show();
		$('#id_Password',obj).parent().parent().show();
		$('#id_ResetPassword',obj).val('');
		$('#id_Password',obj).val('');
	}
	else
	{
		$('#id_ResetPassword',obj).parent().parent().hide();
		$('#id_Password',obj).parent().parent().hide();
		$('#id_ResetPassword',obj).val('111111');
		$('#id_Password',obj).val('111111');
	}
}

function toggleShowSuper(show,obj)
{
	var dept=$('#id_AuthedDept',obj)
	if(show)
	{
		$("#checksuper",obj).hide();
		$('#id_AutheTimeDept',obj).parent().parent().show();
		$('#id_is_staff',obj).parent().parent().show();

		is_alldept=$("#id_is_alldept",obj).prop("checked")

		$('#id_is_alldept',obj).parent().parent().show();
		$('#id_is_public',obj).parent().parent().hide();
		$('#id_groups',obj).parent().parent().show();
		if(!is_alldept)
		{
		if(dept.attr('multiple')!=undefined)
			dept.parent().parent().parent().show();
		else
			dept.parent().parent().parent().show();
		}
		//deptTree(obj);
	}
	else
	{
		$("#checksuper",obj).show();
		$('#id_AutheTimeDept',obj).parent().parent().hide();
		$('#id_is_staff',obj).parent().parent().hide();


		$('#id_is_alldept',obj).parent().parent().hide();
		$('#id_is_allzone',obj).parent().parent().hide();
		$('#id_is_all_restaurant',obj).parent().parent().hide();
		$('#id_is_public',obj).parent().parent().show();


		$('#id_groups',obj).parent().parent().hide();
		if(dept.attr('multiple')!=undefined ||!dept.attr('multiple'))
			dept.parent().parent().parent().hide();
		else
			dept.parent().parent().hide();
	}

}
function getOptions_html_User(deptids){
	var html=""

	for(var i=0;i<deptids.length;i++)
		html+="<option value='"+deptids[i]+"' selected>"+deptids[i]+"</option>"
	return html;
}
function contain_child(obj){
	$(".parent",obj).click(function(event){
		par=$(this).attr("alt");
		ischecked=$(this).prop("checked")
		$.each($(".file input",obj),function(){
			var file_par=$(this).attr("alt1")
			file_par=file_par.split(",")
			if(IsContain(file_par,par)){
				if(!ischecked)
					$(this).removeAttr("checked")
				else
					$(this).attr("checked","checked")
			}
		});
		$.each($(".folder input",obj),function(){
			var folder_par=$(this).attr("alt1");
			if(folder_par!=undefined){
				fld_par=folder_par.split(",");
				if(IsContain(fld_par,par)){
					if(!ischecked)
						$(this).removeAttr("checked")
					else
						$(this).attr("checked","checked")
				}
			}
		});
	event.stopPropagation();
	return true;

	});

}


function save_hide_Autued_Deptment (obj,page) {
	if (page=='user_auth')
	{
		deptNames=getSelected_deptNames("showTree_"+page);
		$("input[alt='department']",obj).val(formatArrayEx(deptNames));

		var deptIDs=getSelected_dept("showTree_"+page)
		var authData=[]
		for (i in deptIDs)
		{
			if($("#diyBtn_"+deptIDs[i]).prop("checked"))
				var Data={deptid:deptIDs[i],iscascadecheck:1}
			else
				var Data={deptid:deptIDs[i],iscascadecheck:0}
			authData.push(Data)
		}


		$("#id_AuthedDept",obj).val(JSON.stringify(authData));
		dlgdestroy(page)
	}else if(page=='user_zone'){
		var zones=getSelected_dept("showTree_"+page);
		    if(zones.length>0)
		    {
			    var names=getSelected_deptNames("showTree_"+page);
			    var ischecked=0;
			    if($("#id_cascadecheck_"+page).prop("checked"))
				    ischecked=1;
			    $("#id_isContainChild",obj).val(ischecked)
			    $("#zone",obj).val(formatArrayEx(names));
			    $("#id_AuthedZone",obj).val(JSON.stringify(zones));
		    }
		    else
		    {
			    $("#zone",obj).val('')
			    $("#id_AuthedZone",obj).val('');
		    }
		    dlgdestroy(page)
	}
	else
	{
		var deptids=getSelected_dept("showTree_"+page);
		if(deptids.length>0)
		{
			if(deptids.length>1){
				alert(gettext("Multiple selections are not allowed"))
				return
			}
			var deptID=deptids[0]
			var deptNames=getSelected_deptNames("showTree_"+page);
			var ischecked=0;
			if($("#id_cascadecheck_"+page).prop("checked"))
				ischecked=1;
			$("#id_isContainChild",obj).val(ischecked)
			$("#department_time",obj).val(formatArrayEx(deptNames));
			$("#id_AutheTimeDept",obj).val(deptID);
		}
		else
		{
			$("#department_time",obj).val('')
			$("#id_AutheTimeDept",obj).val('');
		}
		dlgdestroy(page)
	}
}


function SaveAuthedDept_user(url,urlStr,title,pagename)
{
        var deptIDs=getSelected_dept("showTree_"+pagename)
	var action=true;
		if(deptIDs==""){
		action=confirm("{% trans "The department is not selected. If you continue to clear the authorized department of the selected user, continue?" %}")
		}
		if(action){
			$.blockUI({title:title,theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Please wait...' %}<br /></h1>'});
			querystr=url.ret
			//authData=[{"deptid":1,"iscascadecheck":1},{"deptid":2,"iscascadecheck":0}]
			var authData=[]
			for (i in deptIDs)
			{
				if($("#diyBtn_"+deptIDs[i]).prop("checked"))
					var Data={deptid:deptIDs[i],iscascadecheck:1}
				else
					var Data={deptid:deptIDs[i],iscascadecheck:0}
				authData.push(Data)


			}

		        querystr=url.ret+'&authdata='+JSON.stringify(authData)
			$.ajax({type: "POST",
				url: urlStr,
				data:querystr,
				dataType:"json",
				success: function(retdata){
							if(retdata.ret==0){
								reloadData(tblName[g_activeTabID])
								$.unblockUI()
								//$("#id_error").css("display","block");
								//$("#id_error").html(retdata.message)

							}else{
								$.unblockUI()
								alert(retdata.message);
							}},
					error: function(){ $.unblockUI();alert($.validator.format('{% trans 'Operating failed for {0} !' %}',options[g_activeTabID].title));}
					});
				}


}


//授权部门
function deptTree(obj){
		//getFormDept(obj)
		var error=$('#id_AuthedDept',obj).parent().find(".errorlist",obj).html();
		$('#id_AuthedDept',obj).parent().html((error==null?"":"<ul class='errorlist'>"+error+"</ul>")
		+'<div>'
		+'<span style="float:left;border-top:1 solid #5B80B2;"><input alt="department" type="text" style="width:200px !important;" readOnly="readOnly"  id="department"/></span>'
		+'<span style="float:left;"><img class="drop_dept" alt="{% trans 'open department tree' %}" src="/media/img/sug_down_on.gif" id="id_drop_dept"/></span>'
		+'</div>'
		+"<div style='display:none;'><input id='id_AuthedDept' name='AuthedDept' type='hidden' /></div>"
		);
		//$("#id_AuthedDept",obj).html(getOptions_html_User(selectDept));
		//$("#department",obj).val(formatArrayEx(selectNames));

		$("#id_drop_dept",obj).click(function(){

			//createQueryDlgbypage('user_auth')
			createQueryDlgbypage('user_auth',false,true)
			 $('#dlg_dept_title_user_auth').hide()
		    $("#dlg_other_body_user_auth").html("{% trans 'For the convenience of use, after selecting the subordinates, the subordinate departments do not need to check.' %}")
		    $("#dlg_for_query_user_auth").dialog({width:460,height:460})

		    $("#dlg_other_user_auth").css("height",60).show()


			$('#dlg_for_query_user_auth').dialog({position: {my: "left top-120", at: "right top",of:"#id_drop_dept"},buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
									  click:function(){save_hide_Autued_Deptment(obj,'user_auth');}},
									 {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}] })


		});

}
//模糊查询
function searchShowUser(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
	var url="/iclock/data/User/?q="+encodeURI(v)
	savecookie("search_urlstr",url);
	$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function doAction_user(url, action,opName)
{
        if(action=='setUserAuthedDept')
        {
		    createDlgSetUserAuthedDept(url,opName)


       }else if(action=='setUserAuthedZone'){
		    createDlgSetUserAuthedZone(url,opName)
       }else if(action=='setUserAuthedHall'){
            createDlgSetAuthedDining(url,opName)
       }

}
function createDlgSetUserAuthedDept(url,opName)
{
		    var title=opName//"{% trans 'User Management Department Configuration' %}";
		    var urlStr=g_urls[g_activeTabID]+ '?action=setUserAuthedDept'+'&opname='+encodeURI(opName);
            // 对多个用户设置授权部门时不回显
            var k = url.selectedCount === 1 ? url.ss[0] : ''
		    createQueryDlgbypage_dept('user_auth', false, true, '&modelName=User&request_page=User_list&K=' + k)
		    $('#dlg_dept_title_user_auth').hide()
		    $("#dlg_other_body_user_auth").html("{% trans 'For the convenience of use, after selecting the subordinates, the subordinate departments do not need to check.' %}")
		    $("#dlg_for_query_user_auth").dialog({dialogClass: "",width:500,height:480,title:title,
		    buttons:[{id:"btnShowOK",text:'{% trans "save and return" %}',click:function(){SaveAuthedDept_user(url,urlStr,title,'user_auth');$(this).dialog("destroy");}},
		    {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); } }]

		    })
		    $("#dlg_other_iclock_auth").css("height",60).show()
}
function createDlgSetUserAuthedZone(url,opName)
{
		    var title=opName//"{% trans 'User Authorization Zone Configuration' %}";
		    var urlStr=g_urls[g_activeTabID]+ '?action=setUserAuthedZone'+'&opname='+encodeURI(opName);
		    var rowInfo=''
		    if (url.count==1){
		    	rowInfo=url.ret+'&modelName=User'
		    }
		    createQueryDlgbypage_zone('user_zone',false,false,rowInfo)
		    $("#dlg_for_query_user_zone").dialog({dialogClass: "",width:500,height:480,title:title,
		    buttons:[{id:"btnShowOK",text:'{% trans "save and return" %}',click:function(){SaveAuthedZone(url,urlStr,title,'user_zone');$(this).dialog("destroy");}},
		    {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); } }]

		    })
		    $("#dlg_dept_title_user_zone").hide()
		    $("#dlg_other_user_zone").css("height",60).show()
}

function SaveAuthedZone(url,urlStr,title,pagename)
{
        var zones=getSelected_dept("showTree_"+pagename)
	var action=true;
		if(zones==""){
		action=confirm("{% trans "There is no selection area. If you continue to clear the authorized area of the selected user, continue?" %}")
		}
		if(action){
			$.blockUI({title:title,theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Please wait...' %}<br /></h1>'});
		        querystr=url.ret+'&authdata='+JSON.stringify(zones)
			$.ajax({type: "POST",
				url: urlStr,
				data:querystr,
				dataType:"json",
				success: function(retdata){
							if(retdata.ret==0){
								$.unblockUI()
								alert(retdata.message);
							}else{
								$.unblockUI()
								alert(retdata.message);
							}
							reloadData(tblName[g_activeTabID])
						},
					error: function(){ $.unblockUI();alert($.validator.format('{% trans 'Operating failed for {0} !' %}',options.title));}
					});
				}
}

function Show_DiningData_user_auth(page,url)
{
    var setting = {
        check: {enable: true,chkStyle: "checkbox",chkboxType: { "Y": "", "N": "" }},
        async: {
                enable: true,
                url: "/ipos/getData_dining/?func=diningtree&modelName=userDining"+url.ret,
                autoParam: ["id"]
            }
    };
    $.fn.zTree.init($("#showTree_user_auth"), setting,null);
    $("#id_cascadecheck_"+page).click(function(){
		var check=$("#id_cascadecheck_"+page).prop("checked")
		cascadecheckchange(page,check)
	})
}

function createDlgSetAuthedDining(url,opName)
{
    var title=opName;
    var urlStr=g_urls[g_activeTabID]+ '?action=setUserAuthedDining';

                var html="<div id='dlg_for_query_user_auth' style='overflow:hidden;'>"
                             +"<div id='dlg_dept_user_auth' class='dlgdiv'>"
                                     +"<div id='dlg_dept_body_user_auth' style='overflow:hidden;'>"
                                             +"<ul id='showTree_user_auth' class='ztree' style='height:300px;overflow:auto;'></ul>"
                                     +"</div>"
                             +"</div>"
							 +"<input type='checkbox' id='select_all_dining' onclick='select_all_dining()' id='select_all_dining'>{% trans 'select all' %}"
							 +"<input type='checkbox' style='margin-left:10px;' id='select_other_dining' onclick='select_other_dining()'>{% trans 'anti-selection' %}"
                        +"</div>"
    $(html).dialog({title:title,modal:true,resizable:false,buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',click:function(){SaveAuthedHall(url,urlStr,title,'user_auth');$(this).dialog("destroy");}},
                             {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
                            }],
                        open:function(){Show_DiningData_user_auth('user_auth',url);},
                         close:function(){$(this).dialog("destroy"); }
                })
}
//授权餐厅全选
function select_all_dining(){
//获取 zTree 对象
	var zTree = $.fn.zTree.getZTreeObj("showTree_user_auth");
//获取全选框勾选状态
    ck=$("#select_all_dining").prop('checked');
	if (ck==true){
//全部勾选
		zTree.checkAllNodes(true)
	}else{
//全部取消勾选
		zTree.checkAllNodes(false)
	}
}
//授权餐厅反选
function select_other_dining(){
	var zTree = $.fn.zTree.getZTreeObj("showTree_user_auth");
//获取所有未勾选节点
	var uncheck_array=zTree.getCheckedNodes(false)
//取消所有节点勾选，再将之前未勾选节点更新为勾选状态，达到反选效果
	zTree.checkAllNodes(false)
	$.each(uncheck_array, function( index,node) {
		node.checked=true
		zTree.updateNode(node);//更新状态
	});
}
function SaveAuthedHall(url,urlStr,title,pagename)
{
    var zones=getSelected_dept("showTree_"+pagename)
    var action=true;
    if(zones==""){
    action=confirm("{% trans "There is no restaurant selected. If you continue to clear the authorized restaurant of the selected user, continue?" %}")
    }
    if(action){
        $.blockUI({title:title,theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Please wait...' %}<br /></h1>'});
            querystr=url.ret+'&authdata='+JSON.stringify(zones)
        $.ajax({type: "POST",
            url: urlStr,
            data:querystr,
            dataType:"json",
            success: function(retdata){
                    if(retdata.ret==0){
                        $.unblockUI()
                        alert(retdata.message);
                    }else{
                        $.unblockUI()
                        alert(retdata.message);
                    }
                    reloadData(tblName[g_activeTabID])
                },
            error: function(){ $.unblockUI();alert($.validator.format('{% trans 'Operating failed for {0} !' %}',options.title));}
            });
        }
}

{% endblock %}
{% block extractButton %}
{% if request|reqHasPerm:"add" %}

            <LI id="id_user_authe_dept" onclick='batchOp(function(url,opName){doAction_user(url, "setUserAuthedDept",opName)},undefined,"{% trans 'Setting Authorization Department' %}");'><SPAN class="icon iconfont icon-shezhiguishu"></SPAN>{% trans "Setting Authorization Department" %}</LI>
            <LI id="id_user_authe_zone"  onclick='batchOp(function(url,opName){doAction_user(url, "setUserAuthedZone",opName)},undefined,"{% trans 'Setting the Authorization Area' %}");'><SPAN class="icon iconfont icon-shezhiguishu"></SPAN>{% trans "Setting the Authorization Area" %}</LI>
        {% if ''|isZKTime == '0' %}
            <LI id="id_user_authe_hall"  onclick='batchOp(function(url,opName){doAction_user(url, "setUserAuthedHall",opName)},undefined,"{% trans 'Setting up an authorized restaurant' %}");'><SPAN class="icon iconfont icon-shezhiguishu"></SPAN>{% trans "Setting up an authorized restaurant" %}</LI>
        {% endif %}
{% endif %}


{% endblock %}


