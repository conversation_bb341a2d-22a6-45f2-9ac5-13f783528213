# -*- coding: utf-8 -*-
# Generated by Django 1.11.8 on 2017-12-18 20:05
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0003_auto_20171130_1600'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cardcashsz',
            name='log_flag',
            field=models.IntegerField(blank=True, choices=[(1, '\u8bbe\u5907\u4e0a\u4f20'), (2, '\u7cfb\u7edf\u6dfb\u52a0'), (3, '\u7ea0\u9519\u8865\u5165'), (4, '\u652f\u4ed8\u5b9d\u5145\u503c'), (5, '\u5fae\u4fe1\u5145\u503c'), (100, '\u5f02\u5e38\u8bb0\u5f55')], default=2, editable=False, null=True, verbose_name='\u8bb0\u5f55\u7c7b\u578b'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='log_flag',
            field=models.IntegerField(blank=True, choices=[(1, '\u8bbe\u5907\u4e0a\u4f20'), (2, '\u7cfb\u7edf\u8865\u5165'), (3, '\u7ea0\u9519\u8865\u5165'), (100, '\u5f02\u5e38\u8bb0\u5f55')], editable=False, null=True, verbose_name='\u8bb0\u5f55\u6807\u5fd7'),
        ),
    ]
