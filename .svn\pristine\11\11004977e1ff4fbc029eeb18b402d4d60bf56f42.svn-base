{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
{% block content_title %}
<style type="text/css">
table.gridtable {
    font-family: "Source Han Sans CN Regular","verdana,arial","sans-serif";
    font-size: 11px;
    color: #333333;
    border-width:  1px;
    border-color:  #666666;
    border-collapse:  collapse;
}
table.gridtable th {
    width: 125px;
    border-width:  1px;
    padding:  8px;
    border-style:  solid;
    border-color:  #dddddd;
    background-color:  #dedede;
}
table.gridtable td {
    border-width:  1px;
    padding:  8px;
    border-style:  solid;
    border-color:  #dddddd;
    background-color:  #ffffff;
    text-align:center;
    vertical-align:middle;
}

/* CSS样式制作 */
 a, address, b, big, blockquote, body, center, cite, code, dd, del, div, dl, dt, em, fieldset, font, form, h1, h2, h3, h4, h5, h6, html, i, iframe, img, ins, label, legend, li, ol, p, pre, small, span, strong, u, ul, var {

padding: 0;

margin: 0;

}

a {

color: black;

text-decoration: none;

}

ul {

list-style: none;

}

#tab {

width: 970px;

height: 500px;

border: 1px solid #ddd;

box-shadow: 0 0 2px #ddd;
margin-bottom: 3px;

/*处理超出的内容*/

overflow: hidden;
overflow-y: scroll;
}

/*选项卡的头部*/

#tab-header {

background-color: #F7F7F7;

height: 33px;

text-align: center;

position: relative;

}

#tab-header ul {

width: 970px;

position: absolute;

left: -1px;

}

#tab-header ul li {

float: left;

width: 130px;

height: 33px;

line-height: 33px;

padding: 0 1px;

border-bottom: 1px solid #dddddd;

}

#tab-header ul li.selected {

background-color: white;

font-weight: bolder;

border-bottom: 0;

border-left: 1px solid #dddddd;

border-right: 1px solid #dddddd;

padding: 0;

}

#tab-header ul li:hover {

color: orangered;

}

/*主要内容*/

#tab-content .dom {

    display: none;
    height: 560px;

}

#tab-content .dom ul li {


/*background-color: red;*/

margin: 3px 5px;

width: 150px;

}

#tab-content .dom ul li a:hover {

color: orange;

}
</style>
{% endblock %}
<script>
{% block tblHeader %}

hasImport={% if user|HasPerm:"iclock.import_FoodSchedule" %}true{% else %}false{% endif %}
jqOptions[g_activeTabID].colModel={{colModel}}
jqOptions[g_activeTabID].onSelectRow=function(ids){Check_shift(ids);}

tblName[g_activeTabID]='FoodSchedule';
jqOptions[g_activeTabID].pager='id_pager_'+tblName[g_activeTabID]
jqOptions[g_activeTabID].sortname='dining';
options[g_activeTabID].title="{% trans 'FoodSchedule' %}"

//show_left=false
weekStartDay=0;
schClass=[];
selected_data=[]
options[g_activeTabID].dlg_width=400;
options[g_activeTabID].dlg_height=400;
var checkBox = [];

	{% block customHeight %}

    Custom_Jqgrid_Height=200;
    
    {% endblock %}
var flag={% if request.user.is_superuser %}true{% else %}false{% endif %}

function ShowDininghallTree(page, tag, obj, dining_id) {
    var d_url = "/ipos/getData_dining/?func=diningtree&dining_id=" + dining_id;
    var setting = {
        check: {enable: true, chkStyle: "checkbox", chkboxType: {"Y": "", "N": ""}},
        async: {
            enable: true,
            url: d_url,
            autoParam: ["id"]
        }
    };
    $.fn.zTree.init($("#showTree_" + page), setting, null);
    if (tag) {
        var zTree = $.fn.zTree.getZTreeObj("showTree_" + page);
        zTree.setting.check.enable = false;
        zTree.setting.callback.onClick = function onClick(e, treeId, treeNode) {
            var id = treeNode.id;
            $("#department", obj).val(treeNode.name);
            $("#id_parent", obj).val(id);
            hideDeptment();
        }
    }
}





function save_hide_Autued_Deptment_FoodSchedule (obj,page) {
		var deptids=getSelected_dept("showTree_"+page);
		if(deptids.length>0)
		{
			var deptID=deptids[0]
			var deptNames=getSelected_deptNames("showTree_"+page);
			$("#department",obj).val(formatArrayEx(deptNames));
			$("#id_Num_RunOfDept",obj).val(deptID);
		}
		else
		{
			$("#department",obj).val('')
			$("#id_Num_RunOfDept",obj).val('');
		}
		dlgdestroy(page)

}

function num_deptTree(obj){
		var depName=$("#id_span_parent",obj).html();
		depName=$.trim(depName)
		sch_html='<div class="west_info">'
		sch_html=sch_html+'<span style="float:left;border-top:1 solid #5B80B2;"><input alt="department_time" type="text" style="width:150px !important;" disabled="true"  id="department"  value="'+depName+'"></span>'
                if(flag)
                    sch_html=sch_html+'<span style="float:left;"><img class="drop_dept" alt="{% trans 'open department tree' %}" src="/media/img/sug_down_on.gif" id="id_drop_dept_time"/></span>'
		sch_html=sch_html+'</div>'
		$('#id_Num_RunOfDept',obj).after(sch_html)
		$('#id_Num_RunOfDept',obj).hide()
		if(flag)
                {
		$("#id_drop_dept_time",obj).click(function(){
			createQueryDlgbypage('numrun_auth')
			var zTree = $.fn.zTree.getZTreeObj("showTree_numrun_auth");
			zTree.setting.check.enable = false;
			$('#dlg_dept_title_numrun_auth').hide()
			$('#dlg_for_query_numrun_auth').dialog({position: {my: "left top-150", at: "right top",of:"#id_drop_dept_time"},buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
									  click:function(){save_hide_Autued_Deptment_FoodSchedule(obj,'numrun_auth');}},
									 {id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}] })

		
                     });
                }
	}
function num_showDeptment (obj) {
	var top =  $("#department",obj).position().top-135;
	var left =  $("#department",obj).position().left+165;
	var d_height=$("#department",obj).height();
	$("#show_deptment",obj).css("display","block").css({position: 'absolute',"top": top+d_height+6+'px',"left": left+'px'});
}

function process_dialog_FoodSchedule(obj,flag){
    $("#id_start_time",obj).datepicker(datepickerOptions);
    $("#id_end_time",obj).datepicker(datepickerOptions);
    if(flag=='add'){
	    var treeObj = $.fn.zTree.getZTreeObj("showTree_"+g_activeTabID);
        var nodes = treeObj.getSelectedNodes();
	    if (nodes.length>0)
	    {
            var deptID=nodes[0].id;
            var name=nodes[0].name;
            $("#id_dining",obj).val(name);
            $("input[type='hidden'][name='dining']",obj).val(deptID);
	    }
    }

	if(flag=='edit') {
        $('#id_dining', obj).attr('readonly', 'True').css('background', '#E3E3E3');
        $('#id_code', obj).attr('readonly', 'True').css('background', '#E3E3E3');
    }

	num_deptTree(obj);
	f=$(obj).find("#id_edit_form").get(0);
		$(f).validate({
				rules: {
						'Name':{string:true,maxlength:30},
						'start_time': {required:true},
						'end_time': {required:true},
						'Cycle': {required:true,digits:true,min:1,max:1000},
						'Units': {required:true,digits:true}
					}
				});
	
}

function datas_FoodSchedule(id){
	var r=$("#id_grid_"+tblName[g_activeTabID]).jqGrid("getRowData",id);
	var id=r.id
    var code=r.code
    var name=r.name
    var schedule_model=r.schedule_model
	var start_time=r.start_time
	var end_time=r.end_time
	var data=[id,code,$(name).text(), start_time,end_time,schedule_model];
	return data
}
function Check_shift(id){
	data=datas_FoodSchedule(id)
	PageSelectRow(data);
 }
function afterPost_FoodSchedule(flag,obj)
{
	$("#id_edit_form",obj).find(":input[type=text]").each(function(i,elem){
		$(this).val("");})
}

//模糊查询
function searchShowNUM(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
    var url="/iclock/data/FoodSchedule/?q="+encodeURI(v)
    savecookie("search_urlstr",url);
    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function strOfData_FoodSchedule(data)
{
	return stripHtml(data.name);
}

function PageSelectRow(rowData)
{
	selected_data=rowData;
    if(selected_data[5] == '{% trans "temporary food schedule" %}'){

        show_food_shift_Detail(rowData,0);
    }else{
        show_food_shift_Detail(rowData,1);
    }

}


    function show_food_shift_Detail(rowData,delflag) {
        var schedule_id=rowData[0];
        $("#id_Shift_Detail_dlg fieldset legend").html(rowData[1]+"&nbsp;&nbsp;"+"{% trans 'food schedule detail' %}");
        queryStr="schedule_id="+schedule_id;
        var mealDist = '{{ "1"| getPosMeal }}'.replace(/&quot;/g,'"');
        mealDist = eval('(' + mealDist + ')');
        $.ajax({
            type:"POST",
            url: "/iclock/ipos/food_shift_detail/",
            data:queryStr,
            dataType:"json",
            success:function(json){
                var week=["{% trans 'Mon' %}", "{% trans 'Tue' %}", "{% trans 'Wed' %}", "{% trans 'Thu' %}", "{% trans 'Fri' %}", "{% trans 'Sat' %}", "{% trans 'Sun' %}"];
                if(delflag == 1){
                    var th_html = ''
                    for(key in mealDist){
                        th_html += '<th>'+mealDist[key]+'</th>'
                    }
                    var content_html = '';
                    for(var i = 0; i < week.length; i++){
                        content_html += '<tr><td>'+ week[i] + '</td>'
                        for(k in mealDist){
                            if(json.hasOwnProperty(i)){
                                var content_html1 = ''
                                for(var u = 0; u < json[i]['meals'].length;u++){
                                    if(json[i]['meals'][u].split('_')[0] == k){
                                        content_html1 = '<td>'+json[i]['meals'][u].split('_')[1]+'</td>'
                                    }
                                }
                                if(content_html1 != ''){
                                    content_html += content_html1
                                }else{
                                content_html += '<td></td>'
                                }
                            }else{
                                content_html += '<td></td>'
                            }
                        }
                        content_html += '</td>'
                    }
                    var html = '<table  class ="gridtable" ><tr><th>{% trans 'week' %}</th>'+th_html+'</tr>\n'+ content_html + '</table>'
                    $("#tz_dlg_"+tblName[g_activeTabID]).html(html);
                    $("#tz_dlg_"+tblName[g_activeTabID]+ " .tzbar").tooltip()
                }else{
                    var th_html = ''
                    for(key in mealDist){
                        th_html += '<th>'+mealDist[key]+'</th>'
                    }
                    var content_html = ''
                    for(var key in json){
                        content_html += '<tr><td>'+ key + '</td>'
                        for(k in mealDist){
                            for(var i=0 ; i < json[key]['meals'].length;i++){
                                var content_html1 = ''
                                if(k == json[key]['meals'][i].split('_')[0]){
                                    content_html1 += '<td>'+json[key]['meals'][i].split('_')[1]+'</td>'
                                    break;
                                }
                            }
                            if(content_html1 == ''){
                                content_html += '<td></td>'
                            }else{
                                content_html += content_html1
                            }
                        }

                    }

                    var html = '<table  class ="gridtable" ><tr><th>{% trans 'Date' %}</th>'+th_html+'</tr>\n'+ content_html + '</table>'
                    $("#tz_dlg_"+tblName[g_activeTabID]).html(html);
                    $("#tz_dlg_"+tblName[g_activeTabID]+ " .tzbar").tooltip()
                }
            }
        });
    }

function getTZDayLabel(index)
{
	return "{% trans "No." %} "+(index+1)+" {% trans "day" %}"
}
function getTZWeekLabel(index)
{  
    
    index+=parseInt(weekStartDay); 
	return ["{% trans 'Mon' %}","{% trans 'Tue' %}","{% trans 'Wed' %}","{% trans 'Thu' %}","{% trans 'Fri' %}","{% trans 'Sat' %}"],"{% trans 'Sun' %}"[index % 7];
}

var tab_week_day = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

var week=["{% trans 'Mon' %}", "{% trans 'Tue' %}", "{% trans 'Wed' %}", "{% trans 'Thu' %}", "{% trans 'Fri' %}", "{% trans 'Sat' %}", "{% trans 'Sun' %}"];
/*
function getSchClass_html()
{
	var options_html="";
	for(i=0;i<schClass.length;i++)
		options_html+="<option value='"+schClass[i].SchclassID+"'>"+schClass[i].SchName+"("+schClass[i].StartTime.substr(0,5)+"-"+schClass[i].EndTime.substr(0,5)+")</option>"
	return options_html;
}
*/
function getDates_html_num_run(d)
{
    var dates_html="<tr><td>"
           +"<div id='select_div'>"
           +"<input type='checkbox' id='is_select_all_dates_num_run' onclick='check_all_for_row_dates_num_run(this.checked);' />"
           +"({% trans 'Selected:' %} <span id='selected_count_dates'>0</span>)"
       +"</td></tr>";
    var cycle=d[4];
    var unit=d[6];
    var days;
    if(unit==0)
      days=cycle;
    else if(unit==1)
       days=cycle*7;
    else if(unit==2)
       days=cycle*31;
    for(var i=0;i<days;i++)
		dates_html+="<tr><td><input type='checkbox' name='numrundates' class='class_select_dates_num_run' alt='"+i+"' onclick='showSelected_dates_num_run();' />"+((unit==1)?getTZWeekLabel(i):getTZDayLabel(i))+"</td></tr>";
   return dates_html;
}
function showSelected_dates_num_run() {
	if (!$(".class_select_dates_num_run").checked) {  
        $("#is_select_all_dates_num_run").prop("checked", false);  
    }
	var chsub = $("input[type='checkbox'][name='numrundates']").length;  
	var checkedsub = $("input[type='checkbox'][name='numrundates']:checked").length;  
	if (checkedsub == chsub){ 
		$("#is_select_all_dates_num_run").prop("checked", true); 
	}
    var c = 0;
    $.each($(".class_select_dates_num_run"),function(){
			if(this.checked) c+=1;})
    $("#selected_count_dates").html("" + c);
}

function check_all_for_row_dates_num_run(checked) {

	 if ($("#is_select_all_dates_num_run").prop("checked")){ 
		$("input[type='checkbox'][name='numrundates']").prop("checked",true);
	 }else { 
		$("input[type='checkbox'][name='numrundates']").prop("checked",false); 
	 } 
    showSelected_dates_num_run();
}

function getSelected_Dates_nr()
{
    var sdates=[];
    $.each($(".class_select_dates_num_run"),function(){
            if(this.checked) 
                sdates.push(this.alt)
    });
    return sdates;
}
function actionSucess_NoReload_FoodSchedule(retdata)
{
	if(retdata.ret==0){
            dlgdestroy('');
        if (options[g_activeTabID].canEdit||options[g_activeTabID].canDelete){
            show_food_shift_Detail(selected_data,1);
        }else{
            show_food_shift_Detail(selected_data,0);

        }
	}
	else
		alert(retdata.message);

}

function doAction_FoodSchedule(url, action)
{	
	var result=getSelected(options[g_activeTabID].edit_col,"true");
	if (action == 'addShiftTimeTable')
		createDlgShift();
    if (action == 'deleteAllShiftTimeTbl'){
        $.ajax({ type: "POST",
				url: "/iclock/att/deleteAllShiftTime/",
                data:result.ret,
				dataType:"json",
				success: function(retdata){
	                selected_data=datas_FoodSchedule(result.ss[0]);
				    actionSucess_NoReload_FoodSchedule(retdata)
                    alert('{% trans 'successfully deleted' %}')
				},
				error: function(request, errorMsg){
				   alert($.validator.format('{% trans 'Operating failed for {0} : {1}' %}', options[g_activeTabID].title, errorMsg));// $.unblockUI();
				   }
               });
       
    }
}

function addFoodPeriod(id,dining){
    //get_Food(dining)
    roadFoodPeriodData(id)
    WeekForm_Html(id,dining)

}
function editFoodPeriod(id,dining){
    addFoodPeriod(id,dining)

}

function roadFoodPeriodData(id){
    queryStr="schedule_id="+id;
    //queryStr="schedule_code="+code;
    $.ajax({
        type:"POST",
        url: "/iclock/ipos/food_shift_detail/",
        data:queryStr,
        dataType:"json",
        async:false,
        success:function(json){
            schedule_food = json;
        }
    })
}

function createDlgShift1(id){

	createDlgShift(id)
}

function createDlgShift(id)
{	
    if(typeof id=='undefined')
	{
		var result=getSelected(options[g_activeTabID].edit_col,"true");
		if (result.ss.length>1){
			alert('{% trans "Most can only choose a departures" %}');
			return;
		}else
			selected_data=datas_FoodSchedule(result.ss);
	}
	else
        {
		  var result={'ss':id}
		  selected_data=datas_FoodSchedule(id);
         }
		var block_html="<div id='dlg_for_query_'><form id='id_edit_form'>"
						+"<table align='top'>"
						+"<tr>"
						+"<td ><fieldset style='border:1px solid #77B7DE;'><legend>{% trans 'Time-table:' %}</legend>"
						+"<div style='height: 280px;width: 500px;'><table id=id_grid_sches></table><div id=id_pager_sches></div></div></fieldset>"
						+"</td>"
						+"<td>"
						+"<fieldset style='border:1px solid #77B7DE;'><legend>{% trans 'Date:' %}</legend>"
						+"<div style='height:280px;width:200px;overflow-y:scroll'><table >"+getDates_html_num_run(selected_data)+"</table></div></fieldset>"
						+"</td>"                
						+"</tr>"
						+"<tr><td>"
						+"<fieldset style='border:1px solid #77B7DE;padding: 2px;'><legend>{% trans 'Overtime hours setting' %}</legend><input type='checkbox' id='is_overtime' name='is_OT' />{% trans 'This period is recorded as overtime' %}&nbsp;&nbsp;"
						+"{% trans 'Overtime:' %}<input type='text' id='id_overTime_Num_RUN' size='5' name='OverTime' />{% trans 'minute(s)' %}{% trans '(0 means calculated according to time period)' %}"
						+"</fieldset>"
									
						+"</td></tr>"
						+"<tr><td><span id='id_error_sec'></span></td></tr>"
						+"<input type='hidden' value='' name='sTimeTbl' id='id_sTimeTbl' />"
						+"<input type='hidden' value='' name='sDates' id='id_sDates' />"
						+"</td></tr>"         
						+"</table></form>"
						
						+"</div>"

			$(block_html).dialog({  modal:true,
						resizable:false,
						width: 800,
						height:500,
						title:$($("#id_grid_"+tblName[g_activeTabID]).jqGrid("getRowData",result.ss).Name).text()+'{% trans "Add Time-table" %}',
						buttons:[{id:'btnShowOK',text:'{% trans "Submit" %}',click:function(){btn_addTimeZone(); }},{text:'{% trans "Return" %}',click:function(){$(this).dialog("destroy"); }}],
                                                close:function(){$(this).dialog("destroy"); } 
                                                
					      });
			
			var jqOptions2=copyObj(jq_Options);
			jqOptions2.colModel=[
						{name: 'SchclassID', hidden:true},
						{name: 'SchName', index: 'SchName', width: 120,label:'{% trans "time slot name" %}'},
						{name: 'StartTime', index: 'StartTime', width: 90,label:'{% trans "Starting time" %}'},
						{name: 'EndTime',  width: 90,label:'{% trans "End Time" %}'},
						{name: 'TimeZoneOfDept',  width: 100,label:'{% trans "Home unit" %}'}
			]
 
			jqOptions2.sortname="SchclassID";
			jqOptions2.sortorder="";
			jqOptions2.url="/iclock/data/SchClass/?t=SchClass_mini_list.js"
			jqOptions2.height=220
			jqOptions2.gridComplete=''
			jqOptions2.pager="#id_pager_sches";
			renderGridData('sches',jqOptions2)
	   
}


function btn_addTimeZone()
{
	var overTime=$("#id_overTime_Num_RUN").val();
	//var sTimeTbl=$("#id_sTimeTbl_sel").val();
	var sTimeTbl=getSelected_emp_ex('sches')
	var sDates=getSelected_Dates_nr();
	if(sTimeTbl.length==0 || sDates.length==0)
		$("#id_error_sec").html("<ul class='errorlist'><li>{% trans 'The shift time-table and the dates should be select at least one' %}</li></ul>").show();
	else{
		$("#id_error_sec").css("display","none");
		$("#id_sTimeTbl").val(sTimeTbl);
		$("#id_sDates").val(sDates);
		var queryStr=$("#id_edit_form").formSerialize()+"&shift_id="+selected_data[0]+"&cycle="+selected_data[4]+"&unit="+selected_data[6]+"&weekStartDay="+weekStartDay;
		$.ajax({ type: "POST",
			url: "/iclock/att/addShiftTimeTable/",
			data:queryStr,
			dataType:"json",
			success: function(retdata){
				actionSucess_NoReload_FoodSchedule(retdata)
				},
			error: function(request, errorMsg){
				alert($.validator.format('{% trans 'Operating failed for {0} : {1}' %}', options[g_activeTabID].title, errorMsg)); //$.unblockUI();
				}
		});
		
	}
}

    //时间有效性验证 开始日期要小于结束日期
function validate_date(ComeTime,EndDate)
{
	var cTime=ComeTime.split("-");
	var eTime=EndDate.split("-");
	var cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
	var edate=new Date(parseInt(eTime[0],10),parseInt(eTime[1],10)-1,parseInt(eTime[2],10));
	var days=(edate.valueOf()-cdate.valueOf())/(1000*3600*24);
	return days;
}

function createEvents()
{
		$('#id_schclasses .class_select_schClass').each(function() {
			var eventObject = {
				alt:$(this).attr('Alt'),
				title: $.trim($(this).attr('Alt1')) 	// use the element's text as the event title
			};
			$(this).data('eventObject', eventObject);
			$(this).draggable({
				zIndex: 999,
				revert: true,      			// will cause the event to go back to its
				revertDuration: 0  			//  original position after the drag
			});
		});
}

function get_day_tmp_name() {
    $.ajax({
        type:"POST",
        dataType:"json",
        url:"/ipos/get_day_tmp_name/",
        async:false,
        success:function (data) {
            day_tmp_name = data['name'];
        }
    })
}

function DayForm_Html(dining_id, scheduletime){
    var mealDist = '{{ "1"| getPosMeal }}'.replace(/&quot;/g,'"');
    mealDist = eval('(' + mealDist + ')');
    var html_content = '';
    get_day_tmp_name();
    var html = '<div id="day_form">'+
            '<div>' +
            '<font color="red">*</font><label for="id_name">{% trans 'Name: ' %}</label>' +
            '<input type="text" name="name" maxlength="20" required="" id="id_name" value="'+day_tmp_name+'">' +
            '</div>' +
            '<br/>' +
            '<div id="tab">\n' +
           '<!--选项的头部-->\n' +
           '<div id="tab-header">\n'+
            '</div>';


     for(var key in mealDist){
            var html_food_sch = '';
            for(var j = 0; j < FoodMenu.food_menu_list.length; j++){
                html_food_sch += '<li style="float: left; width:150px;padding-bottom:5px">\n' +
                                '<input type="checkbox" name="'+key+'_Food" value="'+FoodMenu.food_menu_list[j].code+'"> '+
                               FoodMenu.food_menu_list[j].name +
                               '</li>\n'
            }

            html_content += '<div style=" width:220px;\n' +
                            '            float:left;height:100%' +
                            '            clear:right;' +
                            'margin-right: 10px;\n' +
                            'padding-right: 1px;"><ul><li style="font-size:17px;"><strong>'+mealDist[key]+
                            '</strong></li></ul>' +

                            '<div class="tab_meal" id="'+key+'_tab_meal" style="height:400px;width: 100%;border: 1px solid #ddd;' +
                                'overflow:scroll;overflow-x:hidden">' +
                                '<ul id="sch_meal_'+key+'">'+html_food_sch+'</ul>' +
                            '</div></div>';

     }
     html +='<!--主要内容-->\n' +
            '<div id="tab-content">\n' +
                html_content +
            '</div>\n' +
            '</div>' +
            '</div>';

    $(html).dialog({modal:true,
        resizable:false,
        width: 970,
        height:650,
        title:'{% trans "Temporary Order Shifts" %}'+'('+scheduletime+')',
        buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
									  click:function(){save_day_FoodSchedule(dining_id, scheduletime);}},
				{id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}],
        close:function(){$("#day_form").remove();}
    });
    $(".tab-ul li").on('click',function(){
        $(this).addClass("selected").siblings().removeClass("selected");
        $("#tab-content .dom").eq($(".tab-ul li").index(this)).show().siblings("#tab-content .dom").hide();
    })

    }

    function save_week_FoodSchedule(id){
        var content_obj = document.getElementById('tab-content')
        var tab_meal_obj = document.getElementsByClassName('tab_meal')
        var data = {}
        for (var j = 0; j < tab_meal_obj.length; j++){
            var input = tab_meal_obj[j].getElementsByTagName("input");
            var weekday = tab_meal_obj[j].id;
            for (var i = 0; i < input.length; i++){
                var obj = input[i];
                //判断是否是checkbox并且已经选中
                if (obj.type == "checkbox" && obj.checked){
                    var name = obj.name
                    var code = obj.value;//获取checkbox的值
                    var item = [name,code]
                    name = name.split("_")[0] +'_'+ weekday[0]
                    if(name in data){
                        data[name] = data[name]+','+code
                    }else{
                        data[name] = code
                    }
                }

            }
        }
        $.ajax({
            type:"POST",
            url: "/iclock/ipos/set_foodschedule/?tag=week&id="+id,
            dataType:"json",
            data:{'data':JSON.stringify(data)},
            success:function(data){
                        alert(data.message)
        		}
		});
    }
    function save_day_FoodSchedule(dining_id, scheduletime){
        var content_obj = document.getElementById('tab-content');
        var id_name = $("#id_name").val();
        if (id_name == ''){
            alert('{% trans 'Please enter a name!' %}')
        }else{
            var input = content_obj.getElementsByTagName("input");
            var data = {};
            for (var i = 0; i < input.length; i++){
                var obj = input[i];
                //判断是否是checkbox并且已经选中
                if (obj.type == "checkbox" && obj.checked){
                    var name = obj.name[0];
                    var code = obj.value;//获取checkbox的值
                    var item = [name,code];
                    if(name in data){
                        data[name] = data[name]+','+code
                    }else{
                        data[name] = code
                    }
                }

            }
            if ($.isEmptyObject(data)){
                alert('{% trans 'Meal scheduling failed, Please select the dishes' %}');
                return false;
            }
            $.ajax({
            type:"POST",
            url: "/iclock/ipos/set_foodschedule/?tag=day&dining_id="+dining_id+"&id_name="+id_name+"&scheduletime="+scheduletime,
            dataType:"json",
            data:{'data':JSON.stringify(data)},
            success:function(data){
                        alert(data.message)
                        $("#day_form").remove();
                    }
            });
        }
    }

function WeekForm_Html(id,dining){
    var mealDist = '{{ "1"| getPosMeal }}'.replace(/&quot;/g,'"');
    mealDist = eval('(' + mealDist + ')');
    var html_content = ''
     var html = '<div id="week_form">'+
        '<div id="tab">\n' +
           '<!--选项的头部-->\n' +
           '<div id="tab-header">\n' +
               '<ul class="tab-ul">\n';
    var tab_week_day = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    var week=["{% trans 'Mon' %}", "{% trans 'Tue' %}", "{% trans 'Wed' %}", "{% trans 'Thu' %}", "{% trans 'Fri' %}", "{% trans 'Sat' %}", "{% trans 'Sun' %}"];
     for(var i = 0; i < tab_week_day.length; i++){
            if(i == 0){
                html += '<li class="selected">\n'
                html_content += '<div id="tab_FoodSchedule_'+tab_week_day[i]+'" class="dom" style="display: block;">\n'
            }else{
                html += '<li class="">\n'
                html_content += '<div id="tab_FoodSchedule_'+tab_week_day[i]+'" class="dom">\n'

            }
            html += '<a href="#">\n' +
                        week[i] +
                    '</a>\n' +
               '</li>\n';


            var html_meal = ''
            for(var key in mealDist){
                if(schedule_food.hasOwnProperty(i)) {
                    var html_food_menu = '';
                    for (var u = 0; u < schedule_food[i]['meals'].length; u++) {
                        if (schedule_food[i]['meals'][u].split('_')[0] == key) {
                            food_menu = schedule_food[i]['meals'][u].split('_')[1].split(',');
                            food_id = schedule_food[i]['mealsid'][u].split(',');
                            for (var j = 0; j < food_menu.length; j++){
                                if(food_menu[j] != ""){
                                html_food_menu += '<li style="float: left;" id="sch_meal_'+food_id[j]+'">\n' +
                                                  '<label>'+food_menu[j]+'</label>' + ' '+
                                                   "<a href='#' onclick='del_food("+food_id[j]+","+key+");'><img title='{% trans 'Remove the dish from the menu' %}' src='../media/img/close1.png'/></a>"+
                                                   '</li>\n'
                                }
                            }
                        }
                    }
                }else{
                    html_food_menu = '<li></li>'
                }
                html_meal += '<div style=" width:220px;\n' +
                    '            float:left;height:100%' +
                    '            clear:right;' +
                    'margin-right: 10px;\n' +
                    'padding-right: 1px;"><ul><li>'+mealDist[key]+'：<a href="#" onclick="add_food_to_sche' +
                    '('+i+',' +
                    ''+key+',' +
                    ''+dining+','+id+');"><img title="{% trans 'Add dishes' %}" src="../media/img/add.png"/></a></li></ul>' +

                '<div class="tab_meal" id="'+key+'_tab_meal" style="height:400px;width: 100%;border: 1px solid #ddd;' +
                    'overflow:scroll;overflow-x:hidden">' +
                    '<ul id="sch_meal_'+i+'_'+key+'">'+html_food_menu+'</ul>' +
                '</div></div>'

             }

            html_content += '<div style="height: 100%">\n' +
                    html_meal +
                    '</div>\n' +
               '</div>\n'
            }
        html += '</ul>\n' +
                   '</div>\n' +
                   '<!--主要内容-->\n' +
                   '<div id="tab-content">\n' +
                        html_content +
                   '</div>\n' +
                '</div>';
    $(html).dialog({modal:true,
        resizable:false,
        width: 970,
        height:650,
        title:'{% trans "Periodic food schedule" %}',
        buttons:[
				{id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}],
        close:function(){$("#week_form").remove();}
    });
    $(".tab-ul li").on('click',function(){
        $(this).addClass("selected").siblings().removeClass("selected");
        $("#tab-content .dom").eq($(".tab-ul li").index(this)).show().siblings("#tab-content .dom").hide();
    })
}

function del_food(food_id, meal){
    $.ajax({
        type: "POST",
        async: false,
        url: "ipos/del_food/?meal_id="+food_id+"&meal="+meal,
        dataType: "json",
        success:function (data) {
            if(data.ret==0){
                $("#sch_meal_"+food_id).hide();
            }
        }
    })
}

function get_choose_food(week_day, meal_id, id){
    $.ajax({
        type: "POST",
        async: false,
        url: "ipos/get_choose_food/?schdule_id="+id+"&week_day="+week_day+"&meal_id="+meal_id,
        dataType: "json",
        success:function (json) {
            choose_food = json
        }
    })
}


function add_food_to_sche(week_day, meal_id, dining, id){
    //get_choose_food(week_day, meal_id, id);
    var cPage = ''
    var block_html="<div id='simple_food_data'>"
                    +"<div>"
                    +"<table id='id_grid_mini' >	</table>"
                    +"<div id='id_pager_mini'></div>"
                    +"</div>"
                    +"<div id='id_message_simple'></div>"
                    +"</div>"
    $(block_html).dialog({modal:true,
        resizable:false,
        width: 500,
        height:500,
        title:'{% trans "List of dishes" %}',
        buttons:[{id:"btnShowOK",text:'{% trans 'determine' %}',
									  click:function(){if(window['beforeSave_week_meal_food_sch'](this)==false) return ;save_week_meal_FoodSchedule(id,week_day,meal_id,dining);$(this).dialog("destroy");}},
				{id:"btnShowCancel",text:'{% trans 'Return' %}',click:function(){$(this).dialog("destroy"); }
									}],
        close:function(){$("#simple_food_data").remove();}
    });
    food_data_url = "/iclock/simple_data/get_food_data/?dining_id="+dining+"&sch_id="+id+"&week_day="+week_day+"&meal_id="+meal_id;
    var jqOptions_mini=copyObj(jq_Options);
    jqOptions_mini.multiselect=true;
    $.ajax({
        type:"GET",
        url:"/iclock/att/getColModel/?dataModel=simple_food_data",
        dataType:"json",
        data:'',
        async:false,
        success:function(json){
            jqOptions_mini.rows=100;
            jqOptions_mini.colModel=json['colModel'];
            jqOptions_mini.height=300;
            jqOptions_mini.url=food_data_url;
            jqOptions_mini.pager="#id_pager_mini";
            jqOptions_mini.gridComplete=null;
            jqOptions_mini.editurl=food_data_url;
            jqOptions_mini.beforeSelectRow=function (rowid, e) {
                var $myGrid = $(this),
                i = $.jgrid.getCellIndex($(e.target).closest('td')[0]),
                cm = $myGrid.jqGrid('getGridParam','colModel');
                return (cm[i].name ==='cb');
            },
            jqOptions_mini.onPaging=function(){
                  cPage++; //翻页事件
            },
            jqOptions_mini.loadComplete=function(data){
              if (cPage == 0) {
                  checkBox = [];//每次加载清空选中状态
              }
              var rowArr = data.rows;
              if (checkBox.length > 0) {
                  for (var i = 0; i < rowArr.length; i++) {
                      for (var j = 0; j < checkBox.length; j++) {
                          if (rowArr[i].id == checkBox[j]) {
                              $("#id_grid_mini").jqGrid('setSelection', rowArr[i].id);
                              break;
                          }
                      }
                  }
              }
            },
            jqOptions_mini.onSelectRow=function(id, status){
                var row = $("#id_grid_mini").jqGrid('getRowData', id).id;
                if (status){
                    checkBox.push(row)
                }else{
                    for (var i = 0; i < checkBox.length; i++)
                    {
                        if(checkBox[i]==row)
                        {
                            checkBox.splice(i,1);
                        }
                    }
                }
            },
            jqOptions_mini.onSelectAll=function(id, status){
                for (var index = 0; index < id.length; index++) {
                    var row = $("#id_grid_mini").jqGrid('getRowData', id[index]).id;
                    if (status)
                    {
                        if (checkBox.toString().indexOf(row) <0) {
                            checkBox.push(row);
                        }
                    }
                    else
                    {
                        for (var i = 0; i < checkBox.length; i++) {
                            if (checkBox[i] == row) {
                                checkBox.splice(i, 1);
                            }
                        }
                    }
                }
            }
            renderGridData("mini",jqOptions_mini)
        }
    })
}

function beforeSave_week_meal_food_sch(){
    /*
    var foodid = $("#id_grid_mini").jqGrid("getGridParam", "selarrrow");
    if(foodid==''){
        alert('{% trans 'Please select the dishes that need to be added!' %}');
        return false;
    }

     */
    if(checkBox.length==0){
        alert('{% trans 'Please select the dishes that need to be added!' %}');
        return false;
    }
}

function save_week_meal_FoodSchedule(id,week_day,meal_id) {
    var food_checked = [];
    for(var i=0;i<checkBox.length;i++){
        if(food_checked.indexOf(checkBox[i])==-1){
            food_checked.push(checkBox[i])
        }
    }
    formdata = new FormData();
    formdata.append('foodid',food_checked);
    formdata.append('week_day', week_day);
    formdata.append('meal_id', meal_id);
    formdata.append('sch_id', id);
    $.ajax({
        type:"POST",
        url:"ipos/save_week_meal_foodschedule/",
        data:formdata,
        dataType:"json",
        async:false,
        contentType:false,
        processData:false,
        success:function (data) {
            if (data.ret==0){
                //alert('{% trans 'Meal arrangement successful' %}');
                meal_sch = data.meal_sch;
                food_sch = data.food_sch;
                week = data.week;
                meal = data.meal;
                for(var i=0;i<meal_sch.length;i++){
                    $("#sch_meal_"+week+"_"+meal).append('<li style="float: left;" id="sch_meal_'+meal_sch[i]+'">' +
                        '<label>'+food_sch[i]+'</label>' + ' ' +
                        '<a href="#" onclick="del_food('+meal_sch[i]+','+meal+');"><img title="{% trans 'Remove the dish from the menu' %}" src="../media/img/close1.png"/></a>' +
                        '</li>')
                }
            }else
                alert('{% trans 'Food schedule failed' %}')
        }
    })

}


function get_Food(id){
    $.ajax({
    type:"POST",
    async: false,
    url: "/iclock/ipos/get_food/?dining_id="+id,
    dataType:"json",
    success:function(json){
                FoodMenu = json
            }
    });

}

function addfood_tmpShift(){
	var treeObj = $.fn.zTree.getZTreeObj("showTree_"+g_activeTabID);
	var nodes = treeObj.getSelectedNodes();
	var deptIDs='';
	var deptName='';
    if(nodes.length==0){alert('{% trans "Please first expand the restaurant tree on the left and select the restaurant" %}');return;}
	if (nodes.length>0)
	{
	    deptIDs=nodes[0].id;
	    deptName=nodes[0].name;
	}
	var ComeTime=$("#id_ComeTime_"+tblName[g_activeTabID]).val();
	var EndDate=$("#id_EndTime_"+tblName[g_activeTabID]).val();


    var cTime=ComeTime.split("-");
    var days=validate_date(ComeTime,EndDate)+1;
    var	cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
    $("#"+g_activeTabID+" #id_error").css("display","none");
    $.cookie("ComeTime",ComeTime, { expires: 7 });
    $.cookie("EndDate",EndDate, { expires: 7 });
    var eTitle=""
    eTitle+=deptName;
    var block_html="<div id='TemporaryShifts' style='width:600px;padding-top:0;padding-bottom:0;'>"
        +"<div class='dcontent'><form id='id_edit_form_user_of_run'>"
        +"<table align='center'>"
        +"<tr><td>&nbsp;</td></tr>"
        +"<tr>"
            +"<td>"
			+"<div id='calendar' style='width:650px;float: right;'></div>"
			+"</td></tr>"
        +"<tr><td><span id='id_error_sec'></span></td><td>"
        +"<div id='dlg_btn' style='text-align:right; margin-top: 10px; margin-right: 20px;margin-bottom:10px;'>"
        +"<input type='button' value='"+'{% trans 'Return' %}'+"' id='btnCancel_user_of_run' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only'>&nbsp;&nbsp;&nbsp;"
        +"</div>"
        +"</td></tr>"
        +"<input type='hidden' value='' name='sTimeTbl' id='id_sTimeTbl' />"
        +"<input type='hidden' value='' name='sDates' id='id_sDates' />"
        +"<input type='hidden' value='' name='sDates_st' id='id_sDates_st' />"
        +"<input type='hidden' value='' name='sDates_et' id='id_sDates_et' />"
        +"</td></tr>"
        +"</table></form>"

        +"</div>"
        +"<div class='ui-widget'>"
            +"<div class='ui-widger-header ui-corner-all' style='margin-top: 20px; padding: 0 .7em;'>"
                +"<p><span class='ui-icon ui-icon-info' style='float: left; margin-right: .3em;'></span>"
                +"<strong>{% trans 'Operation Tips:' %}</strong>{% trans 'You can click the day to schedule operate with the mouse' %}</p>"
            +"</div>"
        +"</div>"

    $(block_html).dialog({modal:true,
        resizable:false,
        width: 780,
        height:540,
        title:'{% trans "temporary food schedule" %}'+"("+eTitle+")",
        close:function(){$("#TemporaryShifts").remove();reloadData(tblName[g_activeTabID]);}
    });

    get_Food(deptIDs);

    createEvents();
    cal=$('#calendar').fullCalendar({
        header: {
            left: 'prev,next, today',
            center: 'title',
            right: 'month'
        },
        buttonText:{
            prev:'{% trans 'forward' %}',
            next:'{% trans 'backward' %}',
            today:'{% trans 'Nowadays' %}',
            month:'{% trans 'by month' %}',
            agendaWeek:'{% trans 'by week' %}',
            agendaDay:'{% trans 'By day' %}',
        },
        titleFormat:'YYYY/MM/DD',
        allDayText:'{% trans 'All day' %}',
        aspectRatio:2.0,
        axisFormat:'H(:mm)',
        timeFormat:{agendaDay:'H:mm{ - H:mm}',agendaWeek:'H:mm{ - H:mm}'},
        theme:true,
        selectable: true,
        unselectCancel:'',
        eventBackgroundColor:'#ff0000',
        unselectAuto:false,
        dayNames:['{% trans 'Sun' %}','{% trans 'Mon' %}','{% trans 'Tue' %}','{% trans 'Wed' %}','{% trans 'Thu' %}','{% trans 'Fri' %}','{% trans 'Sat' %}'],
        dayNamesShort:['{% trans 'Sun' %}','{% trans 'Mon' %}','{% trans 'Tue' %}','{% trans 'Wed' %}','{% trans 'Thu' %}','{% trans 'Fri' %}','{% trans 'Sat' %}'],
        editable: true,
        droppable: true,
        diableResizing:false,
        drop: function(date, allDay) { // this function is called when something is dropped
            var originalEventObject = $(this).data('eventObject');
            var copiedEventObject = $.extend({}, originalEventObject);

            copiedEventObject.start = date;
            copiedEventObject.allDay = allDay;
            $('#calendar').fullCalendar('renderEvent', copiedEventObject, true);
            var copyevent=$.extend({}, eventObj);
            copyevent.events=copiedEventObject
            copyevent.eventName='drop'
            postShiftData(copyevent)
        },
        eventResize: function(calEvent, dayDelta, revertFunc) {
            if (dayDelta) {
                 var copyevent=$.extend({}, eventObj);
                copyevent.events=calEvent
                copyevent.eventName='eventResize'
                //copyevent.delta=dayDelta.days()
                postShiftData(copyevent)
            }else{
                 alert(revertFunc)
            }


        },
        eventResizeStart:function(event, jsEvent, ui, view ){
            var st=event.start.format("YYYY-MM-DD")
            var tmp=event.end;
            if(tmp==null){
                tmp=event.start;
            }
            var et=tmp.format("YYYY-MM-DD")
            $("#id_sDates_st").val(st)
            $("#id_sDates_et").val(et)
         },
        eventDrop: function(event, delta) {
            var copyevent=$.extend({}, eventObj);
            copyevent.events=event
            copyevent.eventName='eventdrop'
            copyevent.delta=delta.days()
            postShiftData(copyevent)
        },
        eventDragStart:function(event, jsEvent, ui, view ){
            var st=event.start.format("YYYY-MM-DD")
            var tmp=event.end;
            if(tmp==null){
                tmp=event.start;
            }
            var et=tmp.format("YYYY-MM-DD")
            $("#id_sDates_st").val(st)
            $("#id_sDates_et").val(et)
         },
        dayClick:function(start, allDay, jsEvent, view){
            DayForm_Html(deptIDs, start.format("YYYY-MM-DD"))
        },
        eventClick:function(calEvent, jsEvent, view){
            var start=calEvent.start;
            var end=calEvent.end;
            if(end==null){
                end=start
            }
            var daystr=start.format("YYYY-MM-DD")
            var dayend=end.format("YYYY-MM-DD")
            var shiftid=calEvent.alt;
            var title=calEvent.title;
            str="{% trans "Are you sure delete temporary shift for the selected employee" %}{% trans "or department" %}({% trans "employee:" %}"+emp.length+"):\n"+title+"\n{% trans "the between" %}"+daystr+"{% trans "to" %}"+dayend+"{% trans "Shifting" %}"
            action=confirm(str)
            if(action){
                ClearDayFormData(start,end,shiftid,'')
            }
        }
    })
    $("#btnCancel_user_of_run").click(function(){$("#TemporaryShifts").remove();reloadData(tblName[g_activeTabID]);});

}

{% endblock %}










{% block $function %}
    var day_tmp_name = '';
    var FoodMenu = '';
    var schedule_food = '';
    var choose_food = '';
	var info='<div class="west_info"><p>{% trans "1. Shifts are used for periodic scheduling of employees, and the period can be set to days, weeks, and months." %}</p><p>{% trans "2. The role of the vesting unit is to divide the area when there is too much shifts in a large unit, and use it in combination with the authorized shifts of the user." %}</p></div>'
        smenu="<ul><li  class='subnav_on' onclick=submenuClick('/iclock/data/FoodSchedule/',this);><a href='#'>{% trans "shift management" %}</a></li></ul>"
        //$('#menu_div').html(smenu)
//$("#id_Shift_Detail_dlg").css("width",$("#id_content").width()-10);
//$(".module").css("height","43%");
	$("#"+g_activeTabID+" #id_newrec").click(function(event){
        var treeObj = $.fn.zTree.getZTreeObj("showTree_"+g_activeTabID);
        var nodes = treeObj.getSelectedNodes();
        if(nodes.length==0){alert('{% trans "Please first expand the restaurant tree on the left and select the restaurant" %}');return;}
		processNewModel();
	});
    $("#"+g_activeTabID+" #id_search").click(function () {
        search_food_schdule();
    })
	$("#"+g_activeTabID+" #queryButton").hide()
	$("#"+g_activeTabID+" #allDelete").hide()
	$("#"+g_activeTabID+" #id_export").css('display','none');
	$("#"+g_activeTabID+" #id_third").html("");
    setDate(tblName[g_activeTabID])
	$("#tz_dlg_FoodSchedule").html("<div align='center'><h4>{% trans "Click food schedule to show its schedule details" %}</h4></div>");
	 $.ajax({
		type:"POST",
		url: "/iclock/att/attrule/",
		dataType:"json",
		success:function(json){
					weekStartDay=json[0].WorkWeekStartDay;
        		}
		});

	$("#"+g_activeTabID+" #id_reload").click(function(){
		$('#id_Shift_Detail_dlg').html("<fieldset style='border:1px solid #7ac143;'><legend></legend>"
				+"<div id='tz_dlg_FoodSchedule' style='overflow-x:hidden;overflow-y: scroll; height:160px;width:960px;'>"
				+"</div></fieldset>")
		$("#tz_dlg_FoodSchedule").html("<div align='center'><h4>{% trans "Click food schedule to show its schedule details" %}</h4></div>");
		reloadData(tblName[g_activeTabID]);
	});
	
	var h=$('#id_content').height()-Custom_Jqgrid_Height-140;
	$("#id_Shift_Detail_dlg").css('height',h)
    $('#tz_dlg_FoodSchedule').css('height',h-20)
	$("#"+g_activeTabID+" #searchButton").click(function(){
	   searchShowNUM();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	   if(event.keyCode==13)
	  searchShowNUM();
	});

function search_food_schdule() {
    var st = $("#id_ComeTime_FoodSchedule").val();
    var et = $("#id_EndTime_FoodSchedule").val();
    var food_sch_way = $("#id_food_sch").val();

    var urlTime1='';
    var urlother='';
    var isError=validate_form_food_sch();
    if(!isError){
        urlTime1 = "?start_time__gte="+st+"&end_time__lte="+et;
    }else{
        alert('{% trans "Start time cannot be greater than end time" %}')
        return
    }
    if (food_sch_way != ''){
        urlother = "&schedule_model="+food_sch_way;
    }
    var url = "/ipos/data/FoodSchedule/"+urlTime1+urlother;
    savecookie("search_urlstr",url);
    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function validate_form_food_sch(){   //验证表单的合法性(人员(可不选，在计算项目中计算选中部门的人员，其他的就默认值0)、开始时间、结束时间)
        var t_ComeTime=$("#id_ComeTime_FoodSchedule").val();
        var cTime=t_ComeTime.split("-");
        var t_EndDate=$("#id_EndTime_FoodSchedule").val();
        var eTime=t_EndDate.split("-");
        cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
        edate=new Date(parseInt(eTime[0],10),parseInt(eTime[1],10)-1,parseInt(eTime[2],10));
        var days=(edate.valueOf()-cdate.valueOf())/(1000*3600*24)+1;
        if(cdate>edate || t_ComeTime=="" || t_EndDate==""||!valiDate(t_ComeTime)||!valiDate(t_EndDate)){
                return 1;
        }else{
                return 0;
        }
}

function setDate(page_style)
{

	$("#id_ComeTime_"+page_style).datepicker(datepickerOptions);
	$("#id_EndTime_"+page_style).datepicker(datepickerOptions);
	var currDate=new Date();
	//td=moment().format("YYYY-MM-DD")

	td=currDate.getFullYear()
		+"-"
		+(currDate.getMonth()+1<10?"0"+(currDate.getMonth()+1):currDate.getMonth()+1)
		+"-"
	day=(currDate.getDate()<10?"0"+currDate.getDate():currDate.getDate())
	$("#id_ComeTime_"+page_style).val(td+"01")
	//$("#id_EndTime_"+page_style).val(td+day)
	$("#id_EndTime_"+page_style).val(moment().endOf('month').format("YYYY-MM-DD"))

	/*if(loadcookie("id_ComeTime_"+page_style)){
		$("#id_ComeTime_"+page_style).val(loadcookie("id_ComeTime_"+page_style))
		$("#id_EndTime_"+page_style).val(loadcookie("id_EndDate_"+page_style))
	}
	else{
		$("#id_ComeTime_"+page_style).val(td+"01")
		$("#id_EndTime_"+page_style).val(td+currDate.getDate())
	}*/

}

{% endblock %}


{% block loadData %}
    html = "<div id=id_opt_tree><input type='checkbox' id='id_cascadecheck_" + g_activeTabID + "'"
        + " checked/>{% trans 'Include Subordinates' %}<span id='refresh_tree_dininghall' title='{% trans "Reload" %}'  class='icon iconfont icon-shuaxin' style='float:right;padding-right:10px;padding-top:5px;cursor:pointer;'></span></div>"
    $("#" + g_activeTabID + " #id_west").html(html);
    html = "<div id='show_dept_tree_'>"
        + "<ul id='showTree_" + g_activeTabID + "'"
        + " class='ztree' style='margin-left: 0px;height:100%;'></ul>"
        + "</div>";
    $("#west_content_tab_ipos_FoodSchedule").html(html);
    ShowDininghallData(g_activeTabID, true);
    loadNULLPage('#id_grid_' + tblName[g_activeTabID]);
    var zTree = $.fn.zTree.getZTreeObj("showTree_" + g_activeTabID);
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode) {
        $("#tz_dlg_FoodSchedule").empty();
        $("#id_Shift_Detail_dlg fieldset legend").html("");
        $("#tz_dlg_FoodSchedule").html("<div align='center'><h4>{% trans "Click food schedule to show its schedule details" %}</h4></div>");
        var code = treeNode.id;
        var ischecked = 0;
        if ($("#id_cascadecheck_" + g_activeTabID).prop("checked"))
            ischecked = 1;
        if (code != 0) {
            var urlStr = "/ipos/data/FoodSchedule/?mod_name=" + mod_name + "&dining=" + code + "&isContainChild=" + ischecked
        } else {
            var urlStr = "/ipos/data/FoodSchedule/"
        }
        savecookie("search_urlstr", urlStr);
        $("#id_grid_" + tblName[g_activeTabID]).jqGrid('setGridParam', {
            url: urlStr,
            datatype: "json"
        }).trigger("reloadGrid");
    };
{% endblock %}

</script>

{% block top %}
	<div class="sear-box quick-sear-box"  style="min-width:1200px;">
        {% block date_range %}
            <div id="search_Time" class='left' style="width:900px;">
               <label >{% trans "Begin Date" %}</label>
               <input type="text" name="ComeTime" maxlength="10" id="id_ComeTime_FoodSchedule" style='width:80px !important;'/>

               <label  >{% trans "End Date" %}</label>
               <input type="text" name="EndTime" maxlength="10" id="id_EndTime_FoodSchedule"  style='width:80px !important;'/>

                <label>{% trans "Food schedule arrangement" %}</label>
                <select id="id_food_sch">
                    <option value="">--------</option>
                    <option value="0">{% trans "temporary food schedule" %}</option>
                    <option value="1">{% trans "Periodic food schedule" %}</option>
                </select>
                {% block search_range_txt %}
                {% endblock %}
              &nbsp;<span id='id_search' ><a class='m-btn  zkgreen rnd mini'>{% trans 'Query' %}</a></span>

            </div>
        {% endblock %}

		{% block line %}
		{% endblock %}

		{% block search %}
		{% endblock %}
	</div>
{% endblock %}

{% block extractButton %}
    {% if user|HasPerm:"ipos.temporary_food_schedule" %}
      <LI id="id_temporary_schedule" onclick="addfood_tmpShift()" ><SPAN class="icon iconfont icon-lingshipaiban"></SPAN>{% trans "temporary food schedule" %}</LI>
    {% endif %}
{% endblock %}

{% block extraSection %}
<div id="id_Shift_Detail_dlg" style="overflow-x:hidden;overflow-y: hidden; height: 200px;margin-top:5px;">
<fieldset style="border:1px solid #7ac143;"><legend></legend>
<div id="tz_dlg_FoodSchedule" style="overflow-x:hidden;overflow-y: auto; height:150px;width:960px;">
</div></fieldset>
</div>
<div id="id_numrun_tip" class="numrun_tip"></div>
{% endblock %}