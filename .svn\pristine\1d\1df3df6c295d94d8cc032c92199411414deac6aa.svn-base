{% extends "report_ipos.html" %}
{% load i18n %}
{% load iclock_tags %}


{% block getDateUrl %}

function getDateUrl(pos_start_date,pos_end_date)
{

	$("#id_con_error").css("display","none");
	var urlStr=g_urls[g_activeTabID]
	jqOptions.sortname='pos_time'
	var st=moment().startOf('month').format('YYYY-MM-DD')
	var et=moment().endOf('month').format('YYYY-MM-DD')
        if(pos_start_date) st=pos_start_date
        if(pos_end_date) et=pos_end_date+" 23:59:59"
        
	if(urlStr.indexOf("?")!=-1){
		urlStr=urlStr+"&pos_time__gte="+st+"&pos_time__lte="+et;
	}
	else{
		urlStr=urlStr+"?pos_time__gte="+st+"&pos_time__lte="+et;
	}
    
	return urlStr

}
{% endblock %}


{% block queryButton %}
	
$("#"+g_activeTabID+" #queryButton").click(function(){
	createQueryDlg();
});


function searchbydept_ICConsumerList(page) {

	//var dept_ids = getSelected_dept("showTree_" + page)
	//if (dept_ids != null) {
	//	if (dept_ids == undefined || dept_ids == '') {
	//		alert("{% trans "Please select department" %}")
	//		return false;
	//	}
	//} else {
	//	alert("{% trans "Please select department" %}")
	//	return false;
	//}
	//var ischecked = 0;
	//if ($("#id_cascadecheck_" + page).prop("checked")) ischecked = 1;
	//urlStr = "deptIDs=" + dept_ids + "&isContainChild=" + ischecked
	var emp = getSelected_emp_ex("sel_employee_search_ICConsumerList");
	if (emp.length > 0) {
		urlStr = "&user_id__in=" + emp
	}
	else{
		alert("{% trans "Please select a person" %}")
	}

        var isError=validate_form_ipos();
	if(isError){
 
		var st=$("#"+g_activeTabID+" #id_StartDate").val();
		var et=$("#"+g_activeTabID+" #id_EndDate").val();
		var url = "/ipos/report/ICConsumerList/?" + "pos_time__gte=" + st + "&pos_time__lte=" + et +" 23:59:59"+ urlStr
		}
	else
	{
	    alert("{% trans 'Please check if the time format is correct and query for up to 100 days!' %}")
	    return;
	}	

        pos_start_date=st
        pos_end_date=et
 
	savecookie("search_urlstr", url);
	$("#id_grid_report").jqGrid('setGridParam', {
		url: url,
		datatype: 'json'
	}).trigger("reloadGrid");
}


function createQueryDlg(){
	createDlgdeptfor10('employee_search_ICConsumerList',1)
	$('#dlg_for_query_employee_search_ICConsumerList').dialog(
	{
		buttons:
		[
			{id:"btnShowOK",text:'{% trans 'search for' %}',click:function(){searchbydept_ICConsumerList('employee_search_ICConsumerList');$(this).dialog("destroy");}},
			{id:"btnShowCancel",text:"{% trans 'Return' %}" ,click:function(){$(this).dialog("destroy"); }}
		]
	})
}


{% endblock %}

{% block date_set_range %}
    if (pos_start_date)
        $('#'+g_activeTabID+' #id_StartDate').val(pos_start_date)
    else
        $('#'+g_activeTabID+' #id_StartDate').val(moment().startOf('month').format('YYYY-MM-DD'))
     if(pos_end_date)    
        $('#'+g_activeTabID+' #id_EndDate').val(pos_end_date)
    else
        $('#'+g_activeTabID+' #id_EndDate').val(moment().endOf('month').format('YYYY-MM-DD'))
	$("#"+g_activeTabID+" #id_StartDate").datepicker(datepickerOptions);
        $("#"+g_activeTabID+" #id_EndDate").datepicker(datepickerOptions);
        $("#"+g_activeTabID+" #id_search").click(function(){
		//var search_user_pin=$("#search_id_user__PIN").val();
                var isError=validate_form_ipos();
                var urlstr="";
                if(isError){
         
                        var st=$("#"+g_activeTabID+" #id_StartDate").val();
                        var et=$("#"+g_activeTabID+" #id_EndDate").val();
        
                        urlstr=g_urls[g_activeTabID]+"?pos_time__gte="+st+"&"+"pos_time__lte="+et+" 23:59:59"
                        }
                else
                {
                    alert("{% trans 'Please check if the time format is correct and query for up to 31 days!' %}")
                    return;
                }
                pos_start_date=st
                pos_end_date=et
                
		savecookie("search_urlstr",urlstr);
		$("#id_grid").jqGrid('setGridParam',{url:urlstr,datatype:"json"}).trigger("reloadGrid");
	});

    function validate_form_ipos(){   //验证表单的合法性(、开始时间、结束时间)
	var st=$("#"+g_activeTabID+" #id_StartDate").val();
	var et=$("#"+g_activeTabID+" #id_EndDate").val();
        return (moment(st,'YYYY-MM-DD').isValid()&&moment(et,'YYYY-MM-DD').isValid()&&(moment(et,'YYYY-MM-DD').diff(moment(st,'YYYY-MM-DD'), 'days')<=100))
    }
{% endblock %}

