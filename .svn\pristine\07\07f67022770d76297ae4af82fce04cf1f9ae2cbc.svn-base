#!/usr/bin/python
# -*- coding: utf-8 -*-

import datetime
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.conf import settings
from django.db.models import Q
from django.db.models.query import QuerySet
from django.contrib.auth import authenticate
from mysite.iapp.rest.auth import ApiTokenAuthentication  #自定义认证类
from rest_framework import viewsets
from rest_framework.response import Response

from mysite.iclock.templatetags.iclock_tags import showprocessmsg
from mysite.utils import *
from mysite.visitors.models import *
import datetime
import re
from django.utils.translation import gettext_lazy as _
from rest_framework.decorators import api_view,authentication_classes
from mysite.iapp.rest.visitor_serializers import *
from mysite.iapp.rest.WXBizDataCrypt import *
from rest_framework.views import APIView
import requests
from django.shortcuts import render
from mysite.iclock.iutils import userDeptList
from mysite.iclock.models import department
from mysite.iclock.models import checkexact,ATTSTATES
from mysite.iclock.models import USER_OVERTIME
from mysite.iclock.models import LeaveClass
from mysite.iclock.models import outWork
from mysite.iclock.models import USER_SPEDAY_PROCESS
from mysite.iclock.datamisc import save_image_to_folder
from django.db.models.aggregates import Count
from django.db.models.signals import pre_save, post_save, post_init
from django.dispatch import receiver
from mysite.iclock.dataview import createAppMessage, send_wxmessage_for_standard
from django.contrib.auth.decorators import login_required

LOGIN_URL = '/iapp/login/'

@api_view(['GET'])
def getcompanyName(request):
    companyName = GetParamValue('opt_basic_sitetitle')
    return Response({'data': {'co': companyName}, 'ret': 'ok'})


class Enum_(tuple):
     __getattr__ = tuple.index

MEDIA_URL = settings.MEDIA_URL+'wxapp/'
recType= Enum_(['visited','novisited','invalid','cancelreser','reservationsuccess','reservationfail','inviteing','invitesuccess','cancelinvite','v_cancelinvite','v_cancelreser'])

def wx_saveReservation(photodata,ssn):
    """
    保存访客预约比对照片
    """
    
    filename = '%s%s.jpg' % ('visitor_', ssn)
    save_result = save_image_to_folder(data=photodata, data_format=2, name=filename, relative_path='photo/visitor',max_image_width=400)
    if save_result:
        return -1
    cache.delete(f'{settings.UNIT}_DECRIPTION_PHOTO_photo/visitor/{filename}')
    return 0

def visitorReservation(request):#人员预约
    cover = json.loads(request.body.decode('utf-8'))
    photodata = cover.get('photodata', '').replace('data:image/jpeg;base64,', '')
    if not photodata:
        return JsonResponse({"ret": '-1'})
    ssn = cover.get('ssn', '')
    ret = wx_saveReservation(photodata, ssn)
    if ret == -1:
        return JsonResponse({"ret": '-1'})

    v_num = cover.get('visitorCount')
    v_mob = cover.get('phone')
    userid = cover.get('customerId') or cover.get('visitedPersonId')
    v_userid = cover.get('wxOpenId')
    v_user = cover.get('visitorName')
    come_date = cover.get('visitDate')
    level_date = cover.get('visitEndDate')
    v_reason = cover.get('visitReason')
    v_emp = employee.objByPIN(userid)
    come_date = datetime.datetime.strptime('%s' % (str(come_date)),'%Y-%m-%d %H:%M:%S')
    level_date = datetime.datetime.strptime('%s' % (str(level_date)), '%Y-%m-%d %H:%M:%S')
    st = come_date
    et = level_date
    #将时间由str转换成datetime格式
    come_date = datetime.datetime.strptime('%s' % (str(come_date)), '%Y-%m-%d %H:%M:%S')
    level_date = datetime.datetime.strptime('%s' % (str(level_date)), '%Y-%m-%d %H:%M:%S')
    #查询员工用于发送小程序服务通知
    emp = employee.objByPIN(pin=userid)
    print(emp)
    if cover.get('id') :
        try:
            obj = reservation.objects.get(id = cover['id'])
            obj.SSN = ssn
            obj.visempName=v_user
            obj.InterviewedempName= v_emp.EName
            obj.VisPhone=v_mob # 拜访人手机号
            obj.VisitingNum=v_num
            obj.visReason=v_reason
            obj.viscomeDate=come_date or None
            obj.vislevelDate=level_date or None
            obj.userid= userid
            obj.v_userid=v_userid
            obj.audit = 1
            obj.recType=7
            obj.save()
        except Exception as e:
            estr="%s"%e
            return JsonResponse({"ret":'fail',"msg":u"%s"%e.args[0]})  #DataDetailResponse(request, dataModel, f)
        #return JsonResponse({'ret':'ok','data':{'id': cover['id']}})
    else :
        try:
            obj=reservation(
                visempName=v_user,
                SSN = ssn,
                InterviewedempName= v_emp.EName,
                VisPhone=v_mob,
                VisitingNum=v_num,
                visReason=v_reason,
                viscomeDate=come_date or None,
                vislevelDate=level_date or None,
                userid= userid,
                v_userid=v_userid,
                recType=1 )
            obj.save()
        except Exception as e:
            #estr="%s"%e
            return JsonResponse({"ret":'fail',"msg":u"%s"%e.args[0]})  #DataDetailResponse(request, dataModel, f)    
        #wechat_client.message.send_text_card(agentId,[userid],u'{}{}{}'.format(_(u"outside_employee"),v_user,_(u'appointmented_apply')),description.format(v_user,come_date,level_date),baseurl+"audit/?pk="+str(obj.pk))
    #添加发送访客预约申请待审核通知
    datas = {
        "name2": {"value": emp.EName or emp.PIN},
        "thing9": {"value": v_reason},
        "time10": {"value": st},
        "time11": {"value": et},
        "name1": {"value": v_user}
    }
    template_id = "FbcnoAlukwnTm0BdwpNpCOR8X6ZHQDwSZMmgzGqXaVA"
    send_wxmessage_for_standard(emp, template_id, datas, 1)
    return JsonResponse({'ret': 'ok', 'data': {'id': obj.pk}})
class getVisitedInfoView(APIView):
    def post(self, request):
        currentpage= int(request.data.get('page',0))
        pageSize= int(request.data.get('pageSize',10))
        current_page=int(currentpage)
        start=(current_page-1)*pageSize
        end=current_page*pageSize
        q = request.data.get('filter') or ''
        if not q:
            return Response({'data': ''})
        def cover(res):
            d = {'id':res.id,'personPin':res.PIN,'personName':res.EName,'deptName':res.DeptID.DeptName}
            if res.Mobile:
              d['mobile'] = res.Mobile[:3]+'****'+res.Mobile[-4:]
            return d
        queryset = employee.objects.filter(Q(Mobile__contains=q)|Q(EName__contains=q))
        # 被访人表中有数据时只返回已添加到被访人表中的人员
        if Interviewee.objects.exists():
            queryset = queryset.filter(Interviewee__isnull=False)
        queryset = queryset[start:end]
        result = list(map(cover,queryset))
        return Response({'data':result})

#面部保存
def saveVisitorPic(request):
    data = {"photoUrl": "123321","cropUrl": "3211"}
    return JsonResponse({"data":data})

@api_view(['GET'])
@authentication_classes([ApiTokenAuthentication])
def getPersonDetail(request):
    arg = request.query_params
    emp = employee.objByPIN(arg['customerId'])
    tmp = {}
    tmp['pin'] = emp.PIN
    tmp['name'] = emp.EName or ''
    tmp['cardNos'] = emp.Card
    tmp['hireDate'] = emp.Hiredday
    tmp['devicePassword'] = emp.MVerifyPass
    tmp['certNumber'] = emp.SSN
    tmp['avatarUrl'] = emp.getImgUrl()
    tmp['deptCode'] = emp.DeptID.DeptNumber if emp.DeptID else ''
    tmp['deptName'] = emp.DeptID.DeptName if emp.DeptID else ''
    tmp['position'] = emp.Title or ''
    tmp['gender'] = emp.Gender or ''
    tmp['telephone'] = emp.Tele or ''
    tmp['email'] = emp.email or ''
    tmp['mobile'] = emp.Mobile or ''
    tmp['status'] = emp.OffDuty
    return JsonResponse({'data':tmp})

@api_view(['POST'])
@authentication_classes([ApiTokenAuthentication])
def inviteVisitor(request):
    cover = request.data
    v_num=cover.get('visitorCount');v_mob=cover.get('phone');userid=cover.get('PIN')
    v_user=cover.get('visitorName'); come_date=cover.get('visitDate'); level_date=cover.get('visitEndDate');v_reason=cover.get('visitReason');
    v_emp= employee.objByPIN(userid)
    try:
        obj=reservation(visempName=v_user,InterviewedempName= v_emp.EName,VisPhone=v_mob,VisitingNum=v_num,visReason=v_reason,viscomeDate=come_date or None,vislevelDate=level_date or None,userid= userid,recType=recType.inviteing,isinvite =True )
        obj.save()
    except Exception as e:
            estr="%s"%e
            return JsonResponse({"ret":'fail',"message":u"%s"%e.message})  #DataDetailResponse(request, dataModel, f)
    return Response({'ret':'ok'})

@api_view(['POST'])
@authentication_classes([ApiTokenAuthentication])
def getNoAuditedReservation(request):
    return getReservation(request,True)

@api_view(['POST'])
@authentication_classes([ApiTokenAuthentication])
def getAuditedReservation(request):
    return getReservation(request,False)
    
def getReservation(request,audit):    
    currentpage= int(request.data.get('page',1))
    pageSize= int(request.data.get('pageSize',10))
    apiUrl = request.data.get('apiurl','')
    current_page=int(currentpage)
    start=(current_page-1)*pageSize
    end=current_page*pageSize
    q = request.data.get('filter') or ''
    emp = employee.objByPIN(request.data.get('PIN'))
    user = MyUser.objects.filter(id=emp.boundid)
    queryset = reservation.objects.filter(Q(VisPhone__contains=q) | Q(visempName__contains=q)).order_by('-id')
    if not user or not user[0].has_perm('visitors.Audit_reservation'):
        queryset = queryset.filter(userid=request.data.get('PIN'))
    if audit:
        serializers = reservationSerializer(queryset.filter(audit = 0,DelTag=0).exclude(recType__in=[2,3,5,6,8,9,10])[start:end],many = True)
    else:
        serializers = reservationSerializer(queryset.exclude(audit=0).exclude(DelTag=1).exclude(recType__in=[2,3,8,9,10])[start:end],many = True)
    recordvalid=[]
    result = []
    for res in serializers.data:
        res['visitEmpPin'] = emp.PIN
        res['visitEmpName'] = emp.EName
        res['visitEmpPhone'] = emp.Mobile
        res['isVisited'] =  res['recType']
        res['photoUrl'] = wx_get_photo_url(res['SSN'],apiUrl)
        if not checkrecodevaild(res['visitEndDate']) and not res['recType'] in [0,2,3,5,9,10]:
            recordvalid.append(res['id'])
            res['recType'] = recType.invalid
            continue
        result.append(res)
    # result =list(map(cover,serializers.data))
    if recordvalid:
        reservation.objects.filter(id__in = recordvalid).update(recType=recType.invalid, audit=4)
    return Response({'data':result})  

def visitor_application_results( visitor):
    """
    访客预约申请审核结果通知
    """
    if visitor.audit == 1:
        statename = _(u"Agree to visit")
    elif visitor.audit == 2:
        statename = _(u"Refuse visits")
    emp = employee.objByPIN(pin=visitor.userid)#获取被访员工
    st = visitor.viscomeDate.strftime("%Y-%m-%d %H:%M:%S")
    et = visitor.vislevelDate.strftime("%Y-%m-%d %H:%M:%S")
    datas = {
        "thing2": {"value": emp.EName or emp.PIN},
        "thing1": {"value": visitor.visempName},
        "time3": {"value": st},
        "thing4": {"value": statename}
    }
    template_id = "drgztmJNHC3byak2XyzJRiKs14WErqSSIrQWwfJPWQc"
    send_wxmessage_for_standard(visitor.v_userid, template_id, datas, 2)
    return

@api_view(['POST'])
@authentication_classes([ApiTokenAuthentication])
def visitorReservationAudit(request):
    nid = request.data.get('id')
    obj = reservation.objects.get(id = nid)
    obj.recType = 4
    obj.audit = 1
    obj.save()
    visitor_application_results(obj)
    return Response({'ret':'ok'})

@api_view(['POST'])
@authentication_classes([ApiTokenAuthentication])
def visitorReservationRefuse(request):
    nid = request.data.get('id')
    obj = reservation.objects.get(id = nid)
    obj.recType = 5
    obj.audit = 2
    obj.save()
    return Response({'ret':'ok'})

@api_view(['GET'])
@authentication_classes([ApiTokenAuthentication])
def getInviteRecord(request):
    currentpage= int(request.GET.get('page',1))
    pageSize= int(request.GET.get('pageSize',10))
    current_page=int(currentpage)
    start=(current_page-1)*pageSize
    end=current_page*pageSize
    q = request.GET.get('filter') or ''
    apiUrl = request.GET.get('apiUrl')
    pin = request.GET.get('PIN')
    emp = employee.objByPIN(pin)
    serializers = reservationSerializer(reservation.objects.filter(userid=pin,isinvite = 1,DelTag=0).filter(Q(VisPhone__contains=q)|Q(visempName__contains=q)).order_by('-id')[start:end],many = True)
    recordvalid=[]
    def cover(res):    
        res['visitEmpPin'] = emp.PIN
        res['visitEmpName'] = emp.EName
        res['visitEmpPhone'] = emp.Mobile
        if not checkrecodevaild(res['visitEndDate']) and not res['recType'] in [0,2,3,5,9,10]:
            recordvalid.append(res['id'])
            res['recType'] = recType.invalid
        res['isVisited'] =  res['recType']
        res['visitReason'] = res['visitReason'] or ''
        res['photoUrl'] = wx_get_photo_url(res['SSN'],apiUrl)
        return res
    result =list(map(cover,serializers.data))
    if recordvalid:
        reservation.objects.filter(id__in = recordvalid).update(recType=recType.invalid,audit=4)
    return Response({'data':result})  

@api_view(['GET'])
@authentication_classes([ApiTokenAuthentication])
def cancelInvitation(request):
    nid = request.query_params.get('id')
    reservation.objects.filter(id = nid).update(recType = 8, audit = 2)
    return Response({'ret':'ok'})

@api_view(['GET'])
@authentication_classes([ApiTokenAuthentication])
def againInvitation(request):
    nid = request.query_params.get('id')
    reservation.objects.filter(id = nid).update(recType = 6, audit = 0, viscomeDate=None, vislevelDate=None)
    return Response({'ret':'ok'})

@api_view(['GET'])
@authentication_classes([ApiTokenAuthentication])
def getVisitRecord(request):
    currentpage= int(request.GET.get('page',1))
    pageSize= int(request.GET.get('pageSize',10))
    current_page=int(currentpage)
    start=(current_page-1)*pageSize
    end=current_page*pageSize
    apiUrl = request.GET.get('apiUrl','')
    q = request.GET.get('filter') or ''
    serializers = visitionlogsSerializer(visitionlogs.objects.filter(Q(VisPhone__contains=q)|Q(VisempName__contains=q)).exclude(DelTag=1).order_by('-id')[start:end],many = True)
    def cover(res):
        res['photoUrl'] = wx_get_photo_url(res['SSN'],apiUrl)
        return res
    result =list(map(cover,serializers.data))
    return Response({'ret':'ok','data':result})

@api_view(['GET'])
def getVisitRecordById(request):
    id = request.GET.get('id')
    res = reservation.objects.get(pk = id)
    ress = {}
    ress['id'] = id
    ress['visitorName'] = res.visempName
    ress['phone'] = res.VisPhone
    ress['visitDate'] = res.viscomeDate.strftime("%Y-%m-%d %H:%M:%S")
    ress['visitEndDate'] = res.vislevelDate.strftime("%Y-%m-%d %H:%M:%S")
    ress['photodata'] = wx_get_photo('visitor_' + res.SSN,True)
    ress['recType'] = res.recType
    return Response({'data':ress,'ret':'ok'})

@api_view(['POST'])
def getVisitorReservationList(request):
    currentpage= int(request.data.get('page',1))
    pageSize= int(request.data.get('pageSize',10))
    current_page=int(currentpage)
    start=(current_page-1)*pageSize
    end=current_page*pageSize
    q = request.data.get('filter') or ''
    openid = request.data.get('wxOpenId')
    phone = request.data.get('phone')
    id = request.data.get('id', '')
    if not id:
        if q:
            pins = employee.objects.filter(Q(EName__contains=q)|Q(Mobile__contains=q)).value_list('PIN')
            if pins:
                serializers = reservationSerializer(reservation.objects.filter(userid__in=pins,DelTag=0).filter(Q(v_userid=openid)|Q(VisPhone=phone)).order_by('-id')[start:end],many = True)
            else:
                return Response({'data':[]})
        else:
            serializers = reservationSerializer(reservation.objects.filter(Q(v_userid=openid)|Q(VisPhone=phone)).exclude(DelTag=1).order_by('-id')[start:end],many = True)
    else:
        serializers = reservationSerializer(reservation.objects.filter(id = id)[start:end], many=True)
    recordvalid = []
    def cover(res):
        emp = employee.objByPIN(res['userid'])
        if emp:
            res['visitEmpPin'] = emp.PIN
            res['visitEmpName'] = emp.EName
            res['visitEmpPhone'] = emp.Mobile
        else:
            res['visitEmpPin'] = res['userid'] or ''
            res['visitEmpName'] = res['InterviewedempName'] or ''
            res['visitEmpPhone'] = res['Mobile'] or ''
        if not checkrecodevaild(res['visitEndDate']) and not res['recType'] in [0,2,3,5,9,10]:
            recordvalid.append(res['id'])
            res['recType'] = recType.invalid
        res['isVisited'] =  res['recType']
        return res
    result =list(map(cover,serializers.data))
    if recordvalid:
        reservation.objects.filter(id__in = recordvalid).update(recType=recType.invalid,audit=4)
    return Response({'ret': 'ok','data':result})

def cancelReservation(request):
    nid = request.GET.get('id')
    obj = reservation.objects.get(id = nid)
    obj.recType = recType.v_cancelreser
    obj.save()
    return JsonResponse({'ret':'ok'})

#网页
def getInvite(request):
    nid = request.GET.get('id')
    obj = reservation.objects.filter(id = nid)
    serializers = reservationSerializer(obj[0])
    res = serializers.data
    emp = employee.objByPIN(res['userid'])    
    res['visitEmpPin'] = emp.PIN
    res['visitEmpName'] = emp.EName    
    res['visitEmpPhone'] = emp.Mobile
    res['isVisited'] =  res['recType']
    if not checkrecodevaild(res['visitEndDate']):
        obj.update(recType=recType.invalid,audit=4)
        res['recType'] = recType.invalid
    if res['recType'] == recType.invitesuccess and obj[0].DelTag==0:
        return render(request,'iapp/visitor/reservationSuccess.html', {'MEDIA_URL':MEDIA_URL,'item': res})
    if res['recType'] in [recType.invalid, recType.v_cancelinvite, recType.cancelinvite, recType.visited] or obj[0].DelTag==1:
        return render(request,'iapp/visitor/404.html', {'MEDIA_URL':MEDIA_URL,'item': res})
    return render(request,'iapp/visitor/getInvite.html', {'MEDIA_URL':MEDIA_URL,'item': res})

def confirmInvite(request):
    cover = json.loads(request.body.decode('utf-8'))
    nid=cover['id'];visempName=cover['visitorName'];viscomeDate=cover['visitDate'];vislevelDate=cover['visitEndDate'];visReason=cover['visitReason'];Mobile=cover['phone'];photoUrl=cover['photoUrl']
    obj = reservation.objects.get(id = nid)
    obj.visempName=visempName
    obj.viscomeDate=viscomeDate
    obj.vislevelDate=vislevelDate
    obj.visReason=visReason
    obj.recType = recType.invitesuccess
    obj.save()
    
    return JsonResponse({'ret':'ok'})

def cancleReservation(request): # 撤销邀约
    id = request.GET.get('id')
    obj = reservation.objects.get(id = id)
    obj.recType = recType.v_cancelinvite
    obj.save()
    return Response({'ret':'ok'})

def checkrecodevaild(endday):
    if not endday:
        return True
    endday = datetime.datetime.strptime(endday, '%Y-%m-%d %H:%M:%S')
    nowTime=datetime.datetime.now()
    return nowTime<endday

@receiver(post_init, sender=reservation)
def initnotify(sender, instance, **kwargs):
    instance.__original_audit = instance.audit
    instance.__original_recType = instance.recType

@receiver(pre_save, sender=reservation)
def beforenotify(sender, instance, **kwargs) :
    if instance._state.adding and instance.viscomeDate:
        try:
            endday = datetime.datetime.strptime(instance.viscomeDate, '%Y-%m-%d %H:%M:%S')
            leave_time = datetime.datetime.strptime(instance.vislevelDate, '%Y-%m-%d %H:%M:%S')
        except:
            endday = instance.viscomeDate
            leave_time = instance.vislevelDate
        nowTime=datetime.datetime.now()-datetime.timedelta(seconds=60)
        nowtime = datetime.datetime.now()
        if endday<nowTime:
            raise Exception(u"%s"%(_(u'now time cannot be earlier than entry time')))
        if leave_time < nowtime:
            raise Exception(u"%s"%(_(u'leave time cannot be earlier than now time')))
        if instance.vislevelDate and instance.viscomeDate > instance.vislevelDate :
            raise Exception(u"%s"%(_(u'Departure time cannot be earlier than entry time')))
        if instance.recType in [1,6,7]:
            # 证件号代表访客唯一，如果预约对象是同一个人并且有时间重叠返回异常。
            if reservation.objects.filter(SSN =instance.SSN, userid=instance.userid, recType__in=[1,4,6,7], DelTag=0).exclude(Q(vislevelDate__lt=instance.viscomeDate)|Q(viscomeDate__gt=instance.vislevelDate)).count()>0:
                raise Exception(u"%s"%(_(u'Visitor time cannot be overlap the record')))

@receiver(post_save, sender=reservation)
def notify(sender, instance, created=True, **kwargs) :
    if not created :
        emp = employee.objByPIN(instance.userid)
        createAppMessage('visitor',instance,'','', emp)

@receiver(pre_save, sender=visitionlogs)
def visitorlogbeforenotify(sender, instance, **kwargs):
    instance._visitionlogs__original_VisState = instance.VisState

@receiver(post_save, sender=visitionlogs)
def visitorlogbeforenotify(sender, instance, created=True, **kwargs):
    try:
        emp = employee.objects.get(EName=instance.InterviewedempName)
    except:
        return
    if not created :
      instance.instance__create = False
    else :
      instance.instance__create = True
    createAppMessage('visitorlog',instance,'','', emp)

@api_view(['GET'])
@authentication_classes([ApiTokenAuthentication])
def getVisTendency(request):
    user = request.user
    emp = employee.objByPIN(user)
    currentpage= int(request.GET.get('page',1))
    pageSize= int(request.GET.get('pageSize',10))
    current_page=int(currentpage)
    start=(current_page-1)*pageSize
    end=current_page*pageSize
    apiUrl = request.GET.get('apiUrl','')
    id = request.GET.get('id','')
    q = request.GET.get('filter') or ''
    list = []
    if not id:
        AppMessageEmployee_objs = AppMessageEmployee.objects.filter(Q(UserID=emp))# 为空表示给所有人发
        for i in AppMessageEmployee_objs:
            list.append(i.message.id)
        queryset = AppMessage.objects.filter(id__in=list, is_read=False, msg_type=3)
    else:
        queryset = AppMessage.objects.filter(id = id)
    querydata = queryset[start:end]
    Result = []
    for obj in querydata:
        text = json.loads(obj.content)
        dict = {}
        dict['id'] = obj.id
        dict['msg_type'] = obj.msg_type
        dict['title'] = obj.title
        dict['content'] = obj.content
        dict['createtime'] = obj.createtime.strftime("%Y-%m-%d %H:%M:%S")
        dict['is_send'] = obj.is_send
        dict['is_read'] = obj.is_read
        dict['photoUrl'] = wx_get_photo_url(text.get('SSN'), apiUrl)
        Result.append(dict)
    queryset.update(is_read=True)
    return JsonResponse({'ret':'ok', 'result': Result})

def wx_getTeamQueryset(initkwargs, queryset, type=""):
    # 团队列表相关查询
    from mysite.iapp.rest.utils import get_search_date
    dataid = initkwargs.get('dataid', '')
    if dataid:
        queryset = queryset.filter(pk = dataid)
    boundid = employee.objects.get(PIN = initkwargs.get('PIN', '')).boundid
    user = MyUser.objects.get(id=boundid)
    queryset = queryset.filter(Q(State=2) | Q(State__gt=10, roleid=0))
    if (not user.is_superuser) and (not user.is_alldept):
        deptids = userDeptList(user)
        dept_objs = department.objects.filter(Q(DeptID__in=deptids) | Q(parent__in=deptids))  # 获取有权限部门及子部门
        queryset = queryset.filter(UserID__DeptID__in=dept_objs)
    date = initkwargs.get('date', '')
    if date:
        if type == 'outwork':
            apply_date = get_search_date('CHECKTIME', date)
        else:
            apply_date = get_search_date('ApplyDate', date)
        queryset = queryset.filter(Q(**apply_date))
    q = initkwargs.get('q', '')
    if q:
        if type == 'outwork':
            apply_date = get_search_date('CHECKTIME', q)
        else:
            apply_date = get_search_date('ApplyDate', q)
        queryset = queryset.filter(
            Q(UserID__PIN__icontains=q) |
            Q(UserID__EName__icontains=q) |
            Q(**apply_date)
        )
    if type == 'outwork':
        return queryset.order_by('-CHECKTIME')
    else:
        return queryset.order_by('-ApplyDate')

def wx_get_photo_url(SSN, apiurl='', isvisitor=True):
    if SSN and apiurl:
        if not isvisitor:
            folderName = "photo/app/thumbnail"
            fileName = SSN + ".jpg"
        else:
            folderName = "photo/visitor"
            fileName = 'visitor_' + SSN + ".jpg"
        purl = getStoredFileName(folderName, None, fileName)
        if os.path.exists(purl):
            url=getStoredFileURL(folderName, None, fileName, auth=False)
            import random
            url = apiurl + url + '?time=%s'%str(random.random()).split('.')[1]
            return url
    return None

def wx_get_photo(pin,isvisitor = False):
    if not isvisitor:
        photo_path = getStoredFileName("photo/app/thumbnail", None, pin+".jpg")
    else:
        photo_path = getStoredFileName("photo/visitor", None, pin+".jpg")
    try:
        if os.path.exists(photo_path):
           with open(photo_path,"rb") as f:
               data = f.read()
               if data[:10] == b"CRYPT_IMG:":  # 解密
                   data = aes_crypt(data[10:], False)
               ls_f = base64.b64encode(data)
               if sys.version[0] == '3':
                   base64_data = ls_f.decode("utf-8")
               data = 'data:image/jpg;base64,%s' % base64_data
               return data
    except:
        pass
    return None

@api_view(['GET'])
def myteam_userspedays(request):
    leavess = LeaveClass.objects.filter(DelTag = 0)
    leave_dic = {i.LeaveID: i.LeaveName for i in leavess}
    queryset = USER_SPEDAY.objects.all()
    result_t = []
    initkwargs = request.GET
    current_page = int(initkwargs.get('offset', 1))
    pageSize = int(initkwargs.get('limit', 10))
    start = (current_page - 1) * pageSize
    end = current_page * pageSize
    # 团队请假查询
    if int(initkwargs.get('isTeam', 0)) == 1:
        LeaveType = initkwargs.get('LeaveType', 0)
        if LeaveType == '1':  #这边以LeaveType=1标记出差记录
            name_list = []
            count_list = []
            queryset = queryset.filter(DateID__exact=1)
            uspdata = wx_getTeamQueryset(initkwargs, queryset)
            count_us = uspdata.values('UserID__EName').annotate(data=Count('UserID')).order_by()
            for t in count_us:
                name_list.append(t['UserID__EName'])
                count_list.append(t['data'])
            result_t = [name_list,count_list]
        else:
            queryset = queryset.exclude(DateID__exact=1)
            uspdata = wx_getTeamQueryset(initkwargs, queryset, "userspeday")
            count_us = uspdata.values('DateID').annotate(data=Count('DateID')).order_by()
            for t in count_us:
                t['name'] = leave_dic[t['DateID']]
                result_t.append(t)
        def cover(r):
            res = {}
            rr = r.USER_SPEDAY_DETAILS()
            res['id'] = r.id
            res['userid'] = r.UserID_id
            res['userName'] = r.UserID.EName
            res['photo'] = wx_get_photo(r.UserID.PIN)
            res['commname'] = leave_dic[r.DateID]
            res['applydate'] = r.ApplyDate.strftime('%Y-%m-%d %H:%M:%S')
            res['sttime'] = r.StartSpecDay.strftime('%Y-%m-%d %H:%M:%S')
            res['ettime'] = r.EndSpecDay.strftime('%Y-%m-%d %H:%M:%S')
            res['reason'] = r.YUANYING
            res['remark'] = rr.remarks if rr else ''
            return res

        result = list(map(cover, uspdata[start:end]))
        return Response({'data':[result_t,result]})

@api_view(['GET'])
def myteam_checkexacts(request):
    attstadic = dict(list(ATTSTATES))
    queryset = checkexact.objects.all()
    initkwargs = request.GET
    result_t = []
    current_page = int(initkwargs.get('offset', 1))
    pageSize = int(initkwargs.get('limit', 10))
    start = (current_page - 1) * pageSize
    end = current_page * pageSize
    # 团队加班查询
    if int(initkwargs.get('isTeam', 0)) == 1:
        name_list = []
        count_list = []
        ovedata = wx_getTeamQueryset(initkwargs, queryset)
        count_us = ovedata.values('UserID__EName').annotate(data=Count('UserID')).order_by()
        for t in count_us:
            name_list.append(t['UserID__EName'])
            count_list.append(t['data'])
        result_t = [name_list,count_list]
        def cover(r):
            res = {}
            res['id'] = r.id
            res['userid'] = r.UserID_id
            res['userName'] = r.UserID.EName
            res['photo'] = wx_get_photo(r.UserID.PIN)
            res['CHECKTIME'] = r.CHECKTIME.strftime('%Y-%m-%d %H:%M:%S')
            res['applydate'] = r.ApplyDate.strftime('%Y-%m-%d %H:%M:%S')
            res['commname'] = attstadic[r.CHECKTYPE]
            res['reason'] = r.YUYIN
            return res
        result = list(map(cover, ovedata[start:end]))
        return Response({'data':[result_t,result]})

@api_view(['GET'])
def myteam_overtimes(request):
    queryset = USER_OVERTIME.objects.all()
    initkwargs = request.GET
    result_t = []
    current_page = int(initkwargs.get('offset', 1))
    pageSize = int(initkwargs.get('limit', 10))
    start = (current_page - 1) * pageSize
    end = current_page * pageSize
    # 团队加班查询
    if int(initkwargs.get('isTeam', 0)) == 1:
        name_list = []
        count_list = []
        ovedata = wx_getTeamQueryset(initkwargs, queryset)
        count_us = ovedata.values('UserID__EName').annotate(data=Count('UserID')).order_by()
        for t in count_us:
            name_list.append(t['UserID__EName'])
            count_list.append(t['data'])
        result_t = [name_list,count_list]
        def cover(r):
            res = {}
            res['id'] = r.id
            res['userid'] = r.UserID_id
            res['userName'] = r.UserID.EName
            res['photo'] = wx_get_photo(r.UserID.PIN)
            res['commname'] = str(r.AsMinute) + str(_(u"mins"))
            res['applydate'] = r.ApplyDate.strftime('%Y-%m-%d %H:%M:%S')
            res['sttime'] = r.StartOTDay.strftime('%Y-%m-%d %H:%M:%S')
            res['ettime'] = r.EndOTDay.strftime('%Y-%m-%d %H:%M:%S')
            res['reason'] = r.YUANYING
            return res
        result = list(map(cover, ovedata[start:end]))
        return Response({'data':[result_t,result]})

@api_view(['GET'])
def myteam_outwork(request):
    queryset = outWork.objects.all()
    initkwargs = request.GET
    result_t = []
    current_page = int(initkwargs.get('offset', 1))
    pageSize = int(initkwargs.get('limit', 10))
    start = (current_page - 1) * pageSize
    end = current_page * pageSize
    # 团队外勤查询
    if int(initkwargs.get('isTeam', 0)) == 1:
        if settings.OUTWORK_TYPE == 1:
            queryset = queryset.filter(sign_type=1)
        else:
            queryset = queryset.exclude(sign_type=1)
        name_list = []
        count_list = []
        outdata = wx_getTeamQueryset(initkwargs, queryset,'outwork')
        count_us = outdata.values('UserID__EName').annotate(data=Count('UserID')).order_by()
        for t in count_us:
            name_list.append(t['UserID__EName'])
            count_list.append(t['data'])
        result_t = [name_list,count_list]
        def cover(r):
            res = {}
            res['id'] = r.id
            res['userid'] = r.UserID_id
            res['userName'] = r.UserID.EName
            res['photo'] = wx_get_photo(r.UserID.PIN)
            res['applydate'] = r.CHECKTIME.strftime('%Y-%m-%d')
            res['commname'] = r.CHECKTIME.strftime('%H:%M')
            res['location'] = r.location
            return res
        result = list(map(cover, outdata[start:end]))
        return Response({'data':[result_t,result]})

def getempname(id):
    em = employee.objects.filter(boundid = id)
    if em:
        return em[0].EName
    return ''

@api_view(['GET'])
def myteam_detail(request):
    from mysite.iclock.templatetags.iclock_tags import showprocess
    teamid = request.GET.get('teamid', '').strip()
    teamtype = request.GET.get('teamtype', '').strip()
    if teamtype == 'holiday':
        queryset = USER_SPEDAY_PROCESS.objects.filter(USER_SPEDAY_ID = teamid).order_by('procSN')
        def cover(r):
            res = {'done': u'true', 'current': u'true', 'desc':_(u"Approved")}
            res['text'] = getempname(r.User_id)
            res['aplytime'] = r.ProcessingTime.strftime('%Y-%m-%d %H:%M:%S')
            res['remark'] = r.comments
            return res
        result = list(map(cover, queryset))
        return Response({'data':result})
    else:
        if teamtype == 'overtime':
            ooc = USER_OVERTIME.objects.filter(pk = teamid)
        elif teamtype == 'checkexact':
            ooc = checkexact.objects.filter(pk = teamid)
        elif teamtype == 'out':
            ooc = outWork.objects.filter(pk=teamid)
        else:
            ooc = []
        processname = ''
        if ooc and ooc[0].process:
            # processname = showprocess(ooc[0])
            # 多级审批函数修改（适配职务/人员）
            processname = showprocessmsg(ooc[0])
        res = {'done': u'true', 'current': u'true', 'desc': _(u"Approved")}
        res['text'] = _(u"Approval Process")
        res['aplytime'] = processname
        res['remark'] = ''
        return Response({'data':[res]})

#获取访客头像的路径
def wxoa_get_photo_url(SSN):
    if SSN:
        folderName = "photo/visitor"
        fileName = 'visitor_' + SSN + ".jpg"
        purl = getStoredFileName(folderName, None, fileName)
        if os.path.exists(purl):
            purl = "/iclock/picfile/" + folderName + "/" + fileName
            return purl
        folderName1 = "photo"
        fileName1 = SSN + ".jpg"
        # 访客登记显示人员照片
        purl1 = getStoredFileName(folderName1, None, fileName1)
        if os.path.exists(purl1):
            purl1 = "/iclock/picfile/" + folderName1 + "/" + fileName1
            return purl1

    return None

@login_required(login_url=LOGIN_URL)
def get_reservation_list(request):
    offset= int(request.GET.get('offset',0))
    limit= int(request.GET.get('limit',5))
    start=offset
    end= offset + limit
    empid = request.employee['id']
    emp = employee.objByID(empid)
    queryset = reservation.objects.filter(userid = emp.PIN).order_by('-id')
    serializers = reservationSerializer(
        queryset.filter(DelTag=0)[start:end], many=True)
    recordvalid=[]
    def cover(res):
        res['visitEmpPin'] = emp.PIN
        res['visitEmpName'] = emp.EName
        res['visitEmpPhone'] = emp.Mobile or u"%s" % _(u"none")
        res['isVisited'] =  res['recType']
        res['visitReason'] =  res['visitReason'] or u"%s" % _(u"none")
        res['photoUrl'] = wxoa_get_photo_url(res['SSN']) or "/media/wxapp/images/pt.png"
        if not checkrecodevaild(res['visitEndDate']) and not res['recType'] in [0,2,3,5,9,10]:
            recordvalid.append(res['id'])
            res['recType'] = recType.invalid
        return res
    result =list(map(cover,serializers.data))
    if recordvalid:
        reservation.objects.filter(id__in = recordvalid).update(recType=recType.invalid,audit=4)
    return getJSResponse(dumps({'results':result}))

@login_required(login_url=LOGIN_URL)
def get_visitionlog_list(request):#获取员工预约记录
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 5))
    start = offset
    end = offset + limit
    empid = request.employee['id']
    emp = employee.objByID(empid)
    serializers = visitionlogsSerializer(visitionlogs.objects.filter(interviewee_pin=emp.PIN).order_by('-OpStamp').exclude(DelTag=1)[start:end],many = True)
    def cover(res):
        res['photoUrl'] = wxoa_get_photo_url(res['SSN']) or "/media/wxapp/images/pt.png"
        res['visitorCount'] = res['visitorCount'] or  u"%s" %_(u"none")
        res['visitReason'] = res['visitReason'] or u"%s" %_(u"none")
        res['visitDate'] = res['visitDate'] or u"%s" %_(u"none")
        res['visitEndDate'] = res['visitEndDate'] or u"%s" %_(u"none")
        return res
    result =list(map(cover,serializers.data))
    return getJSResponse(dumps({'results':result}))


def get_visitor_reservation_list(request):  # 获取访客预约记录
    from mysite.core.wxsignature import get_openid_by_access_token
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 5))
    code = request.GET.get('code', '')
    get_openid_result = get_openid_by_access_token(code)  # 获取openid
    if get_openid_result['result'] == 0:
        openid = get_openid_result['openid']
        start = offset
        end = offset + limit
        queryset = reservation.objects.filter(DelTag=0, v_userid=openid).order_by('-id')
        serializers = reservationSerializer(
            queryset.filter(DelTag=0)[start:end], many=True)
        recordvalid = []

        def cover(res):
            res['isVisited'] = res['recType']
            res['visitReason'] = res['visitReason'] or _(u"none")
            res['photoUrl'] = wxoa_get_photo_url(res['SSN']) or "/media/wxapp/images/pt.png"
            if not checkrecodevaild(res['visitEndDate']) and not res['recType'] in [0,2,3,5,9,10]:
                recordvalid.append(res['id'])
                res['recType'] = recType.invalid
            return res
        result = list(map(cover, serializers.data))
        if recordvalid:
            reservation.objects.filter(id__in=recordvalid).update(recType=recType.invalid,audit=4)
    else:
        result = {}
    return getJSResponse(dumps({'results': result}))
