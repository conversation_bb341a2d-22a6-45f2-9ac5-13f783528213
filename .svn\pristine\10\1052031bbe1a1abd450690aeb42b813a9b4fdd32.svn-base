# -*- coding: utf-8 -*-
# Generated by Django 1.11.10 on 2018-04-25 07:56
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('visitors', '0002_auto_20171130_1600'),
    ]

    operations = [
        migrations.AddField(
            model_name='visitionlogs',
            name='CommonPlate',
            field=models.CharField(db_column='commonplate',blank=True, max_length=20, null=True, verbose_name='\u666e\u901a\u8f66\u724c'),
        ),
        migrations.AddField(
            model_name='visitionlogs',
            name='Mobile',
            field=models.CharField(db_column='mobile',blank=True, max_length=20, null=True, verbose_name='\u88ab\u8bbf\u4eba\u624b\u673a\u53f7\u7801'),
        ),
        migrations.Add<PERSON>ield(
            model_name='visitionlogs',
            name='SpecialPlate',
            field=models.Char<PERSON><PERSON>(db_column='specialplate', blank=True, max_length=20, null=True, verbose_name='\u7279\u6b8a\u8f66\u724c'),
        ),
        migrations.AddField(
            model_name='visitionlogs',
            name='VisPhone',
            field=models.CharField(db_column='visphone',blank=True, max_length=20, null=True, verbose_name='\u8bbf\u5ba2\u624b\u673a\u53f7\u7801'),
        ),
        migrations.AlterField(
            model_name='visitionlogs',
            name='CertificateType',
            field=models.CharField(db_column='certificatetype',blank=True, choices=[('0', '\u4e00\u4ee3\u8eab\u4efd\u8bc1'), ('1', '\u4e8c\u4ee3\u8eab\u4efd\u8bc1'), ('2', '\u9632\u4f2a\u8eab\u4efd\u8bc1'), ('3', '\u62a4\u7167'), ('4', '\u9a7e\u9a76\u8bc1'), ('5', '\u884c\u9a76\u8bc1'), ('6', '\u793e\u4fdd\u5361')], default='1', max_length=2, null=True, verbose_name='\u8bc1\u4ef6\u7c7b\u522b'),
        ),
    ]
