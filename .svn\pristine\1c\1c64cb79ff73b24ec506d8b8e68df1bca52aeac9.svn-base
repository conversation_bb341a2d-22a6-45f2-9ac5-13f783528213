{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <div class="x-body">
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'StartDate' %}" name="start" id="start" autocomplete="off"></div>
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'End Date' %}" name="end" id="end" autocomplete="off"></div>
        <a class="layui-btn search_btn" data-type="reload" id="search_menu">{% trans 'Inquire' %}</a>
        <div style="padding-top: 20px;"></div>
        <button class="layui-btn" onclick="LoseCard()"><i class="layui-icon"></i>{% trans 'loss' %}</button>
        <button class="layui-btn" onclick="RevertCard()"><i class="layui-icon"></i>{% trans 'Solutions Hanging' %}</button>
        <a class="layui-btn layui-btn-small" style="margin-top:3px;float:right"
        href="javascript:location.replace(location.href);" title="{% trans 'Reload' %}">
        <i class="icon iconfont" style="line-height:30px">&#xe6aa;</i></a>
        <table class="layui-hide" id="user_card" lay-filter="user_card"></table>
    </div>
{% endblock %}
{% block extrjs %}
    <script>
        layui.use(['table', 'laydate'], function () {
            var table = layui.table;
            var laydate = layui.laydate;
            var form = layui.form;
            var startDate = laydate.render({
                elem: '#start',
                type: 'date',
                done: function (value, date) {
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1, //关键
                        date: date.date
                    };
                }
            });
            var endDate = laydate.render({
                elem: '#end',
                type: 'date',
                done: function (value, date) {
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    }
                }
            });
            cols = [[
                {type: 'checkbox'}
                , {field: 'pin', width: 110, title: "{% trans 'BadgeNumber' %}"}
                , {field: 'username', width: 120, title: "{% trans 'Name' %}"}
                , {field: 'deptname', width: 150, title: "{% trans 'Department' %}"}
                , {field: 'cardno', width: 120, title: "{% trans 'ID Card' %}"}
            ]]
            {% if "ipos_wallets"|get_wallet_type %}
                cols[0].push(
                    {field: 'blance', width: 100, title: "{% trans 'cash balance' %}"}
                    , {field: 'allow_balance', width: 100, title: "{% trans 'subsidy balance' %}"}
                    , {field: 'card_privage', width: 120, title: "{% trans 'card type' %}"}
                    , {field: 'cardstatus', width: 100, title: "{% trans 'card status' %}"}
                    , {field: 'issuedate', width: 120, title: "{% trans 'issuing date' %}", sort: true})
            {%else%}
                cols[0].push(
                    {field: 'blance', width: 100, title: "{% trans 'balance' %}"}
                    , {field: 'card_privage', width: 120, title: "{% trans 'card type' %}"}
                    , {field: 'cardstatus', width: 100, title: "{% trans 'card status' %}"}
                    , {field: 'issuedate', width: 120, title: "{% trans 'issuing date' %}", sort: true})
            {%endif%}
            table.render({
                elem: '#user_card'
                , url: '/selfservice/ipos/get_data_info/?info_type=IssueCard'       // 数据接口
                , cellMinWidth: 50 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                , id: 'id'
                , page: true
                , cols: cols
            });
            var $ = layui.$, active = {
                reload: function () {
                    var start = $("input[name='start']").val();
                    var end = $("input[name='end']").val();
                    // console.log(start);
                    table.reload('id', {
                        where: {
                            st: start,
                            et: end
                        }
                    });
                }
            };

            $('#search_menu').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
            table.on('sort(user_card)', function(obj){ //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                  // console.log(obj.field); //当前排序的字段名
                  // console.log(obj.type); //当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
                  //尽管我们的 table 自带排序功能，但并没有请求服务端。
                  //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
                table.reload('id', {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。 layui 2.1.1 新增参数
                    ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                      field: obj.field //排序字段   在接口作为参数字段  field order
                      ,order: obj.type //排序方式   在接口作为参数字段  field order
                    },
                  });
            });
        });

        function LoseCard() {
            var checkStatus = layui.table.checkStatus('id');
            if (checkStatus.data.length ==0) {
                return layer.msg("{% trans 'Please select the card number that needs to be reported as lost' %}", {icon: 5, time: 2000});
            }
            if (checkStatus.data[0].cardstatus != "{% trans 'effective' %}") {
                return layer.msg("{% trans 'Not a valid card, can not report the loss' %}", {icon: 5, time: 1000});
            }
            if (checkStatus.data.length > 1) {
                return layer.msg("{% trans 'Only one card can be reported for loss' %}", {icon: 5, time: 1000});
            }
            layer.confirm("{% trans 'Are you sure to report the loss?' %}", function (index) {
                $.ajax({
                    url: "/selfservice/ipos/get_data_info/?info_type=OpLoseCard",
                    type: "POST",
                    data: {'K': checkStatus.data[0].id},
                    dataType: "json",
                    success: function (data) {
                        if (data.ret == 0) {
                            layer.msg("{% trans 'loss successfully' %}", {icon: 6, time: 1000});
                            layui.table.reload('id');
                        } else {
                            layer.msg("{% trans 'loss abortively:' %}"+data.message, {icon: 5, time: 1000});
                        }
                    }
                });
            });
        }

        function RevertCard() {
            var checkStatus = layui.table.checkStatus('id');
            if (checkStatus.data.length ==0) {
                return layer.msg("{% trans 'Select the card number that you want to unmount' %}", {icon: 5, time: 2000});
            }
            if (checkStatus.data.length > 1) {
                return layer.msg("{% trans 'Only one card can be unhooked' %}", {icon: 5, time: 1000});
            }
            if (checkStatus.data[0].cardstatus != "{% trans 'loss' %}") {
                return layer.msg("{% trans 'Please report the loss first, the operation failed.' %}", {icon: 5, time: 1000});
            }
            layer.confirm("{% trans 'Are you sure you want to unhang?' %}", function (index) {
                $.ajax({
                    url: "/selfservice/ipos/get_data_info/?info_type=OprevertCard",
                    type: "POST",
                    data: {'K': checkStatus.data[0].id},
                    dataType: "json",
                    success: function (data) {
                        if (data.ret == 0) {
                            layer.msg("{% trans 'Solutions Hanging successfully' %}", {icon: 6, time: 1000});
                            layui.table.reload('id');
                        } else {
                            layer.msg("{% trans 'Solutions Hanging abortively:' %}"+data.message, {icon: 5, time: 1000});
                        }
                    }
                });
            });
        }
    </script>
{% endblock %}