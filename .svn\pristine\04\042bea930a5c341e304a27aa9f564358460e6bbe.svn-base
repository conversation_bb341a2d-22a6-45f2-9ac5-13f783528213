{% extends "report_att.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block sidxblock %}
	function getSidx_department_finger(){
		return 'DeptID'
	}
{% endblock %}

{% block queryButton %}
	$("#"+g_activeTabID+" #queryButton").hide()
{% endblock %}

{% block can_export %}
    var can_export={% if user|HasPerm:"meeting.conference_report_export" %}true{% else %}false{% endif %}
{% endblock %}

{% block date_range %}


{% endblock %}
{% block date_set_range %}	

{% endblock %}

{% block getDateUrl %}

function getDateUrl()
{


	var urlStr=g_urls[g_activeTabID]
	return urlStr

}
{% endblock %}


{% block search %}
<div class="s-info right" id="sear_area">			
	<div class="nui-ipt nui-ipt-hasIconBtn " >
		<input id="searchbar" class="search-input" type="text"  value="{% trans "Department number, department name" %}" role='defvalue' autocomplete="off" />
		<span id ="queryButton" class="nui-ipt-iconBtn">
			<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
		</span>
		
	</div>
	
	<div class="main-search-btn">
	
		<span id="searchButton" class="chaxun icon iconfont icon-chaxun" title='{% trans "Search" %}'></span>
	</div>
</div>
{% endblock %}	
