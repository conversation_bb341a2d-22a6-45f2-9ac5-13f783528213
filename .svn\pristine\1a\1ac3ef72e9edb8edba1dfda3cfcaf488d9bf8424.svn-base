{% load i18n %}
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1,initial-scale=1,user-scalable=no" />
<title>{% trans 'Reservation' %}</title>
<link rel="stylesheet" href="static/css/weuix.css"/>
<link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/1.1.2/weui-for-work.min.css"/>
<style lang="css">
	.section__ctn {
    text-align:center;
  }
  .section_gap {
    padding:0 15px;
  }
  .section {
    margin-bottom:20px;
  }
  .section__title {
    margin-bottom:8px;
    padding-left:15px;
    padding-right:15px;
		font-size:12px;
    color:#666;
  }
  .section_gap .section__title {
    padding-left:0;
    padding-right:0;
    text-align:center;
  }
  .weui-start:after {
    content: '*';
    font-weight: 700;
    color: #f00;
    margin-left: 4px;
}

  .section__ctn1 {
      text-align:center;
      margin-bottom:39px;
  }
  .section_gap1 {
    padding:0 15px;
  }
  .section1 {
    background:#f9f9f9;
    margin-bottom:28px;
		padding-top:20px;
  }
  .section__title1 {
    margin-bottom:7px;
    padding-left:15px;
    padding-right:15px;
  }
  .section_gap1 .section__title1 {
    padding-left:0;
    padding-right:0;
    text-align:center;
  }
	.child {
    width: 280px;
    height: 2px;
    position: absolute;
    left: 0;
    top: 30px;
    right: 0;
    bottom: 0;
    margin: auto;
    text-align: center;
    font-size:16px;
    color:black;
}

.section__ctn3 {
      text-align:center;
  }
  .section_gap3 {
    padding:0 0px;
  }
  .section3 {
    background:#f9f9f9;
    margin-bottom:28px;
  }
  .section__title3 {
    margin-bottom:7px;
    padding-left:15px;
    padding-right:15px;
  }
  .section_gap3 .section__title3 {
    padding-left:0;
    padding-right:0;
    text-align:center;
  }
</style>	
<body>
	<div class="page" style="background-color:#f8f8f8;">
	  <div class="weui-panel" style="background-color:#f8f8f8!important;">
			<div class="weui-cells weui-cells_form">
			<div class="section section_gap" >
			<div class="section__title" style="padding-top: 30px;">{% trans 'Please arrive on time as scheduled' %}</div>
			<div class="section__title" style="padding-bottom: 10px;">{% trans 'Register at the front desk with the visitor's QR code or facial recognition' %}</div>
			<div id="outcard" class="weui-btn weui-btn_plain-primary" style="width:70%;font-size: 12px;">{% trans 'Enter facial information immediately' %}</div>
	    </div>
		</div>
		<div class="weui-cells__title" style="color:back">{% trans 'Visitor code' %}</div>
		<div class="weui-cells weui-cells_form">
    <!--<div class="section section_gap" >
    <div class="section__ctn">
     <image
          style="width: 80px; height: 80px; padding: 30px 0 10px 0;"
          src="visitor/<EMAIL>"
        >
     </image>
      </div>
     </div>-->
	   <div id="qrcodeimg" class='tcenter'></div>
    </div>
		</div>
		<div class="weui-cells__title">{% trans 'Appointment time' %}</div>
		<div class="weui-cells weui-cells_form">
			<div class="section__title">{% trans 'Register at the front desk with the visitor's QR code or facial recognition' %}</div>
			<div class="section__title">{% trans 'Register at the front desk with the visitor's QR code or facial recognition' %}</div>
			</div> 
		</div>
		</div>
		</div>
		<div id="about" class="weui-popup__container">
		 <div class="weui-popup__overlay"></div>
		  <div class="weui-popup__modal">
			   
    <div class='section1 section_gap1' id='noface'>
      <div class='section__ctn1' type="file">
      <image
        style="width: 196px; height: 249px; "
        src='visitor/<EMAIL>'
        >
      </image>
      </div>
     <div>
      <div class="child">{% trans 'Please click to input your facial information' %}</div>
     </div>
     <div>
      <div class="section__title1" style="color:black;font-size:12px;">{% trans 'Welcome to Xiamen zkteco' %}</div>
     </div>
      <div>
      <div class="section__title1" style="color:dimgrey;font-size:8px;">{% trans 'Please enter facial information for visitation identification registration' %}</div>
     </div>
      </div>

      <div class='section3 section_gap3' id='faceimage'>
      <div class='section__ctn3'>
      <image
        style="width: 100%; height: 300px;"
        src=''
        id="vzface">
      </image>
      </div>
     <div>
      <div class="section__title3" style="color:black;font-size:12px;">{% trans 'Welcome to Xiamen zkteco' %}</div>
     </div>
      <div>
      <div class="section__title3" style="color:dimgrey;font-size:8px;">{% trans 'Please enter facial information for visitation identification registration' %}</div>
     </div>
      </div>
    
			   <a href="javascript:;" class="weui-btn weui-btn_primary" id="checkcard" style="width:90%">{% trans 'determine' %}</a>
			   <a href="javascript:;" class="weui-btn weui-btn_plain-primary" id='close-popuo' style="width:90%">{% trans 'View my visitor code' %}</a>
			
		  </div>
		</div>
		<input id="uploaderInput" style="display:none" class="weui-uploader__input zjxfjs_file" type="file" accept="image/jpeg" multiple="single" required emptyTips="{% trans 'Please select photo' %}">
<script src="static/js/zepto.min.js"></script>
<script src="static/js/zepto.weui.js"></script>
<script src="static/js/weui.min.js"></script>
<script src="static/js/php.js"></script>
<script src="static/js/lrz.min.js"></script>
<script src="static/js/fastclick.js"></script>
<script src="static/js/qrcode.js"></script>
</body>
<script type="text/javascript">
	var fileArr=new Array();
	$("#outcard").click(function(){
		$("#faceimage").hide()
	  $(".page").hide();
	  $("#about").popup();
	})
  $uploaderInput = $("#uploaderInput");
	$uploaderInput.on("change", function(e) {
	var src, url = window.URL || window.webkitURL || window.mozURL,
	files = e.target.files;
	for(var i = 0, len = files.length; i < len; ++i) {
	    var file = files[i];
	    if(file.size> 5*1024*1024){
	       alert('5M')
	       return
	    }
	    lrz(file, {width: 750, fieldName: "file"}).then(function (data) {
	            fileArr.push(data.base64)
	        }).catch(function (err) {
	            alert(err)
	        });
	    if(url) {
	        src = url.createObjectURL(file);
	    }
	    else {
	        src = e.target.result;
	    }
	    $("#faceimage").show()
			$("#noface").hide()
			$("#vzface").attr("src",src);
	    }
	});
	$(".section__ctn1").click(function(){
		$('#uploaderInput').trigger("click");
	})
	$(".section__ctn3").click(function(){
		$('#uploaderInput').trigger("click");
	})
	$("#checkcard").click(function(){
        if (fileArr.length==0){
        	$.toptip("{% trans 'Upload images cannot be empty' %}");
        	return
        }
				$.ajax({
					type: "POST",
					url: "/token/visitor/saveVisitorPic",
					data: {'mobile': 17828057497, 'photoBase64': fileArr[fileArr.length]},
					beforeSend: function (xhr) {
							$.showLoading()
					},
					success: function (rs) {
						  $.hideLoading()
						  if(rs.cropUrl === '' || rs.cropUrl === undefined){
								$.toast("{% trans 'Image recognition failed' %}")
							}
							else{
								$.toast("{% trans 'Image uploaded successfully' %}")
								$.closePopup();
								$(".page").show();
							}
					}
				})
      })
	$("#close-popuo").click(function(){
		$.closePopup();
		$(".page").show();
	})	
		
	function qr(){
						var txt="432423432432";
						$("#qrcodeimg").empty().qrcode({render:"image",ecLevel:"L",size:200,background:"#fff",fill:"#000",text:txt});
				}
				qr()
</script>
</html>
