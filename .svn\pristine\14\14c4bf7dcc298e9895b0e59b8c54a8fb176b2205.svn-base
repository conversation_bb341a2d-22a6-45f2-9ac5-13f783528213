{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <div class="x-body">
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'StartDate' %}" name="start" id="start" autocomplete="off"></div>
        <div class="layui-input-inline"><input class="layui-input" placeholder="{% trans 'End Date' %}" name="end" id="end" autocomplete="off"></div>
        <div class="layui-input-inline">
            <select name="pos_model" id="pos_model" style="height: 38px">
              <option value="">{% trans 'consumption mode' %}</option>
              <option value="1">{% trans 'fixed mode' %}</option>
              <option value="2">{% trans 'Amount Mode' %} </option>
              <option value="3">{% trans 'key value mode' %}</option>
              <option value="4">{% trans 'counting mode' %} </option>
              <option value="5">{% trans 'commodity model' %}</option>
              <option value="6">{% trans 'Timekeeping Mode' %} </option>
              <option value="7">{% trans 'Accounting Mode' %}</option>
              <option value="8">{% trans 'Manual Supplement Consumption' %} </option>
              <option value="9">{% trans 'Device error correction' %} </option>
              <option value="10">{% trans 'Manual error correction' %} </option>
            </select>
        </div>
            <a class="layui-btn search_btn" data-type="reload" id="search_menu">{% trans 'Inquire' %}</a>
        <a class="layui-btn layui-btn-small" style="margin-top:3px;float:right" href="javascript:location.replace(location.href);" title="{% trans 'Reload' %}">
            <i class="icon iconfont" style="line-height:30px">&#xe6aa;</i>
        </a>
        <table class="layui-hide" id="user_iccon" lay-filter="user_iccon"></table>
    </div>
{% endblock %}
{% block extrjs %}
    <script>
        layui.use(['table', 'laydate'], function () {
            var table = layui.table;
            var laydate = layui.laydate;
            var form = layui.form;
            var startDate = laydate.render({
                elem: '#start',
                type: 'date',
                done: function (value, date) {
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1, //关键
                        date: date.date
                    };
                }
            });
            var endDate = laydate.render({
                elem: '#end',
                type: 'date',
                done: function (value, date) {
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    }
                }
            });

            var cols = [[
                    {type: 'checkbox'}
                    , {field: 'pin', width: 110, title: "{% trans 'BadgeNumber' %}"}
                    , {field: 'username', width: 110, title: "{% trans 'Name' %}"}
                    , {field: 'deptname', width: 120, title: "{% trans 'Department' %}"}
                    , {field: 'cardno', width: 120, title: "{% trans 'ID Card' %}"}
                    , {field: 'money', width: 100, title: "{% trans 'Amount of consumption' %}"}
                    , {field: 'blance', width: 100, title: "{% trans 'balance' %}"}
                    , {field: 'pos_model', width: 100, title: "{% trans 'consumption mode' %}"}
                    , {field: 'meal', width: 100, title: "{% trans 'meal' %}"}
                    , {field: 'dining', width: 100, title: "{% trans 'Restaurant' %}"}
                    , {field: 'pos_time', width: 165, title: "{% trans 'consumption time' %}", sort: true}
                ]]
            var cols_double = [[
                    {type: 'checkbox'}
                    , {field: 'pin', width: 110, title: "{% trans 'BadgeNumber' %}"}
                    , {field: 'username', width: 110, title: "{% trans 'Name' %}"}
                    , {field: 'deptname', width: 120, title: "{% trans 'Department' %}"}
                    , {field: 'cardno', width: 120, title: "{% trans 'ID Card' %}"}
                    , {field: 'money', width: 100, title: "{% trans 'cash consumption' %}"}
                    , {field: 'money_B', width: 100, title: "{% trans 'subsidized consumption' %}"}
                    , {field: 'blance', width: 100, title: "{% trans 'cash balance' %}"}
                    , {field: 'allow_balance', width: 100, title: "{% trans 'subsidy balance' %}"}
                    , {field: 'wallet_type', width: 100, title: "{% trans 'Operating Wallet' %}"}
                    , {field: 'pos_model', width: 100, title: "{% trans 'consumption mode' %}"}
                    , {field: 'meal', width: 100, title: "{% trans 'meal' %}"}
                    , {field: 'dining', width: 100, title: "{% trans 'Restaurant' %}"}
                    , {field: 'pos_time', width: 165, title: "{% trans 'consumption time' %}", sort: true}
                ]]
            {% if "POS_IC"|filter_config_option %}
                {% if "ipos_wallets"|get_wallet_type %}
                    var cols = cols_double
                {% endif %}
                cols[0].push({field: 'sys_card_no', width: 110, title: "{% trans 'card account' %}"})
                table.render({
                    elem: '#user_iccon'
                    , url: '/selfservice/ipos/get_data_info/?info_type=ICConsumerList'       // 数据接口
                    , cellMinWidth: 50 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                    , id: 'id'
                    , page: true
                    , cols: cols
                });
            {% else %}
                {% if "ipos_wallets"|get_wallet_type %}
                    var cols = cols_double
                {% endif %}
                table.render({
                    elem: '#user_iccon'
                    , url: '/selfservice/ipos/get_data_info/?info_type=CardCashSZ&type=6'       // 数据接口
                    , cellMinWidth: 50 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                    , id: 'id'
                    , page: true
                    , cols: cols
                });

            {% endif %}
            var $ = layui.$, active = {
                reload: function () {
                    var start = $("input[name='start']").val();
                    var end = $("input[name='end']").val();
                    var pos_model = $('#pos_model');
                    table.reload('id', {
                        where: {
                            st: start,
                            et: end,
                            pos_model: pos_model.val()
                        }
                    });
                }
            };

            $('#search_menu').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
            table.on('sort(user_iccon)', function(obj){ //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                  // console.log(obj.field); //当前排序的字段名
                  // console.log(obj.type); //当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
                  //尽管我们的 table 自带排序功能，但并没有请求服务端。
                  //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
                table.reload('id', {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。 layui 2.1.1 新增参数
                    ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                      field: obj.field //排序字段   在接口作为参数字段  field order
                      ,order: obj.type //排序方式   在接口作为参数字段  field order
                    },
                  });
            });
        });
    </script>
{% endblock %}