{% load iclock_tags %}
{% load i18n %}
{% block content_title %}{% endblock %}
<script>
function ActionHint(action, aName){return ""}
pageQueryString=location.search;
var groupHeaders=[]
jqOptions[g_activeTabID]=copyObj(jq_Options);
jqOptions[g_activeTabID].rowNum={{limit}}
canDefine={% if user|HasPerm:"iclock.preferences_user" %}true{% else %}false{% endif %}
{% block additionDataOptions %}
{% endblock %}
isSelectAll=false;
extraBatchOp=[];
tblName[g_activeTabID]='{{reportName}}';
var Custom_Jqgrid_Height=""
var page_tab=""
jqOptions[g_activeTabID].rowList=[]
jqOptions[g_activeTabID].multiselect=false
//jqOptions.footerrow=false
//jqOptions.userDataOnFooter=true
jqOptions[g_activeTabID].pager='id_pager_order'

document.getElementById("id_grid_order").id="id_grid_order_"+ tblName[g_activeTabID];

function initInnerorderWindow()
{
    innerLayout=$("#report_module_order").layout(innerSettings_default)
}

{% block RenderReportGrid %}

function RenderReportGrid(urlStr){
    $("#id_grid_order_"+ tblName[g_activeTabID]).jqGrid("GridUnload")
	if(urlStr.indexOf("?")!=-1){
		url=urlStr+"&title=1&stamp="+moment().unix();
	}
	else{
		url=urlStr+"?title=1&stamp="+moment().unix();
	}
    $.ajax({
	    type:"GET",
	    url:url,
	    dataType:"json",
	    success:function(json){
//		    jqOptions=copyObj(jq_Options)
		    grid_disabledfields[g_activeTabID]=json['disabledcols']
		    jqOptions[g_activeTabID].colModel=json['colModel']
		    groupHeaders=[]

		    if (json['groupHeaders'])
			groupHeaders=json['groupHeaders']

		    get_grid_fields(jqOptions[g_activeTabID])
		    hiddenfields(jqOptions[g_activeTabID])

		    jqOptions[g_activeTabID].url=urlStr
		    jqOptions[g_activeTabID].footerrow=true
		    jqOptions[g_activeTabID].userDataOnFooter=true
		    var hcontent=$("#"+g_activeTabID+" #id_content").height();
		    var hbar=$("#"+g_activeTabID+" #id_top").height();
		    var height=hcontent-hbar-100;
		    if (groupHeaders.length>0)
		     height=height-30;

		    if(typeof(Custom_Jqgrid_Height)!='undefined'&&Custom_Jqgrid_Height!=""){
			    jqOptions[g_activeTabID].height=Custom_Jqgrid_Height;
		    }else{jqOptions[g_activeTabID].height=height;}

		    //jqOptions.sortname=''//getSidx('original_records')
			jqOptions[g_activeTabID].url=urlStr
                $("#id_grid_order_" + tblName[g_activeTabID]).jqGrid(jqOptions[g_activeTabID]);
                $("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('setFrozenColumns');
                $("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('setGroupHeaders', {useColSpanStyle: true, groupHeaders: groupHeaders})

	 }
    });
}
{% endblock %}
{% block getorderDateUrl %}
    function getOrderDateUrl(start_date,end_date)
    {
        $("#id_con_error").css("display","none");
        var urlStr=g_urls[g_activeTabID]
        var st=moment().startOf('month').format('YYYY-MM-DD')
        var et=moment().endOf('month').format('YYYY-MM-DD')
        if(start_date) st=start_date
        if(end_date) et=end_date
        if(urlStr.indexOf("?")!=-1){
            urlStr=urlStr+"&StartDate="+st+"&EndDate="+et;
        }
        else{
            urlStr=urlStr+"?StartDate="+st+"&EndDate="+et;
        }
		return urlStr
    }
{% endblock %}

{#- 加密 -#}
function setExportPwd(tblname,tag,urlstr){
    var html="<div id='option_inits' class='required' align='center' style='top:20px;'><form id='dlg_option_inits'><table>"
			+"<tr><td><label>{% trans 'password' %}</label></td>"
			+"<td><input name='file_password' id='id_file_password' type='password' autocomplete='off'></td></tr>"
            +"<tr><td colspan='2'><ul class='errorlist'><li id='id_error_option_inits' style='display:none;'></li></ul></td></tr>"
			+"</table></form></div>"
	var title=""
	title = "{% trans 'Please set the encryption password for exporting files' %}"
    var s = $(html).dialog({
        title:title,
        sel:false,
        resizable:false,
        modal:true,
        width:300,
        height:200,
        focus:function(){
            $('input').focus()
        },
        buttons:[{
                    id:'btnShowOK',
                    text:'{% trans 'confirm' %}',
                    test:function(){
                        $(document).keydown(function (event) {
                            if(event.keyCode == 13){
                                $('#btnShowOK').click()
                            }
                        })
                    },
                    click: function(){
                        var encriptKey = $("#id_file_password").val()
                        clickexports(tblname,tag,urlstr,encriptKey)
                        $(this).dialog('option','sel',true)
                        $(this).dialog("destroy")
                    },
                },
                {
                    id:'btnCancel',
                    text:'{% trans "Return" %}',
                    click:function(){
                        $(this).dialog("destroy")
                    }
                }],
        close:function(){
            $(this).dialog("destroy")
        }
	});
    {#var ww = $('#option_inits').dialog('option','sel')#}
}

$(function(){
    {% block tree_show %}
    var hcontent=$("#"+g_activeTabID+" #id_content").height();
	var hbar=$("#"+g_activeTabID+" #id_top").length>0?$("#id_top").height():0;
	var h=hcontent-hbar;
	$('#report_module_order').css('height',h);
	$("#id_export_order").iMenu();
	{% endblock %}
	{% block date_set_range %}
		if (pos_start_date)
		    $('#'+g_activeTabID+' #id_StartDate_order').val(pos_start_date)
		 else
		    $('#'+g_activeTabID+' #id_StartDate_order').val(moment().startOf('month').format('YYYY-MM-DD'))

		 if(pos_end_date)
		     $('#'+g_activeTabID+' #id_EndDate_order').val(pos_end_date)
		 else
		     $('#'+g_activeTabID+' #id_EndDate_order').val(moment().endOf('month').format('YYYY-MM-DD'))



		$("#"+g_activeTabID+" #id_StartDate_order").datepicker(datepickerOptions);
		$("#"+g_activeTabID+" #id_EndDate_order").datepicker(datepickerOptions);


	    function validate_form_order_ipos(){   //验证表单的合法性(、开始时间、结束时间)
		var st=$("#"+g_activeTabID+"  #id_StartDate_order").val();
		var et=$("#"+g_activeTabID+"  #id_EndDate_order").val();
		return (moment(st,'YYYY-MM-DD').isValid()&&moment(et,'YYYY-MM-DD').isValid()&&(moment(et,'YYYY-MM-DD').diff(moment(st,'YYYY-MM-DD'), 'days')<=31))
	    }

		$("#"+g_activeTabID+" #id_search").click(function(){
			//var search_user_pin=$("#search_id_user__PIN").val();
			var isError=validate_form_order_ipos();
			var urlstr="";
			if(isError){

				var st=$("#"+g_activeTabID+"  #id_StartDate_order").val();
				var et=$("#"+g_activeTabID+"  #id_EndDate_order").val();
				if (st>et) {
					alert('{% trans "The start date cannot be greater than the end date!" %}')
					return
				}
				urlstr=getOrderDateUrl(st,et)
				}
			else
			{
			    alert('{% trans "Please check if the time format is correct and query for up to 31 days!" %}')
			    return;
			}
			pos_start_date=st
			pos_end_date=et

			savecookie("search_urlstr",urlstr);
			{% block DataReloadUrl %}
                $("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('setGridParam',{url:urlstr,datatype:"json"}).trigger("reloadGrid");
            {% endblock %}
		});
	{% endblock %}



	var hcontent=$("#"+g_activeTabID+" #id_content").height();
	var hbar=$("#"+g_activeTabID+" #id_top").length>0?$("#id_top").height():0;
	var h=hcontent-hbar
	$('#report_module_order').css('height',h)

	$("#"+g_activeTabID+" #searchButton").click(function(){
			searchShowOrderReport();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
		if(event.keyCode==13)
			searchShowOrderReport();
	});
	$("#"+g_activeTabID+" #id_custom").click(function(){
		ShowCustomField(jqOptions[g_activeTabID]);
	});



	$("#id_reload_order").click(function(){
		reloadData("order_" + tblName[g_activeTabID]);
	});
	urlStr=getOrderDateUrl(pos_start_date,pos_end_date)
	if (urlStr=='') return


	savecookie("search_urlstr",urlStr);

	RenderReportGrid(urlStr)

	var inputEl = $('#'+g_activeTabID+' .search-input')
         defVal[g_activeTabID] = inputEl.val();
    	 inputEl.bind("focus",function(){
		             var _this = $(this);
				if (_this.val() == defVal[g_activeTabID]) {
				    _this.val('');
				    _this.css('color','#000000');
				    //_this.attr('role','disabled')
				}
		})
	inputEl.bind("blur",function(){
		        var _this = $(this);
			if (_this.val() == '') {
			    _this.val(defVal[g_activeTabID]);
			    _this.css('color','#CCCCCC');
			    _this.attr('role','defvalue')
			}
			else
			    _this.attr('role','cansearch')
		})
	inputEl.bind("keydown",function(event) {
			if (event.which == 13) {
			      var _this = $(this);
			       _this.attr('role','cansearch')
			 }
		})
	{% block queryButton %}
	    $("#"+g_activeTabID+" #queryButton").click(function(){
            createQueryDlg_ipos();
        });
	{% endblock %}

	{% block loadData %}
	//var h=$("#"+g_activeTabID+" #west_content").height()-20
	//$('#showTree_report').css('height',h)
      if(urlStr.indexOf("DiningSumPos")>= 0 || urlStr.indexOf("DiningOrderDetails")>=0 || urlStr.indexOf("DiningFoodScheduleDetails")>=0 || urlStr.indexOf("MealPreDetails")>=0)
       {
	     ShowDininghallData('order_report')
	   }
       else
       {
	     ShowDeptData('order_report')
	   }
		var zTree = $.fn.zTree.getZTreeObj("showTree_order_report");
		zTree.setting.check.enable = false;
		zTree.setting.callback.onClick = function onClick(e, treeId, treeNode){
            var error = validate_form_order_ipos();
            if (error==false){
                alert('{% trans "Please check if the time format is correct and query for up to 31 days!" %}');
                return;
            }
			var IsGridExist=$("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('getGridParam','records')
			if(typeof(IsGridExist)=='undefined')
			{
				//alert('请首先选择报表类型')
				return false
			}

			var deptID=treeNode.id;
			var deptName=treeNode.name;
			//$.cookie("dept_ids",deptID, { expires: 7 });
			var ischecked=0;
			if($("#id_cascadecheck_order").prop("checked"))
				ischecked=1;
			var pos_start_date=$("#"+g_activeTabID+" #id_StartDate_order").val();
			var pos_end_date=$("#"+g_activeTabID+" #id_EndDate_order").val();
			urlStr=getOrderDateUrl(pos_start_date,pos_end_date)
			if (urlStr=='') return
			if(urlStr.indexOf("?")!=-1){
                if(urlStr.indexOf("DiningSumPos")>= 0 || urlStr.indexOf("DiningOrderDetails")>=0 || urlStr.indexOf("DiningFoodScheduleDetails")>=0 || urlStr.indexOf("MealPreDetails")>=0){
                    var urlStr=urlStr+"&Dininghall="+deptID+"&isContainChild="+ischecked
                }else{
                    var urlStr=urlStr+"&deptIDs="+deptID+"&isContainChild="+ischecked
                }
            }
			else{
                if(urlStr.indexOf("DiningSumPos")>= 0 || urlStr.indexOf("DiningOrderDetails")>=0 || urlStr.indexOf("DiningFoodScheduleDetails")>=0 || urlStr.indexOf("MealPreDetails")>=0){
                    var urlStr=urlStr+"?Dininghall="+deptID+"&isContainChild="+ischecked
                }else{
                    var urlStr=urlStr+"?deptIDs="+deptID+"&isContainChild="+ischecked
                }
            }
            savecookie("search_urlstr",urlStr);
            {#RenderReportGrid(urlStr)#}
            $("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('setGridParam',{url:urlStr,datatype:"json"}).trigger("reloadGrid");

		}

	{% endblock %}
});

 function createQueryDlg_ipos(){
    createDlgdeptfor10('employee_search_iposreport',1)
    $('#dlg_for_query_employee_search_iposreport').dialog({
        buttons:
        [
            {id:"btnShowOK",text:'{% trans 'search for' %}',click:function(){searchbydept_iposreport('employee_search_iposreport');$(this).dialog("destroy"); }},
            {id:"btnShowCancel",text:"{% trans 'Return' %}" ,click:function(){$(this).dialog("destroy"); }}
        ]
    })
 }

function searchbydept_iposreport(page){
    var dept_ids=getSelected_dept("showTree_"+page)
    var ischecked=0;
    if($("#id_cascadecheck_"+page).prop("checked"))
        ischecked=1;
    urlStr = "&deptIDs="+dept_ids+"&isContainChild="+ischecked
    var emp = getSelected_emp_ex("sel_employee_search_iposreport");
    if (emp.length > 0) {
        urlStr += "&UserIDs=" + emp
    }
    var st=$("#"+g_activeTabID+" #id_StartDate_order").val();
	var et=$("#"+g_activeTabID+" #id_EndDate_order").val();
    var url = getOrderDateUrl(st,et)+urlStr
    savecookie("search_urlstr", url);
    $("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('setGridParam', {url: url, datatype: 'json'}).trigger("reloadGrid");
}

function resetError()
{
	$("#id_error").hide();
	$("#id_error").html('')

}
function searchShowOrderReport(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	   var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	   var v=""
	var st=$("#"+g_activeTabID+" #id_StartDate_order").val();
	var et=$("#"+g_activeTabID+" #id_EndDate_order").val();
	urlStr=getOrderDateUrl(st,et);
	if (urlStr=='') return
	if(v!='')
	{
		if(urlStr.indexOf("?")==-1){
			urlStr+="?q="+encodeURI(v)
		}
		else{
			urlStr+="&q="+encodeURI(v)
		}
	}
	savecookie("search_urlstr",urlStr);
	$("#id_grid_order_" + tblName[g_activeTabID]).jqGrid('setGridParam',{url:urlStr,datatype:"json"}).trigger("reloadGrid");

}

</script>
<div id="id_top">
	{% block top %}
	<div class="sear-box quick-sear-box" style="min-width:900px">

		{% block date_range %}

		<div id="search_Time" class='left' style="width:860px;">
			<span>
				    <label  >{% trans 'start date' %}</label>
					<input type='text' name='StartDate'  id='id_StartDate_order' style='width:110px;'>
				    <label  >{% trans 'end date' %}</label>
					<input type='text' name='EndDate'  id='id_EndDate_order' style='width:110px;'>
                {% block date_range_ext %}
                {% endblock %}
			</span>
			<span id='id_search' ><a class='m-btn  zkgreen rnd mini'>{% trans 'Query' %}</a></span>
				</div>

		{% endblock %}


		{% block search %}
		<div class="s-info right" id="sear_area">
			<div class="nui-ipt nui-ipt-hasIconBtn " >
				<input id="searchbar" class="search-input" type="text"  value="{% trans "personnel number, name" %}" role='defvalue' autocomplete="off" />
				<span id ="queryButton" class="nui-ipt-iconBtn">
					<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
				</span>

			</div>

			<div class="main-search-btn">

				<span id="searchButton" class="chaxun icon iconfont icon-chaxun"></span>
			</div>
		</div>
		{% endblock %}
	</div>
	{% endblock %}

	{% block toolbar %}

	<div id="id_toolbar">
			<UL class="toolbar" id="navi">
				<LI id="id_reload_order" ><SPAN class="icon iconfont icon-shuaxin"></SPAN>{% trans "Reload" %}</LI>
				<LI id="id_export_order"  ><SPAN class="icon iconfont icon-daochu"></SPAN>{% trans "Export" %}
				        <ul id="op_menu_export" class="op_menu">
						<li><span>{% trans "file format" %}</span>
							<ul>
							<li onclick="javascript:clickexport(tblName[g_activeTabID],0);"><a href="#">EXCEL</a></li>
{#							<li onclick="javascript:clickexport(tblName[g_activeTabID],1);"><a href="#">TXT</a></li>#}
							<li onclick="javascript:clickexport(tblName[g_activeTabID],2);"><a href="#">PDF</a></li>
							</ul>
						</li>
					</ul>
				</LI>
				<LI id="id_custom" ><SPAN class="icon iconfont icon-select"></SPAN>{% trans "Preferences" %}</LI>
				{% block extractButton %}
				{% endblock %}
			</ul>
        </div>

	{% endblock %}

</div>

<!--
	<div id="show_field_selected">
		<div class="title"><span class='close' onclick='hideFields_define();'></span></div>
		<div id="id_fields_selected"></div>
	</div> -->


<div id="report_module_order" style="position:relative; width: 99%;margin-top: 2px;">
	{% block Data %}

		<div class="ui-layout-center" >
			<table id="id_grid_order" >	</table>
			<div id="id_pager_order"></div>
		</div>
	{% endblock %}

{% block extraSection %}{% endblock %}
</div>
<div id="id_tip" class="tip" style="visibility:hidden"></div>



