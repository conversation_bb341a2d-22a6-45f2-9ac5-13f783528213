{% extends "report_ipos.html" %}
{% load i18n %}
{% load iclock_tags %}
<script>
{% block search %}
		<div class="s-info right" id="sear_area">
			<div class="nui-ipt nui-ipt-hasIconBtn " >
				<input id="searchbar" class="search-input" type="text"  value="{% trans "Personnel number, name, operator" %}" role='defvalue' autocomplete="off" />
				<span id ="queryButton" class="nui-ipt-iconBtn">
					<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
				</span>
				
			</div>
			
			<div class="main-search-btn">
			
				<span id="searchButton" class="chaxun icon iconfont icon-chaxun"></span>
			</div>
		</div>
{% endblock %}

{% block tree_show %}
    var hcontent = $("#" + g_activeTabID + " #id_content").height();
    var hbar = $("#" + g_activeTabID + " #id_top_report").length > 0 ? $("#" + g_activeTabID + " #id_top_report").height() : 0;
    var h = hcontent - hbar;
    $('#report_module').css('height', h);
    initInnerWindow();
    $("#id_export_report").iMenu();
    ShowDeptData('report');
    var zTree = $.fn.zTree.getZTreeObj("showTree_report");
    zTree.setting.check.enable = false;
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode) {
        var IsGridExist = $("#id_grid_report").jqGrid('getGridParam', 'records');
        if (typeof (IsGridExist) == 'undefined') {
            //alert('请首先选择报表类型')
            return false
        }
        var deptID = treeNode.id;
        {#var dinding = treeNode.id;#}
        var deptName = treeNode.name;
        //$.cookie("dept_ids",deptID, { expires: 7 });
        var ischecked = 0;
        if ($("#id_cascadecheck_report").prop("checked"))
            ischecked = 1;
        urlStr = getDateUrl(pos_start_date,pos_end_date);
        if (urlStr == '') {
            return;
        }
        if (urlStr.indexOf("?") != -1)
            var urlStr = urlStr + "&deptIDs=" + deptID + "&isContainChild=" + ischecked;
        else
            var urlStr = urlStr + "?deptIDs=" + deptID + "&isContainChild=" + ischecked;
        savecookie("search_urlstr", urlStr);
        RenderReportGrid(urlStr)
    };
{% endblock %}

$(function(){
    var hcontent=$("#"+g_activeTabID+" #id_content").height();
	var hbar=$("#"+g_activeTabID+" #id_top").length>0?$("#id_top").height():0;
	var h=hcontent-hbar;
	$('#report_module').css('height',h);
});

</script>

{% block Data %}
    <div class="inner-ui-layout-west ui-layout-west">
        <div class="ui-widget-header" style="height: 20px;">
                <span id=id_opt_tree>
                <input type='checkbox' id='id_cascadecheck_report'/>
                <label for="id_cascadecheck_report">{% trans "Include Subordinates" %}</label>
                </span>
        </div>
        <div id='show_dept_tree_' class="inner-west-content">
            <ul id='showTree_report' class='ztree' style='margin-left: 0px;height: 100%'></ul>
        </div>
    </div>
    <div class="inner-ui-layout-center ui-layout-center">
        <table id="id_grid_report"></table>
        <div id="id_pager_report"></div>
        <div id="showReportSymbol" style="display:none;">{{ reportSymbol }}</div>
    </div>
{% endblock %}