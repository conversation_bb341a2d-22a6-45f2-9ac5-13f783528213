{% extends "report_att.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block sidxblock %}
	function getSidx_department_report(){
		return 'DeptID'
	}
{% endblock %}

{% block queryButton %}
	$("#"+g_activeTabID+" #queryButton").hide()
{% endblock %}

{% block search %}
<div class="s-info right" id="sear_area">			
	<div class="nui-ipt nui-ipt-hasIconBtn " >
		<input id="searchbar" class="search-input" type="text"  value="{% trans "Department number, department name" %}" role='defvalue' autocomplete="off" />
		<span id ="queryButton" class="nui-ipt-iconBtn">
			<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
		</span>
		
	</div>
	
{#	<div class="main-search-btn">#}
{#	    <span id="searchButton" class="chaxun icon iconfont icon-chaxun"></span>#}
{#	</div>#}
    <div class="main-search-btn" style="padding: 0 0 0 3px">
        <span id='searchButton' ><a class='m-btn  zkgreen rnd mini'>{% trans 'Query' %}</a></span>
    </div>
</div>
{% endblock %}	
