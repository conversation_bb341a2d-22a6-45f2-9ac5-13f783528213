{% extends "data_list.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block tblHeader %}
options[g_activeTabID].dlg_width=400;
options[g_activeTabID].dlg_height=275;



jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='OnlineUsers';
jqOptions[g_activeTabID].sortname='';
jqOptions[g_activeTabID].pager='#id_pager_OnlineUsers';

{% endblock %}

<script>
{% block loadData %}

	html="<p style='margin:5px;'>{% trans '1. Display the current software online administrator' %}</p>"
	$("#west_content_"+g_activeTabID).html(html)
     loadNULLPage('#id_grid_'+tblName[g_activeTabID]);
    var urlStr = "/iclock/data/OnlineUsers/?status=1"
    savecookie("search_urlstr", urlStr);
    $("#id_grid_" + tblName[g_activeTabID]).jqGrid('setGridParam', {
      url: urlStr,
      datatype: "json"
    }).trigger("reloadGrid");


{% endblock %}

{% block top %}
{% endblock %}

{% block exportOp %}
{% endblock %}
{% block newrec %}
{% endblock %}

{% block aDelete %}
{% endblock %}


{% block $function %}
	$("#"+g_activeTabID+" #id_reload").unbind('click').click(function(){
		reloadData(tblName[g_activeTabID],"/iclock/data/OnlineUsers/?status=1");
	});
{% endblock %}

</script>
