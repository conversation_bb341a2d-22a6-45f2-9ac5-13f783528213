{% load iclock_tags %}
{% load i18n %}
{% block content_title %}{% endblock %}
<script>
function ActionHint(action, aName){return ""}
pageQueryString=location.search;
var groupHeaders=[]
jqOptions[g_activeTabID]=copyObj(jq_Options);
jqOptions[g_activeTabID].rowNum={{limit}}
//canDefine={% if user|HasPerm:"iclock.preferences_user" %}true{% else %}false{% endif %}
{% block additionDataOptions %}
{% endblock %}
isSelectAll=false;
extraBatchOp=[];
tblName[g_activeTabID]='{{reportName}}';
var Custom_Jqgrid_Height_report=""
var page_tab=""
jqOptions[g_activeTabID].rowList=[]
jqOptions[g_activeTabID].sortname='id'
jqOptions[g_activeTabID].pager='id_pager_report'


document.getElementById("id_grid_report").id="id_grid_report_"+ tblName[g_activeTabID];


function validate_form_att(){   //验证表单的合法性(人员(可不选，在计算项目中计算选中部门的人员，其他的就默认值0)、开始时间、结束时间)
	var t_ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
	if(t_ComeTime==""){
		t_ComeTime="2012-01-01"
	}
	var cTime=t_ComeTime.split("-");
	var t_EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
	if(t_EndDate==""){
		t_EndDate="2212-01-01"
	}
	var eTime=t_EndDate.split("-");
	cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
	edate=new Date(parseInt(eTime[0],10),parseInt(eTime[1],10)-1,parseInt(eTime[2],10));
	var days=(edate.valueOf()-cdate.valueOf())/(1000*3600*24)+1;
	if(cdate>edate || t_ComeTime=="" || t_EndDate==""||!valiDate(t_ComeTime)||!valiDate(t_EndDate)||days>31){
		return 1;
	}else{
		return 0
	}
}
{% block RenderReportGrid %}

function loadReportGrid()
{
      urlStr = getDateUrl()
	  var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	  if (flag!='cansearch'&&flag!='defvalue') return;
	  if (flag!='defvalue')
		   var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	  else
		   var v=""
	  if(v!='')
	  {
		if(urlStr.indexOf("?")==-1){
			urlStr+="?q="+encodeURI(v)
		}
		else{
			urlStr+="&q="+encodeURI(v)
		}
	  }
	  RenderReportGrid(urlStr)
      savecookie("search_urlstr",urlStr);
}

function RenderReportGrid(urlStr){
    $("#id_grid_report_" + tblName[g_activeTabID]).jqGrid("GridUnload")
     //$.jgrid.gridUnload("#id_grid" )
    if(urlStr.indexOf("?")!=-1){
		urlStr=urlStr+"&stamp="+moment().unix();
	}
	else{
		urlStr=urlStr+"?stamp="+moment().unix();
	}
    $.ajax({
	    type:"GET",
	    url:urlStr+"&title=1",
	    dataType:"json",
	    success:function(json){
		    //jqOptions[g_activeTabID]=copyObj(jq_Options)
		    grid_disabledfields[g_activeTabID]=json['disabledcols']
		    jqOptions[g_activeTabID].colModel=json['colModel']
		    jqOptions[g_activeTabID].pager='id_pager_report'
		    groupHeaders=[]

		    if (json['groupHeaders'])
			groupHeaders=json['groupHeaders']

		    get_grid_fields(jqOptions[g_activeTabID])
		    hiddenfields(jqOptions[g_activeTabID])
		    jqOptions[g_activeTabID].url=urlStr
		    var hcontent=$("#"+g_activeTabID+" #id_content").height();
		    var hbar=$("#id_top_report").height();
		    var height=hcontent-hbar-70;
		    if (groupHeaders.length>0)
		     height=height-30;
		    if((urlStr.indexOf("dailycalcReport")>= 0)||(urlStr.indexOf("calcReport")>= 0)) {
			height=height-30;
		    }
		    if(typeof(Custom_Jqgrid_Height_report)!='undefined'&&Custom_Jqgrid_Height_report!=""){
			    jqOptions[g_activeTabID].height=Custom_Jqgrid_Height_report;
		    }else{jqOptions[g_activeTabID].height=height;}
			if($.isFunction(window['getSidx_'+tblName[g_activeTabID]])){
				jqOptions[g_activeTabID].sortname=window['getSidx_'+tblName[g_activeTabID]]()//getSidx('original_records')
			}else{
				jqOptions[g_activeTabID].sortname=getSidx()
			}
			jqOptions[g_activeTabID].url=urlStr
			jqOptions[g_activeTabID].multiselect=false
		    $("#id_grid_report_" + tblName[g_activeTabID]).jqGrid(jqOptions[g_activeTabID]);
		    $("#id_grid_report_" + tblName[g_activeTabID]).jqGrid('setFrozenColumns');
		    $("#id_grid_report_" + tblName[g_activeTabID]).jqGrid('setGroupHeaders', {useColSpanStyle: true,groupHeaders:groupHeaders})

		    if((urlStr.indexOf("dailycalcReport")>= 0)||(urlStr.indexOf("calcReport")>= 0)) {
			$("#showReportSymbol").show()
		    }else{
			$("#showReportSymbol").hide()
		    }


	 }
    });
}
{% endblock %}
{% block getDateUrl %}

function getDateUrl()
{
	var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
	var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
	var isError=validate_form_att();
	$.cookie("ComeTime",ComeTime, { expires: 7 });
	$.cookie("EndDate",EndDate, { expires: 7 });
	$("#"+g_activeTabID+" #id_con_error").css("display","none");

	var urlStr=g_urls[g_activeTabID]
	if(!isError){
		if(urlStr.indexOf("?")!=-1)
			urlStr+="&startDate="+ComeTime+"&endDate="+EndDate
		else
			urlStr+="?startDate="+ComeTime+"&endDate="+EndDate

	}else{
		alert('{% trans "Please check if the time format is correct and query for up to 31 days!" %}');
		urlStr='';
	}
	return urlStr

}
{% endblock %}


{% block sidxblock %}

	function getSidx(index){
	    var sidx=""

	    if (index=='calculated_leaves'||index=='shift_details'){
			if($("#o_Asc").prop("checked")){
				 if($("#o_DeptID").prop("checked")){
				 sidx=sidx+"UserID__DeptID__DeptName,"
				 }
				 if($("#o_PIN").prop("checked")){
				 sidx=sidx+"UserID__PIN,"
				 }
				 if($("#o_EName").prop("checked")){
				 sidx=sidx+"UserID__EName,"
				 }
				 if($("#o_TTime").prop("checked")){
				sidx=sidx+"AttDate,"
				}
			}else{
				if($("#o_DeptID").prop("checked")){
					sidx=sidx+"-UserID__DeptID__DeptName,"
				}
				if($("#o_PIN").prop("checked")){
				sidx=sidx+"-UserID__PIN,"
				}
				if($("#o_EName").prop("checked")){
				sidx=sidx+"-UserID__EName,"
				}
				if($("#o_TTime").prop("checked")){
				   sidx=sidx+"-AttDate,"
			   }
			}
			if(sidx.length>0)
			{
			   sidx=sidx.substring(0,sidx.length-1);
			}
			else{
				sidx="UserID__PIN,AttDate"
			}
	    }else if(index=="calculated_items"||index=="daily_report"||index=="excep_att_report"||index=="original_records") {
			if($("#o_Asc").prop("checked")){
				if($("#o_DeptID").prop("checked")){
				sidx=sidx+"DeptID__DeptName,"
				}
				if($("#o_PIN").prop("checked")){
				sidx=sidx+"PIN,"
				}
				if($("#o_EName").prop("checked")){
				sidx=sidx+"EName,"
				}

			}else{
				if($("#o_DeptID").prop("checked")){
				sidx=sidx+"-DeptID__DeptName,"
				}
				if($("#o_PIN").prop("checked")){
				sidx=sidx+"-PIN,"
				}
				if($("#o_EName").prop("checked")){
				sidx=sidx+"-EName,"
				}
			}
			if(sidx.length>0)
			{
			   sidx=sidx.substring(0,sidx.length-1);
			}
			else{
				sidx="PIN"
			}
	    }else{
			sidx=""
	    }
	    return sidx
	}
{% endblock %}


{% block loadData %}
    ShowDeptData('report')
    var zTree = $.fn.zTree.getZTreeObj("showTree_report");
    zTree.setting.check.enable = false;
    zTree.setting.callback.onClick = function onClick(e, treeId, treeNode){

    var IsGridExist=$("#id_grid_report_" + tblName[g_activeTabID]).jqGrid('getGridParam','records')
    if(typeof(IsGridExist)=='undefined')
    {
        //alert('请首先选择报表类型')
        return false
    }

    var deptID=treeNode.id;
    var deptName=treeNode.name;
    //$.cookie("dept_ids",deptID, { expires: 7 });
    var ischecked=0;
    if($("#id_cascadecheck_report").prop("checked"))
        ischecked=1;

    urlStr=getDateUrl()
    if (urlStr=='') return
    {% block add_urls %}
      if(urlStr.indexOf("?")!=-1)
        var urlStr=urlStr+"&deptIDs="+deptID+"&isContainChild="+ischecked
      else
          var urlStr=urlStr+"?deptIDs="+deptID+"&isContainChild="+ischecked
    {% endblock %}
    savecookie("search_urlstr",urlStr);
    RenderReportGrid(urlStr)

}

{% endblock %}


$(function(){
	var hcontent=$("#"+g_activeTabID+" #id_content").height();
	var hbar=$("#"+g_activeTabID+" #id_top_report").length>0?$("#"+g_activeTabID+" #id_top_report").height():0;
	var h=hcontent-hbar
	$('#report_module').css('height',h)
	initInnerWindow()
	$("#id_export_report_w").iMenu();

	dateTime1=moment().startOf('month').format("YYYY-MM-DD")
	dateTime=moment().format("YYYY-MM-DD")


{% block date_set_range %}
	if($.cookie("ComeTime")){
		$("#"+g_activeTabID+" #id_ComeTime").val($.cookie("ComeTime"))
		$("#"+g_activeTabID+" #id_EndTime").val($.cookie("EndDate"))
	};
	if (validate_form_att()){
		$("#"+g_activeTabID+" #id_ComeTime").val(dateTime1)
		$("#"+g_activeTabID+" #id_EndTime").val(dateTime)
	}

	$("#"+g_activeTabID+" #id_ComeTime").datepicker(datepickerOptions);
	$("#"+g_activeTabID+" #id_EndTime").datepicker(datepickerOptions);
{% endblock %}





	$("#"+g_activeTabID+" #searchButton").click(function(){
			if((urlStr.indexOf("annualstatic")>= 0||urlStr.indexOf("original_records")>= 0||urlStr.indexOf("dailycalcReport")>= 0))
			{
			  loadReportGrid()
			}
			else
			{
				searchShowReport();
			}
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
		if(event.keyCode==13)
			searchShowReport();
	});
	$("#"+g_activeTabID+" #id_custom").click(function(){
		ShowCustomField();
	});

	$("#id_reload_report").click(function(){
		searchShowReport();
	});






	urlStr=getDateUrl()
	if (urlStr=='') return


	//savecookie("search_urlstr",urlStr);
	RenderReportGrid(urlStr)

	var inputEl = $("#"+g_activeTabID+" .search-input")
         defVal[g_activeTabID] = inputEl.val();
    	 inputEl.on("focus",function(){
		             var _this = $(this);
				if (_this.val() == defVal[g_activeTabID]) {
				    _this.val('');
				    _this.css('color','#000000');
				    //_this.attr('role','disabled')
				}
		})
	inputEl.on("blur",function(){
		        var _this = $(this);
			if (_this.val() == '') {
			    _this.val(defVal[g_activeTabID]);
			    _this.css('color','#CCCCCC');
			    _this.attr('role','defvalue')
			}
			else
			    _this.attr('role','cansearch')
		})
	inputEl.on("keydown",function(event) {
			if (event.which == 13) {
			      var _this = $(this);
			       _this.attr('role','cansearch')
			 }
		})

	{% block queryButton %}
        $("#"+g_activeTabID+" #queryButton").click(function(){
            createQueryDlg();
        });
    {% endblock %}
  {% block initData %}
  {% endblock %}
});

function createQueryDlg(){
	createDlgdeptfor10('employee_search_attreport',1)
	$('#dlg_for_query_employee_search_attreport').dialog({
		buttons:
		[
			{id:"btnShowOK",text:'{% trans 'search for' %}',click:function(){searchbydept_attreport('employee_search_attreport');$(this).dialog("destroy"); }},
			{id:"btnShowCancel",text:"{% trans 'Return' %}" ,click:function(){$(this).dialog("destroy"); }}
		]
	})
}

function searchbydept_attreport(page){
    var dept_ids=getSelected_dept("showTree_"+page)
    var ischecked=0;
    if($("#id_cascadecheck_"+page).prop("checked"))
        ischecked=1;
    urlStr = "&deptIDs="+dept_ids+"&isContainChild="+ischecked
	var emp = getSelected_emp_ex("sel_employee_search_attreport");
	if (emp.length > 0) {
		urlStr += "&UserID=" + emp
	}
    var url = getDateUrl()+urlStr
	savecookie("search_urlstr", url);
	$("#id_grid_report_" + tblName[g_activeTabID]).jqGrid('setGridParam', {url: url, datatype: 'json'}).trigger("reloadGrid");
}

function resetError()
{
	$("#"+g_activeTabID+" #id_error").hide();
	$("#"+g_activeTabID+" #id_error").html('')

}
function searchShowReport(){
	var ischecked=0;
	if($("#id_cascadecheck_report").prop("checked"))
   		ischecked=1;
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	   var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	   var v=""
	urlStr=getDateUrl()
	if (urlStr=='') return
	if(v!='')
	{
		if(urlStr.indexOf("?")==-1){
			urlStr+="?q="+encodeURI(v)+ "&isContainChild=" + ischecked
		}
		else{
			urlStr+="&q="+encodeURI(v) + "&isContainChild=" + ischecked
		}
	}
	savecookie("search_urlstr",urlStr);
	$("#id_grid_report_" + tblName[g_activeTabID]).jqGrid('setGridParam',{url:urlStr,datatype:"json"}).trigger("reloadGrid");

}

function setExportPwd(tblname,tag,urlstr){
    var html="<div id='option_inits' class='required' align='center' style='top:20px;'><form id='dlg_option_inits'><table>"
			+"<tr><td><label>{% trans 'password' %}</label></td>"
			+"<td><input name='file_password' id='id_file_password' type='password' autocomplete='off'></td></tr>"
            +"<tr><td colspan='2'><ul class='errorlist'><li id='id_error_option_inits' style='display:none;'></li></ul></td></tr>"
			+"</table></form></div>"
	var title=""
	title = "{% trans 'Please set the encryption password for exporting files' %}"
    var s = $(html).dialog({
        title:title,
        sel:false,
        resizable:false,
        modal:true,
        width:300,
        height:200,
        focus:function(){
            $('input').focus()
        },
        buttons:[{
                    id:'btnShowOK',
                    text:'{% trans 'confirm' %}',
                    test:function(){
                        $(document).keydown(function (event) {
                            if(event.keyCode == 13){
                                $('#btnShowOK').click()
                            }
                        })
                    },
                    click: function(){
                        var encriptKey = $("#id_file_password").val()
                        clickexports(tblname,tag,urlstr,encriptKey)
                        $(this).dialog('option','sel',true)
                        $(this).dialog("destroy")
                    },
                },
                {
                    id:'btnCancel',
                    text:'{% trans "Return" %}',
                    click:function(){
                        $(this).dialog("destroy")
                    }
                }],
        close:function(){
            $(this).dialog("destroy")
        }
	});
    {#var ww = $('#option_inits').dialog('option','sel')#}
}

</script>


<div id="id_top_report">
	{% block top %}
	<div class="sear-box quick-sear-box"  style="min-width:900px;">
		{% block date_range %}


		<div id="search_Time" class='left' style="width:400px;">
			{% if "opt_users_start_page"|get_params:request == '1' %}
            <div id='id_add_home'  onclick="javascript:saveHome();"  style="float: left;cursor:pointer;"> <i class="icon iconfont icon-shezhiweiqishiye" title='{% trans "Set to start page" %}'></i></div>
            {% endif %}
		   <label >{% trans "Begin Date" %}</label>
		   <input type="text" name="ComeTime" maxlength="10" id="id_ComeTime" style='width:80px !important;'/>

		   <label  >{% trans "End Date" %}</label>
		   <input type="text" name="EndTime" maxlength="10" id="id_EndTime"  style='width:80px !important;'/>

		</div>

		{% endblock %}
		{% block line %}
		{% endblock %}
		{% block search %}
		<div class="s-info right" id="sear_area">
			<div class="nui-ipt nui-ipt-hasIconBtn " >
				<input id="searchbar" class="search-input" type="text"  value="{% trans "personnel number" %}" role='defvalue' autocomplete="off" />
				<span id ="queryButton" class="nui-ipt-iconBtn">
					<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
				</span>

			</div>

			<div class="main-search-btn">

				<span id="searchButton" class="chaxun icon iconfont icon-chaxun"></span>
			</div>
		</div>
		{% endblock %}
	</div>
	{% endblock %}

	{% block toolbar %}

	<div id="id_toolbar_report">
			<UL class="toolbar" id="navi">
				<LI id="id_reload_report"><SPAN  class="icon iconfont icon-shuaxin"></SPAN>{% trans "Reload" %}</LI>
				<LI id="id_export_report_w" ><SPAN  class="icon iconfont icon-daochu"></SPAN>{% trans "Export" %}
				        <ul id="op_menu_export" class="op_menu">
						<li><span>{% trans "file format" %}</span>
							<ul>
							<li onclick="javascript:clickexport(tblName[g_activeTabID],0);"><a href="#">EXCEL</a></li>
{#							<li onclick="javascript:clickexport(tblName[g_activeTabID],1);"><a href="#">TXT</a></li>#}
							<li onclick="javascript:clickexport(tblName[g_activeTabID],2);"><a href="#">PDF</a></li>
							</ul>
						</li>
					</ul>
				</LI>
				<LI id="id_custom"><SPAN class="icon iconfont icon-select"></SPAN>{% trans "Preferences" %}</LI>

			</ul>
        </div>

	{% endblock %}

</div>




<div id="report_module" style="position:relative; width: 99%;margin-top: 2px;">
	{% block Data %}
        {% block left_dept_tree %}

        {% endblock %}
		<div class="inner-ui-layout-center ui-layout-center">
			<table id="id_grid_report" >	</table>
			<div id="id_pager_report"></div>
			<div id="showReportSymbol" style="display:none;">{{reportSymbol}}</div>
		</div>
	{% endblock %}

{% block extraSection %}{% endblock %}
</div>
<div id="id_tip" class="tip" style="visibility:hidden"></div>



