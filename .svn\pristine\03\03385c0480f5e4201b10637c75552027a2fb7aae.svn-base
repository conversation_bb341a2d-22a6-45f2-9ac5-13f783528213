{% load iclock_tags %}
{% load i18n %}


<script>
pageQueryString=location.search;  {# 当前 页面 的查询字符串，页面 #}
//var options={pagerId:"pages", tblId:"tbl", showSelect: true, canSelectRow:false,showStyle:false,
options[g_activeTabID]={pagerId:"pages", tblId:"tbl", showSelect: true, canSelectRow:false,showStyle:false,
//	canEdit: {% if request|reqHasPerm:"change" %}true{% else %}false{% endif %},
	canAdd: {% if request|reqHasPerm:"add" %}true{% else %}false{% endif %},
	canDelete: {% if request|reqHasPerm:"delete" %}true{% else %}false{% endif %},
	canSearch: true, keyFieldIndex:"0", title:'{{ dataOpt.verbose_name|cap }}',

//	addition_fields:[], 	//要求服务器返回的附加字段
//	addition_columns:[],	//本地表格自行生成的列
//	exception_fields:['id'],	//要求服务器不要返回的列
	tblHeader:'{% trans "Please set a template file" %}options.tblHeader',
	dlg_width:810,
	dlg_height:'auto',
	edit_col:1,
	canHome:false
	
};
    
tblName[g_activeTabID]='door_level'    
    
jqOptions[g_activeTabID]=copyObj(jq_Options);
var jqOptions_east=copyObj(jq_Options);

{% autoescape off %}
jqOptions[g_activeTabID].rowNum={{ limit }}
jqOptions[g_activeTabID].colModel={{colModel}}
jqOptions[g_activeTabID].pager='id_pager_door_level'
jqOptions[g_activeTabID].onSelectRow=function(ids){show_detail_level(ids);}
jqOptions[g_activeTabID].multiselect=false


jqOptions_east.rowNum=10000
jqOptions_east.colModel={{colmodels_level_door}}
jqOptions_east.pager='id_pager_door_level_east'
jqOptions_east.onSelectRow=function(ids){show_door_msg(ids);}
jqOptions_east.multiselect=false
{% endautoescape %} 
 
 
 
    
var layoutSettings_center = {
    
 					east__paneSelector:		".inner-east"
                                ,       east__size:'600'
				,	spacing_open:			6  // ALL panes
				,	spacing_closed:			6  // ALL panes
				,	east__spacing_closed:	10

   
    
}
 
 //数据返回
function show_detail_level(ids)
{

        $("#id_grid_door_level_east").jqGrid('setGridParam',{url:"/acc/get_door_level/?id="+ids,datatype: "json"}).trigger('reloadGrid');
		
}
 
function show_door_msg(ids)
{

        $("#id_grid_door_level").jqGrid('setGridParam',{url:"/acc/get_door/?id="+ids,datatype: "json"}).trigger('reloadGrid');
		
} 

function searchShowEmp_door_emp(){
    var v=$("#"+g_activeTabID+" #searchbar").val();
    if(v=='{% trans "door number, door name" %}'){		v=''
	}
    var url="/acc/data/AccDoor/?t=AccDoor_cx.js&q="+encodeURI(v)
    savecookie("search_urlstr",url);
    $("#id_grid_door_level").jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
    
}


$(function(){

    $('#id_inner_center').layout(layoutSettings_center)


    var hcontent=$("#id_inner_center").height();
    var hbar=$("#id_inner_center"+"#id_top").length>0?$("#id_inner_center"+"#id_top").height():0;
    var h=hcontent-hbar-130
    $('.door_level_module').css('height',h)

    g_urls[g_activeTabID]='/acc/data/AccDoor/?t=AccDoor_cx.js'
    loadPageData();
    
    
		jqOptions_east.data=[]
		jqOptions_east.datatype='local'
		jqOptions_east.height=h
		$("#id_grid_door_level_east").jqGrid(jqOptions_east);
    
	$("#"+g_activeTabID+" #searchButton").click(function(){
		searchShowEmp_door_emp();
	})

	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
		if(event.keyCode==13){
			searchShowEmp_door_emp();
		}	
	})
    
    $("#"+g_activeTabID+" #queryButton").hide()
    

});

	var inputEl = $("#"+g_activeTabID+" .search-input")
         defVal[g_activeTabID] = inputEl.val();
    	 inputEl.on("focus",function(){
		             var _this = $(this);
				if (_this.val() == defVal[g_activeTabID]) {
				    _this.val('');
				    _this.css('color','#000000');
				}
		})
	inputEl.on("blur",function(){
		        var _this = $(this);
			if (_this.val() == '') {
			    _this.val(defVal[g_activeTabID]);
			    _this.css('color','#CCCCCC');
			    _this.attr('role','defvalue')
			}
			else
			    _this.attr('role','cansearch')
		})



</script>


<div id='id_inner_center' style="width: 100%;height: 100%;">

<div class="ui-layout-center">
    <div id="id_top">
	<div class="sear-box quick-sear-box" >
		
		
		<div class="s-info left" id="sear_area">
			<div class="nui-ipt nui-ipt-hasIconBtn " >
				<input id="searchbar" class="search-input" type="text"  value="{% trans "door number, door name" %}" role='defvalue' autocomplete="off" />
				<span id ="queryButton" class="nui-ipt-iconBtn">
					<b id="_icon_2_28" class="nui-ico nui-ico-dArr "></b>
				</span>
				
			</div>
			
			<div class="main-search-btn" style="padding: 0 0 0 3px">
{#				<span id="searchButton" class="chaxun icon iconfont icon-chaxun"></span>#}
                <span id='searchButton' ><a class='m-btn  zkgreen rnd mini'>{% trans 'Query' %}</a></span>
			</div>
		</div>
		
				
	</div>
                    
        <div id="id_toolbar">	
            <UL class="toolbar" id="navi" style='z-index: 1 !important;'></UL>
        </div>            
    </div>               

    <div class="door_level_module" style="width: 99%;padding-top: 3px;">
	
	<table id="id_grid_door_level" >	</table>
	<div id="id_pager_door_level"></div>
    </div>
</div>
		
                
<div class="inner-east">

    <div id="id_top">
	<div class="sear-box quick-sear-box" >
		
		
		
				
	</div>
                    
        <div id="id_toolbar" >	
            <UL class="toolbar" id="navi" style='z-index: 1 !important;'>
	            
            </UL>
        </div>            
    </div>               



    <div class="east_module" style="width: 99%;padding-top: 3px;">
	
	<table id="id_grid_door_level_east" >	</table>
	<div id="id_pager_door_level_east"></div>
    </div>


</div>
