{% extends "base.html" %}
{% load i18n %}
{% load iclock_tags %}

{% block title %}{{ location }}{% endblock %}

{% block extrjs %}
<script src="{{ MEDIA_URL }}jslib/simple.datagrid.js"></script>
<script src="{{ MEDIA_URL }}jslib/base.js?scriptVersion=11.004"></script>
<script src="{{ MEDIA_URL }}jslib/core.min.js?scriptVersion=10.001"></script>

<script src="{{ MEDIA_URL }}jslib/jquery-ui/jquery-ui-timepicker-addon.js?scriptVersion=10.002"></script>
{% if not 'en' == LANGUAGE_CODE %}
 {% if 'zh-hans' == LANGUAGE_CODE %}

<script src="{{ MEDIA_URL }}jslib/jquery-ui/i18n/jquery.ui.datepicker-zh-cn.js?scriptVersion=10.002" type="text/javascript"></script>
<script src="{{ MEDIA_URL }}jslib/jquery-ui/i18n/jquery-ui-timepicker-zh-cn.js?scriptVersion=10.002" type="text/javascript"></script>
    {% else %}
<script src="{{ MEDIA_URL }}jslib/jquery-ui/i18n/jquery.ui.datepicker-{{ LANGUAGE_CODE }}.js?scriptVersion=10.002" type="text/javascript"></script>
<script src="{{ MEDIA_URL }}jslib/jquery-ui/i18n/jquery-ui-timepicker-{{ LANGUAGE_CODE }}.js?scriptVersion=10.002" type="text/javascript"></script>
    {% endif %}
{% endif %}

{% endblock %}

{% block extrastyle %}
	<link rel="stylesheet" type="text/css" href="{{ MEDIA_URL }}css/layout/layout.css?cssVersion=10.003"  />
	<link rel="stylesheet" type="text/css" href='{{ MEDIA_URL }}/css/staff.css?cssVersion=10.003' />
	<link rel="stylesheet" type="text/css" href='{{ MEDIA_URL }}/css/simple.datagrid.css' />
	<link rel="stylesheet" id='css_sf' type="text/css" href="{{ MEDIA_URL }}css/sf.css?cssVersion=10.003" />
	<link rel="stylesheet" id='css_sf' type="text/css" href="{{ MEDIA_URL }}/css/staff_employee.css" />
	
{% endblock %}
{% block content %}
<DIV id="id_north" class="ui-layout-north">

	<div id="banner">
        {% if ''|isZKTime == '1' %}
            <div id="banner_logo_zktime">
                <h1><a href=""></a></h1>
            </div>
        {% else %}
            <div id="banner_logo">
                 <h1><a href=""></a></h1>
            </div>
        {% endif %}
	 </div>  	


  
	<div id='mainmenu_out'>
	       <div id='mainmenu_in'>
		       <div id='mainmenu'>
			       <ul class='nav_main'>
					       {{ menu_title }}
			       </ul>
		       </div>
	       </div>
	</div> 
			       
			       
	{% if user.is_authenticated and user.is_staff %}
		<div id="user-tools" >
			{% block userlinks %}
				<a href="/iclock/accounts/logout/" style="text-decoration:none;color: #FFFFFF">{% trans 'Log out' %}</a>
			{% endblock %}
		</div>
	{% endif %}
	
	<div class="ui-layout-north-bottom">  </div>
	
</div>
	
	
<div id='id_container' class="container_">
<div id='bodys'>

	<div id="staff_left">
		{% autoescape off %}
		{{sub_menu}}
		{% endautoescape %}
	</div>
	<div id="staff_content">
        <div class="about">
            <div class="subheader">
            <div class="subheader_line"></div>
            <h2>{% trans "on" %}</h2>
            <div class="subheader_line"></div>
            </div>
        </div>
		<div class="h">
            <div class="person_pic"><img src="{{ person_pics }}" /></div>
            <div class="person_info" >
                <h3 id="welcome">
                    <p>{{employee.pin}}	&nbsp;&nbsp;{{employee.name|default:''}}&nbsp;&nbsp;{% trans 'Welcome!' %}</p>
                    <p>{% trans 'Last login time:' %}{{loginTime}}</p>
                    <p>{% trans 'cumulative logins (times):' %}{{loginCount}}</p>
                    <p>{% trans 'Login IP:' %}{{loginIP}}</p>
                </h3>
<!--                 <div style="width:110px; height:110px; position:absolute; top:201px; left:373px;font-size:11px;">
                    <img src="{{iappQrcodeUrl}}" border='0'>
                    <div style="margin-top: -13px;margin-left: -11px; text-align:center;font-family:'黑体'">{% trans 'self-service' %}</div>
                </div> -->
            </div>
		</div>
	</div>
	
	</div>
    <div id='id_tip'></div>
</div>
<script>
	
function valiDate(str){                
    var reg = /^(\d+)-(\d{1,2})-(\d{1,2})$/;
    var r = str.match(reg);
    if(r==null)return false;
    r[2]=r[2]-1;
    var d= new Date(r[1], r[2],r[3]);
    if(d.getFullYear()!=r[1])return false;
    if(d.getMonth()!=r[2])return false;
    if(d.getDate()!=r[3])return false;
    return true;
}
	
	
	
$(function(){

    var important_day = {{ important_day|safe }};
    if( important_day.Birthday !=''){
            if(important_day.Birthday == '0'){alert("{% trans 'happy birthday' %}")}
            else{ alert('{% trans "there will be" %}'+important_day.Birthday+'{% trans "days is you birthday" %}');}
    }
    if(important_day.Hiredday != ''){
        if(important_day.Hiredday == '0'){alert("{% trans 'Congratulations Hiredday! It is an honor to be able to work together for the rest of our lives.' %}")}
        else{alert("{% trans 'there will be' %}"+important_day.Hiredday+"{% trans 'days，you will become a regular worker' %}");}
    }
    if(important_day.Contractendtime!=''){
        if(important_day.Contractendtime == '0'){alert("{% trans 'I am sorry，Your contract has expired.' %}")}
        else{alert("{% trans 'there will be' %}"+important_day.Contractendtime+"{% trans 'days，Your contract has expired.' %}");}

    }


});

function setleft(){
    var w1=$(window).width()
    var w2=$("#id_container").width()
    var lf=(w1-w2)/2+23
    if(lf<0){
	lf=0;
    }
    $("#id_container").css("left",lf)
}
function submenuClick(url,obj)
{
	if (typeof(obj) != 'undefined'){
		$("#menu_div li").removeClass('subnav_on')
		$(obj).addClass('subnav_on')
	}
	g_urls[g_activeTabID]=url;
	if(url.indexOf("?")!=-1){
		urll=url+"&stamp="+new Date().toUTCString();
	}
	else{
		urll=url+"?stamp="+new Date().toUTCString();
	}

        //$.blockUI({title:'',theme: true ,baseZ:10000,message: '<img src="../media/img/loading.gif" />'});

	//delcookie("search_urlstr")

        $.ajax({
            type: "POST",
            url:urll,
            data:'',
            dataType:"text",
            success:function(data){
		$('#staff_content').html(data)
           },
            error:function(){
            }
        });
	
}
	
	
	
</script>
{% endblock %}
	
