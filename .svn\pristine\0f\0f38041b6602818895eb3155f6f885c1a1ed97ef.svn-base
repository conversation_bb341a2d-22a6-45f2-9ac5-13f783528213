# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2020-06-23 10:30
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0043_auto_20200608_1044'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='iclock',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('pause_iclock', 'Pause device'), ('resume_iclock', 'Resume a resumed device'), ('reloaddata_iclock', 'Upload data again'), ('reloadlogdata_iclock', 'Upload transactions again'), ('info_iclock', 'Refresh device information'), ('reboot_iclock', 'Reboot device'), ('loaddata_iclock', 'Upload new data'), ('cleardata_iclock', 'Clear data in device'), ('clearlog_iclock', 'Clear transactions in device'), ('devoption_iclock', 'Set options of device'), ('unlock_iclock', 'Output unlock signal'), ('unalarm_iclock', 'Terminate alarm signal'), ('attdataProof_iclock', 'Attendance data proofreading'), ('toDevWithin_iclock', 'Transfer to the device templately'), ('mvToDev_iclock', 'Move employee to a new device'), ('AutoToDev_employee', 'Auto transfer employee to the device'), ('Upload_AC_Options', 'Upload AC Options'), ('Upload_User_AC_Options', 'Upload User AC Options'), ('deptEmptoDev_iclock', 'Transfer employee of department to the device'), ('deptEmptoDelete_iclock', 'Delete employee from the device'), ('browselogPic', 'browse logPic'), ('deptEmpDelFromDev_iclock', 'Remove people or feature templates from device'), ('Upload_pos_all_data', 'Upload All Data'), ('Upload_pos_Merchandise', 'Upload Merchandise'), ('Upload_pos_Meal', 'Upload Meal'), ('Upload_Iclock_Photo', 'Upload Iclock Photo'), ('delDevPic_iclock', 'delDevPic employee'), ('set_device_asp', 'set divice ascription')), 'verbose_name': 'device', 'verbose_name_plural': 'device'},
        ),
    ]
