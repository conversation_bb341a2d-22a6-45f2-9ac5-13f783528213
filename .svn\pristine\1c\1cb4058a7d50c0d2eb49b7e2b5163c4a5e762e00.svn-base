/* Galician localization for 'UI date picker' jQuery extension. */
/* Translated by <PERSON> <<EMAIL>>. */
jQuery(function($){
	$.datepicker.regional['gl'] = {
		closeText: 'Pechar',
		prevText: '&#x3C;Ant',
		nextText: 'Seg&#x3E;',
		currentText: 'Hoxe',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Agos<PERSON>','Setem<PERSON>','Outubro','Novembro','Decembro'],
		monthNamesShort: ['<PERSON><PERSON>','Feb','<PERSON>','Abr','<PERSON>','<PERSON><PERSON>',
		'Xu<PERSON>','A<PERSON>','<PERSON>','Out','Nov','Dec'],
		dayNames: ['<PERSON>','Luns','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','S<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','Lun','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','V<PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>o','Ve','Sá'],
		weekHeader: 'Sm',
		dateFormat: 'dd/mm/yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['gl']);
});
