# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2023-03-28 09:53
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0064_auto_20230328_0953'),
        ('meeting', '0013_meetlocation_remark'),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetOrderDetails',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': 'Meet Order Details',
                'verbose_name_plural': 'Meet Order Details',
                'default_permissions': ('browse', 'add', 'change', 'delete'),
            },
        ),
        migrations.AddField(
            model_name='meet_order',
            name='applicant',
            field=models.ForeignKey(db_column='employee_id', editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='Applicant'),
        ),
        migrations.AddField(
            model_name='meetorderdetails',
            name='MeetID',
            field=models.ForeignKey(db_column='meetid_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='meeting.Meet_order', verbose_name='meeting'),
        ),
        migrations.AddField(
            model_name='meetorderdetails',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='Participants'),
        ),
        migrations.AlterUniqueTogether(
            name='meetorderdetails',
            unique_together=set([('MeetID', 'UserID')]),
        ),
    ]
