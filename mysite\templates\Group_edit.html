{% load iclock_tags %}
{% load i18n %}

<div id="id_form" >

<form method="post" id="id_edit_form" enctype="multipart/form-data">
<table>
<tr><th></th><td>
	<span  style="display:none" id="id_span_title">
		{% if add %}{% trans "Add" %}  {% else %} {% trans "Edit" %} {% endif %} {{ dataOpt.verbose_name|escape }}</span>
	<span  style="display:none" id="id_span_parent">
		{{ parent_name }}</span>
</td></tr>

{% autoescape off %}
<tr>
    <th>
    <font color="red">*</font><label for="id_name" >{% trans "Group name" %}:</label>
    </th>
    <td style='vertical-align:top;'> {{ form.name.as_widget }} </td>
</tr>
<tr>
    <td style="display:none;">{{ form.permissions.as_widget }}</td>
</tr>

<tr id="addition_fields">{% block addfields %}{% endblock %}</tr>
</td>
</tr>
</table>
</form>

<div id="tabs_group" >
	<ul>
        {% if ''|isZKTime == '1' %}
            <li><a href="#tabs-employees">{% trans "Personnel" %}</a></li>
            <li><a href="#tabs-device">{% trans "equipment" %}</a></li>
            <li><a href="#tabs-schedule">{% trans "Schedule" %}</a></li>
            <li><a href="#tabs-query">{% trans "Report" %}</a></li>
            {% if "antiepidemic"|get_params:request == '1' %}
                <li><a href="#tabs-epidemic">{% trans "Antiepidemic" %}</a></li>
            {% endif %}
            <li><a href="#tabs-system">{% trans "system" %}</a></li>
        {% endif %}
        {% if ''|isECOpos == '1' %}
            <li><a href="#tabs-employees">{% trans "Personnel" %}</a></li>
            <li><a href="#tabs-information">{% trans "information" %}</a></li>
            <li><a href="#tabs-device">{% trans "equipment" %}</a></li>
            <li><a href="#tabs-query">{% trans "Report" %}</a></li>
            <li><a href="#tabs-system">{% trans "system" %}</a></li>
        {% endif %}
        {% if ''|isZKAdms == '1' %}
            <li><a href="#tabs-employees">{% trans "Personnel" %}</a></li>
            <li><a href="#tabs-adms">{% trans "data" %}</a></li>
            <li><a href="#tabs-system">{% trans "system" %}</a></li>
        {% endif %}
        {% if ''|isZKTime == '0' %}
        {% if ''|isZKAdms == '0' %}
            {% if ''|isECOpos == '0' %}
            <li><a href="#tabs-firmware">{% trans "设备特性模块" %}</a></li>
            <li><a href="#tabs-basic">{% trans "Basic" %}</a></li>
            <li><a href="#tabs-device">{% trans "equipment" %}</a></li>
            <li><a href="#tabs-att">{% trans "Attendance" %}</a></li>
            <li><a href="#tabs-acc">{% trans "access control" %}</a></li>
            <li><a href="#tabs-meeting">{% trans "meeting" %}</a></li>
            <li><a href="#tabs-visitors">{% trans "visitor" %}</a></li>
            <li><a href="#tabs-ipos">{% trans "consumption" %}</a></li>
            {% if "antiepidemic"|get_params:request == '1' %}
                <li><a href="#tabs-epidemic">{% trans "Antiepidemic" %}</a></li>
            {% endif %}
			{% if 'asset'|enabled_module %}
            <li><a href="#tabs-asset">{% trans "Asset" %}</a></li>
            {% endif %}
            {% if 'payroll'|enabled_module %}
            <li><a href="#tabs-payroll">{% trans "Payroll" %}</a></li>
            {% endif %}
            <li><a href="#tabs-system">{% trans "system" %}</a></li>
            {% endif %}
        {% endif %}
        {% endif %}

	</ul>
    {% if ''|isZKTime == '0' %}
    {% if ''|isZKAdms == '0' %}
    {% if ''|isECOpos == '0' %}
	<div id="tabs-basic">
            <ul style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='basic-permissions' ></ul>
	</div>
	<div id="tabs-device">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='device-permissions' ></div>
	</div>
	<div id="tabs-system">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='system-permissions' ></div>
	</div>

	<div id="tabs-firmware">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='firmware-permissions' ></div>
	</div>

	<div id="tabs-att">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='att-permissions' ></div>
	</div>
	<div id="tabs-acc">
		<div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='acc-permissions' ></div>
	</div>
	<div id="tabs-meeting">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='meeting-permissions' ></div>
	</div>
	<div id="tabs-visitors">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='visitors-permissions' ></div>
	</div>
	<div id="tabs-ipos">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='ipos-permissions' ></div>
	</div>
    {% if "antiepidemic"|get_params:request == '1' %}
	<div id="tabs-epidemic">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='epidemic-permissions' ></div>
	</div>
    {% endif %}
    {% if 'asset'|enabled_module %}
    <div id="tabs-asset">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='asset-permissions' ></div>
	</div>
    {% endif %}
    {% if 'payroll'|enabled_module %}
      <div id="tabs-payroll">
        <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='payroll-permissions' ></div>
      </div>
    {% endif %}
    {% endif %}
    {% endif %}
    {% endif %}
    {% if ''|isECOpos == '1' %}
        <div id="tabs-employees">
            <ul style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='employees-permissions' ></ul>
        </div>
        <div id="tabs-device">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='device-permissions' ></div>
	    </div>
        <div id="tabs-system">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='system-permissions' ></div>
        </div>
        <div id="tabs-information">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='information-permissions' ></div>
        </div>
        <div id="tabs-query">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='query-permissions' ></div>
        </div>
    {% endif %}
    {% if ''|isZKTime == '1' %}
        <div id="tabs-employees">
            <ul style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='employees-permissions' ></ul>
        </div>
        <div id="tabs-device">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='device-permissions' ></div>
	    </div>
        <div id="tabs-system">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='system-permissions' ></div>
        </div>
        <div id="tabs-schedule">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='schedule-permissions' ></div>
        </div>
        <div id="tabs-query">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='query-permissions' ></div>
        </div>
        {% if "antiepidemic"|get_params:request == '1' %}
        <div id="tabs-epidemic">
                <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='epidemic-permissions' ></div>
        </div>
        {% endif %}
    {% endif %}
    {% if ''|isZKAdms == '1' %}
        <div id="tabs-employees">
            <ul style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='employees-permissions' ></ul>
        </div>
        <div id="tabs-system">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='system-permissions' ></div>
        </div>
        <div id="tabs-adms">
            <div style='width:101%;height:330px;color:black;overflow:auto;' class='ztree' id='adms-permissions' ></div>
        </div>
    {% endif %}
</div>



<div id="id_error"></div>

{% block add_contents %}{% endblock %}
{% block add_contents_2c %}{% endblock %}

</div>
{% endautoescape %}
