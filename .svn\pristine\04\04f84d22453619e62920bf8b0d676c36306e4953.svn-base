# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2018-08-24 16:15
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0002_auto_20171218_1239'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='sendemail',
            options={'verbose_name': 'Mailing', 'verbose_name_plural': 'SendEmail'},
        ),
        migrations.AlterField(
            model_name='sendemail',
            name='EmailNotice',
            field=models.Char<PERSON>ield(db_column='emailnotice', max_length=40, verbose_name='message theme'),
        ),
        migrations.AlterField(
            model_name='sendemail',
            name='SendTag',
            field=models.IntegerField(blank=True, db_column='sendtag', null=True, verbose_name='send status'),
        ),
        migrations.AlterField(
            model_name='sendemail',
            name='SendTime',
            field=models.DateTimeField(blank=True, db_column='sendtime', max_length=8, null=True, verbose_name='Send time'),
        ),
        migrations.AlterField(
            model_name='sendemail',
            name='purpose',
            field=models.IntegerField(blank=True, choices=[(0, 'Meeting announcement')], null=True, verbose_name='purpose'),
        ),
    ]
