var ZK={extend:function(){var a=function(b){for(var a in b)this[a]=b[a]},d=Object.prototype.constructor;return function(b,c,f){"object"==typeof c&&(f=c,c=b,b=f.constructor!=d?f.constructor:function(){c.apply(this,arguments)});var e=function(){},g=c.prototype;e.prototype=g;e=b.prototype=new e;e.constructor=b;b.superclass=g;g.constructor==d&&(g.constructor=c);b.override=function(a){ZK.override(b,a)};e.override=a;ZK.override(b,f);b.extend=function(a){ZK.extend(b,a)};return b}}(),override:function(a,d){if(d){var b=a.prototype,c;for(c in d)b[c]=d[c]}},apply:function(a,d,b){b&&ZK.apply(a,b);if(a&&d&&"object"==typeof d)for(var c in d)a[c]=d[c];return a}};function getRandomNum(){return parseInt(1E4*Math.random())}function getBrowserType(){return"undefined"!==typeof Worker?"html5":0<navigator.userAgent.indexOf("MSIE 8.0")||0<navigator.userAgent.indexOf("MSIE 9.0")?"simple":"upgradeBrowser"}function encodeContent(a){return encodeURI(a).replace(/&/g,"%26").replace(/\+/g,"%2B").replace(/\s/g,"%20").replace(/#/g,"%23")}function getCanvasContext(a){"undefined"!=typeof window.G_vmlCanvasManager&&(a=window.G_vmlCanvasManager.initElement(a));return a.getContext("2d")}function getXMLRequest(){var a=null;window.XMLHttpRequest?a=new XMLHttpRequest:window.ActiveXObject&&(a=new ActiveXObject("Microsoft.XMLHTTP"));return a};