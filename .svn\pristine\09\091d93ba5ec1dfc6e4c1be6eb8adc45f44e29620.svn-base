{% extends "report_ipos.html" %}
{% load i18n %}
{% load iclock_tags %}


{% block getDateUrl %}

function getDateUrl(pos_start_date,pos_end_date)
{

	$("#id_con_error").css("display","none");

	var urlStr=g_urls[g_activeTabID]
	var code=$("#search_id_code").val()
	var name=$("#search_id_name").val()
	var st=moment().startOf('month').format('YYYY-MM-DD')
	var et=moment().endOf('month').format('YYYY-MM-DD')
        if(pos_start_date) st=pos_start_date
        if(pos_end_date) et=pos_end_date
	if(urlStr.indexOf("?")!=-1){
		urlStr=urlStr+"&StartDate="+st+"&EndDate="+et;
	}
	else{
		urlStr=urlStr+"?StartDate="+st+"&EndDate="+et;
	}
    if(name){
        urlStr+="&name="+name;
    }
    if(code){
        urlStr+="&code="+code;
    }

	return urlStr

}
{% endblock %}


{% block queryButton %}
	
$("#"+g_activeTabID+" #queryButton").click(function(){
	createQueryDlg();
});


function searchbydept_Supplement(page) {

	//var dept_ids = getSelected_dept("showTree_" + page)
	//if (dept_ids != null) {
	//	if (dept_ids == undefined || dept_ids == '') {
	//		alert("{% trans "Please select department" %}")
	//		return false;
	//	}
	//} else {
	//	alert("{% trans "Please select department" %}")
	//	return false;
	//}
	//var ischecked = 0;
	//if ($("#id_cascadecheck_" + page).prop("checked")) ischecked = 1;
	//urlStr = "deptIDs=" + dept_ids + "&isContainChild=" + ischecked
	var emp = getSelected_emp_ex("sel_employee_search_Supplement");
	if (emp.length > 0) {
		urlStr = "&UserIDs=" + emp
	}
	else{
		alert("{% trans "Please select a person" %}")
	}

        var isError=validate_form_ipos();
	if(isError){
 
		var st=$("#"+g_activeTabID+" #id_StartDate").val();
		var et=$("#"+g_activeTabID+" #id_EndDate").val();
		var url = "/ipos/report/Supplement/?" + "StartDate=" + st + "&EndDate=" + et + urlStr
		}
	else
	{
	    alert("{% trans 'Please check if the time format is correct and query for up to 100 days!' %}")
	    return;
	}	

        pos_start_date=st
        pos_end_date=et

	savecookie("search_urlstr", url);
	$("#id_grid_report").jqGrid('setGridParam', {
		url: url,
		datatype: 'json'
	}).trigger("reloadGrid");
}


function createQueryDlg(){
	createDlgdeptfor10('employee_search_Supplement',1)
	$('#dlg_for_query_employee_search_Supplement').dialog(
	{
		buttons:
		[
			{id:"btnShowOK",text:"{% trans 'search for' %}",click:function(){searchbydept_Supplement('employee_search_Supplement');$(this).dialog("destroy");}},
			{id:"btnShowCancel",text:"{% trans 'Return' %}" ,click:function(){$(this).dialog("destroy"); }}
		]
	})
}


{% endblock %}
{% block search %}
{% endblock %}

{% block date_range_ext %}
    <label>{% trans 'Product Number' %}</label>
        <input type='text' name=''  id='search_id_code' style='width:100px;'>
    <label>{% trans 'product name' %}</label>
        <input type='text' name=''  id='search_id_name' style='width:100px;'>
{% endblock %}


