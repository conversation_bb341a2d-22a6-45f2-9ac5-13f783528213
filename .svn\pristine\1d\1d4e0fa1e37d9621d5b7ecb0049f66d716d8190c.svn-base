{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <style type="text/css">
    </style>
{% endblock %}
{% block content %}
<header id="header" class="mui-bar mui-bar-nav">
    <a
        id="back_a"
        href="/iapp/main/"
        class="mui-icon mui-icon-left-nav mui-pull-left"
    ></a>
    <h1 class="mui-title" style="font-size: 16px">
        {% trans 'My attendance' %}
    </h1>
</header>
<div class="mui-content mui-scroll-wrapper">
    <div class="mui-scroll">
        <ul class="mui-table-view">
            <li class="mui-table-view-cell mui-media">
                    <a  href="/iapp/att/checktime/">
                        <svg class="mui-media-object mui-pull-left icon" aria-hidden="true">
                        <use xlink:href="#icon-meiriqiandao"></use>
                    </svg>
                    <div class="mui-media-body">
                        <p class="mui-ellipsis">{% trans 'My check-in' %}</p>
                    </div>
                </a>
            </li>
            <li class="mui-table-view-cell mui-media">
                <a href="/iapp/att/schedule/">
                    <svg
                        class="mui-media-object mui-pull-left icon"
                        aria-hidden="true"
                    >
                        <use xlink:href="#icon-wodepaiban"></use>
                    </svg>
                    <div class="mui-media-body">
                        <p class="mui-ellipsis">{% trans 'My schedule' %}</p>
                    </div>
                </a>
            </li>
            <li class="mui-table-view-cell mui-media">
                <a href="/iapp/att/abnormite/">
                        <svg class="mui-media-object mui-pull-left icon" aria-hidden="true">
                        <use xlink:href="#icon-yichang"></use>
                    </svg>
                    <div class="mui-media-body">
                        <p class="mui-ellipsis">{% trans 'My exception' %}</p>
                    </div>
                </a>
            </li>
        </ul>
    </div>
</div>
{% endblock %}
{% block extrjs %}
<script type="text/javascript">
    var single_page = sessionStorage.getItem("single_page");
    var user_ip = '';
    mui.init();
    //图片轮播
    var slider = mui("#slider");
    slider.slider({
        interval: 5000
    });
    window.onload = function () {
        if (single_page == '1'){
          document.getElementById('back_a').style.display="none";
        }
        mui('.mui-scroll-wrapper').scroll({
            deceleration: 0.0005 //flick 减速系数，系数越大，滚动速度越慢，滚动距离越小，默认值0.0006
        });
    }
    //解决a标签无法跳转问题
    mui('body').on('tap', 'a', function () {
        window.top.location.href = this.href;
    });

</script>
{% endblock %}