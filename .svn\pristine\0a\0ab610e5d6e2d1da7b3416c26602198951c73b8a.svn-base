#!/usr/bin/python
# -*- coding: utf-8 -*-

from rest_framework import serializers
from mysite.accounts.models import MyUser
from mysite.meeting.models import *


class AllEmployeeInfoSerializer(serializers.HyperlinkedModelSerializer):

    class Meta:
        model = Meet
        fields = ('code','itemname')


class OrderMeetingInfoSerializer(serializers.HyperlinkedModelSerializer):
    # conferees = serializers.CharField(source='get_conferee')
    roomName = serializers.CharField(source='LocationID.roomName')
    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(OrderMeetingInfoSerializer, self).to_representation(instance)
        details = MeetOrderDetails.objects.filter(MeetID=ret['id'])

        return ret

    class Meta:
        model = Meet_order
        fields = ('conferenceTitle','MeetContents', 'Starttime', 'Endtime', 'State',  'code', 'conferees')



class MeetingRoomInfoSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Meet
        fields = ('code','itemname')

