html, body {
    width: 100%;
    height: 100%;
    min-width: 1600px;
    min-height: 880px;
    position: relative;
}


.data_visualization_box {
    width: 100%;
    height: 100%;
    background-image: url(../img/large_screen/1066x600/img_bigbg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;

}

.center_bg {
    width: 63.5%;
    height: 84.5%;
    background-image: url(../img/large_screen/1066x600/img_date_bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 17.7%;
    bottom: 6.45%;
    z-index: 100;
}

.data_visualization_top {
    width: 100%;
    height: 8.3%;
    background-image: url(../img/large_screen/1066x600/img_topbg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.visualization_top_datetime {
    height: 7%;
    /*flex 布局*/
    display: flex;
    /*实现垂直居中*/
    align-items: center;
    /*实现水平居中*/
    justify-content: center;
    font-size: 2.22vh;
    font-family:"Source Han Sans CN Regular", "PingFang SC";
    font-weight: 500;
    color: #D1F2FF;
    position: absolute;
    top: 1.3%;
    left: 0.65%;
}

.visualization_top_title {
    width: 25%;
    height: 7%;
    /*flex 布局*/
    display: flex;
    /*实现垂直居中*/
    align-items: center;
    /*实现水平居中*/
    justify-content: center;
    font-size: 3.42vh;
    font-family: Source Han Sans CN Regular;
    font-weight: 400;
    color: #FFFFFF;
    text-shadow: 0px 0.46vh 0.46vh rgba(0, 25, 110, 0.75);

    background: linear-gradient(0deg, #0CA8F9 0%, #43C17B 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: absolute;
    top: 0px;
    left: 37.5%;
}

.visualization_max {
    width: 3.61vh;
    cursor: pointer;
    height: 3.61vh;
    background-image: url(../img/large_screen/1066x600/ic_max.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    float: right;
}

.visualization_app {
    width: 3.61vh;
    height: 3.61vh;
    cursor: pointer;
    background-image: url(../img/large_screen/1066x600/ic_app.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    float: right;
    margin-right: 2.77vh;
}

.visualization_top_button {
    height: 7%;
    position: absolute;
    top: 1%;
    right: 1%;
    /*flex 布局*/
    display: flex;
    /*实现垂直居中*/
    align-items: center;
    /*实现水平居中*/
    justify-content: center;
}

.data_visualization_bottom {
    width: 100%;
    height: 91.7%;
    position: absolute;
    z-index: 200;
}

.visualization_col_1 {
    height: 100%;
    width: 0.7%;
    float: left;
    display: flex;
}

.visualization_col_2 {
    height: 100%;
    width: 28%;
    float: left;
}

.visualization_col_3 {
    height: 100%;
    width: 0.6%;
    float: left;
}

.visualization_col_4 {
    height: 100%;
    width: 41.3%;
    float: left;
}

.visualization_col_5 {
    height: 100%;
    width: 0.6%;
    float: left;
}

.visualization_col_6 {
    height: 100%;
    width: 28%;
    float: left;
}

.visualization_col_7 {
    height: 100%;
    width: 0.8%;
    float: left;
}

.visualization_col_2_box_1 {
    width: 100%;
    height: 30%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.visualization_col_2_box_2 {
    width: 100%;
    height: 28%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.visualization_col_2_box_3 {
    width: 100%;
    height: 38%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.visualization_col_6_box_1 {
    width: 100%;
    height: 30%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.visualization_col_6_box_2 {
    width: 100%;
    height: 28%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.visualization_col_6_box_3 {
    width: 100%;
    height: 23%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.col_6_box_3_chart {
    width: 100%;
    height: 100%;
    display: flex;
    flex-flow: column wrap;
}

.access_chart_head {
    width: 100%;
    height: 1.85vh;
    display: flex;
}

.access_chart_th {
    height: 0.92vh;
    font-size: 0.92vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;

}

#access_now {
    width: 100%;
    flex: 1;
    overflow: hidden;
}

.access_chart_tr {
    height: 100%;
    overflow: hidden;
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
    float: left;
    display: flex;
    align-items: center;
}

.access_chart_div_2 {
    background: rgba(114, 163, 192, 0.2);
    height: 4vh;
    display: block;
    list-style: none;
}

.access_chart_div_1 {
    height: 4vh;
    display: block;
    list-style: none;
}


.visualization_col_6_box_4 {
    width: 100%;
    height: 16%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-flow: column wrap;
}

.visualization_col_4_box_1 {
    width: 100%;
    height: 67.5%;
    display: flex;
    flex-flow: column wrap;
}


.visualization_col_4_box_2 {
    width: 100%;
    height: 31%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-flow: column wrap;
}

.title_type_2_msg {
    height: 3.05vh;
    margin-left: 2.7%;
    position: relative;
}

.title_type_2_logo {
    width: 3.05vh;
    height: 3.05vh;
    position: absolute;
}

.title_type_2_logo_ic_top5 {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_top5.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_2_word {
    height: 1.11vh;
    font-size: 1.2vh;
    font-family:"Source Han Sans CN Regular", "Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
    position: absolute;
    left: 3.05vh;
    top: 0.37vh;
}

.title_type_2_line {
    width: 100%;
    height: 1.11vh;
    background-image: url(../img/large_screen/1066x600/img_littletip_bg.png);
    background-repeat: no-repeat;
    background-size: 95% 100%;
    background-position: center;
    position: absolute;
    bottom: 0.37vh;
}


.title_type_1_msg {
    height: 3.33vh;
    margin-left: 2.7%;
    position: relative;
}

.title_type_1_logo {
    width: 3.05vh;
    height: 3.05vh;
    position: absolute;
}

.title_type_1_logo_ic_date {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_date.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_device {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_device.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_visit {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_visit.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_att {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_att.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_tongxing {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_tongxing.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_zichan {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_zichan.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_acc {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_acc.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_logo_ic_huiyi {
    width: 3.05vh;
    height: 3.05vh;
    background-image: url(../img/large_screen/1066x600/ic_huiyi.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.title_type_1_word {
    height: 1.11vh;
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
    position: absolute;
    left: 3.05vh;
    top: 0.74vh;
}

.title_type_1_light {
    width: 82%;
    height: 1.66vh;
    background-image: linear-gradient(to right, rgba(191, 227, 253, 0.1), rgba(0, 0, 0, 0.2));
    position: absolute;
    left: 1.57vh;
    top: 0.74vh;
}

.title_type_1_interval {
    margin-left: 2.7%;
    width: 92.3%;
    height: 2.03vh;
}

.title_type_1_line {
    width: 82%;
    height: 0.18vh;
    margin-top: 0.92vh;
    margin-left: 1.66vh;
    background: rgba(191, 227, 253, 0.1);
    float: left;
}

.title_type_1_circle {
    height: 2.03vh;
    float: right;
    display: flex;
    justify-content: center;
    align-items: center;
}

.title_type_1_circle_1 {
    width: 0.18vh;
    height: 0.18vh;
    background: #67AEF8;
    border-radius: 50%;
    margin-right: 0.37vh;
}

.title_type_1_circle_2 {
    width: 0.37vh;
    height: 0.37vh;
    background: radial-gradient(circle, #3F78B5, #78B9FC);
    border-radius: 50%;
    margin-right: 0.37vh;
}

.title_type_1_circle_3 {
    width: 0.55vh;
    height: 0.55vh;
    background: radial-gradient(circle, #184236, #43C17B);
    border-radius: 50%;
}

.chart_box {
    flex: 1;
    position: relative;
}

.col_4_box_1_chart {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
}

.col_4_box_1_chart_box {
    width: 35.18vh;
    height: 100%;
    position: relative;
}

#col_4_box_1_chart_blue {
    width: 250px;
    height: 104px;
    position: absolute;
    bottom: 50px;
    left: 31px;
    z-index: 100;
}

#blue_top {
    position: absolute;
    z-index: 700;
    top: 20px;
    width: 250px;
    height: 84px;
    background-image: url(../img/large_screen/1066x600/blue_top.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#blue_middle {
    position: absolute;
    z-index: 600;
    bottom: 42px;
    width: 250px;
    background-image: url(../img/large_screen/1066x600/blue_middle.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#blue_down {
    position: absolute;
    z-index: 500;
    bottom: 0px;
    width: 250px;
    height: 42px;
    background-image: url(../img/large_screen/1066x600/blue_down.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#col_4_box_1_chart_green {
    width: 182px;
    height: 82px;
    position: absolute;
    bottom: 32px;
    left: 201px;
    z-index: 200;
}

#green_top {
    position: absolute;
    z-index: 700;
    top: 20px;
    width: 182px;
    height: 61px;
    background-image: url(../img/large_screen/1066x600/green_top.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#green_middle {
    position: absolute;
    z-index: 600;
    bottom: 32px;
    width: 182px;
    background-image: url(../img/large_screen/1066x600/green_middle.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#green_down {
    position: absolute;
    z-index: 500;
    bottom: 0px;
    width: 182px;
    height: 32px;
    background-image: url(../img/large_screen/1066x600/green_down.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#col_4_box_1_chart_purple {
    width: 107px;
    height: 52px;
    position: absolute;
    bottom: 45px;
    left: 0px;
    z-index: 300;
}

#purple_top {
    position: absolute;
    z-index: 700;
    top: 20px;
    width: 107px;
    height: 37px;
    background-image: url(../img/large_screen/1066x600/purple_top.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#purple_middle {
    position: absolute;
    z-index: 600;
    bottom: 18px;
    width: 107px;
    background-image: url(../img/large_screen/1066x600/purple_middle.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#purple_down {
    position: absolute;
    z-index: 500;
    bottom: 0px;
    width: 107px;
    height: 18px;
    background-image: url(../img/large_screen/1066x600/purple_down.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}


#col_4_box_1_chart_yellow {
    width: 133px;
    height: 57px;
    position: absolute;
    bottom: 15px;
    left: 127px;
    z-index: 400;
}


#yellow_top {
    position: absolute;
    z-index: 700;
    top: 20px;
    width: 133px;
    height: 45px;
    background-image: url(../img/large_screen/1066x600/yellow_top.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#yellow_middle {
    position: absolute;
    z-index: 600;
    bottom: 23px;
    width: 133px;
    background-image: url(../img/large_screen/1066x600/yellow_middle.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

#yellow_down {
    position: absolute;
    z-index: 500;
    bottom: 0px;
    width: 133px;
    height: 23px;
    background-image: url(../img/large_screen/1066x600/yellow_down.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}


.color_title_box {
    position: absolute;
    top: 0px;
    z-index: 800;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    min-width: 9.25vh;
}


.pos_tag {
    font-size: 1.2vh;
    font-family:"Source Han Sans CN Regular","Adobe Heiti Std";
    font-weight: normal;
    color: #FFFFFF;
}

.pos_number {
    font-size: 2.77vh;
    font-family: "Source Han Sans CN Regular","Adobe Heiti Std";
    font-weight: normal;
    color: #FFFFFF;
}


.box_content_2 {
    position: absolute;
    margin-left: 5%;
    width: 90%;
    height: 100%;
    display: flex;
    align-items: center;
}


.box_content_3 {
    position: absolute;
    width: 90%;
    height: 100%;
    display: flex;
    align-items: center;
}

.col_6_box_4_chart {
    width: 100%;
    height: 100%;
}

.col_4_box_2_chart {
    width: 100%;
    height: 100%;
}

.box_msg_group {
    height: 18.79vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.col_2_box_3_chart {
    width: 100%;
    height: 100%;
}

.col_6_box_1_chart {
    width: 100%;
    height: 100%;
}

.col_6_box_2_chart {
    flex: 1;
    height: 100%;
}

.img_leftframe {
    width: 2.31vh;
    height: 18.79vh;
    background-image: url(../img/large_screen/1066x600/img_leftframe.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}

.img_rightframe {
    width: 2.31vh;
    height: 18.79vh;
    background-image: url(../img/large_screen/1066x600/img_rightframe.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
}


.box_msg {
    margin-left: 0.83vh;
    height: 6.48vh;
}

.col_2_box_2_chart {
    height: 100%;
    flex: 1;
}


.msg_sign {
    height: 0.55vh;
    width: 6.85vh;
}

.sign_left {
    width: 1.85vh;
    height: 0.55vh;
    float: left;
}

.sign_interval {
    width: 0.37vh;
    height: 0.27vh;
    float: left;
}

.sign_right {
    width: 4.62vh;
    height: 0.55vh;
    background: rgba(81, 135, 174, 0.33);
    float: left;
}

.msg_tag {
    margin-top: 1.2vh;
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
    margin-bottom: 0.92vh;
}

.msg_number {
    font-size: 2.22vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #FFFFFF;
    float: left;
}

.msg_unit {
    margin-top: 1.11vh;
    height: 1.11vh;
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #FFFFFF;
    float: left;
}

.col_2_box_1_row_1 {
    height: 8.61vh;
    width: 92%;
    margin-left: 4%;

}

.row_1_total {
    width: 50%;
    height: 100%;
    background-image: url(../img/large_screen/1066x600/img_number_bg.png);
    background-repeat: no-repeat;
    background-size: 11.94vh 5.83vh;
    background-position: center bottom;
    float: left;
}

.row_1_total_tag {
    text-align: center;
    width: 100%;
    height: 1.85vh;
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #93D6EA;
}

.row_1_total_number {
    text-align: center;
    width: 100%;
    font-size: 2.77vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: bold;
    color: #FFFFFF;
}

.box_content_1 {
    width: 90%;
    margin-left: 5%;
    height: 100%;
}

.template_row {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.template_row_msg {
    width: 100%;
    height: 4.62vh;
}

.template_box_1 {
    height: 100%;
    width: 33%;
    float: left;
    display: flex;
}


.template_box_2 {
    height: 100%;
    width: 33%;
    float: left;
    display: flex;
    justify-content: center;
}

.template_box_3 {
    height: 100%;
    width: 33%;
    float: left;
    display: flex;
    justify-content: flex-end;
}

.template_left {
    float: left;
    width: 4.62vh;
    height: 100%;
    background-image: url(../img/large_screen/1066x600/img_icbg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.template_img {
    width: 3.05vh;
    height: 2.68vh;

}

.template_right {
    float: left;
    height: 100%;
    overflow: hidden;
}

.template_right_msg {
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.template_type {
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #93D6EA;
    margin-right: 0.27vh;
}

.template_number {
    font-size: 1.48vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #FFFFFF;
}

.attendance_status_logo {
    width: 5.83vh;
    height: 5.83vh;
    float: left;
}

.attendance_status_msg {
    height: 5.83vh;
    float: left;
}

.attendance_status_tag {
    margin-top: 1.11vh;
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
    line-height: 1.85vh;
}

.attendance_status_number {
    margin-top: 0.46vh;
    font-size: 2.22vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1.66vh;
}

.attendance_status_box {
    height: 5.09vh;
    width: 90%;
    margin-left: 3%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attendance_rate_tag {
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
}

.attendance_rate {
    font-size: 1.66vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #FFFFFF;
    margin-left: 1vh;
}

.attendance_rate_box {
    height: 3.05vh;
    width: 96%;
    margin-left: 3%;
}

.attendance_rate_msg {
    width: 25%;
    height: 3.05vh;
    margin-left: 0.64vh;
    display: flex;
    align-items: flex-end;
    float: left;
}

.visitor_order_status_box {
    margin-top: 1.38vh;
    width: 90%;
    margin-left: 2.7%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.visitor_order_status_left {
    float: left;
    height: 1.38vh;
    width: 3.05vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.visitor_order_status_right {
    float: left;
    height: 100%;
}

.order_status_logo {
    width: 0.55vh;
    height: 0.55vh;
    background: #12CDD8;
    box-shadow: 0px 0px 0.27vh 0px rgba(190, 250, 254, 0.72);
    border-radius: 50%;
}

.order_status_tag {
    font-size: 1.2vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: 400;
    color: #93D6EA;
}

.order_status_number {
    height: 5.18vh;
    font-size: 2.22vh;
    font-family: "Source Han Sans CN Regular","PingFang SC";
    font-weight: 400;
    color: #FFFFFF;
    display: table-cell;
    vertical-align: middle
}

.ipos_chart_title_left_img {
    width: 15.74vh;
    height: 0.64vh;
    background-image: url(../img/large_screen/1066x600/ipos_title_left_frame.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.ipos_chart_title_right_img {
    width: 15.74vh;
    height: 0.64vh;
    background-image: url(../img/large_screen/1066x600/ipos_title_right_frame.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.ipos_chart_title_tag {
    margin-left: 0.18vh;
    margin-right: 0.18vh;
    font-size: 1.48vh;
    font-family: "Source Han Sans CN Regular","Microsoft YaHei";
    font-weight: bold;
    color: #93D6EA;
}

.ipos_chart_title {
    margin-top: 2.77vh;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}


