{% extends "admin/base_site.html" %}
{% load i18n %}

{% block breadcrumbs %}
<div class="breadcrumbs">
     <a href="../../../../">{% trans "Home" %}</a> &rsaquo;
     <a href="../../../">{{ app_label|capfirst }}</a> &rsaquo; 
     <a href="../../">{{ opts.verbose_name_plural|capfirst }}</a> &rsaquo;
     <a href="../">{{ object|truncatewords:"18" }}</a> &rsaquo;
     {% trans 'Delete' %}
</div>
{% endblock %}

{% block content %}
{% if perms_lacking %}
    <p>{% blocktrans with object as escaped_object %}Deleting the {{ object_name }} &#39;{{ escaped_object }}&#39; would result in deleting related objects, but your account doesn&#39;t have permission to delete the following types of objects:{% endblocktrans %}</p>
    <ul>
    {% for obj in perms_lacking %}
        <li>{{ obj }}</li>
    {% endfor %}
    </ul>
{% else %}
    <p>{% blocktrans with object as escaped_object %}Are you sure you want to delete the {{ object_name }} [{{ escaped_object }}]? All of the following related items will be deleted:{% endblocktrans %}</p>
    <ul>{{ deleted_objects|unordered_list }}</ul>
    <form action="" method="post">
    <div>
    <input type="hidden" name="post" value="yes" />
    <input type="submit" value="{% trans "Yes, I&#39;m sure" %}" />
    </div>
    </form>
{% endif %}
{% endblock %}
