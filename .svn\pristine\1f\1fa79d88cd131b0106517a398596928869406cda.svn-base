{% load i18n %}
{% load iclock_tags %}

<div id='id_option1' style='padding: 20px;'>
    <form id='id_options_mobile_setting' method='post'>
        <div style='width:700px;height: 28px;margin-left: 50px;'><h1><a style='font-weight: bold;font-size: 20px'>{% trans 'Platform selection (requires purchasing domain name and SSL certificate)' %}</a></h1></div>
            <table style="margin-left: 150px;margin-top: 30px;">
                <tr>
                    <td id="wx_mobile_td" rowspan="2" style="width:120px;"><img id="wx_mobile_img" src="../../media/img/ic_wechat_selected.png"><a id="link_Wx" href="#" style='font-weight: bold;'>{% trans 'WeChat' %}</a></td>
{#                    <td style="width:40px;"></td>#}
{#                    <td id="ali_mobile_td" rowspan="2" style="width:150px;"><img id="ali_mobile_img" src="../../media/img/ic_pay.png"><a id="link_Ali" href="#" style='font-weight: bold;'>{% trans 'Alipay' %}</a></td>#}
                </tr>
            </table>
        <table>
        <tr id="id_wx_payment">
        <td>
            {% for k,v in param.items %}
                <table class='ui-widget-header' style="margin-left: 50px;margin-top: 30px;">
                    {% if k == 'mt_wechat_media_platform' %}
                        <tr>
                            <td style="width:100px;"><h1><a style='font-weight: bold;font-size: 10px'>{% trans 'Official account parameters' %}</a></h1></td>
                            <td style="width:200px;"><input type="checkbox" name="mobile_wx_subscribe1"  value='1' id="id_mobile_wx_subscribe1" />{% trans "official account" %}1</td>
                            <td style="width:100px;"></td>
                            <td style="width:200px;"><input type="checkbox" name="mobile_wx_subscribe2"  value='1' id="id_mobile_wx_subscribe2" />{% trans "official account" %}2</td>
                        </tr>
                        {% for media_idname_k,media_idname_v in media_platform_idname.items %}
                            <tr>
                                <td style="width:100px;"></td>
                                <td style="width:200px;"><a style='font-weight: bold;'>{{ media_idname_k }}</a></td>
                                <td style="width:100px;"></td>
                                <td style="width:200px;"><a style='font-weight: bold;'>{{ media_idname_k }}</a></td>
                                <td rowspan="5" style="width:200px;"></td>
                            </tr>
                            <tr>
                                <td style="width:100px;"></td>
                            {% if media_idname_v != 'app_secret' %}
                                <td style="width:200px;"><input type="text" name='mobile_wx_subscribe1_{{ media_idname_v }}' id='id_mobile_wx_subscribe1_{{ media_idname_v }}' style='width:200px !important;' value="" /></td>
                            {% else %}
                                <td style="width:200px;"><input type="password" name='mobile_wx_subscribe1_{{ media_idname_v }}' id='id_mobile_wx_subscribe1_{{ media_idname_v }}' style='width:200px !important;' value="" /></td>
                            {% endif %}
                                <td style="width:100px;"></td>
                            {% if media_idname_v != 'app_secret' %}
                                <td style="width:200px;"><input type="text" name='mobile_wx_subscribe2_{{ media_idname_v }}' id='id_mobile_wx_subscribe2_{{ media_idname_v }}' style='width:200px !important;' value="" /></td>
                            {% else %}
                                <td style="width:200px;"><input type="password" name='mobile_wx_subscribe2_{{ media_idname_v }}' id='id_mobile_wx_subscribe2_{{ media_idname_v }}' style='width:200px !important;' value="" /></td>
                            {% endif %}
                            </tr>
                        {% endfor %}
                    {% elif k == 'mt_wechat_mini_program' %}
                        <tr>
                            <td style="width:100px;"><h1><a style='font-weight: bold;font-size: 10px'>{% trans 'Applet parameter' %}</a></h1></td>
                            <td style="width:200px;"><input type="checkbox" name="mobile_wx_m_program1"  value='1' id="id_mobile_wx_m_program1" />{% trans "applet" %}1</td>
                            <td style="width:100px;"></td>
                            <td style="width:200px;"><input type="checkbox" name="mobile_wx_m_program2"  value='1' id="id_mobile_wx_m_program2" />{% trans "applet" %}2</td>
                        </tr>
                        {% for mini_idname_k,mini_idname_v in mini_program_idname.items %}
                            <tr>
                                <td style="width:100px;"></td>
                                <td style="width:200px;"><a style='font-weight: bold;'>{{ mini_idname_k }}</a></td>
                                <td style="width:100px;"></td>
                                <td style="width:200px;"><a style='font-weight: bold;'>{{ mini_idname_k }}</a></td>
                                <td rowspan="5" style="width:200px;"></td>
                            </tr>
                            <tr>
                                <td style="width:100px;"></td>
                                {% if mini_idname_v != 'key' %}
                                    <td style="width:200px;"><input type="text" name='mobile_wx_m_program1_{{ mini_idname_v }}' id='id_mobile_wx_m_program1_{{ mini_idname_v }}' style='width:200px !important;' value="" /></td>
                                {% else %}
                                    <td style="width:200px;"><input type="password" name='mobile_wx_m_program1_{{ mini_idname_v }}' id='id_mobile_wx_m_program1_{{ mini_idname_v }}' style='width:200px !important;' value="" /></td>
                                {% endif %}
                                <td style="width:100px;"></td>
                                {% if mini_idname_v != 'key' %}
                                    <td style="width:200px;"><input type="text" name='mobile_wx_m_program2_{{ mini_idname_v }}' id='id_mobile_wx_m_program2_{{ mini_idname_v }}' style='width:200px !important;' value="" /></td>
                                {% else %}
                                    <td style="width:200px;"><input type="password" name='mobile_wx_m_program2_{{ mini_idname_v }}' id='id_mobile_wx_m_program2_{{ mini_idname_v }}' style='width:200px !important;' value="" /></td>
                                {% endif %}
                            </tr>
                        {% endfor %}
{#                    {% else %}#}
{#                        <tr>#}
{#                            <td style="width:100px;"><h1><a style='font-weight: bold;font-size: 10px'>{% trans '企业微信参数' %}</a></h1></td>#}
{#                            <td style="width:200px;"><input type="checkbox" name="mobile_wx_enterprise_wx"  value='1' id="id_mobile_wx_enterprise_wx" />{% trans "企业微信1" %}</td>#}
{#                        </tr>#}
{#                        {% for enterprise_idname_k,enterprise_idname_v in enterprise_wx_idname.items %}#}
{#                            <tr>#}
{#                                <td style="width:100px;"></td>#}
{#                                <td style="width:200px;"><a style='font-weight: bold;'>{{ enterprise_idname_k }}</a></td>#}
{#                                <td rowspan="5" style="width:304px;"></td>#}
{#                            </tr>#}
{#                            <tr>#}
{#                                <td style="width:100px;"></td>#}
{#                                {% if enterprise_idname_v != 'app_secret' %}#}
{#                                    <td style="width:200px;"><input type="text" name='mobile_wx_enterprise_wx_{{ enterprise_idname_v }}' id='id_mobile_wx_enterprise_wx_{{ enterprise_idname_v }}' style='width:200px !important;' value="" /></td>#}
{#                                {% else %}#}
{#                                    <td style="width:200px;"><input type="password" name='mobile_wx_enterprise_wx_{{ enterprise_idname_v }}' id='id_mobile_wx_enterprise_wx_{{ enterprise_idname_v }}' style='width:200px !important;' value="" /></td>#}
{#                                {% endif %}#}
{##}
{#                            </tr>#}
{#                        {% endfor %}#}
                    {% endif %}
                </table>
            {% endfor %}
        </td></tr>
{#        <tr id="id_ali_payment">#}
{#            <td>#}
{#                <table>#}
{#                    支付宝预留#}
{#                </table>#}
{#            </td>#}
{#        </tr>#}
        </table>
    </form>
    <div><ul class='errorlist'><li id='option_mobile_setting_error' style='display:none;'></li></ul></div>
    <div style="margin-left: 50px;margin-top: 30px;"><input id='id_mobile_setting_save' type='button'  class='m-btn  zkgreen rnd' value='{% trans "Save" %}'/></div>
</div>


<script>
{% autoescape off %}
    var wallets='1';
    var param={{ param }};
    var media_platform_idname={{ media_platform_idname }};
    var mini_program_idname={{ mini_program_idname }};
    var enterprise_wx_idname={{ enterprise_wx_idname }};
{% endautoescape %}
    $(function(){
        change_pay_type();
        function change_pay_type() {
            if (wallets === '1') {
                document.getElementById('wx_mobile_img').src = '../../media/img/ic_wechat_selected.png';
                {#document.getElementById('ali_mobile_img').src = '../../media/img/ic_pay.png';#}
                $('#id_wx_payment').show();
                $('#id_ali_payment').hide()
            } else {
                document.getElementById('wx_mobile_img').src = '../../media/img/ic_wechat.png';
                {#document.getElementById('ali_mobile_img').src = '../../media/img/ic_pay_selected.png'#}
                $('#id_ali_payment').show()
                $('#id_wx_payment').hide()
            }
        }
        $("#wx_mobile_td").click(function () {
            wallets = '1';
            change_pay_type()
        });
        $("#ali_mobile_td").click(function () {
            wallets = '2';
            change_pay_type()
        });

        if(param.mt_wechat_media_platform.mp_1.enable==='1'){
            $("#"+g_activeTabID+" #id_mobile_wx_subscribe1").prop("checked","checked");
            $("#"+g_activeTabID+" #id_mobile_wx_subscribe1_server_url").val(param.mt_wechat_media_platform.mp_1.mp_server_url);
            $("#"+g_activeTabID+" #id_mobile_wx_subscribe1_id").val(param.mt_wechat_media_platform.mp_1.mp_appid);
            $("#"+g_activeTabID+" #id_mobile_wx_subscribe1_app_secret").val(param.mt_wechat_media_platform.mp_1.mp_app_secret);
            $("#"+g_activeTabID+" #id_mobile_wx_subscribe1_template_id1").val(param.mt_wechat_media_platform.mp_1.template_id1);
            $("#"+g_activeTabID+" #id_mobile_wx_subscribe1_template_id2").val(param.mt_wechat_media_platform.mp_1.template_id2);
        }
        if(param.mt_wechat_media_platform.mp_2.enable==='1') {
            $("#" + g_activeTabID + " #id_mobile_wx_subscribe2").prop("checked", "checked");
            $("#" + g_activeTabID + " #id_mobile_wx_subscribe2_server_url").val(param.mt_wechat_media_platform.mp_2.mp_server_url);
            $("#" + g_activeTabID + " #id_mobile_wx_subscribe2_id").val(param.mt_wechat_media_platform.mp_2.mp_appid);
            $("#" + g_activeTabID + " #id_mobile_wx_subscribe2_app_secret").val(param.mt_wechat_media_platform.mp_2.mp_app_secret);
            $("#" + g_activeTabID + " #id_mobile_wx_subscribe2_template_id1").val(param.mt_wechat_media_platform.mp_2.template_id1);
            $("#" + g_activeTabID + " #id_mobile_wx_subscribe2_template_id2").val(param.mt_wechat_media_platform.mp_2.template_id2);
        }
        if(param.mt_wechat_mini_program.mp_1.enable==='1') {
            $("#" + g_activeTabID + " #id_mobile_wx_m_program1").prop("checked", "checked");
            $("#" + g_activeTabID + " #id_mobile_wx_m_program1_server_url").val(param.mt_wechat_mini_program.mp_1.mp_server_url);
            $("#" + g_activeTabID + " #id_mobile_wx_m_program1_app_id").val(param.mt_wechat_mini_program.mp_1.mp_appid);
            $("#" + g_activeTabID + " #id_mobile_wx_m_program1_key").val(param.mt_wechat_mini_program.mp_1.mp_app_secret);
        }

        if(param.mt_wechat_mini_program.mp_2.enable==='1') {
            $("#" + g_activeTabID + " #id_mobile_wx_m_program2").prop("checked", "checked");
            $("#" + g_activeTabID + " #id_mobile_wx_m_program2_server_url").val(param.mt_wechat_mini_program.mp_2.mp_server_url);
            $("#" + g_activeTabID + " #id_mobile_wx_m_program2_app_id").val(param.mt_wechat_mini_program.mp_2.mp_appid);
            $("#" + g_activeTabID + " #id_mobile_wx_m_program2_key").val(param.mt_wechat_mini_program.mp_2.mp_app_secret);
        }
        if(param.mt_wechat_enterprise_wx.ente.enable==='1') {
            $("#" + g_activeTabID + " #id_mobile_wx_enterprise_wx").prop("checked", "checked");
            $("#" + g_activeTabID + " #id_mobile_wx_enterprise_wx_server_url").val(param.mt_wechat_enterprise_wx.ente.et_server_url);
            $("#" + g_activeTabID + " #id_mobile_wx_enterprise_wx_id").val(param.mt_wechat_enterprise_wx.ente.et_enterprise_id);
            $("#" + g_activeTabID + " #id_mobile_wx_enterprise_wx_app_secret").val(param.mt_wechat_enterprise_wx.ente.et_enterprise_app_secret);
        }


        $("#"+g_activeTabID+" #id_mobile_setting_save").click(function(){
            var queryStr=$("#"+g_activeTabID+" #id_options_mobile_setting").formSerialize();
            $.blockUI({title:'',theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Preserving...' %}</br></h1>'});
            $.ajax({
                type: "POST",
                url:"/base/isys/option_mt_param_setting/",
                data:queryStr,
                dataType:"json",
                success:function(retdata){
                    $.unblockUI();
                    var message=retdata.message
                    $("#option_mobile_setting_error").css("display","block");
                    $("#option_mobile_setting_error").html(message);
                }
            });
        });

    });


</script>