# -*- coding: utf-8 -*-
from django.utils.translation import gettext_lazy as _
import traceback
import string
import datetime
import os
import subprocess
import shutil
from django.core.cache import cache
from django.http import HttpResponse
from django.conf import settings
from django.utils.encoding import smart_str
from django.db import models, connection,connections
from django.db import OperationalError
from django.utils._os import safe_join
import base64
import six
#from mysite.core.zktools import *
#from django.utils import simplejson as json
#from iclock.iutils import *
#from django.utils import simplejson
import json
import re
import sys
from functools import wraps
from django.core.mail import send_mail
import zipfile
from django.utils.functional import Promise
from django.utils.encoding import force_str as force_text
from django.core.serializers.json import DjangoJSONEncoder


def login_verify_ex(request):
    """
    对登录过程进行附加验证，比如对接AD域，若AD域验证不通过，进行错误提醒
    返回为字典格式
    """
    # return {'ret':-1,'message':'AD域登录失败！'}
    from mysite.base.models import GetParamValue

    if settings.MULTI_TENANT:
        from mysite.base.models import Tenant, AuthDevice
        obj = Tenant.objects.first()
        # 检查用户是否被限制登录
        if obj.deltag:
            return {'ret': -1,
                    'message': _(u"The current account has been banned from logging in! Please contact the supplier")}
        # 检查授权是否过期
        if obj.paid_until < datetime.date.today():
            return {'ret': -1, 'message': _(u"The current enterprise authorization has expired")}
    # 验证码校验
    is_wap = request.POST.get('is_wap', '0') # wap及公众号不校验验证码
    if GetParamValue("opt_basic_login_verify", '0') == '1' and is_wap == '0':
        # 指纹登录，不进行验证码校验
        if request.POST.get('verifytype', '0') == '1':  #验证类型 0 密码，1，指纹
            return None
        uuid = request.POST.get('uuid', '')
        code = request.POST.get('code', '')
        if not code:
            return {'ret': -1, 'message': f"{_('verification code cannot be empty')}"}
        key = f'code:{uuid}'
        cached_code = cache.get(key)
        cache.delete(key)  # 校验时取到验证码后，从缓存删除（一个码只能用一次）
        if cached_code:
            if code != cached_code:
                return {'ret': -1, 'message': f"{_('verification code error')}"}
        else:
            return {'ret': -1, 'message': f"{_('Verification code has expired. Please re-enter.')}"}
    return None

def login_context_ex(request=None):
    """
    登录页渲染内容扩展，可用于新加参数，也可用于替换原有参数
    返回为字典格式
    """
    params_ex = {}
    #登录页提示授权剩余天数提醒（含试用软件）
    from mysite.core.zkmimi import getCloseDate, getISVALIDDONGLE
    retinfo = getISVALIDDONGLE()
    try:
        custom = retinfo[1]['custom']
    except:
        custom = ''
    close_date = getCloseDate()  #授权截止日期/试用截止日期
    if close_date:
        close_date = datetime.datetime.strptime(close_date, "%Y%m%d").date()
        now_date = datetime.datetime.now().date()
        remaining_days = (close_date - now_date).days + 1
        if remaining_days < 0:
            probation_reminder = u"%s"%(_(u'The software has expired'))
        else:
            if custom:  #有客户编码，临时授权（非永久授权）
                probation_reminder = u"%s"%(_(u'The validity of the authorization is %s days')) % (remaining_days)
            else:  #无客户编码，试用软件
                probation_reminder = u"%s"%(_(u'Remaining probation period %s days')) % (remaining_days)
        params_ex = {
            'probation_reminder': probation_reminder
        }
    # 要加其他渲染的数据，扩展params_ex即可
    # params_ex.update({'other_params': 'heheheheh'})
    return params_ex

def safe_unicode(obj, encoding="GB18030"):
    """Return a unicode value from the argument"""
    return smart_str(obj,encoding)
    # if isinstance(obj, unicode):
    #     return obj
    # elif isinstance(obj, str):
    #     if encoding is None:
    #         return u"%s"%(obj)
    #     else:
    #         return u"%s"%(obj, encoding)
    # else:
    #     # it may be an int or a float
    #     return u"%s"%(obj)

def set_cookie(response, key, value, expire=None):
    if expire is None:
        max_age = 365*24*60*60  #one year
    else:
        max_age = expire
        expires = datetime.datetime.strftime(datetime.datetime.utcnow() + datetime.timedelta(seconds=max_age), "%a, %d-%b-%Y %H:%M:%S GMT")
    response.set_cookie(key, value, max_age=max_age, expires=expires,
        domain=settings.SESSION_COOKIE_DOMAIN,
        secure=settings.SESSION_COOKIE_SECURE or None)

def tmpDir():
    ret=os.path.join(addition_file_root(),"tmp")
    try:
        os.makedirs(ret)
    except: pass
    return ret
def logDir():
    ret=os.path.join(addition_file_root(), "logs")
    try:
        if not os.path.isdir(ret):
            os.makedirs(ret)
    except: pass
    return ret

#def tmpDir():
    #ret=settings.LOG_DIR
    #try:
        #os.makedirs(ret)
    #except: pass
    #return ret

def createDir(dirname):
    try:
        if not os.path.isdir(dirname):
            os.makedirs(dirname, mode=0o777)
        return True
    except:
        return False


def outBoxDir():
    ret=os.path.join(addition_file_root(), "outbox")
    return ret
def inBoxDir():
    ret=os.path.join(addition_file_root(), "inbox")
    return ret
def badoutBoxDir():
    ret=os.path.join(addition_file_root(), "badoutbox")
    return ret
def senderoutBoxDir():
    ret=os.path.join(addition_file_root(), "senderoutbox")
    return ret
def smsFile(text, append=True):
    fn="%s/%s.txt"%(outBoxDir(),datetime.datetime.now().strftime("%Y%m%d%H%M%S"))
    f=open(fn, append and "a+" or "w+")
    try:
#		text=u"一二"
        text=text.encode("gb18030")
        f.write(text)
        #file.write(unicode.encode(content, 'utf-8'))
    except Exception as e:
        print ("-------",e)
    #f.write("\n")
    f.close()
    return fn



def CleanDir( Dir ):
    if os.path.isdir( Dir ):
        paths = os.listdir( Dir )
        for path in paths:
            filePath = os.path.join( Dir, path )
            if os.path.isfile( filePath ):
                try:
                    os.remove( filePath )
                except os.error:
                    autoRun.exception( "remove %s error." %filePath )#引入logging
            elif os.path.isdir( filePath ):
                if filePath[-4:].lower() == ".svn".lower():
                    continue
                shutil.rmtree(filePath,True)
    return True





def reportDir():
    ret=os.path.join(addition_file_root(), "reports")
    return ret
def photoDir():
    ret=os.path.join(addition_file_root(), "photo")
    return ret
def photoDirExt(type):
    ret=os.path.join(addition_file_root(), "photo_%s"%(type))
    return ret



def unquote(s):
    """unquote('abc%20def%u4E66') -> 'abc def'."""
    #from django.utils.six.moves.urllib.parse import unquote
    #return unquote(s)
    if sys.version[0] == '3' and type(s) == bytes:
        s = s.decode('GB18030')

    res = s.split('%')
    for i in range(1, len(res)):
        item = res[i]
        try:
            if item[0]=='u':
                if six.PY3:
                    res[i]=chr(int(item[1:5],16))+item[5:]
                else:
                    res[i]=unichr(int(item[1:5],16))+item[5:]
            else:
                if six.PY3:
                    res[i] = chr(int(item[:2],16)) + item[2:]
                else:
                    res[i] = chr(int(item[:2],16)) + item[2:]
        except Exception as e:
            res[i] = '%' + item
        #except UnicodeDecodeError:
            #res[i] = unichr(int(item[:2], 16)) + item[2:]
    return "".join(res)


def saveToFile(fn,text,append=True,ctlsize=False):
    #if ctlsize:
    #	if os.path.getsize(fn)>30*1024:
    #		os.remove(fn)

    f=open(fn, "w+")
    try:
        f.write(text)
    except:
        try:
            f.write(text.encode("utf-8"))
        except: pass
    f.write("\n")
    f.close()
    return fn



def appendFile(s,sn=1):
    f=open("%s/info_%s.txt"%(tmpDir(),sn), "a+")
    try:
        f.write(s)
    except:
        try:
            f.write(s.encode("utf-8"))
        except: pass
    f.write("\n")

def tmpFile(name, text, append=True):
    fn="%s/%s"%(tmpDir(),name)
    f=open(fn, append and "a+" or "w+")
    try:
        f.write(text)
    except:
        try:
            f.write(text.encode("utf-8"))
        except: pass
    f.write("\n")
    f.close()
    return fn
def logFile(text, append=True):
    fn="%s/logs_%s.txt"%(logDir(),datetime.datetime.now().strftime("%Y%m%d"))
    f=open(fn, append and "a+" or "w+")
    try:
        f.write(text)
    except:
        pass
    f.write("\n")
    f.close()
    return fn
def logsFile(fn,text, append=True):
    fn="%s/%s.log"%(logDir(),fn)
    f=open(fn, append and "a+" or "w+")
    try:
        f.write(text)
    except:
        pass
#	f.write("\n")
    f.close()
    return fn


def savePosLogToFile(LogFlag,text,append=True,sn=None,logname=None):#LogFlag=issuecard
    fn=''
    if sys.version[0] == '3' and type(text) ==bytes:
           text = text.decode('GB18030')
    ret=os.path.join(addition_file_root(), "ipos", "%s"%(LogFlag))
    createDir(ret)
    fn="%s/%s.txt"%(ret,datetime.datetime.now().strftime("%Y%m%d"))
    if sn:
        fn="%s/%s_%s.txt"%(ret,sn,datetime.datetime.now().strftime("%Y%m%d"))
    elif logname is not None:
        fn = "%s/%s.txt"%(ret,logname)


    if fn:
        f=open(fn, append and "a+" or "w+")
        try:
            f.write(text+'&opertime='+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        except:
            try:
                f.write(text.encode("utf-8"))
            except: pass
        f.write("\n")
        f.close()
        return fn
def saveScheduleLogToFile(LogFlag,text,user='admin',append=True):#LogFlag=issuecard
    fn=''
    ret=os.path.join(addition_file_root(), "schedule","%s"%(LogFlag))
    createDir(ret)
    fn=u"%s/%s_%s.txt"%(ret,datetime.datetime.now().strftime("%Y%m%d%H%M%S"),user)


    if fn:
        f=open(fn, append and "a+" or "w+")
        try:
            f.write(text)
        except:
            try:
                f.write(text.encode("utf-8"))
            except: pass
        f.write("\n")
        f.close()
        return fn


def saveEmpScheduleLogToFile(LogFlag,text,pin='admin',append=True):#LogFlag=issuecard
    fn=''
    ret=os.path.join(addition_file_root(),"schedule", "%s"%(LogFlag))
    createDir(ret)
    fn="%s/%s.txt"%(ret,pin)


    if fn:
        f=open(fn, append and "a+" or "w+")
        try:
            f.write(text)
        except:
            try:
                f.write(text.encode("utf-8"))
            except: pass
        f.write("\r\n")
        f.close()
        return fn


def errorLog(request=None):
    f=open("%s/error_%s.txt"%(tmpDir(),datetime.datetime.now().strftime("%Y%m%d%H%M%S")), "a+")
    f.write("---%s: "%datetime.datetime.now())
    if request:
        f.write("-- %s%s --\n"%(request.META["REMOTE_ADDR"],request.META["PATH_INFO"]))
#	for v in request.REQUEST: f.write("\t%s=%s\n", v, request.REQUEST[v])
        f.write(request.body)
    f.write("\n")
    traceback.print_exc(file=f)
    f.write("---------------------------------\n")
    try:
        traceback.print_exc()
    except: pass


def saveImage(fn,image):
    imgsave = open(fn,'wb')
    imgsave.write(buffer(image))
    imgsave.close()


def trim0(s):
    while(s[-1]=="\x00"): s=s[:-1]
    return s

def trimTemp(tmp):
    tmp=tmp.replace("\n","").replace("\r","")
    return tmp

def fwVerStd(ver): # Ver 6.18 Oct 29 2007 ---> Ver 6.18 20071029
    ml=['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov','Dec']
    if len(ver)>=20:
        tl=ver[9:].split(" ")
        try:
            tl.remove("")
        except: pass
        try:
            return ver[:9]+"%s%02d%02d"%(tl[2], 1+ml.index(tl[0]), int(tl[1]))
        except:
            return ""
    else:
        return ""

def head_response(mtype='text/plain'): #生成标准的通信响应头
    response = HttpResponse(content_type=mtype)  #文本格式
    response["Pragma"]="no-cache"                   #不要缓存，避免任何缓存，包括http proxy的缓存
    response["Cache-Control"]="no-store"            #不要缓存
    return response



def getJSResponse(content=None,mtype="text/plain",callback=''):
    if callback:mtype="text/html"
    response = head_response(mtype)
    if (type(content)==type({})) or (type(content)==type([])):
        content=dumps1(content)
    content=smart_str(content)
    if callback:
        content="""%s(%s)""" %(callback,content)
    response["Access-Control-Allow-Origin"]="*"  #可能引起过度许可的CORS访问
    # response["Access-Control-Allow-Origin"]="null"
    #response["Content-Length"]=len(content)
    response.write(content)
    return response

def getByte(paths=[]):
    img_str_list = []
    for path in paths:
        with open(path,'rb') as f:
            img_byte = base64.b64encode(f.read())
        img_str = img_byte.decode('ascii')
        img_str_list.append(img_str)
    return img_str_list

# 获取两个日期间的所有月份
def get_month_range(start_day,end_day):
    start_day = datetime.datetime.strptime(start_day, "%Y%m%d")
    end_day = datetime.datetime.strptime(end_day, "%Y%m%d")
    month_range = []
    months = (end_day.year - start_day.year)*12 + end_day.month - start_day.month
    for mon in range(start_day.month-1,start_day.month + months):
        mouth = mon % 12 + 1
        if mouth < 10:
            mouth = '0%s'%mouth
        single = '%s%s'%(start_day.year + mon//12,mouth)
        month_range.append(single)
    return month_range


class JSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, datetime.date):
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, datetime.time):
            return obj.strftime('%H:%M:%S')
        elif isinstance(obj, Promise):
            # 自定义JSON编码器以解决特定对象（如ugettext_lazy或其它Promise对象）无法直接被序列化为JSON的问题。
            return force_text(obj)
        return json.JSONEncoder.default(self, obj)

def dumps(data):
    #return dumps1(data)
    return JSONEncoder().encode(data)


class LazyEncoder(DjangoJSONEncoder):
    """
    自定义JSON编码器以解决特定对象（如ugettext_lazy或其它Promise对象）无法直接被序列化为JSON的问题。
    """
    def default(self, obj):
        if isinstance(obj, Promise):
            # Promise 来自 django.utils.functional，它代表了一种延迟操作，比如翻译字符串。
            # force_text 来自 django.utils.encoding，它尝试将给定的对象转换为一个Unicode字符串。
            return force_text(obj)
        return super(LazyEncoder, self).default(obj)


# 以后用下面的函数2010.11.14
def dumps1(content, **kwargs):
    return json.dumps(content, cls=LazyEncoder, **kwargs)


def dumps2(content):
    return json.dumps(content,ensure_ascii=False)


def loads(str):
    return json.loads(str)


def escape(html):
    """Returns the given HTML with ampersands, quotes and carets encoded."""
    return html.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;').replace("'", '&#39;').replace('\'','&#x27;').replace(' ','&nbsp;').replace('\r','').replace('\n','').replace('\\','').replace('\t','&#9')


def today():
    d=datetime.datetime.now()
    return datetime.datetime(d.year,d.month,d.day)

def nextDay():
    d=datetime.datetime.now()
    d=d+datetime.timedelta(1,0)
    return datetime.datetime(d.year,d.month,d.day)

def endOfDay(d):
    d=d+datetime.timedelta(1,0)
    return datetime.datetime(d.year,d.month,d.day)-datetime.timedelta(0,1)

def startOfDay(d):
    return datetime.datetime(d.year,d.month,d.day)

def decodeTimeInt(t):
    tm_sec=t % 60;
    t/=60;
    tm_min=t % 60;
    t/=60;
    tm_hour=t % 24;
    t/=24;
    tm_mday=t % 31+1;
    t/=31;
    tm_mon=t % 12
    t/=12;
    tm_year=t+2000;
    return "%04d-%02d-%02d %02d:%02d:%02d"%(tm_year,tm_mon+1,tm_mday,tm_hour,tm_min,tm_sec)

def decodeTime(data):
    t=ord(data[0])+(ord(data[1])+(ord(data[2])+ord(data[3])*256)*256)*256
    return decodeTimeInt(t)

def encodeTime(y,m,d,hour,min,sec):
    tt=((y-2000)*12*31+((m-1)*31)+d-1)*(24*60*60)+(hour*60+min)*60+sec;
    return tt



def decodeTimeInt(t):
    tm_sec=t % 60;
    t/=60;
    tm_min=t % 60;
    t/=60;
    tm_hour=t % 24;
    t/=24;
    tm_mday=t % 31+1;
    t/=31;
    tm_mon=t % 12
    t/=12;
    tm_year=t+2000;
    return "%04d-%02d-%02d %02d:%02d:%02d"%(tm_year,tm_mon+1,tm_mday,tm_hour,tm_min,tm_sec)

def decodeTime(data):
    t=ord(data[0])+(ord(data[1])+(ord(data[2])+ord(data[3])*256)*256)*256
    return decodeTimeInt(t)

#def encodeTime(y,m,d,hour,min,sec):
#	tt=((y-2000)*12*31+((m-1)*31)+d-1)*(24*60*60)+(hour*60+min)*60+sec;
#	return tt

def packList(l):
    r=[]
    for i in l:
        if i: r.append(i)
    return r

#设置操作系统的自动化任务
def scheduleTask(cmd, time="00:00", weeks=('Su', 'M', 'T', 'W', 'Th', 'F', 'Sa')):
    l=os.popen("at").read()
    for s in l.split("\n"):
        r=packList(s.split(" "))
        if len(r)>=4:
            try:
                int(r[0])
            except:
                r.pop(0)
            atcmd=" ".join(r[3:])
            if cmd.lower()==atcmd.lower() or cmd[1:-1].lower()==atcmd.lower():
                print ("Delete old schedule")
                subprocess.call("at %s /DELETE"%r[0])
    if weeks:
        dur=" /EVERY:"+(",".join(weeks))
    else:
        dur=""
    cmd="at %s%s %s"%(time, dur, cmd)
#	print cmd
    subprocess.call(cmd)



#临时目录用于保存未保存到数据库中的数据
def tempDir(childwithday=True):
    ret=os.path.join(addition_file_root(), "temp")
    try:
        os.makedirs(ret)
    except: pass
    return ret
def tempFile(name, text, append=True):
    fn="%s/%s"%(tempDir(),name)
    f=open(fn, append and "a+" or "w+")
    try:
        f.write(text)
    except:
        try:
            f.write(text.encode("utf-8"))
        except: pass
    f.write("\n")
    f.close()
    return fn

def deviceHasCmd(deviceSN):

    nocmd_device_cname = "%s_nocmd_device_%s"

    try:
        cache.delete(nocmd_device_cname%(settings.UNIT,deviceSN))
    except: pass

def getNewColModel(colModel,keepName):
    """对原colModel根据要保留的name生成新的列表，keepName为需要的列表如['PIN','ENAME']"""
    for d in colModel:
        if d['name'] not in keepName:
            colModel.remove(d)
    return colModel

def addition_file_root():
    '''
    兼容多租户根目录处理
    '''
    file_root = settings.ADDITION_FILE_ROOT
    if settings.MULTI_TENANT:
        try:
            file_root = safe_join(file_root, connection.tenant.domain_url)
        except:
            pass
    return file_root

def getStoredFileName(relative_path, id, fname=''):
    file_dir = addition_file_root()
    if relative_path:
        dirs = relative_path.split('/')
        for i in range(len(dirs)):
            file_dir = os.path.join(file_dir, dirs[i])
    if not fname:
        # 有可能fname是传None的值。
        fname = ''
    fname = os.path.join(file_dir, fname)
    if id:
        fname, ext= os.path.splitext(fname)
        fname = "%s_%s%s" % (fname, id, ext)
    return fname


def getStoredFileURL(sn, id, fname, auth=True):
    '''
    获取文件存储的url路径
    '''
    if getattr(settings, 'MULTI_TENANT', 0):  #预留
        sn = '%s/%s' % (connection.tenant.domain_url, sn)

    if (sn.find('photo') >= 0 or sn.find('merchandise') >= 0) and auth is False:
        #照片Url路径，需要未授权访问场景时的处理（url越权访问的修改，导致app，小程序等也无法访问到照片，临时方案）
        fname = "/iclock/picfile/%s/%s" % (sn, fname)
    else:
        fname = "/iclock/file/%s/%s" % (sn, fname)
    if id:
        fname, ext = os.path.splitext(fname)
        fname = "%s_%s%s" % (fname,id,ext)
    return fname

def getUploadFileName(sn, id, fname):
    if sn=='':
        return getStoredFileName('upload', id, fname)
    return getStoredFileName('upload/'+sn, id, fname)

def getUploadFileURL(sn, id, fname):
    if sn=='':
        return getStoredFileURL('upload', id, fname)
    return getStoredFileURL('upload/'+sn, id, fname)

import configparser
class myconf(configparser.ConfigParser):
    def __init__(self,defaults=None):
        configparser.ConfigParser.__init__(self,defaults=None)
    def optionxform(self, optionstr):
        return optionstr

def SaveToIni(section,key,value):
    cfFileName=os.path.join(settings.FILEPATH, 'options.dat')
    cf = myconf()
    cf.read(cfFileName)
    cf.set(section, key, value)
    cf.write(open(cfFileName, "w"))

def card8To10Num(card):
    card=str(card)
    c=int('%s%s'%(hex(int(card[:-5])),'{:04x}'.format(int(card[-5:]))),16)
    return c
def card10To8Num(card):
    """用于将十进制10位卡号转换成8位区位码卡号"""
    hc=hex(int(card))
    c4=hc[-4:]
    c2=hc[:-4]
    c="%s%s"%('{:0>3}'.format(int(c2,16)),'{:0>5}'.format(int('0x'+c4,16)))
    return c

def cardHTOL(card):
    card="%s"%('{:0>8}'.format(str(hex(int(card)))[2:]))
    h="0x%s%s%s%s"%(card[6:8],card[4:6],card[2:4],card[0:2])
    l=int(h,16)
    return l

def get_cardno(device,cardno):
    """根据设备类型转换卡号到十进制卡号"""
    if not cardno:return ''
 #   print "111111111111",device.MulCardUser,cardno
    if hasattr(device,'MulCardUser') and device.MulCardUser==1:
        card=str(int('0x'+cardno,16))
    else:
        card=cardno
    return card


def circlePic(img):
    try:
        import PIL.Image as Image
    except:
        return None

    ima=img.convert("RGBA")
    size = ima.size
    r2 = min(size[0], size[1])
    #r2=200
    #if size[0] != size[1]:
    ima = ima.resize((r2, r2), Image.LANCZOS)
    imb = Image.new('RGBA', (r2, r2),(255,255,255,0))
    pima = ima.load()
    pimb = imb.load()
    r = float(r2/2)
    for i in range(r2):
        for j in range(r2):
            lx = abs(i-r+0.5)
            ly = abs(j-r+0.5)
            l  = pow(lx,2) + pow(ly,2)
            if l <= pow(r, 2):
                pimb[i,j] = pima[i,j]
    #imb.save("test_circle.jpg")
    return imb

def isDBDuplicate(estr):
    duplicate_keywords = ("integrity", "SQL0803N", "23000", "IntegrityError", "UNIQUE KEY", "are not unique",
        "Duplicate entry", "unique constraint", "duplicate key", "commands ignored",
        "set_session cannot be used", "already exists", "Duplicate")
    for keyword in duplicate_keywords:
        if keyword in estr:
            # MSSQL报23000,可能是触发了非空约束
            if keyword == '23000' and 'null' in estr.lower():
                return False
            return True
    return False

def restartSvr(svrName):
    try:
        subprocess.call("cmd /C net stop %s & net start %s"%(svrName, svrName))
    except:
        pass
def setValueDic(data):
    d={}
    for line in data.split("\n"):
        if line:
            v=line.split("\r")[0]
        else:
            v=line
        nv=v.split("=", 1)
        if len(nv)>1:
            try:
                v=u'%s'%(nv[1])
                d[nv[0]]=v
            except:
                pass
    return d
def lineToDict(rawData,splitfmt='\t'):
    """将以\t或指定的分隔符号分隔的行变成字典"""

    d={}
    if rawData:
            rawData=rawData.replace('\r','').replace('\n','')
            line=rawData.split(splitfmt)
            for t in line:
                    if t:
                            ll=t.split('=')
                            d[ll[0]]=t[len(ll[0])+1:]
    return d



#获取时间nowTime是本月的第几周
def GetWeekInMonth(nowTime):
    end = int(nowTime.strftime("%W"))
    begin = int(datetime.datetime(nowTime.year, nowTime.month, 1).strftime("%W"))
    week=end - begin + 1
    return week

#从原datasproc移过来的
def trunc(DTime):
    return datetime.datetime(DTime.year,DTime.month,DTime.day,0,0,0)

#从原models移过来
def customSql(sql,params=[],action=True):
    cursor=None
    # 达梦数据库兼容  列名与关键字冲突 列名使用 双引号+大写格式
    if settings.DATABASES['default']['ENGINE'] == 'django_dmPython':
        sql = sql.split(',')
        for i, v in enumerate(sql):
            if v == 'verify':
                sql[i] = '"VERIFY"'
        sql = ','.join(sql)
    try:
        cursor = connection.cursor()
        cursor.execute(sql,params)
    except OperationalError as e:
        raise  # MySQL server has gone away， 抛出异常，调用处处理异常逻辑
    except Exception as e:
        estr = "%s"%e
        if isDBDuplicate(estr):
            pass
        else:
            tempFile("customsql_error_%s.txt" % (datetime.datetime.now().strftime("%Y%m")),
                '%s %s, %s, %s\n' % (datetime.datetime.now().strftime("%Y%m%d%H%M%S"), estr, sql, params))
            if settings.DEBUG:
                import traceback;traceback.print_exc()
                print (sql, params)
    finally:
        if action and cursor:
            connection.commit()
            cursor.close()
    return cursor

def customSqlEx(sql,params=[],action=True,connDB=None):
    cursor=None
    if connDB:
        conn=connections[connDB]
        cursor = conn.cursor()
        if settings.DATABASE_ENGINE == 'ibm_db_django':
            if not params:params=()

    else:
        conn=connection
        cursor = conn.cursor()
        if settings.DATABASE_ENGINE == 'ibm_db_django':
            if not params:params=()
    try:
        t = cursor.execute(sql,params)
    except OperationalError as e:
        raise  # MySQL server has gone away， 抛出异常，调用处处理异常逻辑
    except Exception as e:
        estr = "%s"%e
        if isDBDuplicate(estr):
            pass
        else:
            tempFile("customsql_error_%s.txt" % (datetime.datetime.now().strftime("%Y%m")),
                '%s %s, %s, %s\n' % (datetime.datetime.now().strftime("%Y%m%d%H%M%S"), estr, sql, params))
            if settings.DEBUG:
                import traceback;traceback.print_exc()
                print (sql, params)
    finally:
        if action and cursor:
            conn.commit()
            cursor.close()
#    if action:
 #       connection.commit()
    return cursor

def customBatchSqlEx(sql, params):
    cursor = None
    conn = connection
    cursor = conn.cursor()
    if settings.DATABASE_ENGINE == 'ibm_db_django':
        if not params:
            params=()
    try:
        t = cursor.executemany(sql, params)
    finally:
        conn.commit()
        cursor.close()

#以下的删除只是删除记录在表empofdevice中相应的数据，和设备的数据没有任何关系
def delEmpInDevice(pin,sn):
    try:
        if pin!=-1 and sn!=-1:# 删除某设备上的某人
            sql="delete from empofdevice where badgenumber=%s and sn=%s"
            params=(pin,sn)
        elif sn!=-1 and pin==-1:# 删除某设备上的所有人
            sql="delete from empofdevice where sn=%s"
            params=(sn,)
        elif sn==-1 and pin!=-1:# 删除所有设备上的某人
            sql="delete from empofdevice where badgenumber=%s"
            params=(pin,)
        elif sn==-1 and pin==-1:# 删除所有数据
            sql="delete from empofdevice"
            params=()

        customSql(sql,params)
    except Exception as e:
        #connection.commit()
        print ("delEmpInDevice",e)
        pass




def saveCmd(sn,cmdStr,cmdTime=None,tellCmd=True):
    """
    sn:设备序列号
    cmdStr:命令内容
    cmdTime:命令执行起始时间
    """
    if (not cmdStr) or (not sn):return
    if ('DATA DEL_USER PIN' in cmdStr) or ("DATA DELETE user Pin" in cmdStr):
        try:
            pin=cmdStr.split('=')[1]
            delEmpInDevice(pin,sn)
        except:pass
    if getattr(settings, 'REDIS_CMD', 0):
        # 双写策略，写数据库时同步写缓存
        from mysite.iclock.models import devcmds
        createobj = devcmds.objects.create(
            SN_id = sn,
            CmdContent = cmdStr,
            CmdCommitTime = cmdTime or datetime.datetime.now()
        )
        try:
            from mysite.cache_utils import save_cmd_to_cache
            if not (cmdTime and cmdTime > datetime.datetime.now()):  #还未到时间的命令，暂不加入缓存
                save_cmd_to_cache(createobj.id, sn, cmdStr, createobj.CmdCommitTime)
        except:
            if settings.DEBUG:
                import traceback;
                traceback.print_exc()
    else:
        sql="""insert into devcmds(sn_id, cmdcontent,cmdcommittime) values(%s,%s,%s)"""
        params=(sn, cmdStr, cmdTime or datetime.datetime.now())
        cursor = customSql(sql,params)
    if tellCmd:
        deviceHasCmd(sn)
    return True

def DevIdentity_ex(pushs,vers):
    """
    依据设备的PUSH版本判断支持的功能
    :param pushs: 设备push版本二
    :param vers:
    :return:
    """
    if not pushs:
        return False
    pushl=pushs.split(".")
    if vers=='2.3.0' and len(pushl)<3:#2.32版本不支持加密
        return False
    verl=vers.split(".")#2.3.0
    ps='.'.join('{:0>2}'.format(x) for x in pushl)
    vs='.'.join('{:0>2}'.format(x) for x in verl)
    return ps>=vs

def createQrcode(content,qrcode_name='qrcode',make_base64=False):
    from mysite.iclock.datamisc import save_image_to_folder
    try:
        import StringIO
        output = StringIO.StringIO()
    except:
        import io
        output = io.BytesIO()

    import qrcode
    qr = qrcode.QRCode(
    error_correction=qrcode.constants.ERROR_CORRECT_L,
    )
    qr.add_data(content)

    qr.make(fit=True)
    img=qr.make_image()
    img.save(output)

    #不保存图片，直接以base64格式返回二维码数据
    if make_base64:
        return base64.b64encode(output.getvalue()).decode()

    output.seek(0)
    save_image_to_folder(data=output, data_format=1, name='{}.png'.format(qrcode_name),
        relative_path='qrcode', is_encrypt=False, max_image_width=100, max_image_height=100)


def get_hostname(request):
    """
    通过请求获取域名
    :param request:
    :return:
    """
    # host = request.META['HTTP_HOST']  #Apache开多进程时，无法获取到正确地址
    http_referer = request.META['HTTP_REFERER']
    host = http_referer.split('//')[1].split('/')[0]
    if host.split(':')[0] in ('localhost', '127.0.0.1'):
        # 'localhost', '127.0.0.1'转换成本机ip
        import socket
        hostname = socket.gethostname()
        ip = socket.gethostbyname(hostname)
        host = host.replace(host.split(':')[0], ip)
    hostname = "%s://%s" % ((request.is_secure() or http_referer.startswith('https')) and 'https' or 'http', host)
    return hostname


def create_iapp_qrcode(request):
    u"""获取iapp访问地址"""
    # host = request.META['HTTP_HOST']  #Apache开多进程时，无法获取到正确地址
    hostname = get_hostname(request)  # 通过请求获取域名
    url = hostname + "/iapp/login/"
    createQrcode(url,'iapp_qrcode')
    qrcode_url  = getStoredFileURL('qrcode', None, 'iapp_qrcode.png?stamp=%s' % datetime.datetime.now().strftime('%Y%m%d%H%M%S'))
    return qrcode_url

def sync_biophoto(deptids):
    from mysite.iclock.models import employee, BioData, bioFaceVL
    from mysite.core.zktools import getSQL_insert_new
    if deptids:
        BioData.objects.filter(UserID__DeptID__in=deptids).filter(bio_type=bioFaceVL,majorver=0).delete()
        empobj = employee.objects.filter(DeptID__in=deptids).exclude(DelTag=1)
    else:
        BioData.objects.filter(bio_type=bioFaceVL,majorver=0).delete()
        empobj = employee.objects.filter(DelTag=0)
    for emp in empobj:
        adpic=getStoredFileName("photo", None, "ad_%s.jpg"%emp.pin())
        if os.path.exists(adpic):
            obj = BioData.objects.filter(UserID=emp.id, bio_type=bioFaceVL, majorver=0)
            if not obj:
                dDict={'userid':emp.id,'bio_no':0,'bio_index':0,'bio_format':0,'minorver':'0','duress':0,'majorver':'0','bio_tmp':'','bio_type':bioFaceVL,'valid':0,'UTime':datetime.datetime.now()}
                sql,params=getSQL_insert_new(BioData._meta.db_table,dDict)
                customSql(sql,params)

def set_att_flag_cache(check_time, pin, op_flag=1):
    """
    设置考勤签到标记到缓存
        暂仅用于考勤消费联动
        op_flag :  1 设置缓存，2 删除缓存
    """
    from mysite.base.models import GetParamValue
    key = "%s_iclock_att_flag_%s_%s" % (settings.UNIT, pin, check_time.strftime("%Y-%m-%d"))
    if op_flag == 1 \
        and (int(GetParamValue('ipos_att_ipos', 0, 'ipos'))== 1) \
        and (datetime.date.today() == check_time.date()):
        cache.set(key, 1)
    else:
        cache.delete(key)

def check_is_att(pin):
    """
    判断是否有签到记录
        暂用于考勤消费联动
    """
    from mysite.iclock.models import transactions
    from django.db.models import Q
    #检查缓存flag，有则不再查数据库
    key = "%s_iclock_att_flag_%s_%s" % (settings.UNIT, pin, today().strftime("%Y-%m-%d"))
    flag = cache.get(key)
    if flag:
        return True
    else:
        st = today()
        et = st + datetime.timedelta(days=1)
        att_obj = transactions.objects.filter(UserID__PIN=pin, TTime__gte=st, TTime__lte=et).filter(
            Q(purpose=9)|Q(purpose=None)|Q(purpose=0)).count()
        if att_obj:
            set_att_flag_cache(today(), pin)
            return True

    return False

def get_display_version():
    """
    获取显示的版本号
    """
    version = settings.VERSION
    if sys.version[0] == '2':
        version = '10.0' + settings.VERSION[4:]
    # elif settings.PRODUCTCODE == 14:
    #     #pro自版本20200930起，版本号更新为11.1.0, zktime暂不更新版本号
    #     version = '11.0' + settings.VERSION[4:]
    return version

def check_temperature(temperature, emp, alarm_count=3):
    """
    检查异常温度，超过温度超次的，预警
    """
    try:
        from mysite.base.models import GetParamValue
        if settings.DEMO==1 or int(GetParamValue('opt_basic_antiepidemic','0')) == 0:
            return
        from mysite.base.models import GetParamValue
        from mysite.tasks import job_antiepidemic_alarm
        anti_data = GetParamValue('antiepidemic_settings_data', '{}', '')
        data = loads(anti_data)
        alarm_temperature = data.get('alarm_temperature', '37.3')
        abnormal_temperature = ''
        abnormal_count = 0
        if temperature > alarm_temperature:
            #保存异常体温值
            key_detail = '%s_ANTIEPIDEMIC_ABNORMAL_VALUE_%s' % (settings.UNIT, emp.id)
            if cache.get(key_detail):
                old_value = cache.get(key_detail)
                cache.set(key_detail, str(old_value) +', '+ str(temperature), timeout=60*15)
                abnormal_temperature = str(old_value) +', '+ str(temperature)
            else:
                cache.set(key_detail, str(temperature), timeout=60*15)
                abnormal_temperature = str(temperature)
            #体温异常计数
            key_count = '%s_ANTIEPIDEMIC_ABNORMAL_COUNT_%s' % (settings.UNIT, emp.id)
            if cache.get(key_count):
                abnormal_count = cache.incr(key_count, 1)
            else:
                cache.set(key_count, 1, timeout=60*10)  #缓存设置10分钟，10分钟内3次测量都异常即报警
            #达到预警次数, 发送预警
            if alarm_count == 1 or abnormal_count >= alarm_count:
                job_antiepidemic_alarm(emp, abnormal_temperature)
                cache.delete(key_count)
                cache.delete(key_detail)
    except:
        if settings.DEBUG:
            import traceback;traceback.print_exc()

def aes_crypt(data, encryption=True):
    '''
    AES加解密，用于图片的加密处理，
        encryption:
                True 加密， 对内容进行加密，再转Base64重新保存
                False 解密，对内容先进行base64解码，再解密出图片
    '''
    from Crypto.Cipher import AES
    import base64
    key = "iEpSxImA0vpMUAab"
    vi = "1234567890AaBd98"
    cipher = AES.new(key.encode('utf-8'), AES.MODE_EAX, vi.encode('utf-8'))
    if encryption:
        ciphertext = cipher.encrypt(data)
        return base64.b64encode(ciphertext).decode("utf-8")
    else:
        imgdata = base64.b64decode(data)
        content = cipher.decrypt(imgdata)
        return content

def aes_decrypt(data):
    """
    解密前端AES加密传输的数据
        key: "iEpSxImA0vpMUAab"
        mode: ECB
        padding: pkcs7
    """
    import base64
    from Crypto.Cipher import AES

    if not data:
        return data
    key = "iEpSxImA0vpMUAab"
    aes = AES.new(key.encode('utf-8'), AES.MODE_ECB)
    res = base64.decodebytes(data.encode("utf8"))
    msg = aes.decrypt(res).decode("utf8")
    return msg[0: -ord(msg[-1])]

def savefile(fn,data):
    try:
        imgsave = open(fn,'w')
        imgsave.write(data)
        imgsave.close()
        return True
    except Exception as e:
        print(e)
        pass
    return False

def crypt_img(fn):
    try:
        img = open(fn,'rb')
        data = aes_crypt(img.read())
        img.close()
        r = savefile(fn, "CRYPT_IMG:%s"%data)
        # 删除缓存
        cache_img_key = fn.split(addition_file_root())[1].replace('\\','/')
        cache.delete('%s_DECRIPTION_PHOTO_%s'%(settings.UNIT, cache_img_key))
        return True
    except Exception as e:
        print(e)
        pass
    return False

def cutpic(spic, destpic):
    """
    用ZK算法进行的抠图的两种方式，ZKDetectFace抠图服务及zkcropface.dll，兼容使用
    """
    service_ok = cache.get('%s_ZKDetectFace' % settings.UNIT)
    if service_ok is None:
        import requests
        info_url = "http://%s/ZKDetectFace/info"%(settings.DETECTFACE_SERVER)
        service_ok = False
        try:
            r = requests.get(info_url, timeout=2)
            if 'server_version' in r.json():
                service_ok = True
        except:
            service_ok = False
        #timeout时间内，不再判断服务是否可用
        cache.set('%s_ZKDetectFace' % settings.UNIT, service_ok, timeout=60*30)
    if service_ok:
        return cutpic_zkdetectface(spic, destpic)
    else:
        return cutpic_zkcropface(spic, destpic)

def cutpic_zkcropface(source_path, destination_path):
    """
    通过zkcropface.dll抠图库抠人脸
    """
    from PIL import Image
    from mysite.core.zkcropface import cutpic as _cutpic
    from Crypto.Util.py3compat import tobytes
    ret = _cutpic(tobytes(source_path), tobytes(destination_path))
    #如果识别不到面部，尝试压缩图片,这边操作2次，其中1次长宽值交换
    img = Image.open(source_path)
    i = 0
    while ret == -100:
        if i > 1:
            break
        img_w, img_h = img.size
        if img_w > 640:
            x_s = 640
            y_s = img_h * x_s / img_w
            if i == 0:
                small_img = img.resize((int(x_s), int(y_s)), Image.LANCZOS)
            else:
                small_img = img.resize((int(y_s), int(x_s)), Image.LANCZOS)
            small_img.save(source_path, quality=90)
            ret = _cutpic(tobytes(source_path), tobytes(destination_path))
        i += 1
    img.close()
    if ret >= 0:
        ret = 0
    return {'ret':ret, 'code':''}

def cutpic_zkdetectface(spic, destpic):
    """
    通过DetectFace抠图服务抠人脸
    """
    import requests
    try:
        url='http://%s/ZKDetectFace/detect'%(settings.DETECTFACE_SERVER)
        pdata = {
            "type": 2,
            "srcfilename": spic,
            "desfilename": destpic
        }
        r = requests.post(url, json=pdata)
        return r.json()
    except requests.exceptions.ConnectionError:
        return {'ret':-10000, 'data':{}, 'code':'-99999'}
    except Exception as e:
        print ('-ZKDetectFace--Exception--', e)
    return {'ret':-10000, 'data':{}, 'code':''}

def read_pic_save_face(source_path, destination_path):
    """
    从原始照片中裁剪出面部
        source_path  源图片路径
        destination_path 裁剪后的图片保存路径
    """
    from PIL import Image
    from Crypto.Util.py3compat import tobytes
    from mysite.core.zkcropface import rotatepic

    #如果原图已是加密，则先解密
    f = open(r'%s'%source_path, 'rb')
    data = f.read()
    f.close()
    if data[:10] == b"CRYPT_IMG:":
        image_data = aes_crypt(data[10:], False)
        img = open(source_path,'wb')
        img.write(image_data)
        img.close()

    #重复切图，如目标图片已存在时，报you cannot save an image as "unknown format",因为加密图片无法处理
    #同原图加密的处理方式，先判断目录图片是否存在，存在则解密
    if os.path.exists(destination_path):
        f = open(r'%s'%destination_path, 'rb')
        data = f.read()
        f.close()
        if data[:10] == b"CRYPT_IMG:":
            image_data = aes_crypt(data[10:], False)
            #解密后，还是加密图片，认为这个图片损坏，不处理了，直接删除
            if image_data[:10] == b"CRYPT_IMG:":
                os.remove(destination_path)
            else:
                img = open(destination_path,'wb')
                img.write(image_data)
                img.close()

    if settings.DLIB == 1:  #调用dlib库进行面部识别
        import dlib
        detector = dlib.get_frontal_face_detector()
        image = dlib.load_rgb_image(source_path)
        dets = detector(image, 1)  #第二个参数1表示我们对图像进行向上采样1倍
        i = 0
        while not dets:
            if i > 2:
                break
            rotatepic(source_path, Image.ROTATE_90)
            image = dlib.load_rgb_image(source_path)
            dets = detector(image, 1)
            i += 1
        if not dets:  # 未检测到面部时删除异常对比照片
            if os.path.exists(source_path):
                os.remove(source_path)
            if os.path.exists(destination_path):
                crypt_img(destination_path)
            return {'ret':-100, 'code':''} #未检测到面部
        d = dets[0]
        # print("Detection {}: Left: {} Top: {} Right: {} Bottom: {}".format(i, d.left(), d.top(), d.right(), d.bottom()))
        top, right, bottom, left = d.top(), d.right(), d.bottom(), d.left()
        #在侦测到的面部坐标基础上，扩大截图的范围
        left0 = int(left - (right - left) * 0.3)
        right0 = int(right + (right - left) * 0.3)
        top0 = int(top - (bottom - top) * 0.73)
        bottom0 = int(bottom + (bottom - top) * 0.40)
        left0 = left0 if left0 > 0 else 0
        top0 = top0 if top0 > 0 else 0

        face_image = image[top0:bottom0, left0:right0]
        pil_image = Image.fromarray(face_image)
        out = pil_image.resize((480, int((480 / pil_image.width) * pil_image.height)))
        out.save(destination_path)
        crypt_img(destination_path)
        crypt_img(source_path)
        return {'ret':0, 'code':''}
    else:
        ret = cutpic(source_path, destination_path)
        if ret['ret'] == 0:
            crypt_img(destination_path)
            crypt_img(source_path)
        else:
            if os.path.exists(source_path):
                os.remove(source_path)
            if os.path.exists(destination_path):
                crypt_img(destination_path)
        return ret


def qrcode_crc32(param1, param2, param3='55'):
    """
    对人员信息进行crc32编码，形成二维码名片
        param1 当前业务上是userid
        param2 当前业务上是时间戳
        param3 当前业务上是数据字符前缀
    """
    import time
    import hashlib
    from zlib import crc32
    from Crypto.Util.py3compat import tobytes

    data_str = "%s%s%s" % (param3, str(param1).zfill(8), int(param2))
    data_str_md5 = hashlib.md5(("%s%s"%(param1, data_str)).encode('utf-8')).hexdigest()
    crc_code = str(crc32(tobytes(data_str_md5)))
    qrcode_str = "%s%s" % (data_str, crc_code)

    return qrcode_str

def gen_dates(b_date, days):
    from datetime import timedelta
    day = timedelta(days=1)
    for i in range(days):
        yield b_date + day*i

def get_date_list(start=None, end=None):
    """
    获取日期列表
    :param start: 开始日期
    :param end: 结束日期
    :return:
    """
    if start is None:
        start = datetime.strptime("2000-01-01", "%Y-%m-%d")
    if end is None:
        end = datetime.now()
    data = []
    for d in gen_dates(start, (end-start).days+1):
        data.append(d)
    return data

def secretkey():
    """
    动态二维码密钥生成，生成一个32位长度的字符串（二维码加密方案，由安防部门提供）
    """
    import uuid
    from mysite.base.models import GetParamValue, SetParamValue
    from mysite.base.aes_cipher import AESCipher
    encrypted_secretkey = GetParamValue('dynamic_qr_code_secretkey', '')
    if not encrypted_secretkey:
        secretkey = "".join(str(uuid.uuid4()).split("-"))
        #将密钥加密后再进行保存
        encrypted_secretkey = AESCipher('secretkeysecretk').encrypt(secretkey)
        SetParamValue('dynamic_qr_code_secretkey', encrypted_secretkey)
    secretkey = AESCipher('secretkeysecretk').decrypt(encrypted_secretkey)
    return secretkey

def copy_file(from_path, to_path, del_old=False):
    """
    复制文件
    param: from_path: 源文件路径
    param: to_path: 新文件路径(若目标文件已存在将被覆盖)
    param: del_old: 是否删除源文件
    """
    try:
        with open(from_path, 'r') as f:
            data = f.read()
            savefile(to_path, data)
        if del_old:
            os.remove(from_path)
    except:
        return -1


class RaiseException(Exception):
    """
    用于主动抛出异常
    便于将主动抛出的异常与其他异常区分处理
    """
    def __init__(self, error):
        self.error = error




def get_host_ip():
    """
    获取本地ip地址
    """
    import socket
    ip = '127.0.0.1'
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
    finally:
        s.close()
    return ip

def send_error_email(func):
    """装饰器：捕捉异常时邮件告警"""
    @wraps(func)
    def is_error(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if settings.MULTI_TENANT and settings.ADMINS:
                subject = _(u"Service Exception Notification")
                message = _(u"Exception time: %s\nException function: %s\nException information: %s\n%s\n%s") \
                          % (datetime.datetime.now(), func.__name__, e, '=' * 60, traceback.format_exc())
                from_email = settings.DEFAULT_FROM_EMAIL
                recipient_list = [i[1] for i in settings.ADMINS]
                send_mail(subject, message, from_email, recipient_list)
            raise Exception(traceback.format_exc())
    return is_error

def deepcopy(obj):
    """
    利用pickle序列化与反序列化实现深拷贝
    """
    import pickle

    return pickle.loads(pickle.dumps(obj))


def decrypt_and_copy(source: str, target: str) -> bool:
    """
    解密原图片并保存到目标文件夹
    :param source: 源文件路径
    :param target: 目标路径
    :return: True/False
    """
    try:
        with open(source, 'rb') as f:
            data = f.read()
        if data[:10] == b"CRYPT_IMG:":
            data = aes_crypt(data[10:], False)
        with open(target, 'wb') as f:
            f.write(data)
        return True
    except:
        return False


def zipdir(source: str, target: str):
    """
    将指定目录下文件打包为zip
    :param source: 源目录
    :param target: 生成zip路径
    """
    with zipfile.ZipFile(target, 'w', zipfile.ZIP_DEFLATED) as f:
        for file in os.listdir(source):
            file_path = os.path.join(source, file)
            f.write(file_path, arcname=file)

def is_contain_symbol(string: str) -> bool:
    """
    检测字符串中是否包含符号
    :param string: 目标字符串
    """
    import re
    reg = re.compile('[@_!#$%^&*()<>?/\|\n}{~:]')
    return  reg.search(string) != None