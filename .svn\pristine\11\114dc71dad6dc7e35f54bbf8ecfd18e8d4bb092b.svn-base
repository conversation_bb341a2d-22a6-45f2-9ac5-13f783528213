# -*- coding: utf-8 -*-
# Generated by Django 1.11.7 on 2017-11-30 16:00
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0004_auto_20171130_1600'),
        ('ipos', '0002_issuecard_operate_time'),
    ]

    operations = [
        migrations.CreateModel(
            name='AliWxFulllog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card', models.CharField(blank=True, default='', max_length=20, verbose_name='\u5361\u53f7')),
                ('sys_card_no', models.CharField(blank=True, default='', max_length=20, verbose_name='\u5361\u8d26\u53f7')),
                ('card_serial_num', models.IntegerField(blank=True, null=True, verbose_name='\u5361\u6d41\u6c34\u53f7')),
                ('sn', models.CharField(blank=True, max_length=20, null=True, verbose_name='\u5e8f\u5217\u53f7')),
                ('serial_num', models.IntegerField(blank=True, null=True, verbose_name='\u8bbe\u5907\u6d41\u6c34\u53f7')),
                ('posoptime', models.DateTimeField(blank=True, null=True, verbose_name='\u5145\u503c\u65f6\u95f4')),
                ('posmoney', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='\u5145\u503c\u91d1\u989d')),
                ('blance', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='\u5361\u4e0a\u4f59\u989d')),
                ('logtype', models.IntegerField(blank=True, null=True, verbose_name='\u4ea4\u6613\u7c7b\u578b')),
                ('opid', models.IntegerField(blank=True, null=True, verbose_name='\u64cd\u4f5cID')),
                ('paysource', models.IntegerField(blank=True, choices=[(1, '\u652f\u4ed8\u5b9d\u5145\u503c'), (2, '\u5fae\u4fe1\u5145\u503c')], editable=False, null=True, verbose_name='\u4ea4\u6613\u65b9\u5f0f')),
                ('tradeno', models.CharField(blank=True, max_length=100, null=True, verbose_name='\u8ba2\u5355\u53f7')),
                ('userid', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u4eba\u5458')),
            ],
            options={
                'default_permissions': ('browse',),
            },
        ),
        migrations.AlterModelOptions(
            name='allowance',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('allowanceAudit_allowance', 'Audit Allowance'),), 'verbose_name': '\u8865\u8d34\u8868', 'verbose_name_plural': '\u8865\u8d34\u8868'},
        ),
        migrations.AlterModelOptions(
            name='backcard',
            options={'default_permissions': ('browse',), 'verbose_name': '\u9000\u5361\u8868', 'verbose_name_plural': '\u9000\u5361\u8868'},
        ),
        migrations.AlterModelOptions(
            name='batchtime',
            options={'default_permissions': ('browse', 'change'), 'verbose_name': '\u6d88\u8d39\u65f6\u95f4\u6bb5', 'verbose_name_plural': '\u6d88\u8d39\u65f6\u95f4\u6bb5'},
        ),
        migrations.AlterModelOptions(
            name='cardcashszbak',
            options={'default_permissions': (), 'verbose_name': '\u5361\u73b0\u91d1\u6536\u652f\u5907\u4efd\u8868', 'verbose_name_plural': '\u5361\u73b0\u91d1\u6536\u652f\u5907\u4efd\u8868'},
        ),
        migrations.AlterModelOptions(
            name='cardcashtype',
            options={'default_permissions': (), 'verbose_name': '\u5361\u73b0\u91d1\u6d88\u8d39\u7c7b\u578b', 'verbose_name_plural': '\u5361\u73b0\u91d1\u6d88\u8d39\u7c7b\u578b'},
        ),
        migrations.AlterModelOptions(
            name='cardmanage',
            options={'default_permissions': ('browse',), 'verbose_name': '\u7ba1\u7406\u5361\u8868', 'verbose_name_plural': '\u7ba1\u7406\u5361\u8868'},
        ),
        migrations.AlterModelOptions(
            name='dininghall',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u9910\u5385\u8d44\u6599', 'verbose_name_plural': '\u9910\u5385\u8d44\u6599'},
        ),
        migrations.AlterModelOptions(
            name='handconsume',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u624b\u5de5\u8865\u6d88\u8d39\u8868', 'verbose_name_plural': '\u624b\u5de5\u8865\u6d88\u8d39\u8868'},
        ),
        migrations.AlterModelOptions(
            name='iccard',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u5361\u7c7b\u8d44\u6599', 'verbose_name_plural': '\u5361\u7c7b\u8d44\u6599'},
        ),
        migrations.AlterModelOptions(
            name='iccardmechine',
            options={'default_permissions': (), 'verbose_name': '\u53ef\u7528\u8bbe\u5907', 'verbose_name_plural': '\u53ef\u7528\u8bbe\u5907'},
        ),
        migrations.AlterModelOptions(
            name='iccardposmeal',
            options={'default_permissions': (), 'verbose_name': '\u53ef\u7528\u9910\u522b', 'verbose_name_plural': '\u53ef\u7528\u9910\u522b'},
        ),
        migrations.AlterModelOptions(
            name='icconsumerlist',
            options={'default_permissions': ('browse',), 'verbose_name': '\u6d88\u8d39\u660e\u7ec6', 'verbose_name_plural': '\u6d88\u8d39\u660e\u7ec6'},
        ),
        migrations.AlterModelOptions(
            name='icconsumerlistbak',
            options={'default_permissions': ('browse',), 'verbose_name': '\u6d88\u8d39\u660e\u7ec6', 'verbose_name_plural': '\u6d88\u8d39\u660e\u7ec6'},
        ),
        migrations.AlterModelOptions(
            name='iclockdininghall',
            options={'default_permissions': (), 'permissions': (('iclockdininghall_cardcashsz', 'cardcashsz'), ('iclockdininghall_icconsumerlist', 'icconsumerlist'), ('iclockdininghall_reports', 'reports')), 'verbose_name': '\u6240\u5c5e\u9910\u5385', 'verbose_name_plural': '\u6240\u5c5e\u9910\u5385'},
        ),
        migrations.AlterModelOptions(
            name='issuecard',
            options={'default_permissions': ('browse',), 'permissions': (('issuecard_issuecard', 'issuecard'), ('issuecard_oplosecard', 'oplosecard'), ('issuecard_oprevertcard', 'oprevertcard'), ('issuecard_cancelmanagecard', 'cancelmanagecard'), ('issuecard_nocardretirecard', 'nocardretirecard'), ('issuecard_supplement', 'supplement'), ('issuecard_reimburse', 'reimburse'), ('issuecard_retreatcard', 'retreatcard'), ('issuecard_updatecard', 'updatecard'), ('issuecard_initcard', 'initcard')), 'verbose_name': '\u53d1\u5361\u8868', 'verbose_name_plural': '\u53d1\u5361\u8868'},
        ),
        migrations.AlterModelOptions(
            name='keydetail',
            options={'default_permissions': ('browse',), 'verbose_name': '\u952e\u503c\u6d88\u8d39\u660e\u7ec6\u8868', 'verbose_name_plural': '\u952e\u503c\u6d88\u8d39\u660e\u7ec6\u8868'},
        ),
        migrations.AlterModelOptions(
            name='keyvalue',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u952e\u503c\u8d44\u6599', 'verbose_name_plural': '\u952e\u503c\u8d44\u6599'},
        ),
        migrations.AlterModelOptions(
            name='keyvaluemechine',
            options={'default_permissions': (), 'verbose_name': '\u53ef\u7528\u8bbe\u5907', 'verbose_name_plural': '\u53ef\u7528\u8bbe\u5907'},
        ),
        migrations.AlterModelOptions(
            name='loseunitecard',
            options={'default_permissions': ('browse',), 'verbose_name': '\u6302\u5931\u89e3\u6302\u8868', 'verbose_name_plural': '\u6302\u5931\u89e3\u6302\u8868'},
        ),
        migrations.AlterModelOptions(
            name='meal',
            options={'default_permissions': ('browse', 'change'), 'verbose_name': '\u9910\u522b\u8d44\u6599', 'verbose_name_plural': '\u9910\u522b\u8d44\u6599'},
        ),
        migrations.AlterModelOptions(
            name='merchandise',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u5546\u54c1\u8d44\u6599', 'verbose_name_plural': '\u5546\u54c1\u8d44\u6599'},
        ),
        migrations.AlterModelOptions(
            name='poslog',
            options={'default_permissions': ('browse',)},
        ),
        migrations.AlterModelOptions(
            name='replenishcard',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u6362\u5361\u8868', 'verbose_name_plural': '\u6362\u5361\u8868'},
        ),
        migrations.AlterModelOptions(
            name='splittime',
            options={'default_permissions': ('browse', 'change'), 'verbose_name': '\u5206\u6bb5\u5b9a\u503c', 'verbose_name_plural': '\u5206\u6bb5\u5b9a\u503c'},
        ),
        migrations.AlterModelOptions(
            name='splittimemechine',
            options={'default_permissions': (), 'verbose_name': '\u53ef\u7528\u8bbe\u5907', 'verbose_name_plural': '\u53ef\u7528\u8bbe\u5907'},
        ),
        migrations.AlterModelOptions(
            name='storedetail',
            options={'default_permissions': ('browse',), 'verbose_name': '\u5546\u54c1\u6a21\u5f0f\u660e\u7ec6\u8868', 'verbose_name_plural': '\u5546\u54c1\u6a21\u5f0f\u660e\u7ec6\u8868'},
        ),
        migrations.AlterModelOptions(
            name='timebrush',
            options={'default_permissions': ('browse',), 'verbose_name': '\u8ba1\u65f6\u6d88\u8d39\u8868', 'verbose_name_plural': '\u8ba1\u65f6\u6d88\u8d39\u8868'},
        ),
        migrations.AlterModelOptions(
            name='timedetail',
            options={'default_permissions': ('browse',), 'verbose_name': '\u8ba1\u65f6\u6d88\u8d39\u8868', 'verbose_name_plural': '\u8ba1\u65f6\u6d88\u8d39\u8868'},
        ),
        migrations.AlterModelOptions(
            name='timeslice',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u5361\u73b0\u91d1\u6536\u652f', 'verbose_name_plural': '\u5361\u73b0\u91d1\u6536\u652f'},
        ),
    ]
