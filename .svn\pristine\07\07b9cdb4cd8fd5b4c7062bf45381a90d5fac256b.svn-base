{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <style type="text/css">
        .mui-segmented-control .mui-control-item {
            line-height: 40px;
            text-align: center;
        }
        .mui-table-view-cell>a:not(.mui-btn){
            margin: -18px -15px;
        }
        .mui-control-content {
            min-height: 215px;
        }
        .mui-control-content .mui-loading {
            margin-top: 50px;
        }
        html,#slider,.mui-content{
            width: 100%;
            height: 100%;
        }
        body{
            width: 100%;
            height: 100%;
        }
        .mui-slider-group{
            height: calc(100% - 45px);
        }
    </style>
{% endblock %}
{% block content %}
	<header class="mui-bar mui-bar-nav">
		<a href="/iapp/att/myapply/" class=" mui-icon mui-icon-left-nav mui-pull-left"></a>
		<h1 class="mui-title" style='font-size: 16px'>{% trans 'My leave' %}</h1>
	</header>
    <div class="mui-content">
        <div id="slider" class="mui-slider">
            <div id="sliderSegmentedControl" class="mui-slider-indicator mui-segmented-control mui-segmented-control-inverted">
                <a class="mui-control-item" href="#item1mobile">{% trans 'Approval pending' %}</a>
                <a class="mui-control-item" href="#item2mobile">{% trans 'processed' %}</a>
            </div>
            <div id="sliderProgressBar" class="mui-slider-progress-bar mui-col-xs-6"></div>
            <div class="mui-slider-group">
                <div id="item1mobile" class="mui-slider-item mui-control-content">
                    <div id="scroll1" class="mui-scroll-wrapper">

                        <div class="mui-input-row mui-search">
                            <input type="search"  class="mui-input-clear" id="searchInput1" onkeyup="enterSearch(event, 'item1mobile')" placeholder="{% trans 'Please enter start date or leave class for search' %}">
                        </div>

                        <div class="mui-scroll">
                            <div class="mui-loading">
                                <div class="mui-spinner">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="item2mobile" class="mui-slider-item mui-control-content">
                    <div id="scroll2" class="mui-scroll-wrapper">

                        <div class="mui-input-row mui-search">
                            <input type="search"  class="mui-input-clear" id="searchInput2" onkeyup="enterSearch(event, 'item2mobile')" placeholder="{% trans 'Please enter date or leave class for search' %}">
                        </div>

                        <div class="mui-scroll">
                            <div class="mui-loading">
                                <div class="mui-spinner">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extrjs %}
<script>
    mui.init({
        swipeBack: false,
    });
    (function($) {
        $('.mui-scroll-wrapper').scroll({
            indicators: true //是否显示滚动条
        });
        //初始加载审批中的请假数据
        loadData('item1mobile')
        document.getElementById('slider').addEventListener('slide', function(e) {
            if (e.detail.slideNumber === 0) {
                loadData('item1mobile')
            } else if (e.detail.slideNumber === 1) {
                loadData('item2mobile')
            }
        });
        //点击进入详情页
        $(".mui-scroll").on('tap','.mui-table-view-cell',function(){
            jsonstr = this.getElementsByTagName('input')[0].value  //用input来传递数据
            data = JSON.parse(jsonstr)  //转json字符串为json对象
            if (data.state!='{% trans 'accepted' %}'){
                mui.openWindow({
                    id:'detail',
                    url:'/iapp/att/vacation/'+data.id+'/',
                });
            }
            
        }) 

    })(mui);

    function loadData(itemName){
        if(itemName=='item1mobile'){
            state = 0
        }else{
            state = 1
        }
        mui.ajax('/iapp/get_data_info/?info_type=vacation&state='+state+'',{
            data:{},
            dataType:'json',
            type:'post',
            success:function(data){
                if(data.length< 1){
                        var html = "<h4 align='center' class='mui-text-center mui-h5 center-box-msg'>{% trans 'No leave information yet!' %}</h4>"
                        +"<svg class='icon center-box' aria-hidden='true'> <use xlink:href='#icon-nothing'></use> </svg>"
                    }
                else{
                    var html="<ul id='id"+itemName+"' class='mui-table-view'>";
                    for(var i=0;i<data.length;i++){
                        html +=  " <li class='mui-table-view-cell'>"
                                +"    <div class='mui-media-body'>"
                                +"      <span id='id_leaveClass' style='font-weight:bold'>"+data[i].DateID+"</span>"
                                +"      <p class='mui-ellipsis' style='text-align: center;'><span id='id_startime'>"+data[i].StartSpecDay+"</span>  {% trans 'to' %}  <span id='id_endtime'>"+data[i].EndSpecDay+"</span></p>"
                                +"      <p class='mui-ellipsis' align='right'><span id='id_state'>"+data[i].state+"</span></p>"
                                +"      <input id='id_data' value='"+JSON.stringify(data[i])+"' type='hidden'/>"
                                +"    </div>"
                                +"</li>"
                    }
                    html +="</ul>"
                }
                var item = document.getElementById(itemName);
                if(data.length< 1){item.querySelector('.mui-scroll').setAttribute("style","height: 100%;position: relative;");}
                if (item.querySelector('.mui-loading')) {
                    setTimeout(function() {
                        item.querySelector('.mui-scroll').innerHTML = html;
                    }, 100);
                }
            }
        });

        
    }

    function enterSearch(e, itemName) {
        if (e.keyCode == 13) {
            if (itemName == 'item1mobile') {
                state = 0;
                var searchInput = 'searchInput1';
            } else {
                state = 1;
                var searchInput = 'searchInput2';
            }
            var q = document.getElementById(searchInput).value;
            mui.ajax('/iapp/get_data_info/?info_type=vacation&state=' + state + '&q='+ q +'', {
                data: {},
                dataType: 'json',
                type: 'post',
                success: function (data) {
                    if(data.length< 1){
                        var html = "<h4 align='center' class='mui-text-center mui-h5 center-box-msg'>{% trans 'No relevant information found in the search!' %}</h4>"
                        +"<svg class='icon center-box' aria-hidden='true'> <use xlink:href='#icon-nothing'></use> </svg>"
                    }
                    else{
                        var html = "<ul id='id" + itemName + "' class='mui-table-view'>";
                        for (var i in data) {
                            html += " <li class='mui-table-view-cell'>"
                                + "    <div class='mui-media-body'>"
                                + "      <span id='id_leaveClass' style='font-weight:bold'>" + data[i].DateID + "</span>"
                                + "      <p class='mui-ellipsis' style='text-align: center;'><span id='id_startime'>" + data[i].StartSpecDay + "</span>  {% trans 'to' %}  <span id='id_endtime'>" + data[i].EndSpecDay + "</span></p>"
                                + "      <p class='mui-ellipsis' align='right'><span id='id_state'>" + data[i].state + "</span></p>"
                                + "      <input id='id_data' value='" + JSON.stringify(data[i]) + "' type='hidden'/>"
                                + "    </div>"
                                + "</li>"
                        }
                        html += "</ul>";
                    }
                    var item = document.getElementById(itemName);
                    if(data.length< 1){item.querySelector('.mui-scroll').setAttribute("style","height: 100%;position: relative;");}
                    setTimeout(function () {
                            item.querySelector('.mui-scroll').innerHTML = html;
                        }, 100);

                }
            });

        }
    }

       
</script>
{% endblock %}