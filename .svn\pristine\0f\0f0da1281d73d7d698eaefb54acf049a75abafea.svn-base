{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
{% block tblHeader %}
hasImport={% if user|HasPerm:"ipos.import_issuecard" %}true{% else %}false{% endif %}
jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='IssueCard';
jqOptions[g_activeTabID].pager='#id_pager_'+tblName[g_activeTabID];
jqOptions[g_activeTabID].sortname='cardstatus';
options[g_activeTabID].dlg_width=400;
options[g_activeTabID].dlg_height=380;

$(function(){
$("#"+g_activeTabID+" #search_id_user__PIN").val("{% trans 'personnel number, name' %}")
$("#"+g_activeTabID+" #searchbar").val("{% trans 'personnel number, name' %}")
$("#"+g_activeTabID+" #search_id_user__PIN").css('color','#CCCCCC')
$("#"+g_activeTabID+" #search_id_user__PIN").attr('role','defvalue')
var f = $('#search_menu')
f.validate({
rules:{'user__PIN':{string:true},
        'sys_card_no':{digits:true,maxlength:11,min:0},
        'cardno':{string:true,maxlength:20,min:0}
        }
});
var inputEl = $('#'+g_activeTabID+' #search_id_user__PIN')
         defVal[g_activeTabID] = inputEl.val();
	 
    	 inputEl.bind("focus",function(){
		             var _this = $(this);
				if (_this.val() == defVal[g_activeTabID]) {
				    _this.val('');
				    _this.css('color','#000000');
				    //_this.attr('role','disabled')
				}
		})
	inputEl.bind("blur",function(){
		        var _this = $(this);
			if (_this.val() == '') {
			    _this.val(defVal[g_activeTabID]);
			    _this.css('color','#CCCCCC');
			    _this.attr('role','defvalue')
			}
			else
			    _this.attr('role','cansearch')
		})
	inputEl.bind("keydown",function(event) {
			if (event.which == 13) {
			      var _this = $(this);
			       _this.attr('role','cansearch')
			 }
		})
	$("#"+g_activeTabID+" #queryButton").hide()
    $("#"+g_activeTabID+" #sear_area").hide()

        $("#"+g_activeTabID+" #id_newrec").click(function(event){
                processNewModel();
	});
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowDep_IssueCard();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13)
	    searchShowDep_IssueCard();
	});
	//$("#"+g_activeTabID+" #id_export").css('display','none');
	$("#"+g_activeTabID+" #id_third").html("");
    $("#"+g_activeTabID+" #id_search").click(function(){
		 var flag=$("#"+g_activeTabID+" #search_id_user__PIN").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var search_user_pin= $("#"+g_activeTabID+" #search_id_user__PIN").val();
	else
	    var search_user_pin=""
		var search_card_privage=$("#"+g_activeTabID+" #search_id_card_privage").val();
		var search_cardstatus=$("#"+g_activeTabID+" #search_id_cardstatus").val();
		var search_cardno=($("#"+g_activeTabID+" #search_id_cardno").val()).replace(/\b(0+)/gi,"");
        var search_sys_card_no=''
        {% if "POS_IC"|filter_config_option %}
		search_sys_card_no=$("#"+g_activeTabID+" #search_id_sys_card_no").val();
        {% endif %}
        var urlnew="";
            if (search_user_pin||search_card_privage||search_cardstatus||search_cardno||search_sys_card_no)
            {
                {% if "POS_IC"|filter_config_option %}
                    urlnew="q="+encodeURI(search_user_pin)+"&"+"card_privage="+search_card_privage+"&"+"cardstatus="+search_cardstatus+"&"+"cardno="+search_cardno+"&"+"sys_card_no="+search_sys_card_no
                {% else %}
                    urlnew="q="+encodeURI(search_user_pin)+"&"+"card_privage="+search_card_privage+"&"+"cardstatus="+search_cardstatus+"&"+"cardno="+search_cardno
                {% endif %}
            }
        var url="/ipos/data/IssueCard/?mod_name=ipos&"
		if (urlnew!="" &&  urlnew!=null)
			url+=urlnew;
		$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
		savecookie("search_urlstr",url);
	});
    $("#"+g_activeTabID+" #id_search_clean").click(function(){
        $("#"+g_activeTabID+" #search_id_user__PIN").val('');
        $("#"+g_activeTabID+" #search_id_card_privage").val('');
        $("#"+g_activeTabID+" #search_id_cardstatus").val('all');
        $("#"+g_activeTabID+" #search_id_cardno").val('');
        $("#"+g_activeTabID+" #search_id_sys_card_no").val('');
        {% if "ipos_IC"|filter_config_option %}
        $("#"+g_activeTabID+" #search_id_sys_card_no").val('');
        {% endif %}
        var url="/ipos/data/IssueCard/?mod_name=ipos&"
		$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
		savecookie("search_urlstr",url);
    });
	
    $("#"+g_activeTabID+" #id_issuecard_batch_supplement").click(function(){
			createNewDlg_issuecard_batch_supplement();
	});	
	
});

function change_value(money){
	$('#id_money').val(money)
}

function create_newdlg_overdue_card_renewal(datas) {
  var title = "{% trans 'Overdue card renewal' %}"
  if(datas.count>1){
    alert("{% trans 'Only one person is allowed!' %}");
    return ;
  } else{
    var block_html = ''
    + '<div>'
    +   "<label>{% trans 'Overdue date' %}</label>"
    +   "<input type='text' name='card_renewal_time'  id='id_card_renewal_time' style='width:80px;'>"
    + '</div>'
    // 创建选择薪资项的弹窗
    var dlg = $(block_html).dialog(
      {
        modal: true,
        resizable: false,
        width: 500,
        height: 200,
        title: title,
        close: function(){$(this).dialog("destroy")},
        buttons: [
          {
            id: "btnShowOK",
            text: '{% trans "save and return" %}',
            click: function(){
                var card_renewal_time = $("#id_card_renewal_time").val();
                var post_data = datas.ret +"&card_renewal_time="+card_renewal_time;
                var url_str = g_urls[g_activeTabID]+ '?action=overdue_card_renewal'+'&opname='+encodeURI(title);
                $.blockUI({title:title,theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Please wait...' %}<br /></h1>'});
                $.ajax({type: "POST",
                  url: url_str,
                  data:post_data,
                  dataType:"json",
                  success: function(retdata){
                    $.unblockUI();
                    if (retdata.ret==0){
                      reloadData();
                      $('#btnShowCancel').click();
                    }else{
                      alert(retdata.message);
                    }
                  },
                  error: function(){
                            $.unblockUI();
                            alert($.validator.format("{% trans 'Operating failed for {0} !' %}" ,options.title));
                          }
                  });
            }
          },
          {
            id: "btnShowCancel",
            text: '{% trans "Return" %}',
            click: function(){$(this).dialog("destroy")}
          }
        ]
      }
    )
    // 初始化时间
    $("#id_card_renewal_time").val(moment().startOf('day').format('YYYY-MM-DD'))
    //添加时间选择器
    $("#id_card_renewal_time").datepicker(datepickerOptions);
  }
}

function createNewDlg_issuecard_batch_supplement(){
                var title="{% trans 'Batch supplement' %}";
                createDlgdeptfor10('issuecard_batch_supplement',1)
                $('#dlg_for_query_issuecard_batch_supplement').dialog({title:title,
                        height:571,
                        buttons:[
						        {id:"btnShowInfo",text:"{% trans "Budget amount" %}",click:function(){total_count();}},
                                {id:"btnShowOK",text:"{% trans 'save and return' %}",
									click:function(){ 
										if(typeof beforePost_batch_supplement=="function")
											{if(beforePost_batch_supplement(this,"edit")==false) return;}
													SaveFormData(this,g_urls[g_activeTabID]+'?action=OpBatchSupplement' + '&opname=' + encodeURI(title),'edit',"IssueCard");
													$("#btnShowOK").attr("disabled", "disabled");
											}
								},
                                {id:"btnShowCancel",text:'{% trans "Return" %}',click:function(){$(this).dialog("destroy"); }}
                        ]
                });
                createDlgother_batch_supplement('issuecard_batch_supplement');
		
        }

 function createDlgother_batch_supplement(page){
            var html="<div id='id_form'><div class='module' style='position:relative;'>"
                +"<table id='id_batch_supplement'><tr>"
                +"<td style='vertical-align:top;color:black;'><div id='show_dept_emp_tree'></div></td></tr><tr><td style='vertical-align:top;'><div id='id_conditions'>"
                +"<form id='id_edit_form' method='POST'>"
                +"<table id='id_setField' style='margin-top: -20px;'>"
                        
						
						//充值金额
						html+="<tr><td>"
							   +"<label for='id_supplement_money' class='required3'><font color='white'>*</font>{% trans 'Recharge amount' %}</label></td>"
							   +"<td><input id='id_money' type='text' name='money' style='width:120px'/></td>"+
						   " <td><button type='button' class='supplement_money_btn m-btn  black rnd sm' onclick='change_value(50)'>50</button>"+
						   " <button type='button' class='supplement_money_btn m-btn  black rnd sm' onclick='change_value(100)'>100</button>"+
						   " <button type='button' class='supplement_money_btn m-btn  black rnd sm' onclick='change_value(200)'>200</button>"+
						   "</td></tr>"  
						

                        +"<tr><td></td></tr>"
                        +"<tr><td colspan='2'>"
                        +"<input type='hidden' id='id_userid' value='' name='UserID' />"
                        +"</td></tr>"
                        +"<tr><td colspan='2'><span id='id_error'></span></td>"
                +"</table></form></td>"
                +"</tr></table></div></div>";
        
            $("#dlg_other_body_"+page).html(html)
	
            $("#dlg_other_"+page).position({
                    my: "left top",
                    at: "left bottom",
                    of: "#dlg_dept_"+page
            });
			$("#dlg_other_"+page).css({"display": 'block',"top": '-671px',"left": "3px"})
        }
		
 function beforePost_batch_supplement(obj,actionName){

            f=$('#id_form').find("#id_edit_form")
            $(f).validate({
			rules: {
					"money": {required:true,min:0.1,max:9999,"maxlength":8,isMoney:true},
				}
			});
            if(!f.valid()) return false;

            if(actionName=="edit"){
                    var emp=getSelected_emp_ex("sel_issuecard_batch_supplement");
                    $("#id_userid",obj).val(emp);
                    if(emp.length==0){
                            $("#id_error",obj).css("display","block");
                            $("#id_error",obj).html("<ul class='errorlist'><li>{% trans 'Please select one or more employee!' %}</li></ul>");
                            return false
                    }
                    else{
                            $("#id_error",obj).css("display","none");
                            $("#id_userid",obj).val(emp);
                    }
                    
            }
                
        }		
		
function total_count()
        {		
		  var emp=getSelected_emp_ex("sel_issuecard_batch_supplement");
		  var sup_money = 0;
		  var emp_cnt = 0;	
		  if (emp.length!=0)
		  { 
			  emp_cnt = parseInt(emp.length);
			  if ($('#id_money').val())
			  {
			     sup_money=parseFloat($('#id_money').val())
			  }  
		  }  
		   
		  infohtml="{% trans 'Recharge staff:' %}"+ emp_cnt +" {% trans 'people' %}<br>{% trans 'Total amount of recharge:' %}"+parseFloat(emp_cnt*sup_money).toFixed(2)+" {% trans 'yuan' %}"
                        $("#id_error").css("display","block");
                        $("#id_error").html(infohtml).show();
        } 
		
		
		
function sureMoney(obj){
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d.]/g,"");
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g,"");
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g,".");
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        //校验金额最多两位小数
        obj.value = obj.value.replace(/([0-9]+\.[0-9]{2})[0-9]*/, "$1")
}

function searchShowDep_IssueCard(){
    var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
    var url="/ipos/data/batchtime/?q="+encodeURI(v)
	savecookie("search_urlstr",url);
    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}


function strOfData_IssueCard(data)
{
	return stripHtml(data.PIN)+" "+data.cardno
}

function process_dialog_IssueCard(obj,flag)
{
	  $(obj).dialog({resizable:false,modal:true})
}

function beforePost_IssueCard(obj,actionName)
{
}

function createOpCardDlg(url,action,title)
{
	var block_html="<div id='dlg_to_card'>"
					+           "<table width=100%>"
									+"<tr><th>"
									+"<label class='required' for='id_blance' style='font-size: 25px; font-weight: bold;'>{% trans 'Amount:' %}</label>"
									+"</th><td>"
									+"<input id='id_blance' class='required' type='text' name='blance' value='' style='height: 30px; font-size: 25px; width: 110px;' onkeyup='sureMoney(this)' onpaste='sureMoney(this)'>"
									+"</td>"
									+"</tr>"
					+            "</table>"
					+  "<div  id='id_error'></div>"
					+       "</div>"

	if (action=='OpUpdateCard')
	{
		var id = $("#id_grid_IssueCard").jqGrid('getGridParam','selarrrow');
		var pin = $("#id_grid_IssueCard").jqGrid("getCell",id[0],2)
		var name = $("#id_grid_IssueCard").jqGrid("getCell",id[0],3)
		var dname = $("#id_grid_IssueCard").jqGrid("getCell",id[0],4)
		var card = $("#id_grid_IssueCard").jqGrid("getCell",id[0],5)
		var cardtype = $("#id_grid_IssueCard").jqGrid("getCell",id[0],11).split('---')[1]
		$('#dlg_to_card #pin').val(pin)
		$('#dlg_to_card #ename').val(name)
		$('#dlg_to_card #dname').val(dname)
		$('#dlg_to_card #card').val(card)
		$('#dlg_to_card #id_itype').val(cardtype)
		var block_html=$('#dlg_to_card')
	}
	if (action=='OpChangeCard')
	{
            retdata=[]
	    	$.ajax({
		url:"/ipos/getData/?func=IssueCard&key="+url.ss[0],
		dataType:"json",
		type:"POST",
		async:false,
		cache:false,
		success:function(data){
		retdata=data
                    }
		 });
	    var block_html="<div id='dlg_to_card'>"
			    +"<table width=100%>"
			    +"<tr><th> <label for='id_cardno1' class='required'>{% trans 'Original card number' %}</label></th>"
                                +"<td><input id='id_cardno1'  type='text'  value='"+retdata[0].cardno+"' maxlength='19' name='cardno1' style='width:135px !important;' readonly disabled/></td>"
                                +"<th> <label for='id_cardno' class='required'>{% trans 'New card number' %}</label></th>"
                                +"<td><input id='id_cardno'  type='text'  value='' maxlength='19' name='cardno'  style='width:135px !important;'/></td></tr>"
                                {% if "ipos_wallets"|get_wallet_type %}
								+"<tr><th><label for='id_blance1' class='required'>{% trans 'Original cash wallet balance' %}</label></th>"
                                +"<td><input id='id_blance1'  type='text'  value='"+retdata[0].blance+"' maxlength='19' name='blance1' style='width:135px !important;' readonly disabled/></td>"
                                +"<th><label for='id_blance' class='required'>{% trans 'New Cash Wallet Amount' %}</label></th>"
                                +"<td><input id='id_blance'  type='text'  value='"+retdata[0].blance+"' maxlength='19' name='blance' style='width:135px !important;' disabled/></td></tr>"
								
								+"<tr><th><label for='id_allow_balance1' class='required'>{% trans 'Original subsidized wallet balance' %}</label></th>"
                                +"<td><input id='id_allow_balance1'  type='text'  value='"+retdata[0].allow_balance+"' maxlength='19' name='allow_balance1' style='width:135px !important;' readonly disabled/></td>"
                                +"<th><label for='id_allow_balance' class='required'>{% trans 'New subsidy wallet amount' %}</label></th>"
                                +"<td><input id='id_allow_balance'  type='text'  value='"+retdata[0].allow_balance+"' maxlength='19' name='allow_balance' style='width:135px !important;' disabled /></td></tr>"
								{% else %}
                                +"<tr><th><label for='id_blance1' class='required'>{% trans 'Original Card Balance' %}</label></th>"
                                +"<td><input id='id_blance1'  type='text'  value='"+retdata[0].blance+"' maxlength='19' name='blance' style='width:135px !important;' readonly disabled/></td>"
                                +"<th><label for='id_blance' class='required'>{% trans 'New Card Amount' %}</label></th>"
                                +"<td><input id='id_blance'  type='text'  value='"+retdata[0].blance+"' maxlength='19' name='blance' style='width:135px !important;' disabled/></td></tr>"
								{% endif %}
								
                                +"<tr><th><label for='id_card_cost1' class='required'>{% trans 'Return card cost' %}</label></th>"
                                +"<td><input id='id_card_cost1'  type='text'  value='"+retdata[0].card_cost+"' maxlength='19' name='card_cost1' style='width:135px !important;'/></td>"
                                +"<th><label for='id_card_cost' class='required'>{% trans 'New Card Cost' %}</label></th>"
                                +"<td><input id='id_card_cost'  type='text'  value='0.00' maxlength='19' name='card_cost' style='width:135px !important;'/></td></tr>"
			  
                                
                                +"<tr><th><label for='id_mng_cost' class='required'>{% trans 'New card management fee' %}</label></th>"
                                +"<td><input id='id_mng_cost'  type='text'  value='0.00' maxlength='19' name='mng_cost' style='width:135px !important;'/></td>"
                                +"<th><label for='id_Password' class='required'>{% trans 'New card excess password' %}</label></th>"
                                +"<td><input id='id_Password'  type='text'  value='"+retdata[0].pwd+"' maxlength='19' name='Password' style='width:135px !important;'/></td></tr>"
		    
			    +"</table>"
					+  "<div  id='id_error'></div>"
					+ "</div>"

	}
  
	if (action=='OpReimburse')
	{
  
	    {% if "ipos_wallets"|get_wallet_type %}
        var block_html="<div id='dlg_to_card'>"
	                 +"<table width=100% >"
	                 +"<tr>"
	                 +"<label class='required' for='id_blance' style='font-size: 25px; font-weight: bold;'>{% trans 'Refund amount:' %}</label>"
	                 +"</tr><tr><td>"
	                 +"<label id='ld_moneyA' style='font-size: 25px;font-weight: bold;'>{% trans 'cash:' %}</label>"
	                 +"<input id='id_blance' class='required' type='text' name='blance' value='0' style='height: 30px; font-size: 25px; width: 110px;' onkeyup='sureMoney(this)' onpaste='sureMoney(this)'>"
	                 +"</td><td>"
	                 +"<label id='ld_moneyB' style='font-size: 25px;font-weight: bold;'>{% trans 'subsidy:' %}</label>"
	                 +"<input id='id_blance_B' type='text' value='0' maxlength='19' name='blance_B' style='width:100px;!important;height: 30px;font-size: 25px;' onkeyup='sureMoney(this)' onpaste='sureMoney(this)' />"
	                 +"</td></tr></table>"
	                 +"<div  id='id_error'></div>"
	                 + "</div>"
                    
	    {%else%}
	    var block_html="<div id='dlg_to_card'>"
					+"<table width=100%>"
					+"<tr><th>"
					+"<label class='required' for='id_blance' style='font-size: 25px; font-weight: bold;'>{% trans 'Amount:' %}</label>"
					+"</th><td>"
					+"<input id='id_blance' class='required' type='text' name='blance' value='' style='height: 30px; font-size: 25px; width: 110px;' onkeyup='sureMoney(this)' onpaste='sureMoney(this)' >"
					+"</td>"
					+"</tr>"
					+"</table>"
					+"<div  id='id_error'></div>"
					+"</div>"
                 
	    {%endif%}      
	}    

	$(block_html).dialog({modal:true,
						    resizable:false,　
						  width: 500,
						  height:260,
						  title:title,
						  buttons:[{id:"btnShowOK",text:'{% trans "Submit" %}',
								  click:subdata},
								 {id:"btnShowCancel",text:'{% trans "Return" %}',click:function(){$(this).dialog("destroy"); }
								}],
						  close:function(){$(this).dialog("destroy"); }		
						})
	function subdata(){
		var blance = $("#id_blance").val();
        var card_cost = $('#id_card_cost1').val()
        var new_card_cost = $('#id_card_cost').val()
        var mng_cost = $('#id_mng_cost').val()

        if(card_cost<0 | card_cost>99.00){
            $("#id_error").html("<ul class='errorlist'><li>{% trans 'Return card cost' %}{% trans 'The input range is 0-99' %}</li></ul>").show();
			return false;
        }
        if(new_card_cost<0 | new_card_cost>99.00){
            $("#id_error").html("<ul class='errorlist'><li>{% trans 'New Card Cost' %}{% trans 'The input range is 0-99' %}</li></ul>").show();
			return false;
        }
        if(mng_cost<0 | mng_cost>99.00){
            $("#id_error").html("<ul class='errorlist'><li>{% trans 'New card management fee' %}{% trans 'The input range is 0-99' %}</li></ul>").show();
			return false;
        }

		if((action!='OpUpdateCard'&&action!='OpChangeCard'&&action!='OpReimburse')&&(blance==''||Number(blance)<=0)){
			$("#id_error").html("<ul class='errorlist'><li>{% trans 'The amount is incorrect' %}</li></ul>").show();
			return false;
		}if((action =='OpReimburse')&&(blance==''||(Number(blance)+Number($("#id_blance_B").val()))<=0)){
			$("#id_error").html("<ul class='errorlist'><li>{% trans 'The amount is incorrect' %}</li></ul>").show();
			return false;
		}else{
			var queryStr=url.ret
			if(action=='OpUpdateCard')
			{

			    var urlStr=g_urls[g_activeTabID]+ '?action='+action+'&cardtype='+$('#id_itype').val();
			
			}
			else if(action=='OpChangeCard')
			{
                if ($('#id_cardno').val()==''){
                    $("#id_error").html("<ul class='errorlist'><li>{% trans 'The new card number cannot be empty' %}</li></ul>").show();
                    return false
                }
                var reg = /^[0-9]*$/;
                if (!reg.test($('#id_cardno').val())) {
                    $("#id_error").html("<ul class='errorlist'><li>{% trans 'The new card number can only be a number' %}</li></ul>").show();
                    return false;
               }
			   {% if "ipos_wallets"|get_wallet_type %}
			   queryStr=url.ret+'&cardno='+$('#id_cardno').val()+'&blance='+$('#id_blance').val()+'&allow_balance='+$('#id_allow_balance').val()+'&card_cost='+$('#id_card_cost').val()+'&mng_cost='+$('#id_mng_cost').val()+'&Password='+$('#id_Password').val()+'&card_cost1='+$('#id_card_cost1').val()
			   {% else %}
			   queryStr=url.ret+'&cardno='+$('#id_cardno').val()+'&blance='+$('#id_blance').val()+'&card_cost='+$('#id_card_cost').val()+'&mng_cost='+$('#id_mng_cost').val()+'&Password='+$('#id_Password').val()+'&card_cost1='+$('#id_card_cost1').val()
			   {% endif %}
			   
			   var urlStr=g_urls[g_activeTabID]+ '?action='+action;
               
			   
			}
			else if (action=='OpReimburse'){
				 {% if "ipos_wallets"|get_wallet_type %}
                var moneyB=$("#id_blance_B").val();
                var wType=0
                if(blance==''||Number(blance)<0||moneyB==''||Number(moneyB)<0){
                    $("#id_error").html("<ul class='errorlist'><li>{% trans 'Amount should enter a positive number that is not empty' %}</li></ul>").show();
                    return false}				 
				 
				 
				var urlStr=g_urls[g_activeTabID]+ '?action='+action+'&money='+blance+'&moneyB='+moneyB;
				 {%else%}				
				var urlStr=g_urls[g_activeTabID]+ '?action='+action+'&money='+blance;
                {%endif%}
			}				
			else{
			    var urlStr=g_urls[g_activeTabID]+ '?action='+action+'&money='+blance;
			}
			 
			
			$.blockUI({title:title,theme: true ,baseZ:10000,message: '<h1><img src="/media/img/loading.gif" /> <br>{% trans 'Please wait...' %}<br /></h1>'});
			$.ajax({type: "POST",
				url: urlStr+'&opname='+encodeURI(title),
				data:queryStr,
				dataType:"json",
				success: function(retdata){
					$.unblockUI();
					alert(retdata.message);
					//$('#dlg_to_card').dialog('destroy')
                                         if (retdata.ret==0)
                                        {
                                            $('#btnShowCancel').click();
                                            reloadData();
                                        }
                                        else{
										$("#id_error").html('<ul class="errorlist"><li>'+retdata.message+'</li></ul>').show();
					//$("#"+g_activeTabID+" #id_error").html(retdata.message).css('color','RED')
					}
				},
				error: function(){$.unblockUI();alert($.validator.format("{% trans 'Operating failed for {0} !' %}" ,options.title));}
				});
		}
    }

    



}

function doAction_IssueCard(url,action)
{	
	if(url.count>1&&action!='OpUpdateCard')
	{
	    alert("{% trans 'Only one person is allowed!' %}");
	    return false;
	}    
    if (action=='OpSupplement'){
	createOpCardDlg(url,action,'{% trans 'Recharge' %}')}
    else if (action=='OpReimburse')
	createOpCardDlg(url,action,'{% trans 'Refund' %}')
    else if (action=='OpUpdateCard')
	createOpCardDlg(url,action,'{% trans 'Card data modification' %}')
    else if (action=='OpChangeCard')
    {
	createOpCardDlg(url,action,'{% trans 'Change card' %}')
    }
}


{% endblock %}

{% block newrec %}
{% endblock %}
{% block aDelete %}
{% endblock %}
{% block extractButton %}
    {% if "POS_ID"|filter_config_option %}
        {% if user|HasPerm:"ipos.issuecard_supplement" %}
	<LI id="id_issuecard_supplement"   onclick="batchOp(function(url){doAction_IssueCard(url,'OpSupplement')},undefined,'{% trans "Recharge" %}');"><SPAN class="icon iconfont icon-chongzhi"></SPAN>{% trans "Recharge" %}</LI>
        {% endif %}
    {% endif %}
    {% if "POS_ID"|filter_config_option %}
       {% if user|HasPerm:"ipos.issuecard_reimburse" %}
	<LI id="id_IssueCard_Reimburse"   onclick="batchOp(function(url){doAction_IssueCard(url,'OpReimburse')},undefined,'{% trans "Refund" %}');"><SPAN  class="icon iconfont icon-tuikuan"></SPAN>{% trans "Refund" %}</LI>
        {% endif %}
    {% endif %}

    {% if user|HasPerm:"ipos.issuecard_oplosecard" %}
	<LI id="id_LoseCard" onclick="batchOp('?action=OpLoseCard',itemCanBeDelete,'{% trans "loss" %}');"><SPAN class="icon iconfont icon-guashi"></SPAN>{% trans "loss" %}</LI>
    {% endif %}
    {% if user|HasPerm:"ipos.issuecard_oprevertcard" %}
	<LI id="id_RevertCard" onclick="batchOp('?action=OprevertCard',itemCanBeDelete,'{% trans "Solutions Hanging" %}');"><SPAN class="icon iconfont icon-jiegua"></SPAN>{% trans "Solutions Hanging" %}</LI>
    {% endif %}
    {% if user|HasPerm:"ipos.issuecard_cancelmanagecard" %}
	<LI id="id_CancelManageCard" onclick="batchOp('?action=CancelManageCard',itemCanBeDelete,'{% trans "Logout Management Card" %}');"><SPAN class="icon iconfont icon-zhuxiaoguanliqia"></SPAN>{% trans "Logout Management Card" %}</LI>
    {% endif %}
    

    {% if "POS_ID"|filter_config_option %}
        {% if user|HasPerm:"ipos.issuecard_nocardretirecard" %}
        <LI id="id_NoCardRetireCard" onclick="batchOp('?action=NoCardRetireCard',itemCanBeDelete,'{% trans "No Card Retirement" %}');"><SPAN class="icon iconfont icon-wuqiatuiqia"></SPAN>{% trans "No Card Retirement" %}</LI>
		<LI id="id_ChangeCard"   onclick="batchOp(function(url){doAction_IssueCard(url,'OpChangeCard')},undefined,'{% trans "Change card" %}');"><SPAN class="icon iconfont icon-huanka"></SPAN>{% trans "Change card" %}</LI>
	{%endif%}


    {%else%}
        {% if user|HasPerm:"ipos.issuecard_nocardretirecard" %}
	<LI id="id_NoCardRetireCard" onclick="batchOp('?action=NoCardRetireCard',itemCanBeDelete,'{% trans "No Card Retirement" %}');"><SPAN class="icon iconfont icon-wuqiatuiqia"></SPAN>{% trans "No Card Retirement" %}</LI>
	{%endif%}
    
    {% endif %}


    {% if "POS_ID"|filter_config_option %}
      {% if user|HasPerm:"ipos.issuecard_updatecard" %}
      <LI id="id_ModifyCard"   onclick="batchOp(function(url){doAction_IssueCard(url,'OpUpdateCard')},undefined,'{% trans "Card data modification" %}');"><SPAN  class="icon iconfont icon-xiugai"></SPAN>{% trans "Card data modification" %}</LI>
      {% endif %}
      {% if user|HasPerm:"ipos.issuecard_batch_supplement" %}
      <LI id="id_issuecard_batch_supplement" ><SPAN class="icon iconfont icon-xinzeng"></SPAN>{% trans "Batch supplement" %}</LI>
      {% endif %}
      {% if user|HasPerm:"ipos.overdue_card_renewal" %}
        <LI id="id_overdue_card_renewal"
            onclick="batchOp(function(datas){create_newdlg_overdue_card_renewal(datas)},undefined,'{% trans "Overdue card renewal" %}');">
            <SPAN class="icon iconfont icon-chongzhi"></SPAN>{% trans "Overdue card renewal" %}
        </LI>
      {% endif %}
    {% endif %}

    {% if "POS_ID"|filter_config_option %}
        {% if user.is_superuser %}
        	{% if "id_card_account_check"|get_params:request == '1' %}
		    <LI id="id_checkCardAccount" onclick="batchOp('?action=OpCheckCardAccount',itemCanBeDelete,'{% trans "Card account check" %}');" ><SPAN class="icon iconfont icon-xinzeng"></SPAN>{% trans "Card account check" %}</LI>
		    {% endif %}
        {% endif %}
    {% endif %}



{% endblock %}
{% block otherQuery %}
    <form id="search_menu">

	<span style='float:left;'>
        <span>
			<label  >{% trans 'personnel' %}</label>
			<input type='text' name='user__PIN'  id='search_id_user__PIN' style='width:80px;'>
            <label  >{% trans 'card type' %}</label>
			<select name='' id='search_id_card_privage'><option value=''>--------</option><option value='0'>{% trans 'regular card' %}</option><option value='1'>{% trans 'management card' %}</option><option value='2'>{% trans 'Operation card' %}</option></select>
            <label  >{% trans 'card status' %}</label>
			<select name='' id='search_id_cardstatus'><option value='all'>--------</option><option value='1'>{% trans 'effective' %}</option><option value='3'>{% trans 'loss' %}</option><option value='4'>{% trans 'expired' %}</option>
                <option value='999'>{% trans 'Logout' %}</option><option value='6'>{% trans 'invalid' %}</option><option value='9'>{% trans 'inactivated' %}</option></select>
			<label  >{% trans 'card number' %}</label>
			<input type='text' name='cardno'  id='search_id_cardno' style='width:100px;'>
            {% if "POS_IC"|filter_config_option %}
            <label  >{% trans 'card account' %}</label>
			<input type='text' name='sys_card_no'  id='search_id_sys_card_no' style='width:120px;'>
            {% endif %}
		 </span>
         <span id='id_search' ><a class='m-btn  zkgreen rnd mini'>{% trans 'Query' %}</a></span>
         <span id='id_search_clean' ><a class='m-btn  zkgreen rnd mini'>{% trans 'clear' %}</a></span>
	</span>
            </form>

    <div id='dlg_to_card' style='display: none;'><table width=100%><tr><td>{{form.itype.as_widget}}</td></tr></table>	</div>

{% endblock %}
