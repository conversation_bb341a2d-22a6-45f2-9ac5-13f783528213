{% extends "admin/change_form.html" %}
{% load i18n %}

{% block after_field_sets %}

<p>{% trans "First, enter a username and password. Then, you&#39;ll be able to edit more user options." %}</p>

<fieldset class="module aligned">

<div class="form-row">
  {{ form.username.html_error_list }}
  <label for="id_username" class="required">{% trans 'Username' %}:</label> {{ form.username }}
  <p class="help">{{ username_help_text }}</p>
</div>

<div class="form-row">
  {{ form.password1.html_error_list }}
  <label for="id_password1" class="required">{% trans 'Password' %}:</label> {{ form.password1 }}
</div>

<div class="form-row">
  {{ form.password2.html_error_list }}
  <label for="id_password2" class="required">{% trans 'Password (again)' %}:</label> {{ form.password2 }}
  <p class="help">{% trans 'Enter the same password as above, for verification.' %}</p>
</div>

</fieldset>
{% endblock %}
