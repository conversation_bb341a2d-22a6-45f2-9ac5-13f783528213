# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-05-07 19:17
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0018_auto_20190226_2132'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='iccard',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'ordering': ('id',), 'verbose_name': 'Card Information', 'verbose_name_plural': 'Card Information'},
        ),
        migrations.AlterModelOptions(
            name='issuecard',
            options={'default_permissions': ('browse',), 'permissions': (('issuecard_issuecard', 'issuecard'), ('issuecard_oplosecard', 'oplosecard'), ('issuecard_oprevertcard', 'oprevertcard'), ('issuecard_cancelmanagecard', 'cancelmanagecard'), ('issuecard_nocardretirecard', 'nocardretirecard'), ('issuecard_supplement', 'supplement'), ('issuecard_reimburse', 'reimburse'), ('issuecard_retreatcard', 'retreatcard'), ('issuecard_updatecard', 'updatecard'), ('issuecard_initcard', 'initcard'), ('issuecard_batch_supplement', 'batch_supplement')), 'verbose_name': 'Card Form', 'verbose_name_plural': 'Card Form'},
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='verifytype',
            field=models.IntegerField(blank=True, choices=[(0, 'Card mode'), (1, 'Card mode'), (2, 'Qrcode Mode'), (3, 'Finger mode'), (4, 'Face mode'), (5, 'LightFace mode')], null=True, verbose_name='verifytype'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='verifytype',
            field=models.IntegerField(blank=True, choices=[(0, 'Card mode'), (1, 'Card mode'), (2, 'Qrcode Mode'), (3, 'Finger mode'), (4, 'Face mode'), (5, 'LightFace mode')], null=True, verbose_name='verifytype'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='cardno',
            field=models.CharField(blank=True, db_index=True, max_length=20, null=True, verbose_name='card number'),
        ),
    ]
