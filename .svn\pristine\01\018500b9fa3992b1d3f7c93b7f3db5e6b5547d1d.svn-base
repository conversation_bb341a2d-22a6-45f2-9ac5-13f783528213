#coding=utf-8
from django.utils.translation import gettext_lazy as _
#mport datetime




def OpName(op):
    OPNAMES = {
        0: _("start up"),
        1: _("shutdown"),
        2: _("validation failure"),
        3: _("alarm"),
        4: _("enter the menu"),
        5: _("change settings"),
        6: _("registration fingerprint"),
        7: _("registration password"),
        8: _("card registration"),
        9: _("delete User"),
        10: _("delete fingerprints"),
        11: _("delete the password"),
        12: _("delete RF card"),
        13: _("remove data"),
        14: _("MF create cards"),
        15: _("MF registration cards"),
        16: _("MF registration cards"),
        17: _("MF registration card deleted"),
        18: _("MF clearance card content"),
        19: _("moved to the registration card data"),
        20: _("the data in the card copied to the machine"),
        21: _("set time"),
        22: _("restore factory settings"),
        23: _(u"Delete Record"),
        24: _("remove administrator rights"),
        25: _("group set up to amend Access"),
        26: _("modify user access control settings"),
        27: _("access time to amend paragraph"),
        28: _("amend unlock Portfolio"),
        29: _("unlock"),
        30: _("registration of new users"),
        31: _("fingerprint attribute changes"),
        32: _("stress alarm"),
        34: _(u"anti-submarine"),
        35: _(u"Delete attendance photos"),
        36: _(u"Modify user information"),
        37: _(u"Holiday operation"),
        38: _(u"Restore data"),
        39: _(u"Backup data"),
        40: _(u"U disk upload"),
        41: _(u"U disk download"),
        42: _(u"U disk attendance record encryption"),
        43: _(u"Delete record after successful download of U disk"),
        68: _(u"Registered User Photo"),
        69: _(u"Modify User Photo"),
        70: _(u"Modify User Name"),
        71: _(u"Modify User Permissions"),
        76: _(u"Modify Network Settings IP"),
        77: _(u"Modify Network Settings Mask"),
        78: _(u"Modify Network Settings Gateway"),
        79: _(u"Modify Network Settings DNS"),
        80: _(u"Modify Connection Settings Password"),
        81: _(u"Modify Connection Settings Device ID"),
        82: _(u"Modify cloud server address"),
        83: _(u"Modify Cloud Server Port"),
        87: _(u"Modify the attendance parameter flag"),
        88: _(u"Modify face parameter flag"),
        89: _(u"Modify fingerprint parameter flag"),
        90: _(u"Modify the palm print parameter mark"),
        91: _(u"Modify the palm print parameter mark"),
        92: _(u"u disk upgrade logo"),
        100: _(u"Modify RF card information"),
        101: _(u"Register face"),
        102: _(u"Modify personnel permission"),
        103: _(u"Delete personnel permission"),
        104: _(u"Add personnel permission"),
        105: _(u"Delete access control record"),
        106: _(u"Delete face"),
        107: _(u"Delete person photos"),
        108: _(u"modify parameters"),
        109: _(u"Select WIFISSID"),
        110: _(u"Proxy enable"),
        111: _(u"Proxyip modify"),
        112: _(u"Proxy port modification"),
        113: _(u"Modify personnel password"),
        114: _(u"Modify face information"),
        115: _(u"Change the password of the operator"),
        116: _(u"Restore access control settings"),
        117: _(u"Operator password input error"),
        118: _(u"Operator password lock"),
        120: _(u"Modify the data length of the logic card"),
        121: _(u"Register finger vein"),
        122: _(u"Modify finger vein"),
        123: _(u"Delete finger vein"),
        124: _(u"Register palmprint"),
        125: _(u"Modify palmprint"),
        126: _(u"Delete palmprint")
    }
    try:
        return u'%s(%s)'%(OPNAMES[op],op)
    except:
        return op and "%s"%op or ""

def AlarmName(obj):
    ALARMNAMES={
        50:_("Door Close Detected"),
        51:_("Door Open Detected"),
        55:_("Machine Been Broken"),
        53:_("Out Door Button"),
        54:_("Door Broken Accidentally"),
        58:_("Try Invalid Verification"),
        59:_("Force"),
        60:_(u"Device offline"),
        4:_(u"door open"),
        5:_(u"door closed"),
        65535:_("Alarm Cancelled"),
    }
    try:
        return u'%s'%ALARMNAMES[obj]
    except:
        return obj and "%s"%obj or ""


# def CMDName(cmd_code):
#     CMDNAMES={
#         0:_(u'success'),
#         -1: _(u'Parameter error or number is too long'),
#         -2: _(u'Photo data size does not match'),
#         -3: _(u'Reading and writing error'),
#         -9: _(u'Template length does not match'),
#         -10: _(u'User does not exist'),
#         -11: _(u'Template format error'),
#         -12: _(u'Illegal template'),
#         -1001: _(u'Capacity is full'),
#         -1002: _(u'Device not upgraded BS'),
#         -1003: _(u'Command timeout'),
#         -1004: _(u'Data and device configuration are inconsistent'),
#         -1005: _(u'Device is busy'),
#         -1006: _(u'The data is too long'),
#         -1007: _(u'Memory error'),
#         -1008: _(u'Get server data error'),
#         -1100: _(u'invalid command')
#     }
#     if cmd_code is None:
#         return ''
#     elif cmd_code>=0:
#         return '%s(%s)'%(cmd_code,CMDNAMES[0])
#     else:
#         try:
#             return '%s(%s)'%(cmd_code,CMDNAMES[cmd_code])
#         except:
#             return '%s' % (cmd_code)




