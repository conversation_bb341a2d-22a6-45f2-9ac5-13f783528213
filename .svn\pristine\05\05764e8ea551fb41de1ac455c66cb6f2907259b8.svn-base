/* Portuguese initialisation for the jQuery UI date picker plugin. */
jQuery(function($){
	$.datepicker.regional['pt'] = {
		closeText: '<PERSON><PERSON><PERSON>',
		prevText: '&#x3C;Anterior',
		nextText: '<PERSON><PERSON><PERSON>',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Agosto','Setembro','Outub<PERSON>','Novembro','Dezembro'],
		monthNamesShort: ['<PERSON>','Fev','<PERSON>','Abr','<PERSON>','<PERSON>',
		'Jul','<PERSON><PERSON>','<PERSON>','Out','Nov','Dez'],
		dayNames: ['<PERSON>','<PERSON>-feira','Ter<PERSON>-feira','Quarta-feira','Quinta-feira','<PERSON>ta-feira','Sábad<PERSON>'],
		dayNamesShort: ['<PERSON>','Seg','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','Seg','<PERSON><PERSON>','<PERSON>ua','<PERSON>ui','<PERSON>','<PERSON><PERSON><PERSON>'],
		weekHeader: 'Sem',
		dateFormat: 'dd/mm/yy',
		firstDay: 0,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['pt']);
});
