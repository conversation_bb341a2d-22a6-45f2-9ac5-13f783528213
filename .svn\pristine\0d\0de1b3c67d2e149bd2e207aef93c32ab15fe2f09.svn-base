#!/usr/bin/env python
# coding=utf-8
import datetime
import re
from decimal import Decimal
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _
from mysite.iclock.models import employee

# 项类型
ITEM_TYPE = (
    (0, _('Addition item')),  # 加项
    (1, _('Subtraction item')),  # 减项
)
# 数据来源
ITEM_DATA_SOURCE = (
    (0, _('Import')),  # 导入
    (1, _('Fixed'))  # 固定
)
# 是否显示
ITEM_STATUS = (
    (0, _('Show')),  # 显示
    (1, _('Hide'))  # 隐藏
)

TEMPLATE_DELTAG = (
    (0, _('effective')),  # valid使用中
    (1, _('invalid'))  # invalid已删除
)

EMAIL_SEND_STATUS = (
    (0, _('Unsent')),  # 待发送
    (1, _('Has been sent'))  # 已发送
)


class Item(models.Model):
    """
    公共薪资项
    formula 计算公式
    data_source数据来源：0-导入项 1-固定计算项
    type类型：0-加项 1-减项
    status状态：0-显示 1-隐藏
    """
    code = models.CharField(max_length=10, null=False, blank=False, editable=True, verbose_name=_(u'Numbering'))
    name = models.CharField(max_length=80, null=True, blank=True, editable=True, verbose_name=_(u'ItemName'))
    type = models.IntegerField(null=True, default=0, choices=ITEM_TYPE, editable=True, verbose_name=_(u'Types of'))
    data_source = models.IntegerField(null=True, default=0, choices=ITEM_DATA_SOURCE, editable=True,
                                      verbose_name=_(u'Data Source'))
    formula = models.TextField(max_length=100, null=True, blank=True, editable=False, verbose_name=_(u'Formula'))
    formula_str = models.TextField(max_length=100, null=True, blank=True, editable=True, verbose_name=_(u'Formula'))
    formula_variable = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, editable=True,
                                           verbose_name='')

    status = models.IntegerField(null=True, default=0, choices=ITEM_STATUS, editable=False, verbose_name=_(u'Display'))
    remark = models.TextField(max_length=100, null=True, blank=True, editable=True, verbose_name=_(u'Remark'))

    def __str__(self):
        return u"%s%s%s" % (self.code, '', self.name)

    class Admin:
        @staticmethod
        def initial_data():
            """
            建表成功后，空表添加50个薪资项，默认无效，编号不可修改，前端不可新增删除数据，可修改status
            """
            if Item.objects.all().count() == 0:
                Item(code='C1', name=u"%s" % (_('Base salary')), formula='(a1)', formula_str=u"%s" % (_('Base salary')),
                     formula_variable=None, type=0,
                     data_source=1, status=1).save()
                Item(code='C2', name=u"%s" % (_('Post salary')), formula='(a2)', formula_str=u"%s" % (_('Post salary')),
                     formula_variable=None, type=0,
                     data_source=1, status=1).save()
                Item(code='C3', name=u"%s" % (_('Post subsidy')), formula='(a3)',
                     formula_str=u"%s" % (_('Post subsidy')), formula_variable=None, type=0,
                     data_source=1, status=1).save()
                Item(code='C4', name=u"%s" % (_('Internship salary')), formula='(a4)',
                     formula_str=u"%s" % (_('Internship salary')), formula_variable=None, type=0,
                     data_source=1, status=1).save()
                Item(code='C5', name=u"%s" % (_("Internship subsidy")), formula='(a5)',
                     formula_str=u"%s" % (_('Internship subsidy')), formula_variable=None, type=0,
                     data_source=1, status=1).save()
                Item(code='C6', name=u"%s" % (_("Meal allowance")), formula='(b2)*%s',
                     formula_str=u"%s" % (_('real*%s')), formula_variable=15, type=0,
                     data_source=1, status=1).save()
                Item(code='C7', name=u"%s" % (_("Seniority Award")), formula='(b6)*%s',
                     formula_str=u"%s" % (_('working age*%s')), formula_variable=100, type=0,
                     data_source=1, status=1).save()
                Item(code='C8', name=u"%s" % (_("Full attendence reward")), formula='IF(((b2)>=(b1)),%s,0)',
                     formula_str=u"%s" % (_(
                         'When the actual arrival > = expected arrival, the total attendance award = %s, otherwise the total attendance award = 0')),
                     formula_variable=50, type=0, data_source=1, status=1).save()
                Item(code='C9', name=u"%s" % (_("Deduction for absenteeism")), formula='(b5)*%s',
                     formula_str=u"%s" % (_('Absent*%s')), formula_variable=100, type=1,
                     data_source=1, status=1).save()
                Item(code='C10', name=u"%s" % (_("Deduction for late")), formula='(b3)*%s',
                     formula_str=u"%s" % (_('Late*%s')), formula_variable=50, type=1,
                     data_source=1, status=1).save()
                Item(code='C11', name=u"%s" % (_("Deduction for early")), formula='(b4)*%s',
                     formula_str=u"%s" % (_('Early*%s')), formula_variable=50, type=1,
                     data_source=1, status=1).save()
                Item(code='C12', name=u"%s" % (_("Communication subsidy")), formula='', formula_str='',
                     formula_variable=None, type=0, data_source=0,
                     status=1).save()
                Item(code='C13', name=u"%s" % (_("Vehicle subsidy")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C14', name=u"%s" % (_("High temperature subsidy")), formula='', formula_str='',
                     formula_variable=None, type=0, data_source=0,
                     status=1).save()
                Item(code='C15', name=u"%s" % (_("Achievement bonus")), formula='', formula_str='',
                     formula_variable=None, type=0, data_source=0,
                     status=1).save()
                Item(code='C16', name=u"%s" % (_("Commission bonus")), formula='', formula_str='',
                     formula_variable=None, type=0, data_source=0,
                     status=1).save()
                Item(code='C17', name=u"%s" % (_("Other Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C18', name=u"%s" % (_("Other deduction")), formula='', formula_str='', formula_variable=None,
                     type=1, data_source=0,
                     status=1).save()
                Item(code='C19', name=u"%s" % (_("Endowment insurance")), formula='', formula_str='',
                     formula_variable=None, type=1, data_source=0,
                     status=1).save()
                Item(code='C20', name=u"%s" % (_("Medical insurance")), formula='', formula_str='',
                     formula_variable=None, type=1, data_source=0,
                     status=1).save()
                Item(code='C21', name=u"%s" % (_("Unemployment insurance")), formula='', formula_str='',
                     formula_variable=None, type=1, data_source=0,
                     status=1).save()
                Item(code='C22', name=u"%s" % (_("Other insurance")), formula='', formula_str='', formula_variable=None,
                     type=1, data_source=0,
                     status=1).save()
                Item(code='C23', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C24', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C25', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C26', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C27', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C28', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C29', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C30', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C31', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C32', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C33', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C34', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C35', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C36', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C37', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C38', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C39', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C40', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C41', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C42', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C43', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C44', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C45', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C46', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C47', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C48', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C49', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()
                Item(code='C50', name=u"%s" % (_("Addition")), formula='', formula_str='', formula_variable=None,
                     type=0, data_source=0,
                     status=1).save()

    class Meta:
        db_table = 'payroll_item'
        unique_together = ["code"]
        verbose_name = _('Salary item')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'change', 'delete', 'add')

    @staticmethod
    def get_obj_by_code(code):
        # 根据code获取薪资项
        if code is None:
            return None
        code = code.upper()
        try:
            d = Item.objects.get(code=code)
        except Exception as e:
            d = None
        return d

    @staticmethod
    def colModels():
        ret = [
            {'name': 'id', 'hidden': True, 'width': 60},
            {'name': 'code', 'width': 120, 'label': u"%s" % (_('Numbering'))},
            {'name': 'name', 'width': 100, 'label': u"%s" % (_('ItemName'))},
            {'name': 'type', 'width': 100, 'label': u"%s" % (_('Types of'))},
            {'name': 'formula_display', 'sortable': False, 'hidden': False, 'width': 300,
             'label': u"%s" % (_('formula'))},
            {'name': 'data_source', 'sortable': False, 'width': 100, 'label': u"%s" % (_('Data Source'))},
            {'name': 'remark', 'sortable': False, 'width': 120, 'label': u"%s" % (_('Remark'))}
        ]
        return ret

    def formula_display(self):
        if self.data_source == 0:
            return ''
        if self.formula_str.find('%s') == -1:
            if self.formula_str:
                return self.formula_str
            else:
                return self.formula_variable or 0
        else:
            return self.formula_str % str(self.formula_variable or 0)


class SalaryTemplate(models.Model):
    """
    薪资模板
    deltag 删除标记，已删除为无效
    """
    code = models.CharField(max_length=10, null=False, blank=False, editable=True, verbose_name=_(u'Numbering'))
    name = models.CharField(max_length=80, null=False, blank=False, editable=True, verbose_name=_(u'ItemName'))
    deltag = models.IntegerField(null=True, default=0, choices=TEMPLATE_DELTAG, editable=False)

    def __unicode__(self):
        return u'%s %s' % (self.code, self.name)

    def __str__(self):
        return u'%s %s' % (self.code, self.name)

    class Meta:
        db_table = 'payroll_salary_template'
        unique_together = ["code"]
        verbose_name = _('Salary template')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('set_employee_to_salarytemplate', 'add people'),
            ('view_employee_salarytemplate', 'view people'),
        )

    @staticmethod
    def colModels():
        ret = [
            {'name': 'id', 'hidden': True},
            {'name': 'code', 'width': 120, 'label': u"%s" % (_('template number'))},
            {'name': 'name', 'width': 100, 'label': u"%s" % (_('template name'))},
            {'name': 'formula', 'sortable': False, 'width': 500, 'label': u"%s" % (_('formula'))},
            {'name': 'action', 'sortable': False, 'width': 200, 'label': u"%s" % (_(u'operating'))}
        ]
        return ret

    def formula(self):
        """
        获取模板组成项，根据项和项类型拼接成公式
        """
        code_list = SalaryTemplateItem.objects.filter(salary_template=self).values_list('item_code', flat=True)
        item_objs = Item.objects.filter(code__in=code_list).order_by('type').values('name', 'type')
        formula = ''
        for obj in item_objs:
            if obj['type'] == 0:
                formula += ' + '
            elif obj['type'] == 1:
                formula += ' - '
            formula += obj['name']
        return formula


class SalaryTemplateItem(models.Model):
    """
    存放薪资模板组成项
    项可能包含人员项和公共薪资项项，故存code不存外键（数据不同表）
    """
    salary_template = models.ForeignKey(SalaryTemplate, verbose_name=_('Salary template'), null=False, blank=False, on_delete=models.CASCADE)
    item_code = models.CharField(max_length=10, null=False, blank=False, editable=True, verbose_name=_(u'Numbering'))

    class Meta:
        db_table = 'payroll_salary_template_item'
        verbose_name = _('Salary components')
        verbose_name_plural = verbose_name


FIXED_RAISE_STATUS = (
    (0, _('No')),  # 未定薪
    (1, _('Yes'))  # 已定薪
)


class EmployeeItem(models.Model):
    """
    人员薪资项
    """
    employee = models.OneToOneField(employee, null=False, blank=False, editable=True, verbose_name=_(u'Employee'), on_delete=models.CASCADE)
    salary_template = models.ForeignKey(SalaryTemplate, null=True, blank=True, editable=True, verbose_name=_(u'Salary template'), on_delete=models.CASCADE)
    change_time = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'Date of salary adjustment'))
    fixed_raise_status = models.IntegerField(null=True, default=0, choices=FIXED_RAISE_STATUS, editable=False,
                                             verbose_name=_(u'Is fixed salary'))
    a1 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Base salary'))
    a2 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Post salary'))
    a3 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Post subsidy'))
    a4 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Internship salary'))
    a5 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Internship subsidy'))

    def __str__(self):
        return u"%s-%s" % (self.employee.PIN, self.employee.EName or '')

    @staticmethod
    def set_employee_salary_template(employee_obj, salary_template_obj):
        """
        设置人员薪资模板
        人员已设置薪资项时，若薪资项无变化则直接返回成功
        人员不存在薪资项数据时直接创建
        """
        try:
            employee_item = EmployeeItem.objects.get(employee=employee_obj)
            if salary_template_obj == employee_item.salary_template:
                return True
        except EmployeeItem.DoesNotExist:
            employee_item = EmployeeItem(employee=employee_obj)
        employee_item.salary_template = salary_template_obj
        employee_item.change_time = datetime.datetime.now()
        employee_item.save()
        return True

    @staticmethod
    def colModels():
        ret = [
            {'name': 'id', 'hidden': True},
            {'name': 'pei_id', 'hidden': True},
            {'name': 'pin', 'sortable': True, 'width': 80, 'label': u"%s" % (_(u'personnel number'))},
            {'name': 'user_name', 'sortable': True, 'width': 100, 'label': u"%s" % (_(u'Name'))},
            {'name': 'dept_name', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Department name'))},
            {'name': 'position', 'sortable': False, 'width': 60, 'label': u"%s" % (_(u'Job'))},
            {'name': 'fixed_raise_status', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Is fixed salary'))},
            {'name': 'real_fixed_raise_status', 'hidden': True},
            {'name': 'salary_template_name', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Salary template'))},
            {'name': 'salary_template_code', 'sortable': False, 'width': 100,
             'label': u"%s" % (_(u'Salary template code'))},
            {'name': 'change_time', 'sortable': False, 'width': 100,
             'label': u"%s" % (_(u'Date of salary adjustment'))},
            {'name': 'a1', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Base salary'))},
            {'name': 'a2', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Post salary'))},
            {'name': 'a3', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Post subsidy'))},
            {'name': 'a4', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Internship salary'))},
            {'name': 'a5', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Internship subsidy'))},
            {'name': 'fixed_raise_salary_detail', 'sortable': False, 'width': 100,
             'label': u"%s" % (_(u'Salary adjustment details'))},
        ]
        return ret

    class Meta:
        db_table = 'payroll_employee_item'
        verbose_name = _('Employee salary item')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'export')
        permissions = (
            ('add_employeeitem', 'Fixed salary'),
            ('change_employeeitem', 'Adjusted salary'),
            ('set_salary_template_salary_employeeitem', 'Set salary template'),
            ('browser_employeeitemlog', 'Salary adjustment log'),
        )


class EmployeeItemLog(models.Model):
    """
    定调薪日志
    """
    employee = models.ForeignKey(employee, null=False, blank=False, editable=True, verbose_name=_(u'Employee'), on_delete=models.CASCADE)
    salary_template = models.ForeignKey(SalaryTemplate, null=True, blank=True, editable=True, verbose_name=_(u'Salary template'), on_delete=models.CASCADE)
    change_time = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'Date of salary adjustment'))
    a1 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Base salary'))
    a2 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Post salary'))
    a3 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Post subsidy'))
    a4 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Internship salary'))
    a5 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Internship subsidy'))

    @staticmethod
    def colModels():
        ret = [
            {'name': 'id', 'hidden': True},
            {'name': 'pin', 'sortable': True, 'width': 80, 'label': u"%s" % (_(u'personnel number'))},
            {'name': 'user_name', 'sortable': True, 'width': 100, 'label': u"%s" % (_(u'Name'))},
            {'name': 'dept_name', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Department name'))},
            {'name': 'position', 'sortable': False, 'width': 60, 'label': u"%s" % (_(u'Job'))},
            {'name': 'salary_template_name', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Salary template'))},
            {'name': 'salary_template_code', 'sortable': False, 'width': 100,
             'label': u"%s" % (_(u'Salary template code'))},
            {'name': 'change_time', 'sortable': False, 'width': 100,
             'label': u"%s" % (_(u'Date of salary adjustment'))},
            {'name': 'a1', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Base salary'))},
            {'name': 'a2', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Post salary'))},
            {'name': 'a3', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Post subsidy'))},
            {'name': 'a4', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Internship salary'))},
            {'name': 'a5', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Internship subsidy'))},
        ]
        return ret

    class Admin:
        search_fields = ['employee__PIN', 'employee__EName']

    class Meta:
        db_table = 'payroll_employee_item_log'
        # unique_together = ["employee"]
        verbose_name = _('Salary adjustment log')
        verbose_name_plural = verbose_name


class OperateLog(models.Model):
    """
    薪资模块管理员操作日志
    """
    time = models.DateTimeField(_('time'), null=False, blank=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('administrator'), null=True, blank=True, db_column='user_id', on_delete=models.CASCADE)
    model = models.CharField(_('data'), max_length=40, null=True, blank=True)
    operate = models.CharField(_('operation'), max_length=40, default=_("Modify"), null=False, blank=False)
    object = models.CharField(_('object'), max_length=100, null=True, blank=True)
    count = models.IntegerField(_('amount'), default=1, null=False, blank=False)
    ip = models.CharField(_('Login IP'), max_length=100, null=True, blank=True, db_column='ip')

    def __str__(self):
        if self.user:
            return u"[%s]%s, %s" % (self.time, self.user.username, self.operate)
        else:
            return u"[%s]%s, %s" % (self.time, '', self.operate)

    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'time', 'width': 120, 'label': u"%s" % OperateLog._meta.get_field('time').verbose_name},
            {'name': 'user', 'width': 80, 'label': u"%s" % OperateLog._meta.get_field('user').verbose_name},
            {'name': 'operate', 'width': 220, 'label': u"%s" % OperateLog._meta.get_field('operate').verbose_name},
            {'name': 'model', 'width': 220, 'label': u"%s" % OperateLog._meta.get_field('model').verbose_name},
            {'name': 'object', 'width': 220, 'label': u"%s" % OperateLog._meta.get_field('object').verbose_name},
            {'name': 'ip', 'width': 220, 'label': u"%s" % OperateLog._meta.get_field('ip').verbose_name},
            {'name': 'count', 'width': 80, 'label': u"%s" % OperateLog._meta.get_field('count').verbose_name}

        ]

    class Admin:
        db_table = 'payroll_operate_log'
        list_filter = ['time', 'user', 'model']
        search_fields = ['user__username', 'operate', 'model', 'object']

    class Meta:
        verbose_name = _("Salary operation log")
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'delete')


APPROVAL_STATUS = (
    (0, _('Approval pending')),
    (1, _('Approved')),
    (2, _('Approval rejected')),
    (3, _('Again'))
)


class SalarySet(models.Model):
    """
    账套
    """
    code = models.CharField(max_length=10, null=False, blank=False, editable=True, verbose_name=_(u'Numbering'))
    name = models.CharField(max_length=80, null=False, blank=False, editable=True, verbose_name=_(u'ItemName'))
    calculate_month = models.CharField(max_length=10, null=False, blank=False, editable=True,
                                       verbose_name=_(u'Calculate month'))  # 202009
    start_date = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'start date'))
    end_date = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'end date'))
    status = models.IntegerField(null=True, default=0, choices=APPROVAL_STATUS, editable=False,
                                 verbose_name=_(u'status'))

    @staticmethod
    def colModels():
        ret = [
            {'name': 'id', 'hidden': True},
            {'name': 'code', 'sortable': True, 'width': 100, 'label': u"%s" % (_(u'Numbering'))},
            {'name': 'name', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'ItemName'))},
            {'name': 'calculate_month', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Calculate month'))},
            {'name': 'start_date', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'start date'))},
            {'name': 'end_date', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'end date'))},
            {'name': 'status', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'status'))},
            {'name': 'action', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'operating'))}
        ]
        return ret

    def __str__(self):
        return u"%s %s" % (self.code, self.name)

    class Admin:
        search_fields = ['code']

    class Meta:
        db_table = 'payroll_salary_set'
        unique_together = ["code"]
        verbose_name = _('Salary set')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete', 'export')
        permissions = (
            ('set_employee_to_salaryset', 'add people'),
            ('view_employee_salaryset', 'view people'),
            ('calculate_employee_salaryset', 'Calculate salary'),
            ('audit_employee_salaryset', 'Salary audit'),
        )


class SalarySetEmployee(models.Model):
    """
    账套人员
    """
    employee = models.ForeignKey(employee, null=False, blank=False, editable=True, verbose_name=_(u'Employee'), on_delete=models.CASCADE)
    salary_set = models.ForeignKey(SalarySet, null=True, blank=True, editable=True, verbose_name=_(u'Salary set'), on_delete=models.CASCADE)

    class Meta:
        db_table = 'payroll_salary_set_employee'
        unique_together = (("employee", "salary_set"),)
        verbose_name = _('Salary set')
        verbose_name_plural = verbose_name
        default_permissions = ()
        permissions = (
            ('payroll_report', 'Reports'),
        )

    class Admin:
        list_display = ()
        search_fields = ['employee__PIN', 'employee__EName']


class SalarySetData(models.Model):
    """
    账套人员数据
    """
    salary_set = models.ForeignKey(SalarySet, null=True, blank=True, editable=True, verbose_name=_(u'Salary set'), on_delete=models.CASCADE)
    salary_template = models.ForeignKey(SalaryTemplate, null=True, blank=True, editable=True, verbose_name=_(u'Salary template'), on_delete=models.CASCADE)
    start_date = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'start date'))
    end_date = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'end date'))
    employee = models.ForeignKey(employee, null=False, blank=False, editable=True, verbose_name=_(u'Employee'), on_delete=models.CASCADE)
    b1 = models.DecimalField(_('duty'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b2 = models.DecimalField(_('realduty'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b3 = models.DecimalField(_('late'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b4 = models.DecimalField(_('early'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b5 = models.DecimalField(_('absent'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b6 = models.IntegerField(_('working age'), null=True, blank=True, default=0, editable=True)
    c1 = models.DecimalField(_('C1'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c2 = models.DecimalField(_('C2'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c3 = models.DecimalField(_('C3'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c4 = models.DecimalField(_('C4'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c5 = models.DecimalField(_('C5'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c6 = models.DecimalField(_('C6'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c7 = models.DecimalField(_('C7'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c8 = models.DecimalField(_('C8'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c9 = models.DecimalField(_('C9'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c10 = models.DecimalField(_('C10'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c11 = models.DecimalField(_('C11'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c12 = models.DecimalField(_('C12'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c13 = models.DecimalField(_('C13'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c14 = models.DecimalField(_('C14'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c15 = models.DecimalField(_('C15'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c16 = models.DecimalField(_('C16'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c17 = models.DecimalField(_('C17'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c18 = models.DecimalField(_('C18'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c19 = models.DecimalField(_('C19'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c20 = models.DecimalField(_('C20'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c21 = models.DecimalField(_('C21'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c22 = models.DecimalField(_('C22'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c23 = models.DecimalField(_('C23'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c24 = models.DecimalField(_('C24'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c25 = models.DecimalField(_('C25'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c26 = models.DecimalField(_('C26'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c27 = models.DecimalField(_('C27'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c28 = models.DecimalField(_('C28'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c29 = models.DecimalField(_('C29'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c30 = models.DecimalField(_('C30'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c31 = models.DecimalField(_('C31'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c32 = models.DecimalField(_('C32'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c33 = models.DecimalField(_('C33'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c34 = models.DecimalField(_('C34'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c35 = models.DecimalField(_('C35'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c36 = models.DecimalField(_('C36'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c37 = models.DecimalField(_('C37'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c38 = models.DecimalField(_('C38'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c39 = models.DecimalField(_('C39'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c40 = models.DecimalField(_('C40'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c41 = models.DecimalField(_('C41'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c42 = models.DecimalField(_('C42'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c43 = models.DecimalField(_('C43'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c44 = models.DecimalField(_('C44'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c45 = models.DecimalField(_('C45'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c46 = models.DecimalField(_('C46'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c47 = models.DecimalField(_('C47'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c48 = models.DecimalField(_('C48'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c49 = models.DecimalField(_('C49'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c50 = models.DecimalField(_('C50'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)

    class Meta:
        db_table = 'payroll_salary_set_data'
        unique_together = ["salary_set", "employee"]
        verbose_name = _('Salary set data')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'change')
        permissions = (
            ('get_attendance_data_salarysetdata', 'Get attendance data'),
            ('import_attendance_data_salarysetdata', 'Import attendance data'),
            ('edit_salary_data_salarysetdata', 'Salary import'),
        )


class SalarySetResult(models.Model):
    """
    薪资计算结果（工资条）
    """
    salary_set = models.ForeignKey(SalarySet, null=True, blank=True, editable=True, verbose_name=_(u'Salary set'), on_delete=models.CASCADE)
    salary_template = models.ForeignKey(SalaryTemplate, null=True, blank=True, editable=True, verbose_name=_(u'Salary template'), on_delete=models.CASCADE)
    start_date = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'start date'))
    end_date = models.DateField(null=False, blank=False, editable=True, verbose_name=_(u'end date'))
    employee = models.ForeignKey(employee, null=False, blank=False, editable=True, verbose_name=_(u'Employee'), on_delete=models.CASCADE)
    pin = models.CharField(_('Personnel ID'), null=False, max_length=24)
    name = models.CharField(_('Emp Name'), db_column="name", null=True, max_length=24, blank=True, default="")
    email = models.EmailField(_('E-mail'), blank=True, null=True)
    position = models.CharField(_('Title'), max_length=20, null=True, blank=True)
    total = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                                verbose_name=_(u'Total salary'))
    email_send_status = models.IntegerField(null=True, default=0, choices=EMAIL_SEND_STATUS, editable=False)
    a1 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Base salary'))
    a2 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Post salary'))
    a3 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Post subsidy'))
    a4 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Internship salary'))
    a5 = models.DecimalField(null=True, blank=True, max_digits=8, decimal_places=2, default=0, editable=True,
                             verbose_name=_(u'Internship subsidy'))
    b1 = models.DecimalField(_('duty'), max_digits=8, decimal_places=2, default=0, null=True, blank=True, editable=True)
    b2 = models.DecimalField(_('realduty'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b3 = models.DecimalField(_('late'), max_digits=8, decimal_places=2, default=0, null=True, blank=True, editable=True)
    b4 = models.DecimalField(_('early'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b5 = models.DecimalField(_('absent'), max_digits=8, decimal_places=2, default=0, null=True, blank=True,
                             editable=True)
    b6 = models.IntegerField(_('working age'), null=True, blank=True, default=0, editable=True)

    c1 = models.DecimalField(_('C1'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c2 = models.DecimalField(_('C2'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c3 = models.DecimalField(_('C3'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c4 = models.DecimalField(_('C4'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c5 = models.DecimalField(_('C5'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c6 = models.DecimalField(_('C6'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c7 = models.DecimalField(_('C7'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c8 = models.DecimalField(_('C8'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c9 = models.DecimalField(_('C9'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                             blank=True, editable=True)
    c10 = models.DecimalField(_('C10'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c11 = models.DecimalField(_('C11'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c12 = models.DecimalField(_('C12'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c13 = models.DecimalField(_('C13'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c14 = models.DecimalField(_('C14'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c15 = models.DecimalField(_('C15'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c16 = models.DecimalField(_('C16'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c17 = models.DecimalField(_('C17'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c18 = models.DecimalField(_('C18'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c19 = models.DecimalField(_('C19'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c20 = models.DecimalField(_('C20'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c21 = models.DecimalField(_('C21'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c22 = models.DecimalField(_('C22'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c23 = models.DecimalField(_('C23'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c24 = models.DecimalField(_('C24'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c25 = models.DecimalField(_('C25'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c26 = models.DecimalField(_('C26'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c27 = models.DecimalField(_('C27'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c28 = models.DecimalField(_('C28'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c29 = models.DecimalField(_('C29'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c30 = models.DecimalField(_('C30'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c31 = models.DecimalField(_('C31'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c32 = models.DecimalField(_('C32'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c33 = models.DecimalField(_('C33'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c34 = models.DecimalField(_('C34'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c35 = models.DecimalField(_('C35'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c36 = models.DecimalField(_('C36'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c37 = models.DecimalField(_('C37'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c38 = models.DecimalField(_('C38'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c39 = models.DecimalField(_('C39'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c40 = models.DecimalField(_('C40'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c41 = models.DecimalField(_('C41'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c42 = models.DecimalField(_('C42'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c43 = models.DecimalField(_('C43'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c44 = models.DecimalField(_('C44'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c45 = models.DecimalField(_('C45'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c46 = models.DecimalField(_('C46'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c47 = models.DecimalField(_('C47'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c48 = models.DecimalField(_('C48'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c49 = models.DecimalField(_('C49'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)
    c50 = models.DecimalField(_('C50'), max_digits=8, decimal_places=2, default=0, null=True, max_length=20,
                              blank=True, editable=True)

    class Admin:
        search_fields = ['employee__PIN', 'employee__EName']

    class Meta:
        db_table = 'payroll_salary_set_result'
        unique_together = ["salary_set", "employee"]
        verbose_name = _('Salary slip')
        verbose_name_plural = verbose_name
        default_permissions = ('browse',)
        permissions = (
            ('send_payslip_salarysetresult', 'Send payslip'),
        )

    @staticmethod
    def colModels():
        ret = [
            {'name': 'id', 'hidden': True},
            {'name': 'salary_set_name', 'sortable': False, 'index': 'salary_set__name', 'width': 100,
             'label': u"%s" % (_(u'Salary set name'))},
            {'name': 'salary_set_code', 'sortable': False, 'index': 'salary_set__code', 'width': 100,
             'label': u"%s" % (_(u'Salary set code'))},
            {'name': 'pin', 'sortable': True, 'index': 'employee__PIN', 'width': 80,
             'label': u"%s" % _(u'personnel number')},
            {'name': 'user_name', 'sortable': True, 'index': 'employee__EName', 'width': 100,
             'label': u"%s" % _(u'Name')},
            {'name': 'dept_name', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'Department name'))},
            {'name': 'position', 'sortable': False, 'width': 60, 'label': u"%s" % (_(u'Job'))},
            {'name': 'email_send_status', 'sortable': False, 'width': 60,
             'label': u"%s" % (_(u'Payslip sending status'))},
            {'name': 'email', 'sortable': False, 'width': 60, 'label': u"%s" % (_(u'E-mail'))},
            {'name': 'salary_template_name', 'sortable': False, 'index': 'salary_template__name', 'width': 100,
             'label': u"%s" % (_(u'Salary template'))},
            {'name': 'salary_template_code', 'sortable': False, 'index': 'salary_template__code', 'width': 100,
             'label': u"%s" % (_(u'Salary template code'))},
            # {'name': 'b1', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'duty'))},
            # {'name': 'b2', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'realduty'))},
            # {'name': 'b3', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'late'))},
            # {'name': 'b4', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'early'))},
            # {'name': 'b5', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'absent'))},
            # {'name': 'b6', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'working age'))},
            {'name': 'c1', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C1'))},
            {'name': 'c2', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C2'))},
            {'name': 'c3', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C3'))},
            {'name': 'c4', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C4'))},
            {'name': 'c5', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C5'))},
            {'name': 'c6', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C6'))},
            {'name': 'c7', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C7'))},
            {'name': 'c8', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C8'))},
            {'name': 'c9', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C9'))},
            {'name': 'c10', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C10'))},
            {'name': 'c11', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C11'))},
            {'name': 'c12', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C12'))},
            {'name': 'c13', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C13'))},
            {'name': 'c14', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C14'))},
            {'name': 'c15', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C15'))},
            {'name': 'c16', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C16'))},
            {'name': 'c17', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C17'))},
            {'name': 'c18', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C18'))},
            {'name': 'c19', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C19'))},
            {'name': 'c20', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C20'))},
            {'name': 'c21', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C21'))},
            {'name': 'c22', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C22'))},
            {'name': 'c23', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C23'))},
            {'name': 'c24', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C24'))},
            {'name': 'c25', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C25'))},
            {'name': 'c26', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C26'))},
            {'name': 'c27', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C27'))},
            {'name': 'c28', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C28'))},
            {'name': 'c29', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C29'))},
            {'name': 'c40', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C40'))},
            {'name': 'c41', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C41'))},
            {'name': 'c42', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C42'))},
            {'name': 'c43', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C43'))},
            {'name': 'c44', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C44'))},
            {'name': 'c45', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C45'))},
            {'name': 'c46', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C46'))},
            {'name': 'c47', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C47'))},
            {'name': 'c48', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C48'))},
            {'name': 'c49', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C49'))},
            {'name': 'c40', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C40'))},
            {'name': 'c40', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C40'))},
            {'name': 'c41', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C41'))},
            {'name': 'c42', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C42'))},
            {'name': 'c44', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C44'))},
            {'name': 'c44', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C44'))},
            {'name': 'c45', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C45'))},
            {'name': 'c46', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C46'))},
            {'name': 'c47', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C47'))},
            {'name': 'c48', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C48'))},
            {'name': 'c49', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C49'))},
            {'name': 'c50', 'hidden': True, 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'C50'))},
            {'name': 'total', 'sortable': False, 'width': 60, 'label': u"%s" % (_(u'Total salary'))},
        ]
        for t in ret:
            salary_item = Item.get_obj_by_code(t['name'])
            if salary_item and salary_item.status == 0:
                t['hidden'] = False
                t['label'] = salary_item.name
        return ret
