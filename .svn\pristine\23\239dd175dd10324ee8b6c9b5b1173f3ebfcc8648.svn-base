/* Estonian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (mrts.pydev at gmail com). */
jQuery(function($){
	$.datepicker.regional['et'] = {
		closeText: 'Sul<PERSON>',
		prevText: '<PERSON><PERSON>ne<PERSON>',
		nextText: '<PERSON><PERSON><PERSON>ne<PERSON>',
		currentText: '<PERSON><PERSON><PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','April<PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','September','Oktoober','November','Detsember'],
		monthNamesShort: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
		'<PERSON><PERSON>', 'Aug', 'Sept', 'Okt', 'Nov', 'Dets'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','E','T','K','N','R','L'],
		weekHeader: 'näd',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['et']);
});
