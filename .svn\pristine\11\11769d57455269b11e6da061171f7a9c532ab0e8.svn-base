#!/usr/bin/env python
#coding=utf-8
import json
from mysite.utils import *
import requests
from mysite.iclock.models import getDevice
# from ipos.posdevviews import id_pos_getreq
import base64

# 指纹面部在线识别模块

# def test(request):
#     from ipos.posdevviews import id_pos_getreq
#     sn = request.GET.get('SN')
#     device = getDevice(sn)
#     laal = id_pos_getreq(request, device)
#     laal = laal.decode("GB18030")
#     return getJSResponse(laal)
def register_finger_face(state,templates,index,PIN):   #指纹面部信息登记（指纹：1/面部：2，模板，指纹索引（哪个手指）/面部仅一张所以索引为0，人员编号）
    try:
        templates = templates.replace("\n","").replace("\r","")
        if state == '1':
            bodydata = {
                "lang": "zh-CN",
                "sid": "finger/register",
                "payload": {
                    "params": {
                        "humanId":PIN,
                        "finger":[{
                              "index": index,
                              "template":templates
                        }]
                    }
                }
            }
            url = 'http://***********:8066/apiv1/finger/register'
            use_finger_face_interface(url, bodydata)

        elif state == '2':
            url = 'http://***********:8066/apiv1/facenir/register'
            if isinstance(templates, str):
                templates = json.loads(templates)
            for key in ['0','1','2']:
                bodydata = {
                    "lang": "zh-CN",
                    "sid": "facenir/register",
                    "payload": {
                        "params": {
                            "humanId":PIN,
                            "faceNir": [{
                                "index": key,
                                "template": templates[key]
                            }]
                        }
                    }
                }
                use_finger_face_interface(url, bodydata)
    except Exception as e:
        print (e)

def BioFaceFingerPeopleDel(PIN):       #删除人员信息
    url = 'http://***********:8066/apiv1/human/remove'
    for p in PIN:
        bodydata = {
            "lang": "zh-CN",
            "sid": "human/remove",
            "payload": {
                "params": {
                    "humanId": str(p)
                }
            }
        }
        use_finger_face_interface(url, bodydata)

# def BioFaceFingerPeopleModify(PIN,name):          #修改人员信息到百傲慧识
#     bodydata = {
#         "lang": "zh-CN",
#         "sid": "human/update",
#         "payload": {
#             "params": {
#                 "humanId": PIN,
#                 "name": name
#             }
#         }
#     }
#     url = 'http://***********:8066/apiv1/human/update'
#     return use_finger_face_interface(url, bodydata)
def BioFaceFingerPeopleRegister(PIN,name):          #新增人员信息到百傲慧识
    bodydata = {
        "lang": "zh-CN",
        "sid": "human/add",
        "payload": {
            "params": {
                "name": name,
                "humanId": PIN
            }
        }
    }
    url = 'http://***********:8066/apiv1/human/add'
    return use_finger_face_interface(url, bodydata)

def face_finger_compare(template,verifytype):   #对比信息
    from ipos.utils import aes_encryption
    a = base64.b64decode(template)
    if verifytype == '3':
        bodydata = {
        "lang": "zh-CN",
        "sid": "finger/comparison",
        "payload": {
            "params": {
                "finger":{
                      "threshold":30,
                      "template":str(template)
                }
            }
        }
    }
        url = 'http://***********:8066/apiv1/finger/comparison'

    elif verifytype == '4':
        bodydata = {
            "lang": "zh-CN",
            "sid": "facenir/comparison",
            "payload": {
                "params": {
                    "faceNir":{
                          "threshold":60,
                          "template":str(template)
                    }
                }
            }
        }
        url = 'http://***********:8066/apiv1/facenir/comparison'
    return use_finger_face_interface(url,bodydata)
def use_finger_face_interface(url, body):       #使用接口
    textmod = dumps1(body)
    # print body
    header_dict = {"Content-Type": "application/json"}
    #req = urllib2.Request(url=url, data=textmod, headers=header_dict)
    #res = urllib2.urlopen(req)
    req=requests.post(url,data=textmod, headers=header_dict)
    res = req.json()
    # print res
    return res