# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-08-29 16:10
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0047_auto_20220613_1138'),
    ]

    operations = [
        migrations.CreateModel(
            name='BioPayRequestLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('api_url', models.TextField(blank=True, max_length=400, null=True, verbose_name='接口地址')),
                ('post_data', models.TextField(blank=True, max_length=400, null=True, verbose_name='post参数')),
                ('result_data', models.TextField(blank=True, max_length=400, null=True, verbose_name='响应结果')),
                ('request_time', models.DateTimeField(blank=True, null=True, verbose_name='调用时间')),
            ],
            options={
                'verbose_name': '安防云支付调用日志',
                'verbose_name_plural': '安防云支付调用日志',
                'default_permissions': ('browse',),
            },
        ),
        migrations.AlterModelOptions(
            name='cardcashsz',
            options={'default_permissions': (), 'ordering': ['-id'], 'permissions': (('iclockdininghall_icconsumerlist', 'cardcashsz'), ('icconsumerlist_amount_correction', 'cardcashsz'), ('consumer_details_export', 'Consumer details export')), 'verbose_name': 'Card cash receipts and payments', 'verbose_name_plural': 'Card cash receipts and payments'},
        ),
    ]
