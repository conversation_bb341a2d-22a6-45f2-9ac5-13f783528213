#!/usr/bin/python
# -*- coding: utf-8 -*-

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from mysite.accounts.models import MyUser

from mysite.ipos.models import *



class FoodClassifySerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = FoodClassify
        fields = ('code','itemname')


class OrderManagerSerializer(serializers.HyperlinkedModelSerializer):
    UserID = serializers.CharField(source='UserID.id')
    dining_id = serializers.CharField(source='dining_id.code')
    meal_id = serializers.CharField(source='meal_id.code')
    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(OrderManagerSerializer, self).to_representation(instance)
        # 获取餐别截止时间
        order_meal_et =Meal.objects.get(pk=int(ret['meal_id'])) .endtime
        # 获取订单截止时间
        order_meal_et = datetime.datetime.strptime('%s %s' % (str(ret['order_date']), str(order_meal_et)), '%Y-%m-%d %H:%M:%S')
        # 判断订单是否超时
        if datetime.datetime.now() > order_meal_et:
            ret['state'] = 1
        else:
            ret['state'] = 0

        #根据订单号获取订单餐品信息
        food = OrderRecordFoodDetails.objects.filter(order_record_id=ret['id'])
        menulist = {}
        for f in food:
            foodlist = {}
            foodlist['name'] = f.name
            foodlist['price'] = f.price
            foodlist['quantity'] = f.quantity
            foodlist['itemIntroduce'] = f.food_id.remark
            if f.food_id.picture_path:
                foodlist['picture_path'] = f.food_id.picture_path.replace('/file','/picfile')
            else:
                foodlist['picture_path'] = ""
            menulist['%s' % f.food_id] = foodlist
        #添加每个订单餐品信息
        ret['MenuList'] = menulist
        dining = Dininghall.objects.get(code=ret['dining_id'])
        ret['dining_name'] = dining.name if dining else ''
        return ret

    class Meta:
        model = OrderRecord
        # fields = ('code','UserID','MenuList','OrderDate','PlaceTime','state','money','meal')
        fields = ('id', 'order_no', 'UserID', 'create_time', 'order_date', 'is_pick', 'dining_id',
                  'payment_status', 'money', 'meal_id', 'refund_status')




class FoodClassifySerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = FoodClassify
        fields = ('code','itemname')


class CookBookSerializer(serializers.HyperlinkedModelSerializer):
    Classify = serializers.CharField(source='Classify.code')
    image = serializers.SerializerMethodField()

    def get_image(self, obj):
        try:
            ins = self._context['request'].GET
            apiUrl = ins.get('apiZkmini', '')
            photo_path = getStoredFileName("ipos/foodpic", None, obj.code + ".jpg")
            if os.path.exists(photo_path):
                import random
                url = apiUrl + getStoredFileURL("ipos/foodpic", None, obj.code + ".jpg", auth=False) + '?time=%s' % str(random.random()).split('.')[1]
                return url
            return None
        except Exception as e:
            print('e----',e)
            return None

    class Meta:
        model = CookBook
        fields = ('id','itemname', 'itemIntroduce', 'code','Price','Classify','image')


class CardCashSZSerializer(serializers.HyperlinkedModelSerializer):
    meal_name = serializers.CharField(source='meal.name', default=_(u"other"), allow_null=True)
    dining_name = serializers.CharField(source='dining.name', default='', allow_null=True)
    log_flag = serializers.CharField(source='get_log_flag_display', default='', allow_null=True)
    pos_model = serializers.CharField(source='get_pos_model_display', default='', allow_null=True)
    wallet_type = serializers.CharField(source='get_wallet_type_display', default='', allow_null=True)
    photourl = serializers.SerializerMethodField()  #对比照片

    def get_photourl(self, obj):
        try:
            photo_path = getStoredFileName("photo/app/thumbnail", None, obj.UserID.PIN + ".jpg")
            if not os.path.exists(photo_path):
                photo_path = getStoredFileName("photo/thumbnail", None, obj.UserID.PIN + ".jpg")
            with open(photo_path, "rb") as f:
                base64_data = base64.b64encode(f.read()).decode('utf-8')
                data = 'data:image/jpg;base64,%s' % base64_data
        except:
            data = ''
        return data

    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(CardCashSZSerializer, self).to_representation(instance)
        paylog = AliWxFulllog.objects.filter(sys_card_no = ret['sys_card_no'], posoptime = ret['checktime'], posmoney = ret['money'])
        tradeno = paylog[0].tradeno if paylog else _(u"none")
        ret['tradeno'] = tradeno
        return ret

    class Meta:
        model = CardCashSZ
        fields = ('money', 'money_B', 'blance', 'allow_balance', 'checktime', 'meal_name', 'dining_name', 'log_flag',
                  'card', 'sn', 'pos_model', 'wallet_type', 'sys_card_no', 'pay_pin', 'photourl', 'MercOrdercode')


class ICConsumerListSerializer(serializers.HyperlinkedModelSerializer):
    blance = serializers.CharField(source='balance', default='', allow_null=True)
    checktime = serializers.CharField(source='pos_time', default='', allow_null=True)
    meal_name = serializers.CharField(source='meal.name', default=_(u"other"), allow_null=True)
    dining_name = serializers.CharField(source='dining.name', default='', allow_null=True)
    log_flag = serializers.CharField(source='get_log_flag_display', default='', allow_null=True)
    sn = serializers.CharField(source='dev_sn', default='', allow_null=True)
    pos_model = serializers.CharField(source='get_pos_model_display', default='', allow_null=True)
    wallet_type = serializers.CharField(source='get_wallet_type_display', default='', allow_null=True)
    photourl = serializers.SerializerMethodField()  #对比照片

    def get_photourl(self, obj):
        try:
            photo_path = getStoredFileName("photo/app/thumbnail", None, obj.user_pin  + ".jpg")
            if not os.path.exists(photo_path):
                photo_path = getStoredFileName("photo/thumbnail", None, obj.user_pin + ".jpg")
            with open(photo_path, "rb") as f:
                base64_data = base64.b64encode(f.read()).decode('utf-8')
                data = 'data:image/jpg;base64,%s' % base64_data
        except:
            data = ''
        return data

    class Meta:
        model = ICConsumerList
        fields = ('money', 'money_B', 'blance', 'allow_balance', 'checktime', 'meal_name', 'dining_name', 'log_flag',
                  'card', 'sn', 'pos_model', 'wallet_type', 'photourl', 'MercOrdercode')


class AllowanceSerializer(serializers.HyperlinkedModelSerializer):
    is_pass = serializers.CharField(source='get_is_pass_display', default='', allow_null=True)
    is_ok = serializers.CharField(source='get_is_ok_display', default='', allow_null=True)

    class Meta:
        model = Allowance
        fields = ('money', 'is_pass', 'is_ok', 'allow_date', 'receive_date', 'valid_date')


#微信消费小程序补贴记录
class AllowanceSerializerWx(serializers.HyperlinkedModelSerializer):
    is_pass = serializers.CharField(source='get_is_pass_display', default='', allow_null=True)
    is_ok = serializers.CharField(source='get_is_ok_display', default='', allow_null=True)
    photourl = serializers.SerializerMethodField()  #对比照片

    def get_photourl(self, obj):
        try:
            photo_path = getStoredFileName("photo/app/thumbnail", None, obj.UserID.PIN + ".jpg")
            if not os.path.exists(photo_path):
                photo_path = getStoredFileName("photo/thumbnail", None, obj.UserID.PIN + ".jpg")
            with open(photo_path, "rb") as f:
                base64_data = base64.b64encode(f.read()).decode('utf-8')
                data = 'data:image/jpg;base64,%s' % base64_data
        except:
            data = ''
        return data

    def to_representation(self, instance):
        """
        重写to_representation方法，把每一列数据（其中instance代表每一列数据）进行修改重组，然后返回
        """
        ret = super(AllowanceSerializerWx, self).to_representation(instance)
        allowanlog = Allowance.objects.filter(sys_card_no = ret['sys_card_no'], allow_date = ret['allow_date'])
        receive_date = allowanlog[0].receive_date
        allow_date =allowanlog[0].allow_date.date()
        #是否领取补贴
        if ret['is_ok'] == _(u"Yes"):
            paylog = CardCashSZ.objects.filter(sys_card_no = ret['sys_card_no'], checktime = receive_date, hide_column = 2)
        elif ret['is_ok'] == _(u"No"):
            paylog = CardCashSZ.objects.filter(sys_card_no=ret['sys_card_no']).order_by('-checktime')
            ret['allow_date'] = allow_date

        #余额
        blance = paylog[0].blance if paylog else 0
        #补贴余额
        allow_balance = paylog[0].allow_balance if paylog else 0
        ret['blance'] = blance
        ret['allow_balance'] = allow_balance
        return ret

    class Meta:
        model = Allowance
        fields = ('money', 'is_pass', 'is_ok', 'allow_date', 'receive_date', 'valid_date', 'sys_card_no', 'photourl')
