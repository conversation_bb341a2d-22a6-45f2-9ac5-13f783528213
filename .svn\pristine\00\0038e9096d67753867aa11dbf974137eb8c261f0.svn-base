{% load i18n %}
{% autoescape off %}
{% load iclock_tags %}

[
{% for item in latest_item_list %}
{
"id":"{{ item.id|trim }}",
"document_number":"{{ item.document_number|trim }}",
"UserID":"{% if not item.UserID.EName %}{{ item.UserID.PIN|trim }}{% else %}{{ item.UserID.EName|trim }}{% endif %}",
"status":"{{ item.get_status_display|trim }}",
"reason":"{{ item.reason|trim }}",
"apply_time":"{{ item.apply_time }}",
"departure_time":"{{ item.departure_time }}",
"bring_back_time":"{{ item.bring_back_time }}",
"operator":"{{ item.operator|trim }}",
"asset_list":"{{ item.get_asset_list|trim }}",
"detail":"&nbsp;<a onclick='get_bringout_list(\"{{item.id}}\")' style='color:green;'>{% trans 'Details' %}</a>&nbsp;"
}
{%if not forloop.last%},{%endif%}
{% endfor %}
]
{% endautoescape %}