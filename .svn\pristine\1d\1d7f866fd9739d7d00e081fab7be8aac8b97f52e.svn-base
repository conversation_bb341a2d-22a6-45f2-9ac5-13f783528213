# Generated by Django 4.2.11 on 2024-06-26 15:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0069_alter_employee_att_alter_employee_holiday_and_more'),
        ('visitors', '0018_auto_20240408_1051'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='interviewee',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='interviewee',
            name='UserID',
            field=models.OneToOneField(db_column='userid', on_delete=django.db.models.deletion.CASCADE, related_name='Interviewee', to='iclock.employee', verbose_name='Interviewee'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='isinvite',
            field=models.BooleanField(default=False, editable=False, null=True, verbose_name='has been invite'),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='isvisited',
            field=models.<PERSON><PERSON>an<PERSON>ield(default=False, editable=False, null=True, verbose_name='has been visited'),
        ),
    ]
