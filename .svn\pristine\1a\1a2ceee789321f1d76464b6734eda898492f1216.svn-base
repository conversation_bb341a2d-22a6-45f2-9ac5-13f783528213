{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %} %}

<script>
{% block tblHeader %}
    jqOptions[g_activeTabID]=copyObj(jq_Options)
    jqOptions[g_activeTabID].colModel={{colModel}}
    tblName[g_activeTabID]='AccEvent';
    isNew='';
    jqOptions[g_activeTabID].sortname='id';
    jqOptions[g_activeTabID].pager="#id_pager_"+tblName[g_activeTabID];
    options[g_activeTabID].dlg_width=770;
    options[g_activeTabID].dlg_height=500;

    $(function(){
        // 左侧树显示
        var info='<div class=west_info><p>{% trans "1.Event type maintenance, event level for alarm, in real-time monitoring triggered the event will alarm" %}</p></div>'
	    $('#west_content_tab_acc_accevent').html(info)

        $("#"+g_activeTabID+" #id_export").css('display','none');
        $("#"+g_activeTabID+" #id_newrec").css('display','none');
        $("#"+g_activeTabID+" #aDelete").css('display','none');
        $("#"+g_activeTabID+" #_icon_2_28").css('display','none')

        $("#"+g_activeTabID+" #search_number").keypress(function(event){
	        if(event.keyCode==13){
	            search_event_type();
            }
	    });

        $("#"+g_activeTabID+" #search_name").keypress(function(event){
	        if(event.keyCode==13){
	            search_event_type();
            }
	    });
    })

    {#function searchShowEventType() {#}
    {#    var flag=$("#"+g_activeTabID+" #searchbar").attr('role');#}
    {#    if (flag!='cansearch'&&flag!='defvalue') return;#}
    {#    if (flag!='defvalue')#}
    {#        var v=$("#"+g_activeTabID+" #searchbar")[0].value;#}
    {#    else#}
    {#        var v=""#}
    {#    var url=g_urls[g_activeTabID]+"?q="+encodeURI(v)#}
    {#    savecookie("search_urlstr",url);#}
    {#    console.log(url)#}
    {#    $("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");#}
    {#}#}

    function process_dialog_AccEvent(obj,flag) {
        if(flag=='edit'){
            $("#id_code",obj).attr('readonly','True')
            $("#id_name",obj).attr('readonly','True')
        }
        // 取消报警事件级别不可设置为报警
        var code = $("#id_code",obj).val()
        console.log('code:',code)
        if (code == 7){
            alert("{% trans 'The event level cannot be changed!!!' %}")
            $("#search_id_acc_event_no_type").options[2].attr("disabled",'disabled')
        }
    }

    $("#"+g_activeTabID+" #id_search").click(function(){
        search_event_type()
    })

    // 模糊查询
    function search_event_type(){
        var url = "/acc/data/accevent/?"
        var q = ''
        var search_number = $("#search_number").val()
        var search_name = $("#search_name").val()
        var search_level = $("#search_id_acc_event_no_type option:selected").val()
        if(search_number != ''){
            q+="&code="+encodeURI(search_number)
        }
        if(search_name != ''){
            q+="&name="+encodeURI(search_name)
        }
        if(search_level != ''){
            q+="&rank="+encodeURI(search_level)
        }
        if(q!=''){
		    url+=q
	    }
        savecookie("search_urlstr",url);
        $("#id_grid_AccEvent").jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
    }
{% endblock %}

{% block sear_area %}
<div class="left" id="sear_area" style="margin-left:20px;margin-top:2px;min-width:400px;overflow:hidden">
	<div style="min-width:400px;overflow:hidden">
		<span class="search-label">{% trans "Event number" %}</span>
		<input class="new-search-input" id="search_number" type="text"  value="" role='defvalue' autocomplete="off" style="width: 100px;"/>
		<span class="search-label" style="margin-left:24px;">{% trans "Event name" %}</span>
		<input class="new-search-input" id="search_name" type="text"  value="" role='defvalue' autocomplete="off" style="width: 100px;"/>
		{#<span class="search-label" style="margin-left:5px;">{% trans "Event level" %}</span>#}
		{#<input class="new-search-input" id="search_level" type="text"  value="" role='defvalue' autocomplete="off" style="width: 110px;"/>#}

	    <label>{% trans 'Event level' %}</label>
        <select name='event_no' id='search_id_acc_event_no_type'  style='width:120px;'>
            <option value=''>------</option>
            <option value='1'>{% trans 'normal' %}</option>
            <option value='2'>{% trans 'alarm' %}</option>
        </select>
        <span><a id='id_search' class='m-btn  zkgreen rnd mini' style='height:22px;'>{% trans 'Query' %}</a></span>
	</div>
</div>
{% endblock %}
</script>