{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <script src="{{ MEDIA_URL }}js/mui.picker.min.js"></script>
    <script src="{{ MEDIA_URL }}js/mui.view.js "></script>
    <link href="{{ MEDIA_URL }}css/mui.picker.min.css" rel="stylesheet"/>
    <style>
        html,body {
            background-color: white;
        }
        #bottom_btns{
            margin-top: 50px;
        }
        #bottom_btns button{
            width: 32%;
        }
        .mui-bar-tab .mui-tab-item.mui-active{
            color: #8ac85a;
        }
        .mui-table-view.mui-grid-view .mui-table-view-cell .mui-media-body{
            font-size: 12px
        }
        .icon {
           width: 1.85em; height: 1.85em;
           vertical-align: -1.3em;
           fill: currentColor;
           overflow: hidden;
        }
        .mui-table-view-divider {
            background-color: #eee;
            display:block;
            padding-bottom: 5px
        }
        .mui-content {
			background-color: #ffffff;
		}
        /*修改侧滑栏颜色*/
        #name{
            color:#000
        }

        .mui-off-canvas-left, .mui-media {
            background-color: #ffffff;
        }

        #head-img {
            width: 40px;
            height: 40px;
            display: flex;
            border-radius: 50%;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        p.mui-ellipsis {
            font-size: 15px;
            margin-top: 10px;
            margin-bottom: 10px;
            color: #8f8f94;
        }

        .mui-card-header.mui-card-media {
            display: block;
            padding-bottom: 20px;
        }

        #name {
            padding-left: 5px;
            padding-top: 12px;
            font-size: 15px;
        }

    </style>
{% endblock %}
{% block content %}
	<!-- 主界面菜单同时移动 -->
    <!-- 侧滑导航根容器 -->
    <div class="mui-off-canvas-wrap mui-draggable">
        <div class="mui-inner-wrap">
            <header class="mui-bar mui-bar-nav">
                <h1 class="mui-title">{% trans 'Attendance' %}</h1>
            </header>
            <nav class="mui-bar mui-bar-tab">
                {% if 'att'|enabled_module %}
                    <a class="mui-tab-item mui-active" href="#">
                        <span class="mui-icon mui-icon-home"></span>
                        <span class="mui-tab-label">{% trans 'Attendance' %}</span>
                    </a>
                {% endif %}
                {% if 'ipos'|enabled_module %}
                    <a class="mui-tab-item" href="/iapp/ipos_main/">
                        <span class="mui-icon mui-icon-compose"></span>
                        <span class="mui-tab-label">{% trans 'Consumption' %}</span>
                    </a>
                {% endif %}
                <a class="mui-tab-item" href="/iapp/att/message/">
                    <span class="mui-icon mui-icon-chatbubble-filled"></span>
                    <span class="mui-tab-label">{% trans 'Message' %}</span>
                </a>
                <a class="mui-tab-item" href="/iapp/att/my_info/">
                    <span class="mui-icon mui-icon-person"></span>
                    <span class="mui-tab-label">{% trans 'My' %}</span>
                </a>
            </nav>
            <!-- 主页面内容容器 -->
            <div class="mui-content mui-scroll-wrapper">
                <div class="mui-scroll">
                    <div id="slider" class="mui-slider">
                        <div class="mui-slider-group mui-slider-loop">
                            <!-- 额外增加的一个节点(循环轮播：第一个节点是最后一张轮播) -->
                            <div class="mui-slider-item mui-slider-item-duplicate">
                                <a href="#">
                                    <img src="{{ MEDIA_URL }}images/bar_pic2.jpg">
                                </a>
                            </div>
                            <!-- 第一张 -->
                            <div class="mui-slider-item">
                                <a href="#">
                                    <img src="{{ MEDIA_URL }}images/bar_pic1.jpg">
                                </a>
                            </div>
                            <!-- 第二张 -->
                            <div class="mui-slider-item">
                                <a href="#">
                                    <img src="{{ MEDIA_URL }}images/bar_pic2.jpg">
                                </a>
                            </div>
                            <!-- 额外增加的一个节点(循环轮播：最后一个节点是第一张轮播) -->
                            <div class="mui-slider-item mui-slider-item-duplicate">
                                <a href="#">
                                    <img src="{{ MEDIA_URL }}images/bar_pic1.jpg">
                                </a>
                            </div>
                        </div>
                        <div class="mui-slider-indicator">
                            <div class="mui-indicator mui-active"></div>
                            <div class="mui-indicator"></div>
                        </div>
                    </div>

                    <li class="mui-table-view-divider"></li>
                    <ul class="mui-table-view mui-grid-view mui-grid-12">
                        <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                            <a href="/iapp/att/tableapply/">
                                <svg class="icon" aria-hidden="true">
                                    <use xlink:href="#icon-jiabanshenpi"></use>
                                </svg>
                                <div class="mui-media-body">{% trans 'Form application' %}</div>
                            </a>
                        </li>
                        <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                            <a href="/iapp/att/myapply/">
                                <svg class="icon" aria-hidden="true">
                                    <use xlink:href="#icon-qingjia"></use>
                                </svg>
                                <div class="mui-media-body">{% trans 'My application' %}</div>
                            </a>
                        </li>
                        <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                            <a href="/iapp/att/audit/">
                                <svg class="icon" aria-hidden="true">
                                    <use xlink:href="#icon-shenpi"></use>
                                </svg>
                                <div class="mui-media-body">{% trans 'My Approval' %}</div>
                            </a>
                        </li>
                        <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                            <a href="/iapp/att/myatt/">
                                <svg class="icon" aria-hidden="true">
                                    <use xlink:href="#icon-meiriqiandao"></use>
                                </svg>
                                <div class="mui-media-body">{% trans 'My attendance' %}</div>
                            </a>
                        </li>
                        <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                            <a href="/iapp/att/annual/">
                                <svg class="icon" aria-hidden="true">
                                    <use xlink:href="#icon-qingjia1"></use>
                                </svg>
                                <div class="mui-media-body">{% trans 'My annual leave' %}</div>
                            </a>
                        </li>
                        {% if not ''|isZKTime == '1' %}
                            <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                                <a href="/iapp/visitor/myvisitor/">
                                    <svg class="icon" aria-hidden="true">
                                        <use xlink:href="#icon-fangkejilu"></use>
                                    </svg>
                                    <div class="mui-media-body">{% trans 'My Visitors' %}</div>
                                </a>
                            </li>
    <!--                         <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">
                                <a href="/iapp/att/epidemic/">
                                    <svg class="icon" aria-hidden="true">
                                        <use xlink:href="#icon-yiqingfuwu"></use>
                                    </svg>
                                    <div class="mui-media-body">{% trans 'Epidemic Register' %}</div>
                                </a>
                            </li> -->
{#                            <li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-3">#}
{#                                <a href="/iapp/asset/asset_action/">#}
{#                                    <svg class="icon" aria-hidden="true">#}
{#                                        <use xlink:href="#icon-zichan"></use>#}
{#                                    </svg>#}
{#                                    <div class="mui-media-body">{% trans 'Asset' %}</div>#}
{#                                </a>#}
{#                            </li>#}
                        {% endif %}
                    </ul>
                </div>
            </div>
            <div class="mui-off-canvas-backdrop"></div>
        </div>
    </div>
{% endblock %}
{% block extrjs %}
<script type="text/javascript">
    mui.init();
    //图片轮播
    var slider = mui("#slider");
    slider.slider({
        interval: 5000
    });
    window.onload = function () {
        mui('.mui-scroll-wrapper').scroll({
            deceleration: 0.0005 //flick 减速系数，系数越大，滚动速度越慢，滚动距离越小，默认值0.0006
        });
    };
    //解决a标签无法跳转问题
    mui('body').on('tap', 'a', function () {
        window.top.location.href = this.href;
    });

    mui.ajax('/iapp/get_data_info/?info_type=userinfo',{
            data:{},
            dataType:'json',
            type:'post',
            success:function(data){
                for(var i=0; i<data.length; i++){
                    //console.log(data[i]);
                    {#document.getElementById("head-img").src = data[i].head_img;#}
                    {#mui('#name')[0].innerText = data[i].ename;#}
                    //mui('#pin')[0].innerText = data[i].pin;

                    sessionStorage.setItem("name", data[i].ename);
                    sessionStorage.setItem("pin", data[i].pin);
                    sessionStorage.setItem("email", data[i].email);
                    sessionStorage.setItem("mobile", data[i].mobile);
                    sessionStorage.setItem("head_img", data[i].head_img);
                }
            }

        });

</script>
{% endblock %}

