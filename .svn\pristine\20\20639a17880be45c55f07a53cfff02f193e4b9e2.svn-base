# coding=utf-8
"""
设备接口
"""
from mysite.iclock.models import *
from mysite.meeting.models import meet_devices
from mysite.core.zkcmdproc import *
from mysite.utils import *
from mysite.iclock.iutils import *
from mysite.base.models import *
from django.conf import settings
import datetime

"""
@apiGroup iclock
@apiName getIclock
@apiVersion 10.0.1
@api {post} api/v2/iclock/get/?key=ACCESS_KEY 获取设备信息
@apiDescription 获取设备记录</br>
- 不支持模糊查询
- 不给参数时默认返回所有设备信息

@apiParam (content) {array} [sn] 设备序列号
@apiParam (content) {array} [producttype] 设备类型

@apiParamExample {json} 参数示例：
{
"sn":["3485171000033","3485171000032"],
"producttype":[9,15]
}
@apiSampleRequest http://127.0.0.1:81/api/v2/iclock/get/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84
@apiSuccess (success) msg 请求结果.
@apiSuccess (success) ret 返回码.
@apiSuccess (success) data 返回数据.
@apiSuccess (success) count 数据条数.
@apiSuccess (success) items 数据.
@apiSuccess (success) alias 设备名称.
@apiSuccess (success) sn 设备序列号.
@apiSuccess (success) ipddress 设备IP.
@apiSuccess (success) devicename 设备型号.
@apiSuccess (success) producttype 设备类型.
@apiSuccessExample {json}  响应成功:
{
    "msg": "获取设备 1 个。",
    "data": {
        "count": 1,
        "items": [
            {
                "alias": "北4楼",
                "sn": "6405150900090",
                "ipddress": "***********",
                "devicename": "ACP-400"
            }
        ]
    },
    "ret": 0
}

@apiErrorExample {json} 错误返回示例:
{
    "msg": "121212121212 设备不存在",
    "ret": 1
}
@apiError (errorDescription) 0 请求成功
@apiError (errorDescription) 1 请求失败
@apiError (errorDescription) 3 无效的数据格式

@apiExample {python} python示例:
#!/usr/bin/python
#!/usr/bin/python
# coding:utf-8
import urllib2, json

def main():
    params = {"sn": "523652142345"}
    params = json.dumps(params)
    sendreq(params)

def sendreq(params):
    sendurl = 'http://127.0.0.1:81/api/v2/iclock/get/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84'

    wp = urllib2.urlopen(sendurl,params)
    result = wp.read()  # 获取接口返回内容

    if result:
        print result

if __name__ == '__main__':
    main()
"""


def getIclock(param):
    # 查询设备
    ret = 0
    msg = ''
    data = {}
    items = []
    sns = param.get("sn")
    producttypes = param.get("producttype")
    if (producttypes and type(producttypes) != list) or (sns and type(sns) != list):
        result = {'ret': 3, 'msg': u'无效的数据格式'}
        return result
    device = iclock.objects.all().exclude(DelTag=1)
    if producttypes:
        # 兼容设备类型为null的情况  null视为9
        if 9 in producttypes or '9' in producttypes:
            device = device.filter(Q(ProductType__in=producttypes) | Q(ProductType__isnull=True))
        else:
            device = device.filter(ProductType__in=producttypes)
    if sns:
        device = device.filter(SN__in=sns)
    for s in device:
        d = {}
        d['sn'] = s.SN
        d['ipddress'] = s.IPAddress or ""
        d['alias'] = s.Alias or ""
        d['devicename'] = s.DeviceName or ""
        d['producttype'] = s.ProductType or 9
        items.append(d.copy())
    data['count'] = len(items)
    data['items'] = items
    ret = 0
    msg = u"获取设备 %s 个。" % (len(items))
    result = {'ret': ret, 'msg': msg, 'data': data}
    return result


"""
@apiGroup iclock
@apiName deleteIclock
@apiVersion 10.0.1
@api {post} api/v2/iclock/delete/?key=ICLOCK_KEY 删除设备
@apiDescription 此接口提供给删除设备信息使用
@apiParam (content) {String} sn 设备序列号
@apiParamExample {json} 参数示例：
{"sn": "3485171000033"}
@apiSampleRequest http://127.0.0.1:81/api/v2/iclock/delete/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84

@apiSuccess (success) msg 返回结果.
@apiSuccess (success) ret 返回码.

@apiSuccessExample {json}  响应成功:
{
    "msg": "成功删除2台设备",
    "ret": 0
}

@apiErrorExample {json} 错误返回示例:
{
    "msg": "序列号为 3485171000033 的设备不存在",
    "ret": 1
}
@apiError (errorDescription) 0 请求成功
@apiError (errorDescription) 1 请求失败
@apiError (errorDescription) 176 设备序列号不存在

@apiExample {python} python示例:
#!/usr/bin/python
# coding:utf-8
import urllib2, json

def main():
    params = {"sn": "3485171000033"}
    params = json.dumps(params)
    sendreq(params)# 请求删除设备

def sendreq(params):
    sendurl = 'http://127.0.0.1:81/api/v2/iclock/delete/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84'  # 删除设备发送的URL

    wp = urllib2.urlopen(sendurl,params)
    result = wp.read()  # 获取接口返回内容

    if result:
        print result

if __name__ == '__main__':
    main()

"""


def deleteIclock(param):
    # 删除设备
    ret = 0
    msg = ''
    if 'sn' in param:#.has_key("sn"):
        devs = iclock.objects.filter(SN=param["sn"]).exclude(DelTag=1)
        if not devs:
            msg = u"序列号为 %s 的设备不存在" % (param["sn"])
            ret = 176
            result = {'ret': ret, 'msg': msg}
            return result
    else:
        devs = iclock.objects.exclude(DelTag=1)
    if devs:
        pull_flag = 0
        for dev in devs:
            # 以下代码跟delData函数里面删除设备类似。
            sn = dev.SN
            IclockDininghall.objects.filter(SN__exact=sn).delete()
            IclockDept.objects.filter(SN=sn).delete()
            IclockZone.objects.filter(SN=sn).delete()
            doorids = AccDoor.objects.filter(device=sn).values_list('pk', flat=True)
            objs = linkage.objects.filter(device=sn)
            for link in objs:
                SN = link.device_id
                trig_objs = linkage_trigger.objects.filter(linkage=link)
                del_linkage_trig(SN, trig_objs)
                trig_objs.delete()
                linkage_inout.objects.filter(linkage=link).delete()
            objs.delete()
            InterLocks = InterLock.objects.filter(device=sn)
            AntiPassBacks = AntiPassBack.objects.filter(device=sn)
            device_options.objects.filter(SN=sn).delete()
            delEmpInDevice(-1, sn)
            for obj in AntiPassBacks:
                clear_antipassback(obj.device)
            AntiPassBacks.delete()
            for t in InterLocks:
                clear_interlock([t.device_id])
            level_door.objects.filter(door__pk__in=doorids).delete()
            empofdevice.objects.filter(SN=sn).delete()
            meet_devices.objects.filter(SN__SN=sn).delete()
            # 删除设备的时候删除互锁、联动、反潜、首人敞开、多人开门等门禁规则
            InterLock.objects.filter(device__SN=sn).delete()  # 互锁
            linkage.objects.filter(device__SN=sn).delete()  # 联动
            AntiPassBack.objects.filter(device__SN=sn).delete()  # 反潜
            FirstOpen.objects.filter(door__device__SN=sn).delete()  # 首人敞开
            combopen_door.objects.filter(door__device__SN=sn).delete()  # 多人开门
            device = getDevice(sn)
            if device.ProductType == 15:
                pull_flag = 1
            device.Alias = ''
            device.IPAddress = None
            device.Style = None
            device.MaxAttLogCount = None
            device.State = 1
            device.pushver = ""
            device.ProductType = None
            device.save()
        if pull_flag:
            cache.set(settings.UNIT + '_restart_pull', 1)
    ret = 0
    msg = u"成功删除%s台设备" % (len(devs))
    result = {'ret': ret, 'msg': msg}
    return result


"""
@apiGroup iclock
@apiName updateIclock
@apiVersion 10.0.1
@api {post} api/v2/iclock/update/?key=ACCESS_KEY 更新设备
@apiDescription 此接口提供给更新或新增设备信息使用</br>
<table><caption><code>附录1：设备类型说明表</code></caption>
<th>设备类型</th><th>说明</th>
<tr><td>2</td><td>巡更机</td></tr>
<tr><td>4</td><td>简单门禁一体机(考勤PUSH)</td></tr>
<tr><td>5</td><td>门禁控制器(控制器PUSH) </td></tr>
<tr><td>15</td><td>控制器PULL</td></tr>
<tr><td>25</td><td>push一体机(控制器PUSH)</td></tr>
<tr><td>1</td><td>会议</td></tr>
<tr><td>8</td><td>大屏机</td></tr>
<tr><td>9</td><td>考勤机</td></tr>
<tr><td>11</td><td>消费机</td></tr>
<tr><td>12</td><td>出纳机</td></tr>
<tr><td>13</td><td>补贴机</td></tr>
</table>
@apiParam (content) {String} devicename 设备型号 
@apiParam (content) {String} sn 设备序列号
@apiParam (content) {String} ipaddress 设备IP
@apiParam (content) {String} alias  设备名称   
@apiParam (content) {number} producttype  设备类型 </br>
(详见获取考勤记录功能介绍<code>附录1：设备类型说明表</code>) 
@apiParamExample {json} 参数示例：
[
{
"devicename":"iclock520",
"sn":"3485171000033",
"ipaddress":"***************",
"alias":"21楼门禁一体机",
"producttype":4
},
{
"devicename":"iclock520",
"sn":"3485171000023",
"ipaddress":"***************",
"alias":"22楼考勤机",
"producttype":9
}
]
@apiSampleRequest http://127.0.0.1:81/api/v2/iclock/update/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84
@apiSuccess (success) msg 返回结果.
@apiSuccess (success) ret 返回码.
@apiSuccess (success) error_list 更新失败设备序列号.
@apiSuccessExample {json}  响应成功:
{
    "msg": "更新设备成功",
    "ret": 0
}
{
    "msg": "共2台设备，更新成功3台，失败0台，！",
    "ret": 0
}
@apiErrorExample {json} 错误返回示例:
{
    "msg": "共2台设备，更新成功1台，失败1台，！",
    "error_list": [
        "34851713"
    ],
    "ret": 2
}
@apiError (errorDescription) 0 请求成功
@apiError (errorDescription) 1 请求失败
@apiError (errorDescription) 2 批量请求部分失败
@apiError (errorDescription) 180 设备序列号长度小于10
@apiError (errorDescription) 183 SN不能为空
@apiError (errorDescription) 184 无此设备类型
@apiError (errorDescription) 185 设备类型新增时不能为空
@apiError (errorDescription) 186 系统超过授权设备数量，请联系供应商!
@apiError (errorDescription) 187 设备数量已达上限
@apiError (errorDescription) 188 设备序列号未经授权
@apiExample {python} python示例:
#!/usr/bin/python
# coding:utf-8
import urllib2, json

def main():
    params =[{
        "devicename":"iclock520",
        "sn":"3485171000033",
        "ipaddress":"***************",
        "alias":"21楼考勤机"
    },
    {
        "devicename": "iclock520",
        "sn": "3485171000023",
        "ipaddress": "***************",
        "alias": "22楼考勤机"
    }
    ]
    params = json.dumps(params)
    sendreq(params)

def sendreq(params):
    sendurl = 'http://127.0.0.1:81/api/v2/iclock/update/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84' 

    wp = urllib2.urlopen(sendurl,params)
    result = wp.read()  # 获取接口返回内容

    if result:
        print result

if __name__ == '__main__':
    main()
"""


def updateIclock(params):
    # 新增设备,编辑
    i_update = 0
    result = {}
    error_list = []
    total = len(params)
    deptobj = None
    for param in params:
        if not param.get("sn"):
            result["ret"] = 183
            result["msg"] = u"SN不能为空"
            continue
        if not isinstance(param.get('sn'), str):
            result['ret'] = 3
            result['msg'] = 'SN数据格式错误'
            continue
        elif len(param['sn']) < 10:
            result['ret'] = 180
            result['msg'] = u"%s 设备序列号长度不能小于10" % (param['sn'])
            error_list.append(param["sn"])
            continue
        if param.get('producttype', None) and (param['producttype'] not in [2, 4, 5, 15, 25, 1, 8, 9, 11, 12, 13]):
            result["ret"] = 184
            result['msg'] = u"%s 设备类型不存在" % (param['sn'])
            error_list.append(param["sn"])
            continue
        dev = iclock.objects.filter(SN=param['sn'])
        param['DelTag'] = 0
        if dev:  # 更新
            param['whereSN'] = param['sn']
            sql, dev_params = getSQL_update_new('iclock', param)
            if customSqlEx(sql, dev_params):
                i_update += 1
        else:  # 新增
            # 多租户模式下添加设备时先检查设备上限、设备是否已授权接入
            if settings.MULTI_TENANT:
                from mysite.base.multitenant_utils import check_max_device, check_auth_device
                if not check_max_device():
                    result['ret'] = 187
                    result['msg'] = (u'设备数量已达上限')
                    error_list.append(param["sn"])
                    continue
                if not check_auth_device(param["sn"]):
                    result['ret'] = 188
                    result['msg'] = (u'设备序列号未经授权')
                    error_list.append(param["sn"])
                    continue
            if not param.get('producttype', None):
                result['ret'] =185
                result['msg'] = u"%s 设备类型新增时不能为空" % (param['sn'])
                error_list.append(param["sn"])
                continue
            param['TransInterval'] = 1
            param['AlgVer'] = '10'
            param['TZAdj'] = 8
            param['State'] = 1
            param['UpdateDB'] = '1111100000'
            param['AccFun'] = 0
            param['DelTag'] = 0
            param['LogStamp'] = 0
            param['OpLogStamp'] = 0
            param['PhotoStamp'] = 0
            param['Authentication'] = 1
            param['isUSERPIC'] = 0
            param['isFptemp'] = 1
            param['isFace'] = 0
            param['CreateTime'] = datetime.datetime.now()
            param['consume_order'] = 4
            param['offline_consumption'] = False
            cache.delete("iclock_" + param["sn"])
            iclock_count= iclock.objects.all().count()
            ic = iclock.objects.all().exclude(DelTag=1).count()
            if ic >= settings.MAX_DEVICES:
                error_list.append(param["sn"])
                result['ret'] = 186
                result["msg"] = u"系统超过授权设备数量，请联系供应商!"
                break
            param['id'] = iclock_count+1
            sql, dev_params = getSQL_insert_new('iclock', param)
            if customSqlEx(sql, dev_params):
                i_update += 1
        result['ret'] = 0
        result["msg"] = u"更新设备成功"
    if total > 1:
        if len(error_list) > 0:
            result['ret'] = 2
            result["error_list"] = error_list
        result['msg'] = u"共%s台设备，更新成功%s台，失败%s台！" % (total, i_update, len(error_list))
    return result
