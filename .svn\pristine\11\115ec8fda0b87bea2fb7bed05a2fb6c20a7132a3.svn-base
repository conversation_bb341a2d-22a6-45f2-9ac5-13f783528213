{% load i18n %}
{% autoescape off %}
[
    {% for item in latest_item_list %}
    {
        "id": "{{ item.id }}",
        "code": "{{ item.code }}",
        "name": "{% if can_change %}<a class='can_edit' href='#' onclick='javascript:editclick({{item.id}});'>{{ item.name|trim }}</a>{% else %}{{ item.name|trim }}{% endif %}",
        "formula": "{{ item.formula }}",
        "action":"{% if user|HasPerm:'payroll.view_employee_salarytemplate' %}<a style='color:green;' onclick='get_salary_template_employee({{item.id}})'><img title='{% trans 'view people' %}' src='../media/img/images/team.gif'/></a>&nbsp;{% endif %}{% if user|HasPerm:'payroll.set_employee_to_salarytemplate' %}<a style='color:green;' href='#' onclick='createDlgSalaryTemplate_emp({{item.id}})'><img title='{% trans 'add people' %}' src='../media/img/add.png'/></a>{% endif %}"
    }
    {%if not forloop.last%},{%endif%}
    {% endfor %}
]
{% endautoescape %}
