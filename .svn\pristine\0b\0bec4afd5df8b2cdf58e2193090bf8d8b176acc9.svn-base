/*!
 * CSS3 Microsoft Metro Buttons
 * Inspired by <PERSON>'s CSS3 Google Buttons, Twitter Bootstrap, and Microsoft. Icons from glyphicons.com and Syncfusion's Metro Studio.
 * I do not claim ownership on the origin of design and icons.
 * Built by <PERSON> Subido (http://github.com/ace-subido)
 */a:focus{outline:thin dotted;outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}a:hover,a:active{outline:0}button,input,select,textarea{margin:0;font-size:100%}button,input{*overflow:visible;line-height:normal}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}button,input[type="button"],input[type="reset"],input[type="submit"]{cursor:pointer;-webkit-appearance:button;-moz-appearance:none}@-moz-document url-prefix(){button,input[type="button"],input[type="reset"],input[type="submit"]{cursor:pointer;padding:6px 14px}}input[type="search"]{-webkit-appearance:textfield;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}input[type="search"]::-webkit-search-decoration,input[type="search"]::-webkit-search-cancel-button{-webkit-appearance:none}.dropdown{position:relative}.dropdown-toggle{*margin-bottom:-3px}.dropdown-toggle:active,.open .dropdown-toggle{outline:0}.caret{display:inline-block;width:0;height:0;text-indent:-99999px;*text-indent:0;vertical-align:top;margin-top:5px;margin-left:2px;margin-right:2px;border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid black;opacity:.5;filter:alpha(opacity=50);content:"\2193"}.caret.white{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid white;opacity:.95;filter:alpha(opacity=95)}.dropdown .caret{margin-top:8px;margin-left:2px}.dropdown:hover .caret,.open.dropdown .caret{opacity:1;filter:alpha(opacity=100)}.m-dropdown-menu{position:absolute;top:98%;left:0;z-index:1000;float:left;display:none;min-width:225px;max-width:225px;padding:0 0 6px 0;margin:0;list-style:none;background-color:white;-webkit-box-shadow:0 1px 8px rgba(0,0,0,0.1);-moz-box-shadow:0 1px 8px rgba(0,0,0,0.1);box-shadow:0 1px 8px rgba(0,0,0,0.1);font-size:14px;font-family:"Segoe UI",Helvetica,Arial,sans-serif;border:1px solid #eee}.m-dropdown-menu.bottom-up{top:auto;bottom:100%;margin-bottom:2px}.m-dropdown-menu .divider{border-top:1px solid #ebebeb;margin-top:9px;margin-bottom:10px;padding:0;cursor:default}.m-dropdown-menu a{position:relative;padding:6px 0 6px 30px;color:#333;text-decoration:none;display:block;clear:both;font-weight:normal;line-height:18px;white-space:nowrap}.m-dropdown-menu a [class^="icon-"]{position:absolute;left:7px;top:8px}.m-dropdown-menu li>a:hover,.m-dropdown-menu .active>a,.m-dropdown-menu .active>a:hover{text-decoration:none;background-color:#eee}.dropdown.open{*z-index:1000}.dropdown.open .dropdown-toggle{color:#08c;background:#ccc;background:rgba(0,0,0,0.3)}.dropdown.open .m-dropdown-menu{display:block}.m-btn{position:relative;display:inline-block;overflow:visible;margin:0;padding:10px 14px;margin-top:8px;cursor:pointer;outline:0;border:0;background-color:#eee;background-image:-moz-linear-gradient(top,#eee,#eee);background-image:-ms-linear-gradient(top,#eee,#eee);background-image:-webkit-gradient(linear,0 0,0 100%,from(#eee),to(#eee));background-image:-webkit-linear-gradient(top,#eee,#eee);background-image:-o-linear-gradient(top,#eee,#eee);background-image:linear-gradient(top,#eee,#eee);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee',endColorstr='#eeeeee',GradientType=0);-webkit-background-clip:padding;-moz-background-clip:padding;background-clip:padding;zoom:1;z-index:1;*display:inline;font-family:"Segoe UI",Helvetica,Arial,sans-serif;font-size:14px;line-height:14px;color:#333;min-width:42px;text-shadow:#fff 0 1px 0;text-align:center;text-decoration:none;white-space:nowrap;vertical-align:inherit}.m-btn:hover,.m-btn:focus,.m-btn:active,.m-btn.active{color:#333;text-decoration:none;background-color:#dcdcdc;background-image:-moz-linear-gradient(top,#dcdcdc,#dcdcdc);background-image:-ms-linear-gradient(top,#dcdcdc,#dcdcdc);background-image:-webkit-gradient(linear,0 0,0 100%,from(#dcdcdc),to(#dcdcdc));background-image:-webkit-linear-gradient(top,#dcdcdc,#dcdcdc);background-image:-o-linear-gradient(top,#dcdcdc,#dcdcdc);background-image:linear-gradient(top,#dcdcdc,#dcdcdc);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dcdcdc',endColorstr='#dcdcdc',GradientType=0);z-index:100;outline:0}.m-btn:active,.m-btn.active{background-color:#eee;background-image:-moz-linear-gradient(top,#eee,#dcdcdc);background-image:-ms-linear-gradient(top,#eee,#dcdcdc);background-image:-webkit-gradient(linear,0 0,0 100%,from(#eee),to(#dcdcdc));background-image:-webkit-linear-gradient(top,#eee,#dcdcdc);background-image:-o-linear-gradient(top,#eee,#dcdcdc);background-image:linear-gradient(top,#eee,#dcdcdc);background-repeat:repeat-x;-webkit-box-shadow:inset 0 1px 8px rgba(0,0,0,0.25);-moz-box-shadow:inset 0 1px 8px rgba(0,0,0,0.25);box-shadow:inset 0 1px 8px rgba(0,0,0,0.25);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee',endColorstr='#dcdcdc',GradientType=0)}.m-btn:focus{border-color:#4d90fe}.m-btn::-moz-focus-inner{padding:0;border:0}.m-btn.red-stripe{border-left:3px solid #d84a38}.m-btn.blue-stripe{border-left:3px solid #4d90fe}.m-btn.purple-stripe{border-left:3px solid #852b99}.m-btn.green-stripe{border-left:3px solid #35aa47}.m-btn.red:active,.m-btn.red.active,.m-btn.red.disabled,.m-btn.red[disabled],.m-btn.blue:active,.m-btn.blue.active,.m-btn.blue.disabled,.m-btn.blue[disabled],.m-btn.purple:active,.m-btn.purple.active,.m-btn.purple.disabled,.m-btn.purple[disabled],.m-btn.green:active,.m-btn.green.active,.m-btn.green.disabled,.m-btn.green[disabled],.m-btn.black:active,.m-btn.black.active,.m-btn.black.disabled,.m-btn.black[disabled]{-webkit-box-shadow:inset 0 1px 8px rgba(0,0,0,0.25);-moz-box-shadow:inset 0 1px 8px rgba(0,0,0,0.25);box-shadow:inset 0 1px 8px rgba(0,0,0,0.25);color:white!important}.m-btn.red.disabled,.m-btn.red[disabled],.m-btn.blue.disabled,.m-btn.blue[disabled],.m-btn.purple.disabled,.m-btn.purple[disabled],.m-btn.green.disabled,.m-btn.green[disabled]{opacity:.7}.m-btn.black.disabled,.m-btn.black[disabled]{opacity:.5}.m-btn.red{color:white;text-shadow:none;background-color:#d84a38;background-image:-moz-linear-gradient(top,#dd4b39,#d14836);background-image:-ms-linear-gradient(top,#dd4b39,#d14836);background-image:-webkit-gradient(linear,0 0,0 100%,from(#dd4b39),to(#d14836));background-image:-webkit-linear-gradient(top,#dd4b39,#d14836);background-image:-o-linear-gradient(top,#dd4b39,#d14836);background-image:linear-gradient(top,#dd4b39,#d14836);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dd4b39',endColorstr='#d14836',GradientType=0)}.m-btn.red:hover,.m-btn.red:focus,.m-btn.red:active,.m-btn.red.active,.m-btn.red[disabled],.m-btn.red.disabled{background-color:#c53727;background-image:-moz-linear-gradient(top,#c53727,#c53727);background-image:-ms-linear-gradient(top,#c53727,#c53727);background-image:-webkit-gradient(linear,0 0,0 100%,from(#c53727),to(#c53727));background-image:-webkit-linear-gradient(top,#c53727,#c53727);background-image:-o-linear-gradient(top,#c53727,#c53727);background-image:linear-gradient(top,#c53727,#c53727);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#c53727',endColorstr='#c53727',GradientType=0)}.m-btn.red:active,.m-btn.red.active{background-color:#dd4b39;background-image:-moz-linear-gradient(top,#dd4b39,#c53727);background-image:-ms-linear-gradient(top,#dd4b39,#c53727);background-image:-webkit-gradient(linear,0 0,0 100%,from(#dd4b39),to(#c53727));background-image:-webkit-linear-gradient(top,#dd4b39,#c53727);background-image:-o-lineark-gradient(top,#dd4b39,#c53727);background-image:linear-gradient(top,#dd4b39,#c53727);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dd4b39',endColorstr='#c53727',GradientType=0)}.m-btn.blue{color:white;text-shadow:none;background-color:#4d90fe;background-image:-moz-linear-gradient(top,#4d90fe,#4787ed);background-image:-ms-linear-gradient(top,#4d90fe,#4787ed);background-image:-webkit-gradient(linear,0 0,0 100%,from(#4d90fe),to(#4787ed));background-image:-webkit-linear-gradient(top,#4d90fe,#4787ed);background-image:-o-linear-gradient(top,#4d90fe,#4787ed);background-image:linear-gradient(top,#4d90fe,#4787ed);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#4d90fe',endColorstr='#4787ed',GradientType=0)}.m-btn.blue:hover,.m-btn.blue:focus,.m-btn.blue:active,.m-btn.blue.active,.m-btn.blue[disabled],.m-btn.blue.disabled{background-color:#0072bb;background-image:-moz-linear-gradient(top,#0072bb,#0072bb);background-image:-ms-linear-gradient(top,#0072bb,#0072bb);background-image:-webkit-gradient(linear,0 0,0 100%,from(#0072bb),to(#0072bb));background-image:-webkit-linear-gradient(top,#0072bb,#0072bb);background-image:-o-linear-gradient(top,#0072bb,#0072bb);background-image:linear-gradient(top,#0072bb,#0072bb);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#0072bb',endColorstr='#0072bb',GradientType=0)}.m-btn.blue:active,.m-btn.blue.active{background-color:#4d90fe;background-image:-moz-linear-gradient(top,#4d90fe,#0072bb);background-image:-ms-linear-gradient(top,#4d90fe,#0072bb);background-image:-webkit-gradient(linear,0 0,0 100%,from(#4d90fe),to(#0072bb));background-image:-webkit-linear-gradient(top,#4d90fe,#0072bb);background-image:-o-linear-gradient(top,#4d90fe,#0072bb);background-image:linear-gradient(top,#4d90fe,#0072bb);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#4d90fe',endColorstr='#0072bb',GradientType=0)}.m-btn.green{color:white;text-shadow:none;background-color:#35aa47;background-image:-moz-linear-gradient(top,#35aa47,#35aa47);background-image:-ms-linear-gradient(top,#35aa47,#35aa47);background-image:-webkit-gradient(linear,0 0,0 100%,from(#35aa47),to(#35aa47));background-image:-webkit-linear-gradient(top,#35aa47,#35aa47);background-image:-o-linear-gradient(top,#35aa47,#35aa47);background-image:linear-gradient(top,#35aa47,#35aa47);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#35aa47',endColorstr='#35aa47',GradientType=0)}.m-btn.green:hover,.m-btn.green:focus,.m-btn.green:active,.m-btn.green.active,.m-btn.green.disabled,.m-btn.green[disabled]{background-color:#1d943b;background-image:-moz-linear-gradient(top,#1d943b,#1d943b);background-image:-ms-linear-gradient(top,#1d943b,#1d943b);background-image:-webkit-gradient(linear,0 0,0 100%,from(#1d943b),to(#1d943b));background-image:-webkit-linear-gradient(top,#1d943b,#1d943b);background-image:-o-linear-gradient(top,#1d943b,#1d943b);background-image:linear-gradient(top,#1d943b,#1d943b);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#1d943b',endColorstr='#1d943b',GradientType=0)}.m-btn.green:active,.m-btn.green.active{background-color:#35aa47;background-image:-moz-linear-gradient(top,#35aa47,#1d943b);background-image:-ms-linear-gradient(top,#35aa47,#1d943b);background-image:-webkit-gradient(linear,0 0,0 100%,from(#35aa47),to(#1d943b));background-image:-webkit-linear-gradient(top,#35aa47,#1d943b);background-image:-o-linear-gradient(top,#35aa47,#1d943b);background-image:linear-gradient(top,#35aa47,#1d943b);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#35aa47',endColorstr='#1d943b',GradientType=0)}.m-btn.purple{color:white;text-shadow:none;background-color:#852b99;background-image:-moz-linear-gradient(top,#852b99,#852b99);background-image:-ms-linear-gradient(top,#852b99,#852b99);background-image:-webkit-gradient(linear,0 0,0 100%,from(#852b99),to(#852b99));background-image:-webkit-linear-gradient(top,#852b99,#852b99);background-image:-o-linear-gradient(top,#852b99,#852b99);background-image:linear-gradient(top,#852b99,#852b99);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#852b99',endColorstr='#852b99',GradientType=0)}.m-btn.purple:hover,.m-btn.purple:focus,.m-btn.purple:active,.m-btn.purple.active,.m-btn.purple.disabled,.m-btn.purple[disabled]{background-color:#6d1b81;background-image:-moz-linear-gradient(top,#6d1b81,#6d1b81);background-image:-ms-linear-gradient(top,#6d1b81,#6d1b81);background-image:-webkit-gradient(linear,0 0,0 100%,from(#6d1b81),to(#6d1b81));background-image:-webkit-linear-gradient(top,#6d1b81,#6d1b81);background-image:-o-linear-gradient(top,#6d1b81,#6d1b81);background-image:linear-gradient(top,#6d1b81,#6d1b81);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#6d1b81',endColorstr='#6d1b81',GradientType=0)}.m-btn.purple:active,.m-btn.purple.active{background-color:#35aa47;background-image:-moz-linear-gradient(top,#852b99,#6d1b81);background-image:-ms-linear-gradient(top,#852b99,#6d1b81);background-image:-webkit-gradient(linear,0 0,0 100%,from(#852b99),to(#6d1b81));background-image:-webkit-linear-gradient(top,#852b99,#6d1b81);background-image:-o-linear-gradient(top,#852b99,#6d1b81);background-image:linear-gradient(top,#852b99,#6d1b81);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#852b99',endColorstr='#6d1b81',GradientType=0)}.m-btn.black{color:white;text-shadow:none;background-color:#555;background-image:-moz-linear-gradient(top,#555,#555);background-image:-ms-linear-gradient(top,#555,#555);background-image:-webkit-gradient(linear,0 0,0 100%,from(#555),to(#555));background-image:-webkit-linear-gradient(top,#555,#555);background-image:-o-linear-gradient(top,#555,#555);background-image:linear-gradient(top,#555,#555);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#555555',endColorstr='#555555',GradientType=0)}.m-btn.black:hover,.m-btn.black:focus,.m-btn.black:active,.m-btn.black.active,.m-btn.black.disabled,.m-btn.black[disabled]{background-color:#222;background-image:-moz-linear-gradient(top,#222,#222);background-image:-ms-linear-gradient(top,#222,#222);background-image:-webkit-gradient(linear,0 0,0 100%,from(#222),to(#222));background-image:-webkit-linear-gradient(top,#222,#222);background-image:-o-linear-gradient(top,#222,#222);background-image:linear-gradient(top,#222,#222);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#222222',endColorstr='#222222',GradientType=0)}.m-btn.black:active,.m-btn.black.active{background-color:#222;background-image:-moz-linear-gradient(top,#444,#222);background-image:-ms-linear-gradient(top,#444,#222);background-image:-webkit-gradient(linear,0 0,0 100%,from(#222),to(#222));background-image:-webkit-linear-gradient(top,#444,#222);background-image:-o-linear-gradient(top,#444,#222);background-image:linear-gradient(top,#444,#222);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#444444',endColorstr='#222222',GradientType=0)}.sm{font-size:11px}.mini{height:13px;font-size:11px;line-height:13px;padding:4px 10px}.big{height:38px;font-size:18px;line-height:38px;padding:20px 26px}.rnd{-webkit-border-radius:5px;-moz-border-radius:5px;border-radius:5px}.big.rnd{-webkit-border-radius:11px;-moz-border-radius:11px;border-radius:11px}.m-btn.disabled,.m-btn[disabled]{color:#999;background-color:#f5f5f5;background-image:-moz-linear-gradient(top,#eee,#ddd);background-image:-ms-linear-gradient(top,#eee,#ddd);background-image:-webkit-gradient(linear,0 0,0 100%,from(#eee),to(#ddd));background-image:-webkit-linear-gradient(top,#eee,#ddd);background-image:-o-linear-gradient(top,#eee,#ddd);background-image:linear-gradient(top,#eee,#ddd);background-repeat:repeat-x;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee',endColorstr='#dddddd',GradientType=0);cursor:default;-webkit-box-shadow:inset 0 1px 6px rgba(0,0,0,0.25);-moz-box-shadow:inset 0 1px 6px rgba(0,0,0,0.25);box-shadow:inset 0 1px 6px rgba(0,0,0,0.25)}.m-btn.icn-only{min-width:14px}.m-btn.bigicn-only{min-width:34px}.m-btn-group{position:relative;display:inline-block;list-style:none;padding:0;margin:0;zoom:1;*display:inline}.m-btn+.m-btn,.m-btn+.m-btn-group,.m-btn-group+.m-btn,.m-btn-group+.m-btn-group{margin-left:15px}.m-btn.dropdown-carettoggle{min-width:5px;height:18px;padding:8px}.m-btn.dropdown-carettoggle>.caret{margin-top:8px}.m-btn.caret:hover{opacity:1}.m-btn-group .m-btn{position:relative;float:left;margin-left:-1px}.m-btn-group .m-btn:first-child{margin-left:0}.m-btn-group .m-btn.rnd:first-child{-webkit-border-radius:5px 0 0 5px;-moz-border-radius:5px 0 0 5px;border-radius:5px 0 0 5px}.m-btn-group .m-btn.rnd.dropdown-carettoggle{-webkit-border-radius:0 5px 5px 0;-moz-border-radius:0 5px 5px 0;border-radius:0 5px 5px 0}.m-btn-strip .m-btn,.m-btn-strip .m-btn-group{vertical-align:top}.m-btn-group.open{*z-index:1000}.m-btn-group.open .dropdown-carettoggle,.m-btn-group.open .dropdown-toggle{background-image:none;-webkit-box-shadow:inset 0 1px 6px rgba(0,0,0,0.2);-moz-box-shadow:inset 0 1px 6px rgba(0,0,0,0.2);box-shadow:inset 0 1px 6px rgba(0,0,0,0.2)}.m-btn-group.open .m-dropdown-menu{display:block;margin-top:1px}label.m-wrap,input.m-wrap,button.m-wrap,select.m-wrap,textarea.m-wrap{font-size:14px;font-weight:normal;line-height:20px}input.m-wrap,button.m-wrap,select.m-wrap,textarea.m-wrap{font-family:"Segoe UI","Helvetica Neue",Helvetica,Arial,sans-serif}label.m-wrap{display:block;margin-bottom:5px}select.m-wrap,textarea.m-wrap,input[type="text"].m-wrap,input[type="password"].m-wrap,input[type="datetime"].m-wrap,input[type="datetime-local"].m-wrap,input[type="date"].m-wrap,input[type="month"].m-wrap,input[type="time"].m-wrap,input[type="week"].m-wrap,input[type="number"].m-wrap,input[type="email"].m-wrap,input[type="url"].m-wrap,input[type="search"].m-wrap,input[type="tel"].m-wrap,input[type="color"].m-wrap,.m-uneditable-input{vertical-align:top;display:inline-block;height:20px;padding:6px 6px;margin-bottom:9px;margin-top:0;font-size:14px;line-height:20px;color:#333;background-color:#fff;border:1px solid #eee;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}input.m-wrap,textarea.m-wrap,.m-uneditable-input{width:206px}textarea.m-wrap{height:auto}textarea.m-wrap,input[type="text"].m-wrap,input[type="password"].m-wrap,input[type="datetime"].m-wrap,input[type="datetime-local"].m-wrap,input[type="date"].m-wrap,input[type="month"].m-wrap,input[type="time"].m-wrap,input[type="week"].m-wrap,input[type="number"].m-wrap,input[type="email"].m-wrap,input[type="url"].m-wrap,input[type="search"].m-wrap,input[type="tel"].m-wrap,input[type="color"].m-wrap,.m-uneditable-input{background-color:#fff;border:1px solid #eee;-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none;-webkit-transition:border linear .2s,box-shadow linear .2s;-moz-transition:border linear .2s,box-shadow linear .2s;-o-transition:border linear .2s,box-shadow linear .2s;transition:border linear .2s,box-shadow linear .2s;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}textarea.m-wrap:focus,input[type="text"].m-wrap:focus,input[type="password"].m-wrap:focus,input[type="datetime"].m-wrap:focus,input[type="datetime-local"].m-wrap:focus,input[type="date"].m-wrap:focus,input[type="month"].m-wrap:focus,input[type="time"].m-wrap:focus,input[type="week"].m-wrap:focus,input[type="number"].m-wrap:focus,input[type="email"].m-wrap:focus,input[type="url"].m-wrap:focus,input[type="search"].m-wrap:focus,input[type="tel"].m-wrap:focus,input[type="color"].m-wrap:focus,.m-uneditable-input:focus{border-color:#111;outline:0;outline:thin dotted \9;-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none}input[type="radio"].m-wrap,input[type="checkbox"].m-wrap{margin:4px 0 0;margin-top:1px \9;*margin-top:0;line-height:normal;cursor:pointer;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}input[type="file"].m-wrap,input[type="image"].m-wrap,input[type="radio"].m-wrap,input[type="checkbox"].m-wrap{width:auto}select.m-wrap,input[type="file"].m-wrap{height:34px;*margin-top:4px;line-height:30px}select.m-wrap{width:220px;background-color:#fff;border:1px solid #aaa}select[multiple].m-wrap,select[size].m-wrap{height:auto}select.m-wrap:focus,input[type="file"].m-wrap:focus,input[type="radio"].m-wrap:focus,input[type="checkbox"].m-wrap:focus{outline:thin dotted #333;outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}.m-uneditable-input,.m-uneditable-textarea{color:#999;cursor:default;background-color:#fafafa;border-color:#aaa;-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none}.m-uneditable-input{overflow:hidden;white-space:nowrap}.m-uneditable-textarea{width:auto;height:auto}input.m-wrap:-moz-placeholder,textarea.m-wrap:-moz-placeholder{color:#999}input.m-wrap:-ms-input-placeholder,textarea.m-wrap:-ms-input-placeholder{color:#999}input.m-wrap::-webkit-input-placeholder,textarea.m-wrap::-webkit-input-placeholder{color:#999}.m-radio,.m-checkbox{min-height:18px;padding-left:18px}.m-radio input[type="radio"].m-wrap,.m-checkbox input[type="checkbox"].m-wrap{float:left;margin-left:-18px}.m-controls>.m-radio:first-child,.m-controls>.m-checkbox:first-child{padding-top:5px}.m-radio.inline,.m-checkbox.inline{display:inline-block;padding-top:5px;margin-bottom:0;vertical-align:middle}.m-radio.inline+.m-radio.inline,.m-checkbox.inline+.m-checkbox.inline{margin-left:10px}.m-ctrl-small{width:120px!important}.m-ctrl-medium{width:206px!important}.m-ctrl-large{width:320px!important}.m-ctrl-huge{width:480px!important;font-size:24px!important;line-height:36px!important;padding:22px 8px!important}input[class*="span"].m-wrap,select[class*="span"].m-wrap,textarea[class*="span"].m-wrap,.m-uneditable-input[class*="span"]{float:none;margin-left:0}.m-input-append input[class*="span"],.m-input-append .m-uneditable-input[class*="span"],.m-input-prepend input[class*="span"],.m-input-prepend .m-uneditable-input[class*="span"]{display:inline-block}input.m-wrap,textarea.m-wrap,.m-uneditable-input{margin-left:0}.m-input-prepend .add-on>[class^="icon-"]{margin-top:5px;margin-left:3px}.m-input-append .add-on>[class^="icon-"]{margin-top:5px;margin-left:0}input[disabled].m-wrap,select[disabled].m-wrap,textarea[disabled].m-wrap{cursor:not-allowed;background-color:#fafafa}input[readonly].m-wrap,select[readonly].m-wrap,textarea[readonly].m-wrap{cursor:default;background-color:#fafafa}input[type="radio"][disabled].m-wrap,input[type="checkbox"][disabled].m-wrap,input[type="radio"][readonly].m-wrap,input[type="checkbox"][readonly].m-wrap{background-color:transparent}input.m-wrap:focus:required:invalid,textarea.m-wrap:focus:required:invalid,select.m-wrap:focus:required:invalid{color:#b94a48;border-color:#444}input.m-wrap:focus:required:invalid:focus,textarea.m-wrap:focus:required:invalid:focus,select.m-wrap:focus:required:invalid:focus{border-color:#444}.m-input-append,.m-input-prepend{margin-bottom:5px;font-size:0;white-space:nowrap}.m-input-append input,.m-input-prepend input,.m-input-append select,.m-input-prepend select,.m-input-append .uneditable-input,.m-input-prepend .uneditable-input{position:relative;margin-bottom:0;*margin-left:0;font-size:14px;vertical-align:top;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-append input:focus,.m-input-prepend input:focus,.m-input-append select:focus,.m-input-prepend select:focus,.m-input-append .m-uneditable-input:focus,.m-input-prepend .m-uneditable-input:focus{z-index:2}.m-input-append .add-on,.m-input-prepend .add-on{display:inline-block;width:auto;height:24px;min-width:16px;padding:4px 5px;font-size:14px;font-weight:normal;line-height:24px;text-align:center;text-shadow:0 1px 0 #fff;background-color:#ddd;border:1px solid #eee}.m-input-append .add-on,.m-input-prepend .add-on,.m-input-append .m-btn,.m-input-prepend .m-btn{vertical-align:top;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-append .active,.m-input-prepend .active{background-color:#a9dba9;border-color:#46a546}.m-input-prepend .add-on,.m-input-prepend .m-btn{margin-top:0;margin-right:-1px}.m-input-prepend .add-on:first-child,.m-input-prepend .m-btn:first-child{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-append input,.m-input-append select,.m-input-append .m-uneditable-input{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-append .add-on,.m-input-append .m-btn{margin-left:-1px;margin-top:0}.m-input-append .add-on:last-child,.m-input-append .m-btn:last-child{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-prepend.input-append input,.m-input-prepend.input-append select,.m-input-prepend.input-append .m-uneditable-input{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-prepend.m-input-append .add-on:first-child,.m-input-prepend.m-input-append .m-btn:first-child{margin-right:-1px;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.m-input-prepend.m-input-append .add-on:last-child,.m-input-prepend.m-input-append .m-btn:last-child{margin-left:-1px;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}/*!
 * CSS3 Microsoft Metro Buttons
 * Inspired by Tim O'Donnell's CSS3 Google Buttons, Twitter Bootstrap, and Microsoft. Icons from glyphicons.com and Syncfusion's Metro Studio.
 * I do not claim ownership on the origin of design and icons.
 * Built by Ace Subido (http://github.com/ace-subido)
 */.m-btn [class^="icon-"]{display:inline-block;width:14px;height:14px;margin-top:0;line-height:14px;vertical-align:top;background-image:url(../img/glyphicons-halflings.png);background-repeat:no-repeat}.m-btn[class^="icon-"]:last-child{*margin-left:0}.m-btn .icon-white{background-image:url(../img/glyphicons-halflings-white.png)}.disabled>[class^="icon-"],[disabled]>[class^="icon-"]{opacity:.5;filter:alpha(opacity=50)}.disabled>[class^="m-icon-"],[disabled]>[class^="m-icon-"]{opacity:.4;filter:alpha(opacity=40)}.icon-glass{background-position:0 0}.icon-music{background-position:-24px 0}.icon-search{background-position:-48px 0}.icon-envelope{background-position:-72px 0}.icon-heart{background-position:-96px 0}.icon-star{background-position:-120px 0}.icon-star-empty{background-position:-144px 0}.icon-user{background-position:-168px 0}.icon-film{background-position:-192px 0}.icon-th-large{background-position:-216px 0}.icon-th{background-position:-240px 0}.icon-th-list{background-position:-264px 0}.icon-ok{background-position:-288px 0}.icon-remove{background-position:-312px 0}.icon-zoom-in{background-position:-336px 0}.icon-zoom-out{background-position:-360px 0}.icon-off{background-position:-384px 0}.icon-signal{background-position:-408px 0}.icon-cog{background-position:-432px 0}.icon-trash{background-position:-456px 0}.icon-home{background-position:0 -24px}.icon-file{background-position:-24px -24px}.icon-time{background-position:-48px -24px}.icon-road{background-position:-72px -24px}.icon-download-alt{background-position:-96px -24px}.icon-download{background-position:-120px -24px}.icon-upload{background-position:-144px -24px}.icon-inbox{background-position:-168px -24px}.icon-play-circle{background-position:-192px -24px}.icon-repeat{background-position:-216px -24px}.icon-refresh{background-position:-240px -24px}.icon-list-alt{background-position:-264px -24px}.icon-lock{background-position:-287px -24px}.icon-flag{background-position:-312px -24px}.icon-headphones{background-position:-336px -24px}.icon-volume-off{background-position:-360px -24px}.icon-volume-down{background-position:-384px -24px}.icon-volume-up{background-position:-408px -24px}.icon-qrcode{background-position:-432px -24px}.icon-barcode{background-position:-456px -24px}.icon-tag{background-position:0 -48px}.icon-tags{background-position:-25px -48px}.icon-book{background-position:-48px -48px}.icon-bookmark{background-position:-72px -48px}.icon-print{background-position:-96px -48px}.icon-camera{background-position:-120px -48px}.icon-font{background-position:-144px -48px}.icon-bold{background-position:-167px -48px}.icon-italic{background-position:-192px -48px}.icon-text-height{background-position:-216px -48px}.icon-text-width{background-position:-240px -48px}.icon-align-left{background-position:-264px -48px}.icon-align-center{background-position:-288px -48px}.icon-align-right{background-position:-312px -48px}.icon-align-justify{background-position:-336px -48px}.icon-list{background-position:-360px -48px}.icon-indent-left{background-position:-384px -48px}.icon-indent-right{background-position:-408px -48px}.icon-facetime-video{background-position:-432px -48px}.icon-picture{background-position:-456px -48px}.icon-pencil{background-position:0 -72px}.icon-map-marker{background-position:-24px -72px}.icon-adjust{background-position:-48px -72px}.icon-tint{background-position:-72px -72px}.icon-edit{background-position:-96px -72px}.icon-share{background-position:-120px -72px}.icon-check{background-position:-144px -72px}.icon-move{background-position:-168px -72px}.icon-step-backward{background-position:-192px -72px}.icon-fast-backward{background-position:-216px -72px}.icon-backward{background-position:-240px -72px}.icon-play{background-position:-264px -72px}.icon-pause{background-position:-288px -72px}.icon-stop{background-position:-312px -72px}.icon-forward{background-position:-336px -72px}.icon-fast-forward{background-position:-360px -72px}.icon-step-forward{background-position:-384px -72px}.icon-eject{background-position:-408px -72px}.icon-chevron-left{background-position:-432px -72px}.icon-chevron-right{background-position:-456px -72px}.icon-plus-sign{background-position:0 -96px}.icon-minus-sign{background-position:-24px -96px}.icon-remove-sign{background-position:-48px -96px}.icon-ok-sign{background-position:-72px -96px}.icon-question-sign{background-position:-96px -96px}.icon-info-sign{background-position:-120px -96px}.icon-screenshot{background-position:-144px -96px}.icon-remove-circle{background-position:-168px -96px}.icon-ok-circle{background-position:-192px -96px}.icon-ban-circle{background-position:-216px -96px}.icon-arrow-left{background-position:-240px -96px}.icon-arrow-right{background-position:-264px -96px}.icon-arrow-up{background-position:-289px -96px}.icon-arrow-down{background-position:-312px -96px}.icon-share-alt{background-position:-336px -96px}.icon-resize-full{background-position:-360px -96px}.icon-resize-small{background-position:-384px -96px}.icon-plus{background-position:-408px -96px}.icon-minus{background-position:-433px -96px}.icon-asterisk{background-position:-456px -96px}.icon-exclamation-sign{background-position:0 -120px}.icon-gift{background-position:-24px -120px}.icon-leaf{background-position:-48px -120px}.icon-fire{background-position:-72px -120px}.icon-eye-open{background-position:-96px -120px}.icon-eye-close{background-position:-120px -120px}.icon-warning-sign{background-position:-144px -120px}.icon-plane{background-position:-168px -120px}.icon-calendar{background-position:-192px -120px}.icon-random{width:16px;background-position:-216px -120px}.icon-comment{background-position:-240px -120px}.icon-magnet{background-position:-264px -120px}.icon-chevron-up{background-position:-288px -120px}.icon-chevron-down{background-position:-313px -119px}.icon-retweet{background-position:-336px -120px}.icon-shopping-cart{background-position:-360px -120px}.icon-folder-close{background-position:-384px -120px}.icon-folder-open{width:16px;background-position:-408px -120px}.icon-resize-vertical{background-position:-432px -119px}.icon-resize-horizontal{background-position:-456px -118px}.icon-hdd{background-position:0 -144px}.icon-bullhorn{background-position:-24px -144px}.icon-bell{background-position:-48px -144px}.icon-certificate{background-position:-72px -144px}.icon-thumbs-up{background-position:-96px -144px}.icon-thumbs-down{background-position:-120px -144px}.icon-hand-right{background-position:-144px -144px}.icon-hand-left{background-position:-168px -144px}.icon-hand-up{background-position:-192px -144px}.icon-hand-down{background-position:-216px -144px}.icon-circle-arrow-right{background-position:-240px -144px}.icon-circle-arrow-left{background-position:-264px -144px}.icon-circle-arrow-up{background-position:-288px -144px}.icon-circle-arrow-down{background-position:-312px -144px}.icon-globe{background-position:-336px -144px}.icon-wrench{background-position:-360px -144px}.icon-tasks{background-position:-384px -144px}.icon-filter{background-position:-408px -144px}.icon-briefcase{background-position:-432px -144px}.icon-fullscreen{background-position:-456px -144px}[class^="m-icon-"]{display:inline-block;width:14px;height:14px;margin-top:0;line-height:14px;vertical-align:top;background-image:url(../img/syncfusion-icons.png);background-position:0 0;background-repeat:no-repeat}[class^="m-icon-big-"]{display:inline-block;width:30px;height:30px;margin:6px;vertical-align:top;background-image:url(../img/syncfusion-icons.png);background-position:0 0;background-repeat:no-repeat}.m-icon-white{background-image:url(../img/syncfusion-icons-white.png)}[class^="big-"]:last-child{*margin-left:0}.m-icon-swapright{background-position:-27px -10px}.m-icon-swapdown{background-position:-68px -10px}.m-icon-swapleft{background-position:-8px -10px}.m-icon-swapup{background-position:-47px -10px}.m-icon-big-swapright{background-position:-42px -28px}.m-icon-big-swapdown{background-position:-115px -28px}.m-icon-big-swapleft{background-position:-6px -28px}.m-icon-big-swapup{background-position:-78px -28px}/*!
 * CSS3 Microsoft Metro Buttons
 * Inspired by Tim O'Donnell's CSS3 Google Buttons, Twitter Bootstrap, and Microsoft. Icons from glyphicons.com and Syncfusion's Metro Studio.
 * I do not claim ownership on the origin of design and icons.
 * Built by Ace Subido (http://github.com/ace-subido)
 */article,aside,details,figcaption,figure,footer,header,hgroup,nav,section{display:block}audio,canvas,video{display:inline-block;*display:inline;*zoom:1}audio:not([controls]){display:none}html{font-size:100%;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{max-width:100%;height:auto;border:0;-ms-interpolation-mode:bicubic}