#!/usr/bin/env python
#coding=utf-8
import datetime
from django.contrib.auth.decorators import permission_required, login_required
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist

from mysite.iclock.i18n_backend import trans_opt
from mysite.utils import *
from mysite.ipos.models import *
from mysite.iclock.models import *
from mysite.iclock.iutils import userIClockList,getDiningByUser,AllChildrenDininghall,userDininghallList
@login_required
def getData(request):
    funid = request.GET.get("func", "")
    dining_id = request.GET.get("dining_id", "")
    if funid=='diningtree':
        rt = []
        Dingids = []
        model_name = request.GET.get('modelName', '')
        k = request.GET.get('K', '')
        if model_name == 'User' and k:
            Dingids = DingAdmin.objects.filter(user__exact=k).values_list('ding_id', flat=True)
        elif model_name == 'userDining' and k:
            edit_user = MyUser.objects.get(pk=k)
            if edit_user.is_superuser or edit_user.is_all_restaurant:
                Dingids = Dininghall.objects.exclude(DelTag=1).values_list('id', flat=True)
            else:
                Dingids = DingAdmin.objects.filter(user__exact=k).values_list('ding_id', flat=True)
        child = []
        if dining_id:
            AllChildrenDininghall(int(dining_id), child)
        if request.user.is_superuser or request.user.is_all_restaurant:
            if dining_id :
                objs = Dininghall.objects.all().order_by('parent', 'code').exclude(DelTag=1).exclude(id=dining_id).exclude(id__in=child)
            else:
               objs = Dininghall.objects.all().order_by('parent', 'code').exclude(DelTag=1)
        else:
            if dining_id:
                objs = Dininghall.objects.filter(id__in = getDiningByUser(request.user)).exclude(DelTag=1).exclude(id=dining_id).exclude(id__in=child).order_by('code')
            else:
                objs = Dininghall.objects.filter(id__in=getDiningByUser(request.user)).exclude(DelTag=1).order_by('code')
        ll = {}
        Dings = {}
        for o in objs:
            if o in child: continue
            Dings['%s_code' % o.id] = o.code
            Dings['%s_id' % o.id] = o.id
            Dings['%s_name' % o.id] = o.name
            Dings['%s_parent' % o.id] = o.parent
            try:
                ll[int(o.parent)].append(o.id)
            except:
                ll[int(o.parent)] = []
                ll[int(o.parent)].append(o.id)
        usedDing = []
        for z in objs:
            d = {}
            if z.id in usedDing or z in child:
                continue
            usedDing.append(z.id)
            d["id"] = z.id
            d["name"] = z.name
            d["pid"] = z.parent
            d["icon"] = "/media/img/icons/home.png"
            d["value"] = 0
            d["open"] = True
            d["isParent"] = False
            if z.id in Dingids:
                d['checked'] = True
            else:
                d['checked'] = False
            # if d['pid']==0:
            #	d['icon']="/media/img/icons/home.png"
            try:
                d["children"] = get_ding_list(z.id, ll, Dings, usedDing, Dingids)
            except:
                d["children"] = []
            rt.append(d.copy())
        return getJSResponse(rt)


def get_ding_list(key,ll,Dings,usedDing,Dingids):
    d=[]
    dd={}
    for o in ll[key]:
        if Dings['%s_id'%o] in usedDing:
            continue
        usedDing.append(Dings['%s_id'%o])
        dd["id"]=Dings['%s_id'%o]
        dd["name"]=Dings['%s_name'%o]
        dd["pid"]=Dings['%s_parent'%o]
        dd["value"]=Dings['%s_code'%o]
        dd["open"]=True
        dd["isParent"]=False
        if o in Dingids:
            dd['checked'] = True
        else:
            dd['checked'] = False
        dd['icon'] = '/media/img/icons/home.png'
        try:
            dd["children"]=get_ding_list(o,ll,Dings,usedDing,Dingids)
        except:
            dd["children"]=[]
        d.append(dd.copy())
    return d