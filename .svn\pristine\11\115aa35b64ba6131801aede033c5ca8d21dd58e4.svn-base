{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <script src="{{ MEDIA_URL }}js/mui.min.js"></script>
    <script src="{{ MEDIA_URL }}js/mui.js"></script>
    <link href="{{ MEDIA_URL }}css/mui.min.css" rel="stylesheet"/>
    <style type="text/css">
        .mui-segmented-control .mui-control-item {
            line-height: 40px;
            text-align: center;
        }
        .mui-table-view-cell>a:not(.mui-btn){
            margin: -18px -15px;
        }
        .mui-control-content {
            min-height: 215px;
        }
        .mui-control-content .mui-loading {
            margin-top: 50px;
        }
        html,#slider,.mui-content{
            width: 100%;
            height: 100%;
        }
        body{
            width: 100%;
            height: 100%;
        }
        .mui-slider-group{
            height: calc(100% - 45px);
        }
        .mui-bar-tab .mui-tab-item.mui-active
        {
            color: #95CD6A;
        }

    </style>
{% endblock %}
{% block content %}
	<header class="mui-bar mui-bar-nav">
		<a href="/iapp/att/epidemic/" class=" mui-icon mui-icon-left-nav mui-pull-left"></a>
		<h1 class="mui-title" style='font-size: 16px'>{% trans 'Daily temperature' %}</h1>
	</header>
    <div id="temperature_collect">
        <div class="mui-card" style="top:40px;border-radius: 10px;height: 100px;">
            <!--页眉，放置标题-->
            <div id="totalItem" class="mui-card-header mui-card-media" style="opacity:0.5;height:30vw;background-color: #8AD013">
    {#                <div style="width:30%;height:10%;float:right;background-image: url('/iclock/file/app/fangyi.jpg');-moz-background-size:100% 100%; background-size:100% 100%;"></div>#}
                    <div>
                        {% trans 'Date:' %}<label id="TTime" style="border: 5px;"></label>
                    </div>
                    <br />
                    <div>
                        <div style="float: left;">
                            <label>{% trans 'Total number of people: ' %} {% trans 'people' %}</label><br />
                            <label>{% trans 'Temperature measured: ' %} {% trans 'people' %}</label>
                        </div>
                        <div style="float: right;">
                            <label>{% trans 'Abnormal temperature: ' %} {% trans 'people' %}</label><br />
                            <label>{% trans 'Temperature not measured: ' %} {% trans 'people' %}</label>
                        </div>
                    </div>
                </div>
        </div>
        <div  class="mui-content">
            <div class="mui-scroll-wrapper" style="top:25%;height: 70%;">
                <div id="DeptData" class="mui-scroll">
                </div>
            </div>
        </div>
    </div>
    <div id="untest_person" class="mui-content mui-hidden" >
        <div class="mui-input-row mui-search">
            <input type="search" class="mui-input-clear" id="UntestedEmp" style=""
                   onkeyup="UntestedEmpSearch()" placeholder="{% trans 'Please enter the employee name or pin for search' %}">
        </div>
        <div class="mui-table-view-cell" style="white-space: nowrap;text-align:center">
            <p style="float:left;width: 33%;">{% trans 'Department' %}</p>
            <p style="float:left;width: 33%;">{% trans 'PIN' %}</p>
            <p style="float:left;width: 33%;">{% trans 'Emp Name' %}</p>
        </div>
        <div id="pullrefresh" class="mui-scroll-wrapper" style="height: 78%;top:22%;">
            <div class="mui-scroll" >
                <ul id="UntestEmp" class="mui-table-view">
                </ul>
            </div>
        </div>
    </div>
    <nav class="mui-bar mui-bar-tab ">
        <div class="mui-tab-item mui-active" onclick="ChangeItem('temperature_collect','untest_person')">{% trans 'Temperature Summary' %}</div>
        <div class="mui-tab-item" style="touch-action: none;" onclick="ChangeItem('untest_person','temperature_collect')">{% trans 'Untested personnel' %}</div>
    </nav>
{% endblock %}
{% block extrjs %}
<script>
    mui.init({
        swipeBack: true,
        // beforeback: function() {
        //     plus.webview.getLaunchWebview().show("pop-in", 200, function () {
        //         plus.webview.currentWebview().close("none");
        //     });
        //     return false;  //返回false,不再走页面关闭逻辑
        // },
        pullRefresh: {
            container: '#pullrefresh',
            down: {
                style:'circle',//必选，下拉刷新样式，目前支持原生5+ ‘circle’ 样式
                callback: pulldownRefresh
            },
            up: {
                auto:true,
                contentrefresh: '{% trans 'Loading...' %}',
                callback: pullupRefresh
             }
        }
    });
    (function($) {
        $('.mui-scroll-wrapper').scroll({
            indicators: true //是否显示滚动条
        });
        //初始加载审批中的体温数据
        loadData();

    })(mui);
    /*上拉加载具体业务实现*/
    offset = 0;
    var endRefresh = false;
    function pullupRefresh() {
        var q = document.getElementById('UntestedEmp').value;
        setTimeout(function() {
            mui('#pullrefresh').pullRefresh().endPullupToRefresh(endRefresh); //参数为true代表没有更多数据了。
            var newCount = 15; //每次加载15条
            mui.ajax('/iapp/get_data_info/?info_type=untest_person'+"&limit="+newCount+"&offset="+offset+"&q="+q,{
                data:{},
                dataType:'json',
                type:'get',
                timeout: 5000,
                success: function (data) {
                    mui('#pullrefresh').pullRefresh().endPullupToRefresh(endRefresh); //参数为true代表没有更多数据了。
                    var untestemphtml = [];
                    for(var i=0;i<data.length;i++){
                        untestemphtml.push('<li class="mui-table-view-cell">\n' +
                            '    <div style="white-space: nowrap;text-align:center;text-overflow:ellipsis;padding-left:1%;padding-right:1%;">\n' +
                            '        <p style="float:left;width: 33%;overflow:hidden;text-overflow:ellipsis;">'+data[i].DeptName+'</p>\n' +
                            '       <p style="float:left;width: 33%;overflow:hidden;text-overflow:ellipsis;">'+data[i].PIN+'</p>\n' +
                            '       <p style="float:left;width: 33%;overflow:hidden;text-overflow:ellipsis;">'+data[i].EName+'</p>\n' +
                            '    </div>\n' +
                            '</li>')
                    }
                    var itemList = document.getElementById('UntestEmp');
                    var div = document.createElement('ul');
                    div.innerHTML = untestemphtml.join('');
                    itemList.appendChild(div);
                },
                    error: function (xhr, type, errorThrown) {

                }
            });
            offset += 1;
        }, 10);
    }
    /*下拉刷新具体业务实现*/
    function pulldownRefresh() {
        setTimeout(function() {
            /*清空数据,重新加载*/
            offset = 0;
            endRefresh = false;
            var table = document.getElementById('UntestEmp');
            table.innerHTML = "";
            pullupRefresh();
            mui('#pullrefresh').pullRefresh().refresh(true);        //重置上拉加载
            mui('#pullrefresh').pullRefresh().endPulldownToRefresh(true);
            mui.toast("{% trans 'Refresh successful' %}");
        }, 10);
    }
    function loadData(){

        mui.ajax('/iapp/get_data_info/?info_type=temperature_collect',{
            data:{},
            dataType:'json',
            type:'post',
            success:function(data){
                if(data.ret==0){
                    var btnArray = ['{% trans 'No' %}', '{% trans 'Yes' %}'];
                    mui.confirm('{% trans 'Do you want to go ahead and bind it' %}', '{% trans 'Unbound Administrator' %}', btnArray, function(e) {
                        if (e.index == 1) {
                            window.location.href="../user_bind/edit/"
                        } else {
                            window.location.href="../epidemic/"
                        }
                    })
                }
                else{
                    var totalhtml = '<div>' +
                    '                    <label style="border: 5px;">{% trans 'Date:' %}'+data.TTime+'</label>\n' +
                    '                    </div>\n' +
                    '                    <br />\n' +
                    '                    <div>\n' +
                    '                        <div style="float: left;">\n' +
                    '                            <label>{% trans 'Total number of people: ' %}'+data.TotalNum+'{% trans 'people' %}</label><br />\n' +
                    '                            <label>{% trans 'Temperature measured: ' %}'+data.TestedNum+'{% trans 'people' %}</label>\n' +
                    '                        </div>\n' +
                    '                        <div style="float: right;">\n' +
                    '                            <label>{% trans 'Abnormal temperature: ' %}'+data.AbnormalNum+'{% trans 'people' %}</label><br />\n' +
                    '                            <label>{% trans 'Temperature not measured: ' %}'+data.UntestedNum+'{% trans 'people' %}</label>\n' +
                    '                        </div>\n' +
                    '                    </div>';
                    document.getElementById('totalItem').innerHTML = totalhtml;
                    var deptdatahtml = '';
                    for(var i=0;i<data.DeptTemperature.length;i++){
                        if(data.DeptTemperature[i].AbnormalEmp.length<1){
                            deptdatahtml+= '<div class="mui-card">\n' +
                        '            <ul class="mui-table-view">\n' +
                        '                <li class="mui-table-view-cell mui-collapse">\n' +
                        '                        <div style="white-space: nowrap;text-align:center;">\n' +
                        '                            <p style="float:left;width: 23%;font-weight:bolder">'+data.DeptTemperature[i].DeptName+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">{% trans 'Temperature measured' %}</p>\n' +
                        '                            <p style="float:left;width: 23%;">{% trans 'Temperature not measured' %}</p>\n' +
                        '                            <p style="float:left;width: 23%;">{% trans 'Abnormal temperature' %}</p>\n' +
                        '                        </div>\n' +
                        '                        <div style="white-space: nowrap;text-align:center">\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].TotalNum+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].TestedNum+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].UntestedNum+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].AbnormalNum+'</p>\n' +
                        '                        </div>\n' +
                        '                   </li>\n' +
                        '            </ul>\n' +
                        '         </div>'
                        }
                        else{
                            deptdatahtml += '<div class="mui-card">\n' +
                        '            <ul class="mui-table-view">\n' +
                        '                <li class="mui-table-view-cell mui-collapse">\n' +
                        '                    <a class="mui-navigate-right" href="#">\n' +
                        '                        <div style="white-space: nowrap;text-align:center;">\n' +
                        '                            <p style="float:left;width: 23%;font-weight:bolder;overflow:hidden;text-overflow:ellipsis;">'+data.DeptTemperature[i].DeptName+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">{% trans 'Temperature measured' %}</p>\n' +
                        '                            <p style="float:left;width: 23%;">{% trans 'Temperature not measured' %}</p>\n' +
                        '                            <p style="float:left;width: 23%;">{% trans 'Abnormal temperature' %}</p>\n' +
                        '                        </div>\n' +
                        '                        <div style="white-space: nowrap;text-align:center">\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].TotalNum+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].TestedNum+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].UntestedNum+'</p>\n' +
                        '                            <p style="float:left;width: 23%;">'+data.DeptTemperature[i].AbnormalNum+'</p>\n' +
                        '                        </div>\n' +
                        '                    </a>\n' +
                        '                    <ul class="mui-table-view">\n'
                        for(var j=0;j<data.DeptTemperature[i].AbnormalEmp.length;j++){
                                deptdatahtml += '                         <li class="mui-table-view-cell">\n' +
                            '                             <div style="white-space: nowrap;text-align:center">\n' +
                            '                                <p style="float:left;width: 23%;font-weight:bolder">{% trans 'PIN' %}</p>\n' +
                            '                                <p style="float:left;width: 23%;">{% trans 'Emp Name' %}</p>\n' +
                            '                                <p style="float:left;width: 23%;">{% trans 'Times of temperature measurement' %}</p>\n' +
                            '                                <p style="float:left;width: 23%;">{% trans 'temperature' %}</p>\n' +
                            '                            </div>\n' +
                            '                            <div style="white-space: nowrap;text-align:center;text-overflow:ellipsis;">\n' +
                            '                                <p style="float:left;width: 23%;overflow:hidden;text-overflow:ellipsis;">'+data.DeptTemperature[i].AbnormalEmp[j].PIN+'</p>\n' +
                            '                                <p style="float:left;width: 23%;overflow:hidden;text-overflow:ellipsis;">'+data.DeptTemperature[i].AbnormalEmp[j].EName+'</p>\n' +
                            '                                <p style="float:left;width: 23%;overflow:hidden;text-overflow:ellipsis;">'+data.DeptTemperature[i].AbnormalEmp[j].Times+'</p>\n' +
                            '                                <p style="float:left;width: 23%;overflow:hidden;text-overflow:ellipsis;">'+data.DeptTemperature[i].AbnormalEmp[j].temperature+'</p>\n' +
                            '                            </div>\n' +
                            '                         </li>\n'
                            }
                            deptdatahtml += '            </ul>\n' +
                            '                    </div>'
                        }

                    }
                    document.getElementById('DeptData').innerHTML = deptdatahtml;
                }

            }
        });


    }
function UntestedEmpSearch(showName,hiddenName) {
                    /*清空数据,重新筛选*/
            offset = 0;
            endRefresh = false;
            var table = document.getElementById('UntestEmp');
            table.innerHTML = "";
            pullupRefresh();
}
function ChangeItem(showName,hiddenName) {
    var showItem = document.getElementById(showName);
    var hiddenItem = document.getElementById(hiddenName);
    hiddenItem.classList.add('mui-hidden');
    showItem.classList.remove('mui-hidden');

    if(showName == 'untest_person'){
            offset = 0;
            endRefresh = false;
            var table = document.getElementById('UntestEmp');
            table.innerHTML = "";
            pullupRefresh();
            mui('#pullrefresh').pullRefresh().refresh(true);        //重置上拉加载
            mui('#pullrefresh').pullRefresh().endPulldownToRefresh(true);
     }
}
</script>
{% endblock %}