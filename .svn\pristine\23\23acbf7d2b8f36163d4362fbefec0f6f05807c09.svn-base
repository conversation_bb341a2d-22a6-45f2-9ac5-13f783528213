{% extends "iapp/wap_base.html" %}
{% load iclock_tags %}
{% load i18n %}

{% block extrastyle %}
    <script src="{{ MEDIA_URL }}js/mui.picker.min.js"></script>
    <script src="{{ MEDIA_URL }}js/mui.view.js "></script>
    <link href="{{ MEDIA_URL }}css/mui.picker.min.css" rel="stylesheet"/>
    <style type="text/css">
        #bottom_btns{
            margin-top: 50px;
        }
        #bottom_btns button{
            width: 32%;
        }
        .my_info_right{
            float: right;
        }
        .my_info_div{
            width: 98%;
            margin: auto;
        }
        .my_info_div ul li input{
            width: 50%;
        }
        .bottom_menu{
            margin-top: 30px;
        }
        .mui-btn {
            background-color: #7ac143;
            border:0px;
            font-size: 16px;
            padding: 8px;
            margin: 3px;
        }

    </style>
{% endblock %}
{% block content %}
    <header class="mui-bar mui-bar-nav">
        <a href="/iapp/att/tableapply/" class="mui-icon mui-icon-left-nav mui-pull-left"></a>
        <h1 class="mui-title" style='font-size: 16px'>{% trans 'Application for renewal' %}</h1>
    </header>
    <div class="mui-content my_info_div">
        <ul class="mui-table-view">
            <li id="li_checkdate_a" class="mui-table-view-cell">
                <a class="mui-navigate-right">
                    <span class="mui-pull-left">{% trans 'Reissue date' %}</span>
                    <span id='checkdate_a' data-options='{"type":"date"}' class="mui-pull-right" 
                            style="margin-right: 20px;min-width:160px;text-align: right;">{% trans 'Please select (required)' %}</span>
                </a>
            </li>
            <li id="li_checktime" class="mui-table-view-cell">
                <a class="mui-navigate-right">
                    <span class="mui-pull-left">{% trans 'Reissue time' %}</span>
                    <span id='checktime' data-options='{"type":"time"}' class="mui-pull-right"
                            style="margin-right: 20px;min-width:160px;text-align: right;">{% trans 'Please select (required)' %}</span>
                </a>
            </li>
            <li id="li_checktype" class="mui-table-view-cell">
                <a class="mui-navigate-right">
                    <span class="mui-pull-left">{% trans 'record type' %}</span>
                    <span id='checktype' class="mui-pull-right mui-parenttitle"
                            style="margin-right: 20px;min-width:160px;text-align: right;">{% trans 'Please select (required)' %}</span>
                </a>
            </li>
            <li class="mui-table-view-cell">
                <label>{% trans 'Reason' %}</label>
                <div class="mui-input-row" style="margin: 5px 0px;">
                    <textarea id="reason" rows="5" placeholder="{% trans 'Please provide detailed reasons' %}"></textarea>
                </div>
            </li>
        </ul>
        <div class="mui-content-padded">
            <button id='saveBtn' class="mui-btn mui-btn-block mui-btn-primary">{% trans 'Submit' %}</button>
        </div>
    </div>
{% endblock %}
{% block extrjs %}
    <script type="text/javascript">
        function getCookie(name) {
            var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
            if(arr=document.cookie.match(reg)) {
                return decodeURI(arr[2]);
            } else {
                return null;
            }
        } 
        (function($, doc){
            $.init();

            var saveUrl = '/iclock/apply/checkexact/_new_/'

            //编辑页面
            // var newsid = window.location.href.split('?')[1];
            var newsid = {{datakey}};
            var state
            if (newsid > 0) {
                mui.ajax('/iapp/get_data_info/?info_type=checkexact&id='+newsid+'',{
                    dataType:'json',
                    type:'post',
                    timeout:5000,
                    headers:{'Content-Type':'application/json'},
                    success:function(data){
                        $('#checkdate_a')[0].innerText=data[0].checkdate_a
                        $('#checktime')[0].innerText=data[0].checktime
                        $('#checktype')[0].value=data[0].checktype
                        $('#checktype')[0].innerText=data[0].checktype_name
                        $('#reason')[0].value=data[0].reason
                        state = data[0].state
                    },
                    error:function(xhr,type,errorThrown){
                        //异常处理；
                        // console.log(type);
                    }
                });
                saveUrl = '/iclock/apply/checkexact/'+newsid+'/'
            }
            //补签日期
            document.getElementById("li_checkdate_a").addEventListener('tap', function(){
                var dDate = new Date();
                var optionsJson = document.getElementById("checkdate_a").getAttribute('data-options') || '{}';
                var options = JSON.parse(optionsJson);
                var picker = new mui.DtPicker(options);
                picker.show(function (rs){
                    var timestr = rs.text;
                    $('#checkdate_a')[0].innerText=rs.text
                    picker.dispose();
                });
            })
            //补签时间
            document.getElementById("li_checktime").addEventListener('tap', function(){
                var dDate = new Date();
                var optionsJson = document.getElementById("checktime").getAttribute('data-options') || '{}';
                var options = JSON.parse(optionsJson);
                var picker = new mui.DtPicker(options);
                picker.show(function(rs){
                    var timestr = rs.text;
                    $('#checktime')[0].innerText=rs.text
                    picker.dispose();
                });
            })

            //补签类型
            var userPicker = new $.PopPicker();
            var showUserPickerButton = document.getElementById('checktype');
            showUserPickerButton.value = 0;
            document.getElementById('li_checktype').addEventListener('tap', function(event) {
                $.ajax('/iapp/get_data_info/?info_type=get_recordstatus',{
                    dataType:'json',
                    type:'get',
                    timeout:5000,
                    headers:{'Content-Type':'application/json'},
                    success:function(data){
                        var l = []
                        for(var i=0; i<data.length; i++) {
                            l[i] = data[i]
                        }
                        userPicker.setData(l)
                        for(var i=0; i<data.length; i++){
                            userPicker.show(function(items){
                                showUserPickerButton.innerText = items[0].text;
                                showUserPickerButton.value = items[0].value;
                            });

                        }

                },
                error:function(xhr,type,errorThrown){
                    //异常处理；
                    // console.log(type);
                }
            });
            }, false)

            //提交
            document.getElementById("saveBtn").addEventListener('tap',function(){
                checkdate_a = $('#checkdate_a')[0].innerText
                checktime = $('#checktime')[0].innerText
                checktype = $('#checktype')[0].innerText

                if (checkdate_a == "{% trans 'Please select (required)' %}" || checktime == "{% trans 'Please select (required)' %}" || checktype == "{% trans 'Please select (required)' %}"){
                    mui.toast('{% trans 'Incomplete required fields' %}');
                    return false
                }
                if(state !== 'Apply' && state !== '{% trans 'Apply' %}'&& state !== 'Again Apply' && state !== '{% trans 'Again' %}'&& state !== undefined){
                       mui.toast('{% trans 'Approved data cannot be modified' %}');
                       return false
                }
                //点击响应逻辑
                $.ajax(saveUrl,{
                    data:{
                        checkdate_a:checkdate_a,
                        checktime:checktime,
                        audit:1,
                        reson: mui('#reason')[0].value,
                        YUYIN: mui('#reason')[0].value,
                        checktype: mui('#checktype')[0].value,
                        CHECKTYPE: mui('#checktype')[0].value,
                        CHECKTIME: checkdate_a+' '+checktime+':00'
                    },
                    dataType:'json',
                    type:'post',
                    timeout:10000,
                    headers:{'Content-Type':'application/x-www-form-urlencoded','X-CSRFToken':getCookie('csrftoken')},
                    success:function(data){
                        if (data.ret==0){
                            $.toast(data.message);  //自动消失提示框
                            intervalTimer = setInterval(function () {
                                window.location.href = "/iapp/main/";
                            }, 4000);
                        }
                    },
                    error:function(xhr,type,errorThrown){
                        //异常处理；
                        // console.log(type);
                    }
                });

            });

        })(mui);
    </script>
{% endblock %}