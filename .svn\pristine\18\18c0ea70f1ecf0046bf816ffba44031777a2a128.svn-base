{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <div class="x-body">
        <div class="layui-input-inline" style="width: 150px;"><input class="layui-input" placeholder="{% trans 'StartDate' %}" name="start" id="start" autocomplete="off"></div>
        <div class="layui-input-inline" style="width: 150px;"><input class="layui-input" placeholder="{% trans 'End Date' %}" name="end" id="end" autocomplete="off"></div>
        <div class="layui-input-inline">
            <select name="is_receive" id="is_receive" style="height:38px">
                <option value="" style="line-height: 36px ">{% trans 'Whether to receive' %}</option>
                <option value="0">{% trans 'Unclaimed' %}</option>
                <option value="1">{% trans 'Claimed' %}</option>
            </select>
        </div>
        <a class="layui-btn search_btn" data-type="reload" id="search_menu">{% trans 'Inquire' %}</a>
        <a class="layui-btn layui-btn-small" style="margin-top:3px;float:right"
           href="javascript:location.replace(location.href);" title="{% trans 'Reload' %}">
            <i class="icon iconfont" style="line-height:30px">&#xe6aa;</i></a>
        <table class="layui-hide" id="user_allowance" lay-filter="user_allowance"></table>
    </div>
{% endblock %}
{% block extrjs %}
    <script>
        layui.use(['table', 'laydate'], function () {
            var table = layui.table;
            var laydate = layui.laydate;
            var form = layui.form;
            var startDate = laydate.render({
                elem: '#start',
                type: 'date',
                done: function (value, date) {
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1, //关键
                        date: date.date
                    };
                }
            });
            var endDate = laydate.render({
                elem: '#end',
                type: 'date',
                done: function (value, date) {
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    }
                }
            });
            cols = [[
                      {type: 'checkbox'}
                    , {field: 'pin', width: 110, title: "{% trans 'BadgeNumber' %}"}
                    , {field: 'username', width: 110, title: "{% trans 'Name' %}"}
                    , {field: 'cardno', width: 120, title: "{% trans 'Id card' %}"}
                    , {field: 'sys_card_no', width: 100, title: "{% trans 'card account' %}"}
                    , {field: 'money', width: 100, title: "{% trans 'subsidy amount' %}"}
                    , {field: 'receive_money', width: 100, title: "{% trans 'receipt amount' %}"}
                    , {field: 'is_pass', width: 110, title: "{% trans 'Any review' %}"}
                    , {field: 'is_ok', width: 110, title: "{% trans 'Whether to receive' %}"}
                    , {field: 'allow_date', width: 110, title: "{% trans 'subsidy date' %}", sort: true}
                    , {field: 'receive_date', width: 166, title: "{% trans 'receive time' %}", sort: true}
                ]]
            {% if "POS_IC"|filter_config_option %}
                cols[0].push({field: 'valid_date', width: 110, title: "{% trans 'effective date' %}", sort: true})
            {% endif %}

{#            {% if ""|get_clearmoney_type %}#}
{#                cols[0].splice(6, 0, {field: 'clear_money', width: 100, title: "{% trans '清零补贴' %}"});#}
{#            {% endif %}#}

            table.render({
                elem: '#user_allowance'
                , url: '/selfservice/ipos/get_data_info/?info_type=Allowance'       // 数据接口
                , cellMinWidth: 50 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                , id: 'id'
                , page: true
                , cols: cols
            });
            var $ = layui.$, active = {
                reload: function () {
                    var start = $("input[name='start']").val();
                    var end = $("input[name='end']").val();
                    var is_receive = $('#is_receive');
                    table.reload('id', {
                        where: {
                            st: start,
                            et: end,
                            is_receive: is_receive.val()
                        }
                    });
                }
            };

            $('#search_menu').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
            table.on('sort(user_allowance)', function(obj){ //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                  // console.log(obj.field); //当前排序的字段名
                  // console.log(obj.type); //当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
                  //尽管我们的 table 自带排序功能，但并没有请求服务端。
                  //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
                table.reload('id', {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。 layui 2.1.1 新增参数
                    ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                      field: obj.field //排序字段   在接口作为参数字段  field order
                      ,order: obj.type //排序方式   在接口作为参数字段  field order
                    },
                  });
            });
        });
    </script>
{% endblock %}