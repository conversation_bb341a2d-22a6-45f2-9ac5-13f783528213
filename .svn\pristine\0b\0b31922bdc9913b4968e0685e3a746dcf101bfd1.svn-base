{% extends "selfservice/web_base.html" %}
{% load iclock_tags %}
{% load i18n %}
{% block content %}
    <form class="layui-form" style="width:80%;">
        <div class="layui-form-item layui-row layui-col-xs12"></div>
        <div class="layui-form-item layui-row layui-col-xs12">
            <label class="layui-form-label">{% trans 'BadgeNumber' %}</label>
            <div class="layui-input-block">
                <input type="text" name=pin id="id_pin" class="layui-input" required disabled>
            </div>
        </div>
        <div class="layui-form-item layui-row layui-col-xs12">
            <label class="layui-form-label">{% trans 'name' %}</label>
            <div class="layui-input-block">
                <input type="text" name=username id="id_username" class="layui-input" required disabled>
            </div>
        </div>
{#        <div class="layui-form-item layui-row layui-col-xs12">#}
{#            <label class="layui-form-label">{% trans 'Sex' %}</label>#}
{#            <div class="layui-input-block">#}
{#                <input type="text" name=Gender id="id_Gender" class="layui-input" required disabled>#}
{#            </div>#}
{#        </div>#}
{#        <div class="layui-form-item layui-row layui-col-xs12">#}
{#            <label class="layui-form-label">{% trans 'Mobile' %}</label>#}
{#            <div class="layui-input-block">#}
{#                <input type="text" name=Mobile id="id_Mobile" class="layui-input" required>#}
{#            </div>#}
{#        </div>#}
{#        <div class="layui-form-item layui-row layui-col-xs12">#}
{#            <label class="layui-form-label">{% trans 'Social Insurance' %}</label>#}
{#            <div class="layui-input-block">#}
{#                <input type="text" name=SSN id="id_SSN" class="layui-input" required disabled>#}
{#            </div>#}
{#        </div>#}
        <div class="layui-form-item layui-row layui-col-xs12">
            <label class="layui-form-label">{% trans 'e-mail address' %}</label>
            <div class="layui-input-block">
                <input type="text" name=email id="id_email" class="layui-input" required disabled>
            </div>
        </div>
{#        <div class="layui-form-item layui-row layui-col-xs12">#}
{#          <div class="layui-input-block">#}
{#              <button class="layui-btn layui-btn-m" lay-submit="" lay-filter="edit">{% trans 'Save Changes' %}</button>#}
{#<!--              <button type="reset" class="layui-btn layui-btn-primary">{% trans 'Reset' %}</button>-->#}
{#          </div>#}
{#      </div>#}
    </form>
{% endblock %}
{% block extrjs %}
  <script>
  layui.use('form', function(){
        var form = layui.form;
        var layer = layui.layer;
        form.on('submit(edit)', function(data){
            var p_obj = data.field;
            $.ajax('/selfservice/att/user_info_edit/', {
                data: {
                    user_pin: p_obj.pin,
                    phone: p_obj.Mobile,
                    email: p_obj.email,
                },
                dataType: 'json',
                        type: 'post',
                        timeout: 10000,
                        async: false,
                        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                        success: function (data) {
                            if (data.ret == 0) {
                                    alert("{% trans 'Personal info' %}{% trans 'Save Success' %}");
<!--                                layer.msg("{% trans 'Personal info' %}{% trans 'Save Success' %}",{icon: 6});-->
                                parent.location.reload();
                            } else {
<!--                                layer.msg(data.message);-->
                                    alert(data.message);
                                    parent.location.reload();
                            }
                        },
                        error: function (xhr, type, errorThrown) {
                            //异常处理；
                            // console.log(type);
                        }
            })
        });
  });


  {% autoescape off %}
    try {
       var user_info={{ data }}
    }
    catch(err){
       user_info = "";
    }

  {% endautoescape %}
    $(function(){
      for(var i in user_info){
        $('#id_'+i).attr('value',user_info[i])
      }
  });


  </script>
{% endblock %}