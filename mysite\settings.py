#!/usr/bin/python
# -*- coding: utf-8 -*-
import os.path
import re
import sys, time,copy
# Django settings for mysite project.
#from django.conf import settings
#from mysite.core.tools import decryption
#from django import VERSION as DjangoVersion
#DJANGO_VERSION=DjangoVersion[:2]
#正式版改False
DEBUG =False
ICON = 0#1代表软件为（白标）
#TEMPLATE_DEBUG = DEBUG
BASE_DIR = os.path.dirname(os.path.dirname(__file__))
USE_TZ=False
USE_L10N = False
# Host for sending e-mail.
EMAIL_HOST = 'smtp.zkteco.com'
MESSAGR_HOST = 'http://sdk.open.api.igexin.com/apiex.htm'

# Port for sending e-mail.
#EMAIL_PORT = 25

# Optional SMTP authentication information for EMAIL_HOST.
#EMAIL_HOST_USER = '<EMAIL>'
#EMAIL_HOST_PASSWORD = '666888'
#EMAIL_USE_TLS = False
        # ADMINS defined in 1.5 template, not defined in 1.6 template

#ADMINS = (
#    # ('Your Name', '<EMAIL>'),
#)
WORK_PATH=os.path.dirname(__file__)#os.path.split(__file__.decode(sys.getfilesystemencoding()))[0]
#CACHE_BACKEND = 'file://%s/tmp/django_cache'%WORK_PATH
# MANAGERS defined in 1.5 template, not defined in 1.6 template
#MANAGERS = ADMINS

DATETIME_FORMAT = 'Y-m-d H:i:s'
STD_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
DATABASE_ENGINE='sqlite3'
DATABASE_NAME=WORK_PATH+'/icdat.db'
#DATABASE_ENGINE = 'oracle'           #
#DATABASE_NAME = 'oracle9'             # 改成数据库名称
#DATABASE_USER = 'system'             # 改成数据库用户名.
#DATABASE_PASSWORD = 'admin'         # 改成数据库密码.
#DATABASE_HOST = '*************'     # 改成数据库服务器地址
#DATABASE_PORT = '1521'             # 数据库服务器端口号,一般不用改

# Local time zone for this installation. Choices can be found here:
# http://www.postgresql.org/docs/8.1/static/datetime-keywords.html#DATETIME-TIMEZONE-SET-TABLE
# although not all variations may be possible on all operating systems.
# If running in a Windows environment this must be set to the same as your
# system time zone.
#TIME_ZONE = 'Etc/GMT%+-d'%(time.timezone/3600)
from tzlocal import get_localzone
TIME_ZONE = 'Asia/Shanghai'  #指定个默认时区，有些系统获取不到时区出现报错
try:
    TIME_ZONE=get_localzone().zone#'Asia/Shanghai'
except:
    pass
# Language code for this installation. All choices can be found here:
# http://www.w3.org/TR/REC-html40/struct/dirlang.html#langcodes
#LANGUAGE_CODE = 'zh-CN'
#LANGUAGE_CODE = 'zh-Hans'
#LANGUAGE_CODE = 'zh-Hant'
#SITE_ID = 1
LANGUAGE_CODE = 'en'

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
USE_I18N = True

# Absolute path to the directory that holds media.
# Example: "/home/<USER>/media.lawrence.com/"
MEDIA_ROOT = WORK_PATH+'/media/'

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash if there is a path component (optional in other cases).
# Examples: "http://media.lawrence.com", "http://example.com/media/"
MEDIA_URL = '/media/'
#ADDITION_FILE_ROOT = WORK_PATH+'/files/'
#os.chdir(WORK_PATH)
#os.chdir('..')
#FILEPATH=os.getcwd()
FILEPATH=os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
ADDITION_FILE_ROOT = os.path.join(FILEPATH, 'files', '')
# URL prefix for admin media -- CSS, JavaScript and images. Make sure to use a
# trailing slash.
# Examples: "http://foo.com/media/", "/media/".
ADMIN_MEDIA_PREFIX = '/admin/media/'

IGNORABLE_404_URLS = [
    re.compile(r'^/favicon.ico$'),
    re.compile(r'^/part.pac'),
    #re.compile(r'^/robots\.txt$'),
]

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

#关于redis一些配置
try:
    import json
    conf_json_file = os.path.join(FILEPATH, 'redis.json')
    with open(conf_json_file, 'r', encoding='utf-8-sig') as f:
        CONF_EX = json.load(f)
except:
    CONF_EX = {}

# Make this unique, and don't share it with anybody.
SECRET_KEY = 't10g+$^b29eonku&fr+l50efir4&o==k*9)%#*zi5@osf6)q@x'
LOCALE_PATHS = (
    FILEPATH+'/mysite/locale',
)

ROOT_URLCONF = 'mysite.urls'
WSGI_APPLICATION = 'mysite.wsgi.application'

AUTH_USER_MODEL = 'accounts.MyUser'
ALLOWED_HOSTS = ['*']
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
CSRF_TRUSTED_ORIGINS = []
USE_X_FORWARDED_HOST = True  # Django 将使用 X-Forwarded-Host 头部的值来确定请求的域名，而不是请求实际到达的服务器域名
# List of callables that know how to import templates from various sources.
#if DJANGO_VERSION>(1,4):
#    TEMPLATE_LOADERS = (
#   'django.template.loaders.filesystem.Loader',
#   'django.template.loaders.app_directories.Loader',
#    #     'django.template.loaders.eggs.Loader',
#    )
#else:
#    TEMPLATE_LOADERS = (
#   'django.template.loaders.filesystem.load_template_source',
#   'django.template.loaders.app_directories.load_template_source',
#    )



#正式发布用下面的
#TEMPLATE_LOADERS = (
#    ('django.template.loaders.cached.Loader', (
#        'django.template.loaders.filesystem.Loader',
#        'django.template.loaders.app_directories.Loader',
#    )),
#)


MIDDLEWARE = (
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # 'mysite.accounts.middleware.MySessionMiddleware',  # 该中间件是早期用于控制在线管理员数功能(强制下线),但后期管理员的限制调整为控制新增,不再使用该业务
    'mysite.base.threadlocals.middleware.ThreadLocalMiddleware',
    'mysite.base.middleware.DynamicSettingsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'mysite.iclocklocale.LocaleMiddleware',
    # 'mysite.iclocklocale.AuthenticationMiddleware',  # 弃用此中间件, django4提示必须使用django.contrib.auth.middleware.AuthenticationMiddleware, 另缓存已在get_user处理
    'mysite.base.middleware.FileMiddleware',
    'mysite.accounts.middleware.UserRestrictMiddleware',
    'mysite.accounts.middleware.ForceUserLogoutMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
   # 'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'mysite.base.middleware.DecryptMiddleware',
    'mysite.base.middleware.PermissionAuthMiddleware',
    'mysite.api.middleware.LogMiddleware',
    'mysite.base.middleware.DeviceLanguageMiddleware',  # ID消费根据设备上传的语言，返回对应语言的翻译。
)

CACHE_MIDDLEWARE_SECONDS=600
_ = lambda s: s
from django.utils.translation import gettext_lazy as _
LANGUAGES=(
    ('en', _('English')),
    ('zh-cn',_('Simplified Chinese')),
  ('zh-hant', _('Traditional Chinese')),
('vi', _('Vietnamese')),
#  ('ru', _('Russian')),

)
UIES=(
    ('ui0',_(u'standard')),
    ('ui1',_(u'sky blue')),

)
DEFAULT_CHARSET='utf-8'

ROOT_URLCONF = 'mysite.urls'

STATIC_URL = '/media/'
#STATIC_ROOT = os.path.join(BASE_DIR, STATIC_URL.replace("mysite/", ""))
STATIC_ROOT=FILEPATH+'/mysite/media/'
STATICFILES_DIRS = (
    MEDIA_ROOT,
)

#TEMPLATE_DIRS = (
#    WORK_PATH+'/templates/',    # Put strings here, like "/home/<USER>/django_templates" or "C:/www/django/templates".
#    # Always use forward slashes, even on Windows.
#    # Don't forget to use absolute paths, not relative paths.
#)




#if 'mysite.iclocklocale.myContextProc' not in TEMPLATE_CONTEXT_PROCESSORS:
#TEMPLATE_CONTEXT_PROCESSORS = (
#    'django.contrib.messages.context_processors.messages',
#    "django.core.context_processors.debug",
#   "django.core.context_processors.i18n",
#   "django.core.context_processors.media",
##    "django.core.context_processors.auth",
#   'mysite.iclocklocale.auth',
#   'mysite.iclocklocale.myContextProc',
#   'django.template.context_processors.request'
#   )

SALE_EXTENS={}  #扩展功能
AUTHENTICATION_BACKENDS = (
    #'django.contrib.auth.backends.ModelBackend',
    'mysite.accounts.backends.ModelBackend',
    #'authurls.EmployeeBackend',
    )
SALE_MODULE=['att']  # 临时强制设置考勤模块以显示固件管理
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.messages',
    'django.contrib.sessions',
    'mysite.accounts',
    'mysite.base',
    'mysite.iclock',
#    'mysite.core',
    'mysite.acc',
    'mysite.meeting',
    'mysite.visitors',
    'mysite.ipos',
    'mysite.asset',
    'mysite.payroll',
#    'mysite.iapp'

]
if os.path.exists(FILEPATH+'/rosetta'):
    INSTALLED_APPS.append('django.contrib.admin')
    INSTALLED_APPS.append('rosetta')


#if 'acc' in settings.SALE_MODULE:
#   INSTALLED_APPS.append('mysite.acc')
#if 'meeting' in settings.SALE_MODULE:
#   INSTALLED_APPS.append('mysite.meeting')
#if 'ipos' in settings.SALE_MODULE:
#   INSTALLED_APPS.append('mysite.ipos')
#if 'visitors' in settings.SALE_MODULE:
#   INSTALLED_APPS.append('mysite.visitors')
#if 'patrol' in settings.SALE_MODULE:
#   INSTALLED_APPS.append('mysite.patrol')
#   INSTALLED_APPS.append('mysite.app')



try:
    # corsheaders 用于处理iOS发起请求出现options问题
    import corsheaders
    tmp_middleware = list(MIDDLEWARE)
    tmp_middleware.insert(
        0, 'corsheaders.middleware.CorsMiddleware'
        )
    MIDDLEWARE = tuple(tmp_middleware)
    INSTALLED_APPS.append('corsheaders')
    CORS_ORIGIN_ALLOW_ALL = True  # 跨域添加参数为True
except:
    pass


SITETITLE=""
ALL_TAG="ALL"

#SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
#SESSION_SAVE_EVERY_REQUEST=True
MAX_UPDATE_COUNT=50
UPDATE_COUNT=0
MCOUNT=0
APPEND_SLASH=False
LOGIN_REDIRECT_URL="/iclock/imanager"
LOGIN_URL="/accounts/login/"
ICLOCK_AUTO_REG=1
PIN_WIDTH=0
IDFORPIN=0
IMPORT_TRANS=0			#导入考勤记录标签，1可以导入，0不可以
PIRELLI=0			#倍耐力定制为1，标准版为0
transaction_absolute_path = ""
REBOOT_CHECKTIME=0  #
NOCMD_DEVICES=[]
ENCRYPT=0
PAGE_LIMIT=30
UPGRADE_FWVERSION=""
AUTO_PROXY_IP=""
MIN_TRANSINTERVAL=2 #最小传输数据间隔时间(分钟）
MIN_REQ_DELAY=60   #最小检查服务器命令间隔时间(秒）
DISABLED_PINS=['0',0]	#不允许的考勤号码
DISABLED_BIODATA=['0',0] #不允许上传模板信息
TRANS_REALTIME=1	#设备是否实时上传记录
DATABASE_USER='root'
DATABASE_PASSWORD=''
DATABASE_HOST='127.0.0.1'
DATABASE_PORT=1521
NATIVE_ENCODE='GB18030'
MAX_EXPORT_COUNT=20000
MAXFP_L=598  #指纹模板长度
DEPTVERSION=1
SHOWEMPPHOTO=0  #福建泰康要求没有面部的人员不显示照片
RESTARTAPACHE=0
IGNORE_LOG=0    #考勤记录采集天数限制, 几天之前数据不插入数据库
GUIDE_ECO_ATT_LOG = 0

#ID消费机通讯加密
ID_COMM_ENCRYPTION=1
CONSUMPTION_INTERVAL=5  #ID消费刷卡最小时间间隔

PUSH_COMM_ENCRYPTION = 0  #push协议定义的交换密钥、加密因子，加密数据的通讯方式

#APP相关参数
SHOW_APP_SETTINGS=1
AUTH_TERMINAL=1  #绑定手机
ALLOW_REGISRTER = 1  #是否客户自主注册
MAX_APP_CLIENT=500  #最大APP客户端数（含小程序），生产环境，由授权控制（预留）
SUPPORT_CUSTOMIZED_UPGRADE=0  #支持定制APP升级
PROMPT_UPGRADE=1  #客户服务端版本不支持APP时，是否提示更新
OUTWORK_TYPE = 0  #外勤模式  0 先签到再审批  1先审批再签到
ISSONLINE_URL = ''

RESET_DATA=0  #初始化数据参数

OFFLINE_CONSUMPTION=0  #是否支持离线消费 1启用，设备需要定制
ALLOW_ONLINE = 0 #IC在线补贴 1启用
DOUBLE_SCREEN_CONSUMPTION = 0 #id5001双屏消费 1启用
ZKBIOPAY = 1  # 1:安防云支付, 0:微信支付宝支付

SUPPORT_BIOPHOTO_POS = 1  #大连版可见光比对照片消费（如CM300）控制，暂无法区分具体设备，统一控制比对照片
SUPPORT_ZIP_FORMAT_TRANSFER = 0 #大连版（如CM300），zip文件更新用户信息及可见光照片（用于文件方式更新用户信息、用户可见光照片）

SUPPORT_VISITOR_DEVICE=0  #是否支持连接访客设备(暂指证卡的ID2000,ID830设备)
SUPPORT_VISITOR_Z1022V = 0  #是否支持连接访客设备(ZK-Z1022V，江工部门引入的访客设备)

ASSET_DEVICE = 0 #资产设备 1启用
ASSET_REAL_TIME_MONITORING = 0 #资产实时监控 1启用
LOG_MODE = 0  # 1:旧系统日志（管理员操纵日志、员工登录日志、接口调用日志、中间表对接）的日志处理方法, 0:新的日志处理方法
IC_POSLOG_CHECK = 0  #IC消费记录检测

ID_CARD_ACCOUNT_CHECK = 0
SMART_NUMBER_PLATE = 0  #办公室门牌（软件应用生态部产品，江工部门）

#引入开源面部识别库dlib(注意更新numpy及dlib库文件), 当前该功能仅用于测试, 不可用于生产环境
DLIB = 0

#检测web（apache）是否可用的心跳，默认url是ping，但因为抠图dll可能会卡死，需要重启进程才恢复，故加了个url为检测抠图的心跳
JOB_CHECK_URL_TYPE = 0

SALES_REGION = 'CN'  #销售区域  CN中国  VN越南

SUPPORT_P3000_ADVANCED = 0  # P3000系列设备，当前仅支持一些普通功能，如要支持火警、消防类的，打开此参数

MULTI_TENANT = False  #Multi-tenant(多租户模式)

SUPPORT_DOOR4_TO_DOOR2 = 0  # 控制器4门转2门开关，因现在控制器多不支持此功能，为市场方便维护，默认关闭该功能
# try:
#     TMP_DIR=os.environ['TMP']
# except:
#     TMP_DIR="/tmp"

# if not os.path.exists(TMP_DIR+"/"):
#     TMP_DIR=os.path.split(os.tempnam())[0]
LOG_DIR=WORK_PATH+"/tmp"
#标准销售模块:adms,att,acc,meeting,visitors,ipos,patrol,payroll

ENABLED_MOD=[
#	"msg", 		#信息定制模块
#	"weather", 	#天气预报模块
    "person",
    "udisk",	#导入U盘数据文件模块:打开此模块，系统设置中出现u盘导入选项，勾选保存后，u盘导入功能可以使用。
    "adms",      #简单管理模块
    "att",      #考勤管理模块
    "fptemp",         #指纹管理模块
    "uru",      #指纹采集器登记指纹模块
    "acc",  #门禁管理模块
    "face",#面部管理
    "ihr",#人事管理
    "ipos",
    "meeting",
    "visitors",
	"iapp",
    "firmware",
    # "asset",   #定制 不需要展示资产和薪资模块
    # "payroll",
#	"sms", #短信息
#	"patrol"
]

STANDARD_MODULES=[
                        {'id':'person','caption':_(u'Personnel')},
                        {'id':'adms','caption':_(u'ADMS')},
                        {'id':'att','caption':_(u'Attendance')},
                        {'id':'ipos','caption':_(u'Consumption')},
                        {'id':'acc','caption':_(u'Access')},
                        {'id':'visitors','caption':_(u'Visitor')},
                        {'id':'meeting','caption':_(u'Meeting')},
                        {'id':'epidemic','caption':_(u'Antiepidemic')},
                        {'id':'asset','caption':_(u'Asset')},
                        {'id': 'payroll', 'caption': _(u'Payroll')},
                        #{'id':'patrol','caption':_(u'巡更')}
                        {'id':'firmware','caption':_(u'固件管理')},
                        {'id':'system','caption':_(u'System')},
]



MOD_DICT={'person':0,'adms':9,'att':9,'meeting':1,'patrol':2,'acc':5,'ipos':3,'visitors':10,'asset':30}  #与设备表的ProductType 记录表purpose对应
#ENABLED_MOD_TMP=[]
#SALE_MODULE_TMP=copy.deepcopy(SALE_MODULE)
#for m in SALE_MODULE_TMP:
#    if m not in ENABLED_MOD:
#   SALE_MODULE.remove(m)

#G_MODULE=SALE_MODULE[0]  #当前在使用的模块

UNIT=""

APP_HOME=os.path.split(WORK_PATH)[0]
LOG_DIR=os.path.join(APP_HOME, "tmp")
COMPANY=''
#if "USER_COMPANY" in os.environ:
#   UNIT=os.environ['USER_COMPANY']

#if UNIT:
#   UNIT_URL="/u/"+UNIT
#   p_path=APP_HOME+'/attsite_'+UNIT+'.ini'
#   LOGIN_REDIRECT_URL=UNIT_URL+"/iclock/data/iclock/"
#   LOGIN_URL=UNIT_URL+"/iclock/accounts/login/"
#   SESSION_COOKIE_PATH=UNIT_URL
#   LOGOUT_URL=UNIT_URL+"/iclock/accounts/logout/"
#else:
#   UNIT_URL="/"
#   p_path=APP_HOME+'/attsite.ini'
cfFileName=os.path.join(FILEPATH,'attsite.ini')
#print WORK_PATH, p_path
CFG=None
TRIALVERSION=0
SERVER_PORT=80
SERVER_TYPE='WSGI'
CARDTYPE=-1
WRITE_LOG=1   #是否写入日志文件attsite.ini中[SYS]WRITE_LOG=1
CACHE_TYPE='memcached'
REDIS_CACHE="redis://127.0.0.1:6379/1"
NOEXPORT=0
TAIKANG=0
OEM_CODE=''
LICENSEURL=''
DOUBLE_WALLET=0
DEMO=1
ICP=''
ICP_CHECK_URL=''
MD5FILES=''#mysite目录生成的md5
CACHEIP='127.0.0.1:11211'
PRODUCTCODE=11 #11=ecopro  14=zktime10
STRONG_PASSWORD=0
SUPPORT_APP=1  #支持APP
SHOW_MINI_PROGRAM=0  #展示微信小程序二维码
SHOW_WECHAT_OFFICIAL_ACCOUNT=0  #展示微信公众号二维码
SUPPORT_QYWX=0  #支持访客企业微信
NAME_BY_DB=1 #收集设备人员时姓名以数据库为准
pwd_encrypt=0
DATABASE_UPGRADE = 0
LARGE_SCREEN_DISPLAY = 0  #大屏显示开关控制，暂不允许通过attsite配置
RELEASE_PATH_EX = ''

# =============使用Redis进行性能优化的一些参数==================
REDIS_SUPPORT = 1
# 命令存Redis，异步更新数据库（数据库记录写入缓存，通信过程中，在缓存中更新数据，最后批量更新缓存数据进数据库）
REDIS_CMD = 1
# 考勤记录（设备），存Redis，异步批量保存到数据库
REDIS_ATTLOG = 1
# 门禁记录
REDIS_ACCLOG = 1
# 涉及消费poslog记录异步保存，卡流水号获取（从卡现金收支表计算流水，不维护卡流水号表了）
REDIS_POSLOG = 1
# 照片相关
REDIS_PHOTO = 1
#从缓存解析照片到磁盘的消费者数（线程数），默认1，对于普通机械硬盘，建议设置值不超过5，推荐3，固态硬盘可相应调大数值，以实际测试效果为准
REDIS_PHOTO_CONSUMERS = 1

REDIS_OPERLOG = 1  # OPERLOG相关记录，除照片外

REDIS_OPTIONS = 1
# ============================================================

BIOPHOTO_FORMAT = 1
if os.path.exists(cfFileName):
    import configparser
    cf = configparser.RawConfigParser(strict=False,allow_no_value=True)
    try:
        cf.read(cfFileName,encoding='utf-8')
    except:
        cf.read(cfFileName,encoding='utf-8-sig')
    ICON = cf.getint('SYS','ICON',fallback=0) #白标
    PIN_WIDTH=cf.getint('SYS','PIN_WIDTH',fallback=PIN_WIDTH)
    IDFORPIN=cf.getint('SYS','IDFORPIN',fallback=IDFORPIN)
    IMPORT_TRANS=cf.getint('SYS','IMPORT_TRANS',fallback=IMPORT_TRANS)
    PIRELLI=cf.getint('SYS','PIRELLI',fallback=PIRELLI)								#倍耐力定制为1，标准版为0
    IGNORE_LOG=cf.getint('SYS','IGNORE_LOG',fallback=IGNORE_LOG)
    SERVER_TYPE=cf.get('Options','Type',fallback=SERVER_TYPE)
    SERVER_PORT=cf.get('Options','Port',fallback=SERVER_PORT)#CFG.Options.Port
    DEBUG=cf.getint('SYS','DEBUG',fallback=DEBUG)
    BIOPHOTO_FORMAT=cf.getint('SYS','BIOPHOTO_FORMAT',fallback=0) #比对照片下发设备格式 format=0 base64, format=1 url
    RESTARTAPACHE=cf.get('SYS','RESTARTAPACHE',fallback=0)
    WRITE_LOG=cf.getint('SYS','WRITE_LOG',fallback=WRITE_LOG)
    CACHE_TYPE=cf.get('Options','cache',fallback=CACHE_TYPE)
    REDIS_CACHE=cf.get('SYS','redis',fallback=REDIS_CACHE)
    NOEXPORT=cf.getint('SYS','NOEXPORT',fallback=0)
    SHOWEMPPHOTO=cf.getint('SYS','SHOWPHOTO',fallback=1)
    PRINTVISQSCODE = cf.getint('SYS', 'PRINTVISQSCODE', fallback=0)  # 访客模块是否打印小票
#供第三方数据接口使用
    TAIKANG = cf.getint('Options','TAIKANG',fallback=0)#TAIKANG=0 无对接 TAIKANG=1仅对接ZKNET TAIKANG=2仅对接第三方 TAIKANG=3 同时对接ZKNET和第三方
    OEM_CODE = cf.get('Options','OEM',fallback='')#通过OEM的值代表不同的定制
    DEMO = cf.getint('SYS','DEMO',fallback=0)#DEMO=1时，演示网址对外提供超级管理员，不允许更改密码，不允许增加超级管理员
    ICP = cf.get('SYS','ICP',fallback='')#网站备案/许可证号
    ICP_CHECK_URL = cf.get('SYS','ICP_CHECK_URL',fallback='')#网站备案/许可证号查验地址
    ISSONLINE_URL = cf.get('SYS','ISSONLINE_URL',fallback='')#APP面部签到，后台比对的ISSONLINE的地址,通过ISSONLINE访问zkliveface3.3

    LICENSEURL = cf.get('SYS','license_url',fallback='')#
    MD5FILES = cf.get('SYS','MD5',fallback='')#
    IPAddressByDevice = cf.get('SYS','IPAddressByDevice',fallback='1')#因为在特殊情况下不能以设备的IP为准，比如地址映射，对于特殊客户通过配置IPAddressByDevice=0
    DOUBLE_WALLET = cf.getint('SYS','DOUBLE_WALLET',fallback=0)#
    CACHEIP = cf.get('SYS','CACHE',fallback='127.0.0.1:11211')#Cache IP Address
    PRODUCTCODE = cf.getint('SYS','PRODUCTCODE',fallback=11)#
    STRONG_PASSWORD = cf.getint('SYS','STRONG_PASSWORD',fallback=0)  #强密码策略
    #对接中控智慧云（百傲瑞达云平台）
    BIO_WEBURL = cf.get('SYS','BIOCLOUD_URL',fallback='https://zkcloud.xmzkteco.com')
    BIO_VERIFYURL = cf.get('SYS','BIOCLOUD_VERIFYURL',fallback='BiosecurityRegister/portal/companyRegister.do?verify')
    KAFKA_SERVER = cf.get('SYS','BIOCLOUD_KAFKA',fallback='**************:39092')

    #演示公网访问参数，用于客户问题反馈、在线升级时的演示公网访问
    HTTPS = cf.getint('SYS','HTTPS',fallback=1)  #HTTPS=1，以https://方式访问
    DEMO_URL = cf.get('SYS','DEMO_URL',fallback='https://www.zkecopro.com')  #演示公网地址

    SUPPORT_ONLINE_UPGRADE = cf.getint('SYS','SUPPORT_ONLINE_UPGRADE',fallback=0)  #支持在线升级（暂不开放此功能）

    # LARGE_SCREEN_DISPLAY = cf.getint('SYS','LARGE_SCREEN_DISPLAY',fallback=0)  #大屏显示开关控制

    RELEASE_PATH_EX = cf.get('SYS', 'RELEASE_PATH_EX', fallback='')  #安装包存放路径，演示公网用

    IC_POSLOG_CHECK	= cf.getint('SYS','IC_POSLOG_CHECK',fallback=1)  #消费记录检测, 默认开启

    ID_CARD_ACCOUNT_CHECK = cf.getint('SYS','ID_CARD_ACCOUNT_CHECK',fallback=0)  #ID消费账目校对

    #支持APP（默认开启）
    SUPPORT_APP = cf.getint('SYS','SUPPORT_APP',fallback=1)
    SUPPORT_QYWX = cf.getint('SYS','SUPPORT_QYWX',fallback=0)

    OUTWORK_TYPE = cf.getint('SYS','OUTWORK_TYPE',fallback=0)

    SHOW_MINI_PROGRAM = cf.getint('SYS','SHOW_MINI_PROGRAM',fallback=0)
    SHOW_WECHAT_OFFICIAL_ACCOUNT = cf.getint('SYS','SHOW_WECHAT_OFFICIAL_ACCOUNT',fallback=0)

    #APP授权，升级相关参数，预留
    SHOW_APP_SETTINGS = cf.getint('SYS','SHOW_APP_SETTINGS',fallback=1)
    AUTH_TERMINAL = cf.getint('SYS','AUTH_TERMINAL',fallback=0)
    MAX_APP_CLIENT = cf.getint('SYS','MAX_APP_CLIENT',fallback=500)
    SUPPORT_CUSTOMIZED_UPGRADE = cf.getint('SYS','SUPPORT_CUSTOMIZED_UPGRADE',fallback=0)
    PROMPT_UPGRADE = cf.getint('SYS','PROMPT_UPGRADE',fallback=1)
    NAME_BY_DB = cf.getint('SYS','NAME_BY_DB',fallback=1)

    RESET_DATA = cf.getint('SYS','RESET_DATA',fallback=0)

    ALLOW_ONLINE = cf.getint('SYS','ALLOW_ONLINE',fallback=0)

    DOUBLE_SCREEN_CONSUMPTION = cf.getint('SYS','DOUBLE_SCREEN_CONSUMPTION',fallback=0)

    ZKBIOPAY = cf.getint('SYS','ZKBIOPAY',fallback=0)

    SUPPORT_VISITOR_DEVICE = cf.getint('SYS','SUPPORT_VISITOR_DEVICE',fallback=1)
    SUPPORT_VISITOR_Z1022V = cf.getint('SYS', 'SUPPORT_VISITOR_Z1022V', fallback=1)

    SUPPORT_ZIP_FORMAT_TRANSFER = cf.getint('SYS', 'SUPPORT_ZIP_FORMAT_TRANSFER', fallback=1)

    DLIB = cf.getint('SYS','DLIB',fallback=0)
    JOB_CHECK_URL_TYPE = cf.getint('SYS','JOB_CHECK_URL_TYPE',fallback=0)
    SUPPORT_P3000_ADVANCED = cf.getint('SYS','SUPPORT_P3000_ADVANCED',fallback=0)

    SUPPORT_DOOR4_TO_DOOR2 = cf.getint('SYS', 'SUPPORT_DOOR4_TO_DOOR2', fallback=0)

    SALES_REGION = cf.get('SYS','SALES_REGION',fallback='CN')

    OFFLINE_CONSUMPTION = cf.getint('SYS','OFFLINE_CONSUMPTION',fallback=1)

    #ID消费签名校验，0不校验签名；1设备支持则校验，不支持则不校验；2强制校验签名
    ID_COMM_ENCRYPTION = cf.getint('SYS','ID_COMM_ENCRYPTION',fallback=0)

    CONSUMPTION_INTERVAL = cf.getint('SYS','CONSUMPTION_INTERVAL',fallback=5)

    ASSET_DEVICE = cf.getint('SYS','ASSET_DEVICE',fallback=0)
    ASSET_REAL_TIME_MONITORING = cf.getint('SYS','ASSET_REAL_TIME_MONITORING',fallback=0)
    LOG_MODE = cf.getint('SYS','LOG_MODE',fallback=0)

    REDIS_SUPPORT = cf.getint('SYS', 'REDIS_SUPPORT', fallback=0)  # 默认不启用，要启用在attsite.ini配置REDIS_SUPPORT=1
    REDIS_CMD = cf.getint('SYS', 'REDIS_CMD', fallback=1)
    REDIS_ATTLOG = cf.getint('SYS', 'REDIS_ATTLOG', fallback=1)
    REDIS_ACCLOG = cf.getint('SYS', 'REDIS_ACCLOG', fallback=1)
    REDIS_POSLOG = cf.getint('SYS', 'REDIS_POSLOG', fallback=1)
    REDIS_PHOTO = cf.getint('SYS', 'REDIS_PHOTO', fallback=1)
    REDIS_PHOTO_CONSUMERS = cf.getint('SYS', 'REDIS_PHOTO_CONSUMERS', fallback=1)
    REDIS_OPERLOG = cf.getint('SYS', 'REDIS_OPERLOG', fallback=1)
    REDIS_OPTIONS = cf.getint('SYS', 'REDIS_OPTIONS', fallback=1)

    language=cf.get('Options','language',fallback='zh-cn')
    SUPPORT_MD5 = cf.getint('SYS','SUPPORT_MD5',fallback=0)#在attsite.ini中增加SUPPORT_MD5=1会在files\目录下生成file_md5.txt
    pwd_encrypt = cf.getint('SYS', 'DB_PWD', fallback=1)

    DATABASE_UPGRADE = cf.getint('SYS', 'database_upgrade', fallback=0)
    #CSRF Reffer验证使用，Reffer符合CSRF_TRUSTED_ORIGINS设置的地址，即认定为有效Reffer
    #在attsite 增加参数CSRF_TRUSTED_ORIGINS，例:CSRF_TRUSTED_ORIGINS=https://127.0.0.1:1443,http://192.168.211.103:1443
    CUSTOM_TRUSTED_ORIGINS = cf.get('SYS','CSRF_TRUSTED_ORIGINS', fallback=[])
    if CUSTOM_TRUSTED_ORIGINS:
        CSRF_TRUSTED_ORIGINS.extend(CUSTOM_TRUSTED_ORIGINS.split(','))

    GUIDE_ECO_ATT_LOG=cf.getint('SYS','GUIDE_ECO_ATT_LOG',fallback=GUIDE_ECO_ATT_LOG)

    # 获取在线管理员个数参数,如果没设置，默认为100个
    ONLINE_USER_NUMS = cf.getint('SYS', 'ONLINE_USERS', fallback=100)
    MULTI_TENANT = cf.getint('SYS', 'MULTI_TENANT', fallback=0)

    CHECKLIC = cf.getint('SYS', 'CHECKLIC', fallback=0)
    MULTISERVER = cf.getint('SYS', 'MULTISERVER', fallback=0)
    DETECTFACE_SERVER = cf.get('SYS', 'DETECTFACE_SERVER', fallback='127.0.0.1:36110')

    if language == 'cnt':
        LANGUAGE_CODE = 'zh-Hant'
    elif language  in ['chs','chn','zh_cn']:
        LANGUAGE_CODE = 'zh-cn'
    else:
        LANGUAGE_CODE = language

    DATABASE_NAME = cf.get('DATABASE', 'NAME')
    DATABASE_ENGINE = cf.get('DATABASE', 'ENGINE')
    DATABASE_USER = cf.get('DATABASE', 'USER')
    DATABASE_PASSWORD = cf.get('DATABASE', 'PASSWORD')
    DATABASE_HOST = cf.get('DATABASE', 'HOST')
    DATABASE_PORT = cf.get('DATABASE', 'PORT')

    DATABASE_POOL_SIZE = cf.getint('DATABASE', 'POOL_SIZE', fallback=200)
    DATABASE_MAX_OVERFLOW = cf.getint('DATABASE', 'MAX_OVERFLOW', fallback=100)
    DATABASE_RECYCLE = cf.getint('DATABASE', 'RECYCLE', fallback=60)
    DATABASE_DRIVER = cf.get('DATABASE', 'DRIVER', fallback=None)

    if pwd_encrypt==1:
        from mysite.zkauth import zkDecrypt
        if '@!@=' in DATABASE_PASSWORD:
            DATABASE_PASSWORD=zkDecrypt(DATABASE_PASSWORD[4:],'biotime').decode('utf-8')


    DATABASES = {
        'default': {
            'NAME': DATABASE_NAME,
            'ENGINE': DATABASE_ENGINE,
            'USER': DATABASE_USER,
            'PASSWORD': DATABASE_PASSWORD,
            'HOST': DATABASE_HOST,
            'PORT': DATABASE_PORT,
            'CONN_MAX_AGE': 0,
            'AUTOCOMMIT': True,
            #                'ATOMIC_REQUESTS':True,
            'DISABLE_SERVER_SIDE_CURSORS': True
        }

    }

    DATABASES['default']['ENGINE'] = '%s' % DATABASE_ENGINE
    if DATABASE_ENGINE.lower() in ['mysql', 'oracle', 'postgresql']:
        # DATABASES['default']['ENGINE'] = 'django.db.backends.%s' % (DATABASE_ENGINE)
        # 使用django-db-connection-pool配置数据库连接池
        DATABASES['default']['ENGINE'] = 'dj_db_conn_pool.backends.%s' % (DATABASE_ENGINE)
        DATABASES['default']['POOL_OPTIONS'] = {
            'POOL_SIZE': DATABASE_POOL_SIZE,
            'MAX_OVERFLOW': DATABASE_MAX_OVERFLOW,
            'RECYCLE': DATABASE_RECYCLE
        }
        if DATABASE_ENGINE.lower() in ['oracle']:
            # 为兼容数据库migrations脚本，自定义ENGINE（也方便第三方库升级）
            DATABASES['default']['ENGINE'] = 'mysite.base.db.backends.%s' % (DATABASE_ENGINE)  #继承 django-db-connection-pool
            DATABASES['default']['OPTIONS'] = {
                'threaded': True
                # 'use_returning_into': False
            }
            if 'SERVICE_NAME_' in DATABASE_NAME:
                DATABASES['default']['NAME'] = "%s:%s/%s" % (DATABASE_HOST, DATABASE_PORT, DATABASE_NAME[13:])
                DATABASES['default']['PORT'] = ''
    elif DATABASE_ENGINE.lower() in ['sql_server']:
        # 使用mssql-django库
        # DATABASES['default']['ENGINE'] = 'mssql'
        # 重写mssql-django库部分函数，以兼容一些旧版本mssql数据库以及一些migrations脚本的兼容问题
        DATABASES['default']['ENGINE'] = 'mysite.base.db.backends.mssql'
        DATABASES['default']['OPTIONS'] = {
            # 关于MSSQL驱动程序driver说明
            # Windows 附带的"SQL Server" ODBC 驱动程序是旧版驱动程序, 该驱动程序不知道 SQL Server 2000 之后引入的 SQL Server
            # 数据类型（例如，date、time、datetime2、datetimeoffset）,请下载并安装单独分发的 ODBC 驱动程序,
            # 如 ODBC Driver 17 for SQL Server ,除了支持较新的数据类型外，较新的驱动程序还支持 TLS 协议增强和加密功能
            'driver': DATABASE_DRIVER or 'ODBC Driver 17 for SQL Server', #'SQL Server', 这个系统自带的低版本的驱动不再设置为默认，可能能连上，但使用起来会出现问题
            # 'isolation_level': 'READ UNCOMMITTED',  # 4 种隔离级别 , Read Uncommitted 性能最高，级别最低，允许脏读、重复读、幻读 ,
                                                    #READ COMMITTED,  REPEATABLE READ,  SERIALIZABLE
            # 'MARS_Connection': True
        }

    if 'DB' in cf.sections():

        DB_NAME=cf.get('DB','NAME')
        DB_ENGINE = cf.get('DB','ENGINE')
        DB_USER = cf.get('DB','USER')
        #DB_PASSWORD = decryption(cf.get('DB','PASSWORD'))
        DB_PASSWORD = cf.get('DB','PASSWORD')
        DB_HOST = cf.get('DB','HOST')
        DB_PORT = cf.get('DB','PORT')
        DATABASES['DB']={
                'NAME': DB_NAME,
                'ENGINE': DB_ENGINE,
                'USER': DB_USER,
                'PASSWORD': DB_PASSWORD,
                'HOST':DB_HOST,
                'PORT':DB_PORT,
                'AUTOCOMMIT':True,
                'DISABLE_SERVER_SIDE_CURSORS':True
                }
        if DB_ENGINE.lower() in ['mysql','oracle','postgresql']:
            DATABASES['DB']['ENGINE']='django.db.backends.%s'%(DB_ENGINE)
            if DB_ENGINE.lower() in ['oracle']:
                DATABASES['DB']['OPTIONS'] = {
                    'threaded': True
                    # 'use_returning_into': False
                }
                if 'SERVICE_NAME_' in DB_NAME:
                    DATABASES['DB']['NAME'] = "%s:%s/%s" % (DB_HOST, DB_PORT, DB_NAME[13:])
                    DATABASES['DB']['PORT'] = ''
        elif DB_ENGINE.lower() in ['sql_server']:
            DATABASES['DB']['ENGINE']='%s.pyodbc'%DB_ENGINE
            DATABASES['DB']['OPTIONS']={'use_legacy_datetime':True, 'MARS_Connection': True}



# set this to False if you want to turn off pyodbc's connection pooling
DATABASE_CONNECTION_POOLING = True
UNIT=DATABASE_ENGINE+'_'+DATABASE_NAME   #为保证CACHE数据统一，只要连接数据库相同，键值就相同，与系统安装目录无关

#print "DATABASE FILE:", DATABASE_NAME
#print "CACHE_BACKEND:", CACHE_BACKEND
# The cache backends to use.

L_CACHEIP=CACHEIP.split(',')

if REDIS_SUPPORT:
    CACHE_TYPE = 'redis'

else:
    # 使用memcached，相关redis参数开关关闭
    REDIS_CMD = 0
    REDIS_ATTLOG = 0
    REDIS_ACCLOG = 0
    REDIS_POSLOG = 0
    REDIS_PHOTO = 0
    REDIS_OPERLOG = 0
    REDIS_OPTIONS = 0

if CACHE_TYPE == 'redis':
    redis_mode = CONF_EX['redis']['mode']
    if redis_mode == 'sentinel':
        sentinel_servers = CONF_EX['redis']['sentinel']['sentinel_servers']
        service_name = CONF_EX['redis']['sentinel']['service_name']
        password = CONF_EX['redis']['sentinel']['password']

        sentinels = ['{host}:{port}'.format(
                                host=x['host'],
                                port=x['port']) for x in sentinel_servers
        ]
        sentinels = ','.join(sentinels)
        CACHES = {
            'default': {
                'BACKEND': 'django_redis.cache.RedisCache',
                'LOCATION': '{service_name}/{sentinels}/0'.format(service_name=service_name, sentinels=sentinels),
                'OPTIONS': {
                    'PASSWORD': '{}'.format(password),
                    'CLIENT_CLASS': 'django_redis_sentinel.SentinelClient',
                },
                'TIMEOUT': 86400,
                'KEY_PREFIX': UNIT
            }
        }
    elif redis_mode == 'standaloan':
        params = copy.deepcopy(CONF_EX['redis']['standaloan'])
        max_connections = params['max_connections']
        params['db'] += 1 # django直接cache调用的数据与通讯那些需要持久化的数据区分开，便于数据维护（仅在单例模式）
        CACHES = {
            'default': {
                'BACKEND': 'django_redis.cache.RedisCache',
                'LOCATION': 'redis://:{password}@{host}:{port}/{db}'.format(**params),
                'OPTIONS': {
                    'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                    'CONNECTION_POOL_KWARGS': {
                        'max_connections': max_connections,
                        'retry_on_timeout': True
                    }
                },
                'TIMEOUT': 60 * 60 * 24 * 7,  # 24 * 7小时
                'KEY_PREFIX': UNIT
            }
        }
    elif redis_mode == 'cluster':
        params = CONF_EX['redis']['cluster'].copy()
        redis_path = 'redis://:' + params['password'] + '@{host}:{port}/0'
        startup_nodes = [redis_path.format(host=node['host'], port=node['port']) for node in params['startup_nodes']]
        CACHES = {
            'default': {
                'BACKEND': 'django_redis.cache.RedisCache',
                'LOCATION': startup_nodes,
                'OPTIONS': {
                    'REDIS_CLIENT_CLASS': 'redis.cluster.RedisCluster',
                    'CONNECTION_POOL_CLASS': 'redis.connection.ConnectionPool',
                    'CONNECTION_POOL_KWARGS': {
                        'skip_full_coverage_check': True  # AWS ElasticCache has disabled CONFIG commands
                    }
                },
                'TIMEOUT': 86400,
                'KEY_PREFIX': UNIT
            }
        }
else:
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.memcached.PyMemcacheCache',
            'LOCATION': L_CACHEIP,
            'TIMEOUT': 86400,
            'KEY_PREFIX': UNIT
        }
    }

FORM_RENDERER="mysite.renderers.DjangoTemplates"
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [WORK_PATH+'/templates/', ADDITION_FILE_ROOT+'/templates/'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.media',
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                #'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'mysite.iclocklocale.auth',
                'mysite.iclocklocale.myContextProc',
                ],
        'debug':DEBUG,
        'builtins': [
            'django.templatetags.static',
            'mysite.iclock.templatetags.iclock_tags'
            ],

        },
    },
]
if SUPPORT_APP:
    INSTALLED_APPS.append('rest_framework')
    REST_FRAMEWORK = {
        'PAGINATE_BY': 10,
        'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
        'EXCEPTION_HANDLER': 'mysite.iapp.rest.exceptions.custom_exception_handler',
        'PAGE_SIZE': 100,
        'DATETIME_FORMAT':'%Y-%m-%d %H:%M:%S'
    }

if MULTI_TENANT and DATABASE_ENGINE.lower() in ['postgresql']:
    # 当前多租户仅支持postgresql（MULTI_TENANT参数此时才真正生效）
    DATABASES['default']['ENGINE'] = 'tenant_schemas.postgresql_backend'
    ORIGINAL_BACKEND = 'django_postgrespool2'
    DATABASE_POOL_ARGS = {'max_overflow': 50, 'pool_size': 5, 'recycle': 120}
    MIDDLEWARE = ('tenant_schemas.middleware.TenantMiddleware', *MIDDLEWARE)
    DATABASE_ROUTERS = (
        'tenant_schemas.routers.TenantSyncRouter',
    )
    CACHES['default'].update({
        'KEY_FUNCTION': 'tenant_schemas.cache.make_key'
        })
    DEFAULT_FILE_STORAGE = 'tenant_schemas.storage.TenantFileSystemStorage'  #主流未用到此参数，tenant_schemas提示警告加入
    INSTALLED_APPS.insert(0, 'tenant_schemas')
    SHARED_APPS = INSTALLED_APPS
    TENANT_APPS = tuple(INSTALLED_APPS)
    TENANT_MODEL = "base.Tenant"
else:
    MULTI_TENANT = False

MAX_DEVICES_STATE=200  #机器上次联机时间到现在时间大于此就显示成脱机,单位为(秒)
DEV_STATUS_SAVE=0
PKEY=''#MAC时设置有效
DATABASE_OPTIONS={}     #django1.4
MAX_DEVICES=0	#正式版改为0
MAX_DAYS=0	#正式版改为0 MAC时设置有效
CLOSE_DATE=''
ISVALIDDONGLE=False     #False:否 True:已激活
TRIALVERSION=0#CFG.SYS.TrialVersion
HASDONGLE=False
DONGLEHID=''
#if CFG.SYS.DEBUG==1:
#    DEBUG=True
#TEMPLATE_DEBUG = DEBUG
MAC=''#
AUTO_TRAN=False
AUTO_TRAN_ING=False
ACC_TRAN_SHOWBLUE=True
#'39.9665553397,116.3307438471'

LAT=39.96088948517369
LON=116.32469996686186
DISTANCE=200
APP_VERSION='10.0'
ERRORCODE=0
DATA_UPLOAD_MAX_MEMORY_SIZE=20*1024*1024
FILE_UPLOAD_MAX_MEMORY_SIZE=20*1024*1024
lv=[]
VERSION='10.0'
BUILD='20190115'#以version.txt的BUILD为准 version.txt内容格式 11.0-20190315
if os.path.exists(FILEPATH+'/mysite/core/version.txt'):
    f=open(FILEPATH+'/mysite/core/version.txt','r')
    lv=f.readlines()
    f.close()
if len(lv)>0:
    v=lv[0].replace('\r','').replace('\n','')
    vlist=v.split('-')
    if len(vlist)==1:
        BUILD=vlist[0]
        VERSION= "%s(Build:%s)"%(VERSION,BUILD)
    else:
        BUILD=vlist[1]
        VERSION= "%s(Build:%s)"%(vlist[0],BUILD)

if OEM_CODE:
    VERSION = u"%s %s"%(VERSION,OEM_CODE)
elif len(lv)>1:
    if sys.version_info[0]==3:
        VERSION = u"%s %s"%(VERSION,lv[1])
    else:
        VERSION = u"%s %s"%(VERSION,lv[1].decode('gb18030'))

if sys.version[0] == '2':
    VERSION = '10.0' + VERSION[4:]

SVN_VER=11539
EMPLOYEE_COUNT=0#用于限制使用系统的人员数
SOLUTION=0#用于行业软件或定制软件批量销售时授权控制，比如某校园宿舍管理系统约定为101，在授权的系统页面解决方案处填写101即可，相应的代码通过该参数判断是否开放

# 会话时长参数
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # 是否关闭浏览器使得Session过期
SESSION_SAVE_EVERY_REQUEST = True  # 是否每次请求都保存Session


# 多租户模式下启用异常邮件告警功能
if MULTI_TENANT:
    # 管理员邮箱
    ADMINS = (
        ('name', 'admin email address'),
    )
    MANAGERS = ADMINS

    # 非空链接，却发生404错误，发送通知MANAGERS
    SEND_BROKEN_LINK_EMAILS = True

    # Email设置
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = 'smtp.zkteco.com'  # QQ邮箱SMTP服务器(邮箱需要开通SMTP服务)
    EMAIL_PORT = 25  # QQ邮箱SMTP服务端口
    EMAIL_HOST_USER = ''  # 服务器的邮箱帐号
    EMAIL_HOST_PASSWORD = 'qurzkfrwvvpgbhba'  # 授权码  不是你的QQ密码
    EMAIL_SUBJECT_PREFIX = 'Multi-Tenant'  # 为邮件标题的前缀,默认是'[django]'
    EMAIL_USE_TLS = True  # 开启安全链接
    DEFAULT_FROM_EMAIL = SERVER_EMAIL = EMAIL_HOST_USER  # 设置发件人

if PUSH_COMM_ENCRYPTION:
    MIDDLEWARE = (*MIDDLEWARE, 'mysite.iclock.middleware.PushCommEncryptionMiddleware')
