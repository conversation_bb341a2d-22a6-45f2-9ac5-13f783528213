# coding: utf-8
from django.db import models, connection, connections, IntegrityError
from django.db.models import Q
from django.contrib.auth.models import  Permission, Group
import datetime
import os
import sys
import string
#from django.contrib import auth,admin
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.cache import cache

from mysite.accounts.models import MyUser
from mysite.iclock.i18n_backend import trans_opt
from mysite.utils import *
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.utils.translation import gettext as _trans
from django.contrib.contenttypes.models import ContentType
from django.forms.models import ModelChoiceIterator
import copy
from django.contrib.auth import get_user_model
from mysite.base.models import *
#from django.forms import ModelForm
#from django import forms
from mysite.utils import *
from django.db.models.fields import AutoField
from mysite.core.zktools import *
from mysite.lngdict import *
import threading
from mysite.base.custom_fields import Encryption<PERSON>har<PERSON>ield

(bioZero, bioFinger, bioFace,bioSound,bioIris,bioRetina,bioFriction,bioVein,bioHand,bioFaceVL,bioHandVL)=range(11)
def transAbnormiteName(abnormiteID):
    if abnormiteID==0:
        return _('Valid')
    elif abnormiteID==1:
        return _('Invalid')
    elif abnormiteID==2:
        return _('Repeat')
    elif abnormiteID==3:
        return _('ErrorState')
    elif abnormiteID==4:
        return _('Out')
    elif abnormiteID==5:
        return _('OT')
    elif abnormiteID==6:
        return _('FreeOT')
    elif abnormiteID==7:
        return _('AutoSch')
    else:
        return _('ErrorState')

AUDIT_STATES=(
    (0,_('Apply')),
#	(1,_('Auditing')),
    (2,_('Accepted')),
    (3,_('Refused')),
#	(4,_('Paused')),
#	(5,_('Re-Apply')),
    (6,_('Again')),
#	(7,_('Cancel_leave'))
)
AUDIT_STATES_PROCESS=(
#	(1,_('Auditing')),
    (2,_('Accepted')),
    (3,_('Refused')),
#	(4,_('Paused')),
#	(5,_('Re-Apply')),
    (6,_('Again')),
)
PROCESSTYPE = ((0, _("Role Multi-level")), (1, _("Employee Multi-level")),)
EXCEPTTYPE=(
    (1, _('Leave')),
    (2, _('OverTime')),
    (3, _('Renewal')),
)
DEPT_NAME=_("department")
DEPT_NAME_2=_("department name")
DEPT_NAME_ID=_("department number")

try:
    settings.MAX_DEVICES_STATE=int(GetParamValue('Delay',30))+20
    if settings.MAX_DEVICES_STATE<=300:
        settings.MAX_DEVICES_STATE=320
except:
    settings.MAX_DEVICES_STATE=320
    pass



def getDefaultDept():
    """ 获取默认部门；没，则创建
        """
    dept=None
    try:
        dept = department.objects.filter(parent=0).order_by('DeptID')
        if dept:
            dept =dept[0]
        else:
            d = department.objects.all().order_by('DeptID')
            dept=d[0]
    except:
        pass

    return dept

class NestedDeptException(Exception): pass

# @need_trans_properties('DeptName')
class department(models.Model):
#	DeptID = models.IntegerField(DEPT_NAME_ID,primary_key=True)
    DeptID=models.AutoField(db_column="deptid", primary_key=True, null=False,editable=False)
    DeptNumber = models.CharField(_(u'Department Number'),blank=False,db_column='deptnumber',null=False,max_length=40,help_text=_(u'The maximum length is no more than 40 characters. After modifying the department number, it will not change the department where the person is located.'))   #用于显示的编号
    DeptName = models.CharField(DEPT_NAME_2,max_length=40,db_column='deptname')
    parent = models.IntegerField(db_column="supdeptid",verbose_name=_('parent'), null=False, blank=True, default=0)
    secondary = models.IntegerField(db_column="secondary",verbose_name=_('secondary'), null=True, blank=True, default=0,editable=False)
    #DeptNo = models.IntegerField(verbose_name=_(u'serial number'), null=True, blank=True, default=0,editable=False)
    DeptAddr = models.CharField(_(u'department address'),max_length=50,blank=True,null=True,db_column='deptaddr')
    DeptPerson = models.CharField(_(u'contact'),max_length=20,blank=True,null=True,db_column='deptperson')
    DeptPhone = models.CharField(_(u'contact number'),max_length=20,blank=True,null=True,db_column='deptphone')
    email = models.EmailField(_('E-mail'), blank=True, null=True)
    DelTag = models.IntegerField(_(u'delete tag'),default=0, editable=False, null=True, blank=True,db_column='deltag')
    dn = models.CharField(_('dn'),db_column="dn",max_length=100, null=True, blank=True,editable=False)

    def Parent(self):
        if self.parent:
            return self.objByID(self.parent)
        return None
    @staticmethod
    def clear():
#		deptid=getDefaultDept().DeptID
        for dept in department.objects.all():#.exclude(DeptID=deptid):
            dept.delete()
        UpdateDeptCache()
        cache.delete("%s_allchilds"%(settings.UNIT))

    def get_secondary(self):
        if self.parent == 1:
            return self.DeptID or 0
        d=self
        for i in range(100):
            try:
                d = self.objByID(d.parent)
                if d and d.parent == 1:
                    return d.DeptID
            except:
                break
        return 0

    def AllParents(self):
        ps=[]
        d=self
        for i in range(100):
            try:
                d=self.objByID(d.parent)
                ps.append(d)
                if d==self: break
            except:
                break
        return ps
    def AllParentsDeptID(self):
        ps=[]
        d=self
        for i in range(100):
            try:
                d=self.objByID(d.parent)
                ps.append(d.DeptID)
                if d==self: break
            except:
                break
        return ps
    def DeptLongName(self):
        ps=self.DeptName
        d=self
        for i in range(100):
            try:
                if d.parent==0:
                    break
                d=self.objByID(d.parent)
                #if d==getDefaultDept(): break
                ps="%s->%s"%(ps,d.DeptName)
            except:
                break
        return ps
    def Children(self):
        return department.objects.filter(parent=self.DeptID)
    def AllChildren(self, start=[]):
        for d in self.Children():
            if d not in start:
                start.append(d)
                d.AllChildren(start)

    def getallchilddeptid(self):
        """获取所有次级部门的id"""
        child_list = []
        self.AllChildren(child_list)
        result = []
        for dept in child_list:
            result.append(dept.DeptID)
        return result

    @staticmethod
    def objByDN(dn):
        dn_str=base64.b64encode(dn.encode('utf-8'))
        if dn == None:
            return None
        d = cache.get("%s_iclock_dept_%s_%s" % (settings.UNIT, dn_str, settings.DEPTVERSION))
        if d:
            return d
        try:
            d = department.objects.get(dn=dn[:100])
        except:
            d = None
        if d:
            cache.set("%s_iclock_dept_%s_%s" % (settings.UNIT, dn_str, settings.DEPTVERSION), d)
        return d

    @staticmethod
    def objByID(id):
        if id==None: return None
        d=cache.get("%s_iclock_dept_%s_%s"%(settings.UNIT,id,settings.DEPTVERSION))
        if d: return d
        try:
            d=department.objects.get(DeptID=id)
        except:
            d=None
        if d:
            cache.set("%s_iclock_dept_%s_%s"%(settings.UNIT,id,settings.DEPTVERSION),d)
        return d
    @staticmethod
    def objByNumber(DeptNum):
        if DeptNum==None: return None
        d=cache.get("%s_iclock_deptNum_%s_%s"%(settings.UNIT,DeptNum,settings.DEPTVERSION))
        if d: return d
        try:
            d=department.objects.get(DeptNumber=DeptNum,DelTag = 0)
        except:
            d=None
        if d:
            cache.set("%s_iclock_deptNum_%s_%s"%(settings.UNIT,DeptNum,settings.DEPTVERSION),d)
        return d
    def __unicode__(self):
        try:
            return u"%s %s"%(self.DeptNumber, self.DeptName.decode("utf-8"))
        except:
            return u"%s %s"%(self.DeptNumber, self.DeptName)
    def __str__(self):
        try:
            return u"%s %s"%(self.DeptNumber, self.DeptName.decode("utf-8"))
        except:
            return u"%s %s"%(self.DeptNumber, self.DeptName)
    def save(self):
        try:
            UpdateDeptCache()
            #cache.delete("%s_iclock_dept_%s_%s"%(settings.UNIT,self.DeptID,settings.DEPTVERSION))
            #cache.delete("%s_iclock_deptNum_%s_%s"%(settings.UNIT,self.DeptNumber,settings.DEPTVERSION))

            cache.delete("%s_allchilds"%(settings.UNIT))
        except:pass

        #if (department.objects.all().count()==0) or (not self.parent) or (int(self.parent)==0) or ( department.objects.filter(DeptID=self.parent).count()>0):
        #	if (not self.parent) or (self.DeptID==1): self.parent=0
        #	if self in self.AllParents():
        #		raise NestedDeptException(_('Nested department parent'))
        #else:#改为对于没有父部门时存为一级部门
        #    self.parent=0
        self.secondary=self.get_secondary()
        try:
            d=department.objects.get(DeptNumber=self.DeptNumber)
            if not self.pk and d.DelTag==1:
                self.DelTag=0
                self.pk=d.pk
                super(department,self).save()
            else:
                models.Model.save(self)
        except:
            if self.dn:
                dn_str = base64.b64encode(self.dn.encode('utf-8'))
                cache.delete("%s_iclock_dept_%s_%s" % (settings.UNIT, dn_str, settings.DEPTVERSION))
                self.dn = ''
            models.Model.save(self)
            if not self.secondary:
                self.secondary = self.pk
                self.save()


            #raise Exception("Parent is not exist.")
    def delete(self):
        try:
            UpdateDeptCache()
            cache.delete("%s_allchilds"%(settings.UNIT))
        except: pass
        self.DelTag=1
        super(department, self).save()
    def empCount(self):
        return employee.objects.filter(DeptID=self,OffDuty=0).exclude(DelTag=1).count()
    @staticmethod
    def colModels():
        return [
            {'name':'DeptID','width':100,'hidden':True},
            {'name':'DeptNumber','width':100,'label':u"%s"%(department._meta.get_field('DeptNumber').verbose_name)},
            {'name':'DeptName','width':220,'label':u"%s"%(department._meta.get_field('DeptName').verbose_name)},
            {'name':'Parent','index':'parent','width':220,'label':u"%s"%(_('parent'))},
            {'name':'DeptAddr','width':220,'label':u"%s"%(department._meta.get_field('DeptAddr').verbose_name)},
            {'name':'DeptPerson','width':80,'label':u"%s"%(department._meta.get_field('DeptPerson').verbose_name)},
            {'name':'DeptPhone','width':100,'label':u"%s"%(department._meta.get_field('DeptPhone').verbose_name)},
            {'name':'email','width':150,'label':u"%s"%(department._meta.get_field('email').verbose_name)},
            {'name':'empCount','sortable':False,'width':80,'label':u"%s"%(_('EmpCount'))}
            #{'name':'DeptNo','sortable':False,'width':80,'label':u"%s"%(u'序号')}
            ]
    class Admin:
        search_fields = ['DeptNumber','DeptName']
        @staticmethod
        def initial_data():
            if department.objects.all().count()==0:
                department(DeptName=_("General Department"),DeptNumber='1', parent=0).save()

    class Meta:
        db_table = 'departments'
#		verbose_name=_("transaction")
        verbose_name=DEPT_NAME
        verbose_name_plural=verbose_name
        unique_together = (("DeptNumber",),)
        default_permissions = ('add', 'change', 'delete', 'export')

# 获得部门的下级所有部门
def getChildDept(dept):
    child_list=[]
    dept.AllChildren(child_list)
    return child_list


BOOLEANS=((0,_("No")),(1,_("Yes")),)
BOOLEANS_Classify=((0,_("Yes")),(128,_("No")),)
AC_Group_BOOLEANS=((0,_(u"Custom time period")),(1,_(u"Using Group Settings")),)
DEV_STATUS_OK=1
DEV_STATUS_TRANS=2
DEV_STATUS_OFFLINE=3
DEV_STATUS_PAUSE=0

nocmd_device_cname="%s_nocmd_device_%s"

def deviceCmd(device):
    nocmd_device=cache.get(nocmd_device_cname%(settings.UNIT,device.SN))  #被标记为没有命令的设备，标记超时时长30分钟
    nowCmds=[]
    #nocmd_device=None#测试用，正式的要注释
    if (not nocmd_device) or (device.State==2):
        if getattr(settings, 'REDIS_CMD', 0):
            from mysite.cache import cache_redis
            from mysite.cache_utils import DEVCMDS_QUEUE_KEY
            batch_size = 200
            nowCmds = cache_redis.lrange(DEVCMDS_QUEUE_KEY.format(device.SN), 0, batch_size-1)  #一个批次处理最大命令数
        else:
            cmds = devcmds.objects.filter(SN=device.SN).filter(Q(CmdOverTime__isnull=True, CmdTransTime__isnull=True) | Q(CmdOverTime__isnull=True,CmdTransTime__isnull=False, CmdTransTime__gt=(datetime.datetime.now() - datetime.timedelta(seconds=1800)))).order_by('id')[:200]
            now=datetime.datetime.now()
            for cmd in cmds:
                if cmd.CmdCommitTime<=now:
                    nowCmds.append(cmd)
        if nowCmds:
            if len(nowCmds)>10:
                device.State=2
            else:
                device.State=1
        else:
            # 标记该设备没有命令了（有个问题，committime为将来时间的命令，可能没法及时下发）
            cache.set(nocmd_device_cname%(settings.UNIT,device.SN), 1,timeout=30*60)
            if device.State!=1:
                device.State=1
                laKey="iclock_la_"+device.SN  #实现保存
                cache.delete(laKey)
            if getattr(settings, 'REDIS_CMD', 0):
                # 不要加载命令标识，命令请求结束后，延迟从数据库检查是否有之前未执行的命令
                # 延迟原因：重新加载太及时的话，设备响应的命令状态可能还未同步到数据库，就又把数据库的命令给刷到缓存中，造成命令重复下发
                # 场景示例：假如因为下发的命令5分钟后，还没更新响应结果，那会重新将命令放入缓存
                cache.set('%s_noloadcmd_device_%s'%(settings.UNIT,device.SN), 1, timeout=60 * 5)
    else:
        # 代码走到这相当于没命令的空闲情况
        if getattr(settings, 'REDIS_CMD', 0):
            # 检查数据库是否有待执行命令，重新加载命令到缓存（缓存异常丢失、宕机恢复后，设备长时间脱机后重连等情况的补偿措施）
            # 可能问题：异步加载命令与新增命令同时发生，可能导致命令执行顺序错误（这情况概率有点低，本来就是补偿机制，实在命令顺序乱了，就重新生成下命令吧，业务太复杂了）
            no_load_cmd = cache.get('%s_noloadcmd_device_%s'%(settings.UNIT, device.SN))
            if not no_load_cmd:
                from mysite.cache_utils import async_load_cmd_to_cache
                thread = threading.Thread(target=async_load_cmd_to_cache, args=(device.SN,))
                thread.start()
                #确保nocmd_device_cname这个缓存有效期内，这个加载命令的逻辑只执行一次
                cache.set('%s_noloadcmd_device_%s'%(settings.UNIT, device.SN), 1, timeout=30*60)

    return nowCmds


import socket
sNotify = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

def deviceHasCmd(deviceSN):
    try:
        cache.delete(nocmd_device_cname%(settings.UNIT,deviceSN))
    except: pass
def sendRecCMD(device):
    try:
        ip=device.IPAddress
        if ip: sNotify.sendto("R-CMD", (ip, 4374))
#		print "Notify: ", ip, 4374
    except:
#		print ip
#		errorLog()
        pass

def getValueFrom(data, key):
    d={}
    for v in data.split("\n"):
        if v:
            if v[-1] in ['\r','\n']: v=v[:-1]
        nv=v.split("=")
        if len(nv)>1:
            if key==nv[0]:
                return "=".join(nv[1:])
    return ""


def setValueFor(data, key, value):
    d={}
    for line in data.split("\n"):
        if line:
            v=line.split("\r")[0]
        else:
            v=line
        nv=v.split("=", 1)
        if len(nv)>1:
            try:
                v=str(nv[1])
                d[nv[0]]=v
            except:
#print nv
                pass
    if key:
        d[key]=value
    return "\n".join(["%s=%s"%(k, d[k]) for k in d.keys()])

def mergeValues(data1, data2):
    return setValueFor(data1+"\n"+data2, "","")
last_reboot_cname="%s_lastReboot"%settings.UNIT

def updateLastReboot(iclocks):
    lastReboot=cache.get(last_reboot_cname)
    d=datetime.datetime.now()
    #rebInterval=(REBOOT_CHECKTIME>0 and REBOOT_CHECKTIME or 10)
    ips=[]
# #	print "lastReboot:",lastReboot
#     if not lastReboot: lastReboot={}
#     for i in iclocks:
#         ip=i.IPAddress()
#         if ip:
#             if ip in lastReboot:
#                 if d-lastReboot[ip]>datetime.timedelta(0,rebInterval*60):
#                     ips.append(ip)
#                     lastReboot[ip]=d
# #					print "reboot:", ip, lastReboot[ip]
#             else:
#                 ips.append(ip)
#                 lastReboot[ip]=d
#     if ips: cache.set(last_reboot_cname, lastReboot, rebInterval*60)
#	print "lastReboot:",lastReboot
    return ips

def removeLastReboot(ip):
    lastReboot=cache.get(last_reboot_cname)
    if not lastReboot: return
    if ip in lastReboot:
        lastReboot.pop(ip)
        cache.set(last_reboot_cname, lastReboot)
def checkTime(t):
    if type(t)==type(datetime.datetime.now()):
        return datetime.datetime(t.year,t.month,t.day,t.hour,t.minute,t.second)
    elif type(t)==datetime.time:
        return datetime.datetime(1900,12,30,t.hour,t.minute,t.second)
    elif type(t)==datetime.date:
        return datetime.datetime(t.year,t.month,t.day,0,0,0)
    else:
        return t


def getDevice(sn):
    """
    获取设备对象，建议用此函数获取设备，注意不能在此函数加try except  通过异常判断设备是否存在
    :param sn: 设备序列号
    :return:
    """
    sn = sn and sn.strip() or ""
    if not sn:
        return None
    dev = cache.get("iclock_" + sn)
    if dev:
        return dev
    dev = iclock.objects.get(SN=sn)
    option_queryset = device_options.objects.filter(SN=sn).values_list('ParaName', 'ParaValue')
    option_queryset_dict = {opt[0]: opt[1] for opt in option_queryset}
    options_dict = option_queryset_dict.copy()
    # 部分已在iclock表存在的字段，无需再用参数表值更新（或者说是同样参数以iclock表中值为准的参数）
    exclude_update_params = ['FWVersion', 'IPAddress', 'LockFunOn',
        'ProductType', 'pushver', 'DeviceName', 'MaxAttLogCount',
        'MaxFingerCount', 'MaxUserCount'
    ]
    for k, v in option_queryset_dict.items():
        # 特殊处理的值
        if k in ['~AlgVer', 'FvFunOn', 'AccSupportFunList', 'VerifyStyles', 'CommType', 'PhotoFunOn']:
            #数据库可能存的是ZKFinger VX 这样的值，导致指纹无法下发，这种值直接赋值10
            if k == '~AlgVer' and v not in ['9', '10', '12']:
                options_dict[k] = 10
            elif k == 'FvFunOn' and v == '1':
                options_dict[k] = 1  #指静脉
                #对于PULL的指静脉目前没有办法读取该参数，赋值默认值为3
                options_dict['FvVersion'] = 3
            elif k == 'AccSupportFunList':
                if v[7:8]=='1':  #只要设备支持一人多卡，就要用一人多卡协议下发（此时user表的cardno无效）
                    options_dict['MulCardUser'] = 1
                if v[16:17]=='1': # 0不支持;1支持 用户超级权限独立表
                    options_dict['SuperAuthorizeFunOn']=1
                if v[19:20]=='1':
                    options_dict['AuxInFunOn'] = 1  #辅助输入属性表功能
                if v[26:27]=='1':
                    #表支持DevID字段（控制协议中是否有DevID字段）
                    options_dict['DevIDFunOn'] = 1
                # 采用新的反潜、互锁规则, 下发表的方式
                if v[66:67]=='1':
                    options_dict['NewAntiPassBackFunOn'] = 1
                    options_dict['NewInterLockFunOn'] = 1
            elif k == 'VerifyStyles' and v:
                options_dict['VerifyStyles'] = zk_get_VerifyStyles(v, dev)
            elif k == 'CommType' and v != 'ethernet':
                options_dict['CommType'] = 'wifi'
            elif k == 'PhotoFunOn':  #ID消费机显示照片
                try:
                    options_dict['isUSERPIC'] = int(v)
                except:
                    pass
        else:
            if not v:
                options_dict[k] = 0  #参数值为空，赋默认值0
            elif v.isdigit() and k not in ['~SerialNumber', 'NewVFStyles']:
                options_dict[k] = int(v)  #取整，主要是 ‘0’,‘1’ 之类的功能参数值
        # 修改以 ~ 字符开头的参数名，如 ~APBFO
        if k.startswith('~'):
            options_dict[k[1:]] = options_dict.pop(k)

    for del_k in exclude_update_params:  #剔除不需要由参数表更新的值
        options_dict.pop(del_k, None)

    dev.__dict__.update(**options_dict)

    if dev.ProductType in [11, 12, 13, 14]:
        if not hasattr(dev, 'ding'):
            try:
                from mysite.ipos.models import IclockDininghall
                obj = IclockDininghall.objects.get(SN=dev)
                dev.ding = obj.dining_id
            except:
                dev.ding = None
    elif GetParamValue('opt_basic_att_zone', '0') == '1':
        from mysite.iclock.iutils import getZoneBySN
        result = getZoneBySN(sn)
        if result:
            dev.zoneid = result[0]

    cache.set("iclock_" + sn, dev)
    cache.set("iclock_old_" + sn, dev)

    return dev


TIMEZONE_CHOICES=(
    (-750,'UTC-12:30'),
    (-12,'UTC-12'),
    (-690,'UTC-11:30'),
    (-11,'UTC-11'),
    (-630,'UTC-10:30'),
    (-10,'UTC-10'),
    (-570,'UTC-9:30'),
    (-9,'UTC-9'),
    (-510,'UTC-8:30'),
    (-8,'UTC-8'),
    (-450,'UTC-7:30'),
    (-7,'UTC-7'),
    (-390,'UTC-6:30'),
    (-6,'UTC-6'),
    (-330,'UTC-5:30'),
    (-5,'UTC-5'),
    (-270,'UTC-4:30'),
    (-4,'UTC-4'),
    (-210,'UTC-3:30'),
    (-3,'UTC-3'),
    (-150,'UTC-2:30'),
    (-2,'UTC-2'),
    (-90,'UTC-1:30'),
    (-1,'UTC-1'),
    (-30,'UTC-0:30'),
    (0,'UTC'),
    (30,'UTC+0:30'),
    (1,'UTC+1'),
    (90,'UTC+1:30'),
    (2,'UTC+2'),
    (150,'UTC+2:30'),
    (3,'UTC+3'),
    (210,'UTC+3:30'),
    (4,'UTC+4'),
    (270,'UTC+4:30'),
    (5,'UTC+5'),
    (330,'UTC+5:30'),
    (6,'UTC+6'),
    (390,'UTC+6:30'),
    (7,'UTC+7'),
    (450,'UTC+7:30'),
    (8,'UTC+8'),
    (510,'UTC+8:30'),
    (9,'UTC+9'),
    (570,'UTC+9:30'),
    (10,'UTC+10'),
    (630,'UTC+10:30'),
    (11,'UTC+11'),
    (690,'UTC+11:30'),
    (12,'UTC+12'),
    (750,'UTC+12:30'),
    (13,'UTC+13'),
    (810,'UTC+13:30'),
)
#TIMEZONES_CHOICES=(
#	(1,'1'),
#	(2,'2'),
#	(3,'3'),
#	(4,'4'),
#	(5,'5'),
#	(6,'6'),
#	(7,'7'),
#	(8,'8'),
#	(9,'9'),
#	(10,'10'),
#	(11,'11'),
#	(12,'12'),
#	(13,'13'),
#	(14,'14'),
#	(15,'15'),
#	(16,'16'),
#	(17,'17'),
#	(18,'18'),
#	(19,'19'),
#	(20,'20'),
#	(21,'21'),
#	(22,'22'),
#	(23,'23'),
#	(24,'24'),
#	(25,'25'),
#	(26,'26'),
#	(27,'27'),
#	(28,'28'),
#	(29,'29'),
#	(30,'30'),
#	(31,'31'),
#	(32,'32'),
#	(33,'33'),
#	(34,'34'),
#	(35,'35'),
#	(36,'36'),
#	(37,'37'),
#	(38,'38'),
#	(39,'39'),
#	(40,'40'),
#	(41,'41'),
#	(42,'42'),
#	(43,'43'),
#	(44,'44'),
#	(45,'45'),
#	(46,'46'),
#	(47,'47'),
#	(48,'48'),
#	(49,'49'),
#	(50,'50'),
#)

#ACGroupS_CHOICES=(
#	(1,'1'),
#	(2,'2'),
#	(3,'3'),
#	(4,'4'),
#	(5,'5'),
#	(6,'6'),
#	(7,'7'),
#	(8,'8'),
#	(9,'9'),
#	(10,'10'),
#	(11,'11'),
#	(12,'12'),
#	(13,'13'),
#	(14,'14'),
#	(15,'15'),
#	(16,'16'),
#	(17,'17'),
#	(18,'18'),
#	(19,'19'),
#	(20,'20'),
#	(21,'21'),
#	(22,'22'),
#	(23,'23'),
#	(24,'24'),
#	(25,'25'),
#	(26,'26'),
#	(27,'27'),
#	(28,'28'),
#	(29,'29'),
#	(30,'30'),
#	(31,'31'),
#	(32,'32'),
#	(33,'33'),
#	(34,'34'),
#	(35,'35'),
#	(36,'36'),
#	(37,'37'),
#	(38,'38'),
#	(39,'39'),
#	(40,'40'),
#	(41,'41'),
#	(42,'42'),
#	(43,'43'),
#	(44,'44'),
#	(45,'45'),
#	(46,'46'),
#	(47,'47'),
#	(48,'48'),
#	(49,'49'),
#	(50,'50'),
#	(51,'51'),
#	(52,'52'),
#	(53,'53'),
#	(54,'54'),
#	(55,'55'),
#	(56,'56'),
#	(57,'57'),
#	(58,'58'),
#	(59,'59'),
#	(60,'60'),
#	(61,'61'),
#	(62,'62'),
#	(63,'63'),
#	(64,'64'),
#	(65,'65'),
#	(66,'66'),
#	(67,'67'),
#	(68,'68'),
#	(69,'69'),
#	(70,'70'),
#	(71,'71'),
#	(72,'72'),
#	(73,'73'),
#	(74,'74'),
#	(75,'75'),
#	(76,'76'),
#	(77,'77'),
#	(78,'78'),
#	(79,'79'),
#	(80,'80'),
#	(81,'81'),
#	(82,'82'),
#	(83,'83'),
#	(84,'84'),
#	(85,'85'),
#	(86,'86'),
#	(87,'87'),
#	(88,'88'),
#	(89,'89'),
#	(90,'90'),
#	(91,'91'),
#	(92,'92'),
#	(93,'93'),
#	(94,'94'),
#	(95,'95'),
#	(96,'96'),
#	(97,'97'),
#	(98,'98'),
#	(99,'99'),
#)

#ACUnlockCombS_CHOICES=(
#	(1,'1'),
#	(2,'2'),
#	(3,'3'),
#	(4,'4'),
#	(5,'5'),
#	(6,'6'),
#	(7,'7'),
#	(8,'8'),
#	(9,'9'),
#	(10,'10'),
#)

#PURPOSE=(
#	(9,_('Checking Attendance')),
#	(1,_('Access')),
#	(2,_('Expends')),
#	(3,_('Attendance and Access')),
#	(4,_('Attendance and Expends')),
#	)

PURPOSE=(
    (0,_('No')),
    (1,_('Yes')),
)
ALGVER=(
    (10,_('ZKFinger VX10.0')),
    (9,_('ZKFinger VX9.0')),
    )
AUTHENTICATION=(
    (1,_('Non-Combined verify')),#非组合验证
    (2,_('Combined verify')),#组合验证
)


#ACGroup_VerifyType=(#门禁验证方式
#	(0,_('FP/PW/RF/FACE')),
#	(1,_('FP')),
#	(2,_('PIN')),
#	(3,_('PW')),
#	(4,_('RF')),
#	(5,_('FP/PW')),
#	(6,_('FP/RF')),
#	(7,_('PW/RF')),
#	(8,_('PIN&FP')),
#	(9,_('FP&PW')),
#	(10,_('FP&RF')),
#	(11,_('PW&RF')),
#	(12,_('FP&PW&RF')),
#	(13,_('PIN&FP&PW')),
#	(14,_('FP&RF/PIN')),
#	(15,_('FACE')),
#	(16,_('FACE&FP')),
#	(17,_('FACE&PW')),
#	(18,_('FACE&RF')),
#	(19,_('FACE&FP&RF')),
#	(20,_('FACE&FP&PW')),
#)
ISNOTFACEFP=(
    (0,_('No')),
    (1,_('Yes')),
)
M=settings.MOD_DICT

PRODUCTTYPE=(
        (M['att'],_(u'Attendance')),
        (M['meeting'],_(u'meeting')),
        #(M['patrol'],_(u'巡更')),
        #(M['ipos'],_(u'consumer machine')),
        (4,_(u'Time Attendance Access Control')),
        (25,_(u'Professional Access Control Machine')),
        (M['acc'],_(u'Access Controller')),
        (8,_(u'Information Screen Device')),
        (10,_(u'Visitor Device')),
        (M['asset'],_(u'Locker cabinet Device')),
    )
ACC_TYPE_CHOICES = (
    (1, _(u'single door controller')),
    (2, _(u'Two-door controller')),
    (4, _(u'four-door controller')),
    (8, _(u'eight-door controller'))
    )

#CONSUMEMODEL = ((1, _(u'fixed mode')),(2, _(u'Amount Mode')),(3, _(u'key value mode')),(4, _(u'counting mode')),(5, _(u'commodity model')),(6, _(u'Timekeeping Mode')),(7, _(u'记帐模式')))
CONSUMEMODEL = ((1, _(u'fixed mode')),(2, _(u'Amount Mode')),(3, _(u'key value mode')),(4, _(u'counting mode')),(5, _(u'commodity model')),(6, _(u'Timekeeping Mode')))

CASHMODEL = ((0, _(u'fixed mode')),(1, _(u'Amount Mode')))

CASHTYPE = ((0, _(u'Recharge')),(1, _(u'Refund')))

CONSUORDER = ((1, _(u'Cash wallet only')),(2, _(u'Only subsidize the wallet')),(3, _(u'Priority Cash Wallet')),(4, _(u'Priority subsidy wallet')))#指定消费钱包

class iclock(models.Model):
    SN = models.CharField(_(u'serial number'), max_length=20, primary_key=True, db_column='sn',help_text=_(u'should be consistent with the device'))
    State = models.IntegerField(_(u'status'),default=1, editable=False,db_column='state')#0禁用 1正常 2通讯中 3脱机
    LastActivity = models.DateTimeField(_('last activity'),null=True, blank=True,editable=False,db_column='lastactivity')
    TransTimes = models.CharField(_('transfer time'),max_length=50, null=True, blank=True, db_column='transtimes',editable=False,default="00:00;14:05", help_text=_('Setting device for a moment from the plane started to send checks to the new data server. Hh: mm (hours: minutes) format, with a number of time between the semicolon (;) separately'))
    TransInterval = models.IntegerField(_('Trans interval'),default=1, blank=True, editable=False, db_column='transinterval',null=True,help_text=_('Device set for each interval to check how many minutes to send new data server'))
#	RealLog=models.BooleanField(_('RealTransLog'),null=True,default=True, blank=True,editable=True)
    LogStamp = models.CharField(_('trans record stamp'),max_length=20,default=0, null=True, blank=True, db_column='logstamp',editable=False, help_text=_('Logo for the latest device to the server send the transactions timestamps'))
    OpLogStamp = models.CharField(_('trans OP stamp'),max_length=20,default=0, null=True, db_column='oplogstamp',blank=True, editable=False, help_text=_('Marking device for the server to the employee data transfer as timestamps'))
    PhotoStamp = models.CharField(_('trans photo stamp'),max_length=20,default=0, null=True, db_column='photostamp',editable=False, blank=True, help_text=_('Marking device for the server to the picture transfer as timestamps'))


    PosLogStamp = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='poslogstamp')#为设备最后上传消费记录的记录时间戳标记
    FullLogStamp = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='fulllogstamp')#为设备最后上传充值记录的记录时间戳标记
    AllowLogStamp = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='allowlogstamp')#为设备最后上传补贴记录的记录时间戳标记
    PosLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='poslogstampid')#为设备最后上传消费记录的记录戳标记
    FullLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='fulllogstampid')#为设备最后上传充值记录的记录戳标记
    AllowLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='allowlogstampid')#为设备最后上传补贴记录的记录戳标记

    PosBakLogStampId= models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='posbaklogstampid')#为设备最后上传消费备份记录的记录戳标记
    FullBakLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='fullbaklogstampid')#为设备最后上传充值备份记录的记录戳标记
    AllowBakLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='allowbaklogstampid')#为设备最后上传补贴备份记录的记录戳标记
    TableNameStamp = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='tablenamestamp')

    AliWxLogStamp = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='aliwxlogstamp')#为设备最后上传消费记录的记录时间戳标记
    AliWxLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='aliwxlogstampid')#为设备最后上传充值记录的记录时间戳标记
    AliWxBakLogStampId = models.CharField(max_length=20, null=True, editable=False, blank=True,db_column='aliwxbaklogstampid')#为设备最后上传补贴记录的记录时间戳标记
    icpos_checktime = models.DateTimeField(_('last activity'),null=True, blank=True,editable=False)



    Alias = models.CharField(_('Device Alias Name'),max_length=20,null=True,db_column='alias', blank=True, help_text=_('Device of a name'))
#	DeptID = models.ForeignKey("department", db_column="DeptID", blank=True, verbose_name=DEPT_NAME, default=1,editable=False, null=True, on_delete=models.CASCADE)
    UpdateDB = models.CharField(_('update flag'),max_length=10,db_column='updatedb', default="1111100000", blank=True, editable=False, help_text=_('To identify what kind of data should be transfered '
                                                                                                                                                 'to the server'))
    Style = models.CharField(_('style'),max_length=20, null=True,db_column='style', blank=True, default="", editable=False)#门禁中表示不同设备，见文件开头定义
    FWVersion = models.CharField(_('FW Version'),max_length=100, db_column='fwversion',null=True, blank=True,editable=False)
    FPCount = models.IntegerField(_('FP Count'), null=True,db_column='fpcount', blank=True,editable=False)
    TransactionCount = models.IntegerField(_('Transaction Count'), db_column='transactioncount',null=True, blank=True,editable=False)
    UserCount = models.IntegerField(_('User Count'), null=True, db_column='usercount',blank=True,editable=False)
#	MainTime = models.CharField(_('MainTime'),max_length=20, null=True, blank=True,editable=False)
    MaxFingerCount = models.IntegerField(_('MaxFingerCount'), null=True, db_column='maxfingercount',blank=True,editable=False)
    MaxAttLogCount = models.IntegerField(_(u'recording capacity'), null=True, blank=True,db_column='maxattlogcount',editable=False)
    MaxUserCount = models.IntegerField(_(u'user capacity'), null=True, blank=True,db_column='maxusercount',editable=False)
    DeviceName = models.CharField(_('Device Name'),max_length=30, null=True, db_column='devicename',blank=True,editable=False)
    AlgVer = models.CharField(_('AlgVer'),max_length=30, null=True, db_column='algver',blank=True,default='10',editable=False,choices=ALGVER,help_text=_(u'Choose an algorithm version that is consistent with the device'))
#	FlashSize = models.CharField(_('FlashSize'),max_length=10, null=True, blank=True,editable=False)
#	FreeFlashSize = models.CharField(_('FreeFlashSize'),max_length=10, null=True, blank=True,editable=False)
#	Language = models.CharField(_('Language'),max_length=30, null=True, blank=True,editable=False)
#	VOLUME = models.CharField(_('VOLUME'),max_length=10, null=True, blank=True,editable=False)
    DtFmt = models.CharField(_('DtFmt'),max_length=10, null=True, db_column='dtfmt',blank=True,editable=False)
    IPAddress = models.CharField(_('IPAddress'),max_length=20, null=True, db_column='ipaddress', blank=True,editable=True,help_text=_(u'When the communication method is RS485, the format is filled in here: Machine number: COM2 such as 3: COM2'))
    IsTFT = models.CharField(_('IsTFT'),max_length=5, null=True, blank=True,editable=False,db_column='istft')
    Platform = models.CharField(_('Platform'),max_length=20, null=True, blank=True,editable=False,db_column='platform')
#	Brightness = models.CharField(_('Brightness'),max_length=5, null=True, blank=True,editable=False)
    BackupDev = models.CharField(_(u'Standard agency code'),max_length=30, null=True, db_column='backupdev',blank=True,editable=(settings.TAIKANG>0))#此字段借用给泰康保险用来保存总公司定义的机构代码
    OEMVendor = models.CharField(_('OEMVendor'),max_length=30, null=True, db_column='oemvendor',blank=True,editable=False)
    City = models.CharField(_('city'),max_length=50, null=True, blank=True, db_column='city',help_text=_('City of the location'))
    LockFunOn = models.IntegerField(_(u'Access Controller Type'),db_column='accfun', default=0, choices=ACC_TYPE_CHOICES,blank=True, editable=True, help_text=_(u'Automatic judgment after device connection, only special use needs to be modified'))#用作表示几门控制器
    TZAdj = models.SmallIntegerField(_('Timezone'), default=8, db_column='tzadj',blank=False, editable=True, help_text=_('Timezone of the location (changing the time zone will take effect after the device is restarted)'), choices=TIMEZONE_CHOICES)
    DelTag = models.IntegerField(default=0, editable=False, null=True, db_column='deltag',blank=True)
    Purpose=models.IntegerField(_(u'Use as attendance record'), null=True, db_column='purpose',default=0,blank=True,editable=True,choices=PURPOSE,help_text=_(u'The record of the device participates in the attendance calculation'))
    ProductType=models.IntegerField(_(u'Equipment use'),null=True, db_column='producttype',blank=True,editable=True,default=9,choices=PRODUCTTYPE,help_text=_(u'Cautiously choose the type of equipment, can not be mistakenly selected'))  #8大屏机 9代表考勤,10代表访客 11消费机 12出纳机 13 补贴机  4代表简单门禁一体机(实际是考勤PUSH) 5代表门禁控制器(控制器PUSH) 15 代表PULL的控制器 2代表巡更机 25push一体机(控制器PUSH)
    Authentication=models.SmallIntegerField(_('Authentication'),null=True, db_column='authentication',default=1, blank=True,editable=False,choices=AUTHENTICATION)
    isFace=models.SmallIntegerField(_('isNotFace'),null=True, default=0, db_column='isface',blank=True,editable=False)#是否支持面部
    isFptemp=models.SmallIntegerField(_('isNotFptemp'), null=True, default=1,db_column='isfptemp', blank=True,editable=False)#是否支持指纹
    isUSERPIC=models.SmallIntegerField(_('isNotUSERPIC'),null=True, default=0, db_column='isuserpic', blank=True,editable=False)#是否支持照片下载
    FaceAlgVer=models.CharField(_('FaceAlgVer'),null=True, max_length=10, db_column='facealgver',blank=True,editable=False)#面部算法版本
    faceNumber=models.IntegerField(_(u'Number of faces'),null=True, blank=True,db_column='facenumber',editable=False,default=0)#面部数
    faceTempNumber=models.IntegerField(null=True, blank=True,editable=False,db_column='facetempnumber',default=0)#面部模板个数
    pushver=models.CharField(_('pushver'),null=True, max_length=40, blank=True,editable=False)#push版本号
    TransTime = models.DateTimeField(_('transfer time'),null=True, db_column='transtime',blank=True,editable=False)#自动下发时间
    CreateTime = models.DateTimeField(_('Create time'),null=True,db_column='createtime', blank=True,editable=False)#加入时间

    consume_model = models.IntegerField(verbose_name=_(u'consumption mode'),default=1,editable=True,choices=CONSUMEMODEL, null=True, blank=True)
    dz_money = models.DecimalField(verbose_name=_(u'valued amount'),default=10,max_digits=6,decimal_places=2,null=True,blank=True,editable=True,help_text=_(u'The maximum limit is 999 yuan'))
    time_price = models.DecimalField(verbose_name=_(u'Price (yuan)'),max_digits=10,decimal_places=2,default=6,null=True,blank=True,editable=True)
    long_time = models.IntegerField(_(u'Time is rounded up (minutes)'),default=20, null=True, blank=True, editable=True,help_text=_(u'When the time exceeds the set value, the value is calculated'))
    #IC消费字段
    cash_model = models.IntegerField(verbose_name=_(u'Cashier mode'),editable=True,choices=CASHMODEL, null=True, blank=True)
    cash_type = models.IntegerField(verbose_name=_(u'Cashier Type'),editable=True,choices=CASHTYPE, null=True, blank=True)
    favorable = models.IntegerField(verbose_name=_(u'Promotion ratio'),editable=True, null=True, blank=True,default = 0,help_text=_(u'%% discount rate 20%% is an extra 20%% recharge amount'))
    card_max_money = models.DecimalField(verbose_name=_(u'maximum limit'),max_digits=5,decimal_places=2,null=True,blank=True,default=999,editable=True)
    is_add = models.BooleanField(verbose_name=_(u"Accumulative subsidy"),null=True, default=False)#领取多次下发的补贴
    is_zeor = models.BooleanField(verbose_name=_(u"Zero subsidy"),null=True, default=False)#先清除卡里金额再领取最后一次补贴金额
    is_OK = models.BooleanField(verbose_name=_(u"Press the OK button to subsidize"),null=True, db_column='is_ok',default=False)# 是否按确定键进行补贴
    check_black_list = models.BooleanField(verbose_name=_(u'blacklist check'),null=True,default=True, blank=True, editable=False)#启用True(1)-禁用False(0)-默认为1
    check_white_list = models.BooleanField(verbose_name=_(u'White List Check'),null=True,default=False, blank=True, editable=True)#启用True(1)-禁用False(0)-默认为1
    is_cons_keap = models.BooleanField(verbose_name=_(u"Do you book?"), default=False,null=True)
    is_check_operate = models.BooleanField(verbose_name=_(u"Operator card check"), default=False,null=True)
    #only_RFMachine = models.CharField(_(u'是否只是卡机'), max_length=5, null=True, blank=True, editable=False,default='0')
    consume_order = models.IntegerField(verbose_name = _(u'Designated wallet'), editable = True,default=4, choices = CONSUORDER,help_text=_(u'Default priority subsidy wallet'),null = True, blank = False)  # 双钱包指定消费钱包
    offline_consumption = models.BooleanField(verbose_name=_(u'Offline Consumption'), default=False, null=True, help_text=_(u'Allow equipment to be consumed offline, which requires equipment support.'))  #是否启用离线消费，当前仅s1007固件配合使用
    id = models.IntegerField(default=1, blank=True, editable=False,null=True)#改字段用于控制器DevID,相当于自增字段，当新增设备时，该值要设置为设备总数

    def	GetCopyFields(self):
            return ["TransTimes", "TransInterval"]


    def getInfo(self, info):
        if not self.Info: return ""
        return getValueFrom(self.Info, info)

    def IsTft(self):
        ret=self.IsTFT
        if ret and ret=="1": return True
        ret=self.Platform
        if ret and ret.find("_TFT")>0: return True
        ret=self.Brightness
        if ret and ret>"0": return True
        return False
    def BackupDevice(self):
        pass
#		sn=self.BackupDev
##		print "SN:'%s'"%sn
#		if not sn: return None
#		try:
#			return getDevice(sn)
#		except:
#			pass
    def GetDevice(self):
        try:
            dev=getDevice(self.SN)
        except:
            dev=None
        return dev
        # try:
        #     dev=getattr(self,'device',None)
        #     if not dev:
        #         dev= getDevice(self.SN)
        #         setattr(self,'device',dev)
        #         return dev
        #     else:
        #         return dev
        # except:
        #     return None
    def getDynState(self):
        try:
            if self.State==DEV_STATUS_PAUSE: return DEV_STATUS_PAUSE
            aObj=getDevice(self.SN)#cache.get("iclock_"+self.SN)

            if aObj and not aObj.LastActivity: return DEV_STATUS_OFFLINE
            if aObj and not self.LastActivity:self.LastActivity=aObj.LastActivity
            if aObj  and aObj.LastActivity>self.LastActivity:
                self.LastActivity=aObj.LastActivity
            d=datetime.datetime.now()-self.LastActivity
            #if d>datetime.timedelta(0,settings.MAX_DEVICES_STATE):
            if d.total_seconds()>settings.MAX_DEVICES_STATE:
                return DEV_STATUS_OFFLINE
            else:
                if aObj and aObj.State!=2:
                    return DEV_STATUS_OK
            if aObj and aObj.State==2:
                return DEV_STATUS_TRANS

            return DEV_STATUS_OK
        except:
            return -1

    def getDoorState(self,door_no):
        #print "----",door_no,self.SN
        state=self.getDynState()
        if state==DEV_STATUS_PAUSE:
            return ('disabled',0,0,0)
        elif state==DEV_STATUS_OFFLINE:
            return ('offline',0,0,0)
        elif state==DEV_STATUS_OK or state==DEV_STATUS_TRANS:
            rtstate=cache.get('_device_%s'%self.SN)
            if not rtstate:return ('default',0,0,0)
            d=lineToDict(rtstate)
            tm=d['time']
            sensor=d['sensor']
            relay=int(d['relay'],16)
            alarm=d['alarm']
            rela=relay>>(door_no-1)&0x01
            if door_no<5:
                sens=sensor[0:2]
            else:
                sens=sensor[2:4]
            sens=int(sens,16)
            sens=sens>>((door_no-1)*2)&0x03
            #print "ssss-----",sens
            return ('closed',sens,rela,0)
        else:
            return ('default',0,0,0)

    def get_template_count(self):
        html = "<img src='/media/img/fingerprint.png' title='"+u'%s'%_(u'Number of fingerprints')+"'><span>%s</span>&nbsp;"\
              +"<img src='/media/img/face.png' title='"+u'%s'%_(u'Number of faces')+"'><span>%s</span>&nbsp;"\
              +"<img src='/media/img/fv.png' title='"+u'%s'%_(u'Number of finger veins')+"'><span>%s</span>&nbsp;"\
              +"<img src='/media/img/ico_palm.png' title='"+u'%s'%_(u'Number of palm')+"'><span>%s</span>&nbsp;"\
              +"<img src='/media/img/cphoto.png' title='"+u'%s'%_(u'visible imagery')+"'><span>%s</span>&nbsp;"\
              +"<img src='/media/img/ico_palm.png' title='"+u'%s'%_(u'LightPalm mode')+"'><span>%s</span>&nbsp;"
        dev = getDevice(self.SN)
        if hasattr(dev,'MultiBioDataCount') and dev.MultiBioDataCount:
            self.MultiBioDataCount = dev.MultiBioDataCount
            # 虽大部分已支持一体化协议，采用MultiBioDataCount参数，但是软件往设备更新、删除人员时，设备并未实时上传此参数，
            # 而是沿用getrequest请求时info携带的参数（一个设备不会近红外与可见光同时支持，更多的特征模板待协议讨论）
            face_vl_count = self.get_multi_biodata_count(bioFaceVL)
            if not dev.ProductType or dev.ProductType == 9:
                face_vl_count = dev.faceNumber or 0
            html = html%(self.get_multi_biodata_count(bioFinger),
                    self.get_multi_biodata_count(bioFace),
                    self.get_multi_biodata_count(bioVein),
                    self.get_multi_biodata_count(bioHand),
                    face_vl_count,
                    self.get_multi_biodata_count(bioHandVL)
                )
        else:
            fp_count = dev.FPCount if dev.FPCount and int(dev.FPCount) > 0 else 0
            f_number = dev.faceNumber if dev.faceNumber and int(dev.faceNumber) > 0 else 0
            pv_count = getattr(dev, 'PvCount', 0) if int(getattr(dev, 'PvCount', 0)) > 0 else 0
            html = html%(fp_count, f_number, 0, pv_count, 0, 0)
        return html

    def get_multi_biodata_count(self, index):
        """
        一体化参数中获取模板数量
        """
        slist = self.MultiBioDataCount.split(':')
        try:
            biocount = slist[index]
        except:
            biocount = 0
        return biocount

    def getImgUrl(self):
        if self and self.SN:
            imgUrl = getStoredFileName('photo/device', None, "iclock_%s.jpg"%self.SN)
            if os.path.exists(imgUrl):
                file_url = getStoredFileURL('photo/device', None, 'iclock_%s.jpg'%(self.SN))
                return file_url
        return settings.MEDIA_URL+'img/device/noImg.png'

    def get_locker_cells(self):
        if self.ProductType == 30:
            try:
                cells = device_options.objects.get(ParaName='locker_cells', SN=self.SN).ParaValue
            except:
                cells = 0
            return cells

    def GetCommType(self):
        if self.ProductType==15:
            if self.IPAddress and ':' in self.IPAddress:
                return 'RS485'
            return 'TCP'
        else:
            return 'HTTP'

    def getThumbnailUrl(self):
        return self.getImgUrl()
    def save(self, *args, **kwargs):
        self.SN = self.SN.upper()
        if self.dz_money != None and self.dz_money <= 0:
            raise Exception(f"{_('valued amount cannot be less than 0.01')}")
        #  多租户模式下新增设备时先进行设备上限及设备授权检查
        if settings.MULTI_TENANT and not iclock.objects.filter(id=self.id, DelTag=0):
            from mysite.base.multitenant_utils import check_max_device, check_auth_device
            if not check_max_device():
                raise Exception(_(u'The count of devices has reached its limit!'))
            if not check_auth_device(self.SN):
                raise Exception(_(u'Unauthorized device serial number!'))
        if not self.Authentication:
            self.Authentication=1
        if not self.AlgVer:
            self.AlgVer='10'
        if not self.LockFunOn:
            self.LockFunOn=0
        if self.ProductType==4:
            self.LockFunOn=1
        if not self.pushver:
            self.pushver=''
        if not self.CreateTime:
            self.CreateTime=datetime.datetime.now()
        if not self.TransTime:
            self.TransTime=datetime.datetime(2000,1,1,12,0,0)
        if self.ProductType == 11 and self.DelTag != 1 and int(GetParamValue('ipos_cardtype', 2, 'ipos')) == 1:
            try:
                dev = getDevice(self.SN)
            except:
                dev=None
            if dev and hasattr(dev,'consume_model'):
                if (dev.consume_model != self.consume_model) or \
                    (self.consume_model == 1 and dev.dz_money != self.dz_money) or \
                    (dev.consume_model == 6 and (dev.time_price!=self.time_price or dev.long_time!=self.long_time)) or \
                    (dev.consume_order != self.consume_order):
                    cache.set("%s_iclock_update_%s" % (settings.UNIT,self.SN),'1')#消费机消费模式有改变时,ID消费机需要重新获取信息

        if not self.DelTag: self.DelTag=0
        cache.delete("iclock_"+self.SN)
        cache.delete("iclock_old_"+self.SN)
        super(iclock, self).save(*args, **kwargs)
    def clear(self):
        devs=self.model.objects.all().filter(DelTag__isnull=True)
        for o in devs:
            o.DelTag=1
            o.save()
    def delete(self):
        self.DelTag=1
        self.save()

    @staticmethod
    def get_available_locker_devs():
        available_dev = []
        try:
            devs = iclock.objects.filter(ProductType=30)
            if devs:
                for dev in devs:
                    status = dev.getDynState()
                    if status == 1:
                        available_dev.append(dev.SN)
        except:
            pass
        return available_dev

    def __unicode__(self):
        if self.Alias:
            return u'%s(%s)'%(self.SN,self.Alias)
        return u"%s"%(self.SN)
    def __str__(self):
        if self.Alias:
            return u'%s(%s)'%(self.SN,self.Alias)
        return u"%s"%(self.SN)
    @staticmethod
    def colModels(request=None):
        ret= [
            {'name':'SN','width':120,'label':u"%s"%(iclock._meta.get_field('SN').verbose_name),'frozen':True},
            {'name':'Alias','width':120,'label':u"%s"%(iclock._meta.get_field('Alias').verbose_name),'frozen':False},
            {'name':'DeptIDS','sortable':False,'width':150,'label':u"%s"%(_(u'Home Restaurant')),'mod':'ipos'},
            {'name':'DeptIDS','sortable':False,'width':150,'label':u"%s"%(_(u'home zone')),'mod':'acc;asset'},
            {'name':'DeptIDS','sortable':False,'width':150,'label':u"%s"%(_(u'Home Department')),'mod':'adms;att;meeting;patrol'},
            {'name':'Dept','sortable':False,'width':120,'label':u"%s"%(_(u'Standard agency code')),'mod':'adms;att;meeting;patrol','hidden':(settings.TAIKANG==0)},
            {'name':'IPAddress','width':100,'label':u"%s"%(iclock._meta.get_field('IPAddress').verbose_name)},
            {'name':'ProductType','width':70,'label':u"%s"%(iclock._meta.get_field('ProductType').verbose_name),'mod':'ipos'},
            {'name':'State','sortable':False,'index':'State','width':60,'label':u"%s"%(iclock._meta.get_field('State').verbose_name)},
            {'name':'PosLogStamp','width':120,'label':u"%s"%(_(u'Recent Transfer Record Time')),'mod':'ipos'},
            {'name':'LastLogId','width':110,'label':u"%s"%(_(u'Recently transmitted serial number')),'mod':'ipos','sortable':False},
            {'name':'Data_pos','width':120,'sortable':False,'label':u"%s"%(_(u'data')),'mod':'ipos'},
            {'name':'Data_acc','width':120,'sortable':False,'label':u"%s"%(_(u'data')),'mod':'acc'},
            {'name':'Data_','width':120,'sortable':False,'label':u"%s"%(_(u'data')),'mod':'adms;att;meeting;patrol'},
            {'name':'City','sortable':False,'width':100,'label':u"%s"%(iclock._meta.get_field('City').verbose_name)},
            {'name':'LastActivity','width':100,'label':u"%s"%(iclock._meta.get_field('LastActivity').verbose_name)},
            {'name':'LogStamp','width':120,'label':u"%s"%(iclock._meta.get_field('LogStamp').verbose_name),'mod':'att;acc;meeting;adms'},
            {'name':'templateinfo','sortable':False,'search':False,'width':270,'label':u"%s"%(_(u'Biotemplate number')),'mod':'att;acc;meeting;adms'},
            {'name':'UserCount','width':50,'sortable':False,'label':u"%s"%(iclock._meta.get_field('UserCount').verbose_name),'mod':'adms;att;meeting;patrol;acc'},

            {'name':'TransactionCount','sortable':False,'hidden':True,'width':50,'label':u"%s"%(iclock._meta.get_field('TransactionCount').verbose_name),'mod':'ipos;att;acc;meeting;adms'},
            {'name':'FWVersion','width':120,'label':u"%s"%(iclock._meta.get_field('FWVersion').verbose_name)},
            {'name':'DeviceName','width':120,'label':u"%s"%(iclock._meta.get_field('DeviceName').verbose_name),'mod':'ipos;adms;att;meeting;patrol;acc,visitors'},
            {'name':'MaxAttLogCount','sortable':False,'hidden':True,'width':80,'label':u"%s"%(iclock._meta.get_field('MaxAttLogCount').verbose_name),'mod':'ipos;adms;att;meeting;patrol;acc'},
            {'name':'MaxUserCount','sortable':False,'width':80,'label':u"%s"%(iclock._meta.get_field('MaxUserCount').verbose_name),'mod':'adms;att;meeting;patrol;acc'},
            {'name':'CommType','sortable':False,'width':80,'label':u"%s"%(_(u'communication method')),'mod':'acc;asset'},
            {'name':'AlgVer','sortable':False,'hidden':True,'width':80,'label':u"%s"%(_(u'fingerprint algorithm')), 'mod':'patrol'},
            {'name':'Totalcells','sortable':False,'width':100,'label':u"%s"%(_(u'Total cells')), 'mod': 'asset'},
            {'name':'Memo','sortable':False,'width':500,'label':u"%s"%(_(u'Remarks'))},
            {'name':'getImgUrl','hidden':True}
            ]
        if not request:return ret
        using_m=request.GET.get('mod_name','att')
        if using_m == 'device' and settings.PRODUCTCODE == 14:
            using_m = 'att'
        ret_Data=[]
        is_att_zone=GetParamValue('opt_basic_att_zone', '0')
        for t in ret:
            try:
                if using_m == 'ipos':
                    if int(GetParamValue('ipos_cardtype', 2, 'ipos'))==2:
                        if t['name']=='TransactionCount':
                            t['hidden']=False
                        elif t['name']=='MaxAttLogCount':
                            t['hidden']=False
                else:
                    if t['name'] == 'TransactionCount':
                        t['hidden'] = False
                    elif t['name'] == 'MaxAttLogCount':
                        t['hidden'] = False
                mod=t['mod']
                if using_m in mod:
                    if using_m=='ipos':
                        if t['name']=='ProductType':
                            t['label']=u'%s'%(_(u'Consumer type'))
                    if t['name']=='DeptIDS' and using_m in ['adms','att','meeting','patrol']:
                        if is_att_zone == '1':
                            t['label']=u'%s'%(_(u'home zone'))

                    ret_Data.append(t)
            except:
                ret_Data.append(t)

        return ret_Data
    class Admin:
        list_display = ('SN', 'Alias', 'Style', 'LastActivity')
        search_fields = ["SN", "Alias"]
        lock_fields=['DeptID']
    class Meta:
        db_table = 'iclock'
        verbose_name=_('device')
        verbose_name_plural=verbose_name
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
            ('pause_iclock','Pause device'),
            ('resume_iclock','Resume a resumed device'),
            ('reloaddata_iclock','Upload data again'),
            ('reloadlogdata_iclock','Upload transactions again'),
            ('info_iclock','Refresh device information'),#更新设备信息
            ('reboot_iclock','Reboot device'),#重新启动设备
            ('loaddata_iclock','Upload new data'),#立即检查并传送数据
            ('cleardata_iclock','Clear data in device'),#清除设备上所有的数据
            ('clearlog_iclock','Clear transactions in device'),#清除设备上的考勤记录
            ('devoption_iclock','Set options of device'),#设置通信密码
            ('unlock_iclock', 'Output unlock signal'),
            ('unalarm_iclock', 'Terminate alarm signal'),
            ('synctime_iclok','Synchronize device time'),
            ('attdataProof_iclock','Attendance data proofreading'),
            ('toDevWithin_iclock','Transfer to the device templately'),
            ('mvToDev_iclock','Move employee to a new device'),
            ('AutoToDev_employee','Auto transfer employee to the device'),
            ('to_dev_employee', 'Transfer employee to the device'),  # 传送人员到设备
            ('to_dev_belong_att_employee', 'Transfer personnel to belong attendance equipment'),  # 传送人员到归属考勤设备
            ('to_dev_belong_acc_employee', 'Transfer personnel to home access control equipment'),  # 传送人员到归属门禁设备
            ('to_dev_employee_pic', 'Transfer employee PIC to the device'),  # 下发人员照片到设备
            ('to_dev_employee_temp', 'Transfer to the device templately'),  # 临时调拨人员到设备
            ('del_emp_from_dev', 'Deleted from the device employee'),  # 从设备中删除人员
            ('del_emp_from_acc_dev', 'Remove from home access control device'),  # 从归属门禁设备删除
            ('Upload_AC_Options', 'Upload AC Options'),#上传门禁基本设置
            ('Upload_User_AC_Options', 'Upload User AC Options'),#上传用户门禁设置

            ('deptEmptoDev_iclock','Transfer employee of department to the device'),
            ('deptEmptoDelete_iclock','Delete employee from the device'),#按部门人员从设备中删除人员
            ('browselogPic','browse logPic'),#查看考勤记录照片
            #('toDevPic_iclock','toDevPic employee'),#传送人员照片到设备
            #('delFingerFromDev_iclock','Delete fingers from the device'),
            #('delFaceFromDev_iclock','Delete face from the device'),
            #('clearpic_iclock','Clear pictures in device'),
            ('deptEmpDelFromDev_iclock','Remove people or feature templates from device'),#"从设备删除人员或特征模板"
            ('Upload_pos_all_data', 'Upload All Data'),#上传消费所有数据
            ('Upload_pos_Merchandise', 'Upload Merchandise'),#上传商品资料
            ('Upload_pos_Meal', 'Upload Meal'),#上传餐别资料
            ('Upload_Iclock_Photo','Upload Iclock Photo'),
            ('delDevPic_iclock','delDevPic employee'),#删除设备上的人员照片
            ('set_device_asp', 'set divice ascription'),  # 设置设备的归属
            ('consumer_record_detection', 'Consumer Record Detection'),     # 消费记录检测
            ('sync_locker_data', 'Sync locker data'),    # 同步储物柜数据
            ('download_attphoto', 'Download attendance photos'),  # 下载考勤照片
        )

def ValidIClocks(qs):
    return qs.exclude(DelTag=1).order_by("Alias")

class DeptAdmin(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    dept = models.ForeignKey(department, verbose_name=_('granted department'), null=False, blank=False, on_delete=models.CASCADE)
    iscascadecheck=models.IntegerField(null=True, blank=True,editable=False,default=0)
    def __unicode__(self):
        return u"%s"%(self.user)
    def __str__(self):
        return u"%s"%(self.user)
    class Admin:
        list_display=("user","dept", )
    class Meta:
        verbose_name=_("admin granted department")
        verbose_name_plural=verbose_name
        unique_together = (("user", "dept"),)

#class GroupAdmin(models.Model):
    #user = models.ForeignKey(User, on_delete=models.CASCADE)
    #group = models.ForeignKey(Group, verbose_name=_('granted group'), null=False, blank=False, on_delete=models.CASCADE)

    #def __unicode__(self):
        #return u"%s"%(self.user)
    #class Admin:
        #list_display=("user","group", )
    #class Meta:
        #verbose_name=_("admin granted group")
        #verbose_name_plural=verbose_name
        #unique_together = (("user", "group"),)

class UserAdmin(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    owned = models.IntegerField(db_column='owned',null=True,blank=True)
    dataname = models.CharField(db_column="dataname",null=False,max_length=30)

    def __unicode__(self):
        return u"%s"%(self.user)
    def __str__(self):
        return u"%s"%(self.user)
    class Admin:
        list_display=("user", )
    class Meta:
        verbose_name=_("admin granted")
        verbose_name_plural=verbose_name
        unique_together = (("user", "owned","dataname"),)

class device_options(models.Model):
    SN = models.ForeignKey(iclock,db_column='sn_id', db_constraint=False, on_delete=models.CASCADE)
    ParaName=models.CharField(max_length=30,null=False,db_column='paraname')
    ParaType=models.CharField(max_length=10,null=True,db_column='paratype')
    ParaValue=models.CharField(max_length=100,null=False,db_column='paravalue')
    def __unicode__(self):
        return u"%s"%(self.SN)
    def __str__(self):
        return u"%s"%(self.SN)

    @staticmethod
    def colModels():
        return [
            {'name': 'ParaName', 'index': 'ParaName', 'width': 223, 'label': f"{_('Parameter Name')}"},
            {'name': 'ParaValue', 'index': 'ParaValue', 'width': 323, 'label': f"{_('Parameter Value')}"}
        ]

    class Admin:
        list_display=("SN","ParaName", )
    class Meta:
        verbose_name=_("device_options")
        verbose_name_plural=verbose_name
        unique_together = (("SN", "ParaName"),)
        default_permissions = ()




class IclockDept(models.Model):
    SN = models.ForeignKey(iclock,db_column='sn_id', on_delete=models.CASCADE)
    dept = models.ForeignKey(department, verbose_name=_('granted department'), null=False, blank=False,db_column='dept_id', on_delete=models.CASCADE)
    iscascadecheck=models.IntegerField(null=True, blank=True,editable=False,default=0)
    def __unicode__(self):
        return u"%s"%(self.SN)
    def __str__(self):
        return u"%s"%(self.SN)
    class Admin:
        list_display=("SN","dept", )
    class Meta:
        verbose_name=_("admin granted department")
        verbose_name_plural=verbose_name
        unique_together = (("SN", "dept"),)
        default_permissions=('export',)

        permissions = (
            ('IclockDept_calcreports','IclockDept_calcreports'),('IclockDept_reports','IclockDept_reports'),
            )

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'Device','index':'SN__SN','width':200,'label':u"%s"%(_('device'))}
            #{'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
            #{'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('Emp Name'))},
            #{'name':'DeptName','index':'UserID__DeptID__DeptName','width':100,'label':u"%s"%(_('department name'))},
            #{'name':'Title','index':'UserID__Title','width':80,'label':u"%s"%(_('Title'))},
            #{'name':'Device','hidden':True,'index':'SN','width':180,'label':u"%s"%(_('Device name'))}
            ]
#class iclock_option(models.Model):
#	SN=models.CharField(u'设备编号',max_length=20,null=True, blank=True)
#	ErrorDelay=models.CharField(u'异常连接时间',max_length=3,null=True, blank=True)
#	Delay=models.CharField(u'正常连接时间',max_length=3,null=True, blank=True)
#	Realtime=models.CharField(u'实时上传标记',max_length=2,null=True, blank=True)
#	TransTimes=models.CharField(u'定时上传时间',max_length=20,null=True, blank=True)
#	TransInterval=models.CharField(u'传送时间间隔',max_length=2,null=True, blank=True)
#	TransType=models.CharField(u'传送数据类型',max_length=20,null=True, blank=True)
#	@staticmethod
#	def geticlockoption(sn):
#		e=cache.get("%s_iclock_SN_%s"%(settings.UNIT, sn))
#		if e: return e
#		try:
#			u=iclock_option.objects.get(SN=sn)
#			ll={}
#			ll['ErrorDelay']=u.ErrorDelay
#			ll['Delay']=u.Delay
#			ll['Realtime']=u.Realtime
#			ll['TransTimes']=u.TransTimes
#			ll['TransInterval']=u.TransInterval
#			ll['TransType']=u.TransType
#		except:
#			ll={"ErrorDelay":60,"Delay":60,"Realtime":1,"TransTimes":'00:00',"TransInterval":1,"TransType":'1111111111'}
#		cache.set("%s_iclock_SN_%s"%(settings.UNIT,sn),ll)
#		return ll
#	class Meta:
#		verbose_name=_("iclock option")
#		verbose_name_plural=verbose_name

GENDER_CHOICES = (
    ('M', _('Male')),
    ('F', _('Female')),
)


PRIV_CHOICES=(
    (0,_('Ordinary user')),
    (2,_('Registrar')),
    (6,_('Administrator')),
    (14,_('Supervisor')),
)

def formatPIN(pin):
    if not settings.PIN_WIDTH: return pin
    return devicePIN(pin.rstrip()).zfill(settings.PIN_WIDTH)

def devicePIN(pin):
    if not settings.PIN_WIDTH: return pin
    i=0
    for c in pin[0:-1]:
        if c=="0":
            i+=1
        else:
            break
    return pin[i:]
#MAX_PIN_INT=int("999999999999999999999999"[:settings.PIN_WIDTH])
#if settings.PIN_WIDTH==5: MAX_PIN_INT=65534
#elif settings.PIN_WIDTH==10: MAX_PIN_INT=4294967294L
#elif settings.PIN_WIDTH==1: MAX_PIN_INT=999999999999999999999999L
CHECK_CLOCK_IN=(
    (0,_('By Time Zone')),
    (1,_('Must Clock In')),
    (2,_('Don&#39;t Check In')),
)
CHECK_CLOCK_OUT=(
    (0,_('By Time Zone')),
    (1,_('Must Clock Out')),
    (2,_('Don&#39;t Check Out')),
)

EDU_CHOICES=(
    (6,_('Primary school')),
    (7,_('Junior middle school')),
    (0,_('Secondary school')),
    (1,_('High school')),
    (2,_('College')),
    (3,_('Undergraduate')),
    (4,_('Master')),
    (5,_('Doctorate ')),
    (8,_('Other')),

)
EMPTYPE_CHOICES=(
    ('0',_('Contract')),
    ('1',_('Part-time')),
    ('2',_('Probation period')),
    ('11',_('VIP')),
    ('12',_('Blacklist')),
)

SUPPORT_BIOMETRIC_POS=(
    (1, _(u'Yes')),
    (0, _(u'No')),
)

CLOCK_IN_MOBILE=(
    ('0',_('Enable')),
    ('1',_('Disable')),
)

COMVERIFYS=(#组合验证
(0, _(u"FP/PWD/Card/Face")),#指纹或密码或卡FP/PW/RF/FACE
(1, _(u"FP")),#指纹
(2, _(u"PIN")),#考勤号
(3, _(u"PW")),#密码
(4, _(u"CARD")),#卡
(5, _(u"FP/PW")),#指纹或密码
(6, _(u"FP/Card")),#指纹或卡
(7, _(u"PW/Card")),#密码或卡
(8, _(u"PIN&FP")),#考勤号和指纹
(9, _(u"FP&PW")),#指纹和密码
(10, _(u"FP&Card")),#指纹和卡
(11, _(u"PW&Card")),#密码和卡
(12, _(u"FP&PW&Card")),#指纹和密码和卡
(13, _(u"PIN&FP&PW")),#考勤号和指纹和密码
(14, _(u"FP&(Card/PIN)")),#指纹和卡和考勤号
(15,_(u"FACE")),
(16,_(u'FACE&FP')),#_("FACE_AND_FP")),#面部加指纹
(17, _(u'FACE&PW')),#_("FACE_AND_PW")),#面部加密码
(18, _(u'FACE&Card')),#_("FACE_AND_RF")),#面部加卡
(19, _(u'FACE&FP&Card')),#_("FACE_AND_FP_AND_RF")),#面部加指纹加卡
(20, _(u'FACE&FP&PW')),#_("FACE_AND_FP_AND_PW")),#面部加指纹加密码
(21, _(u'FV')),
(22, _(u'FV&PW')),
(23, _(u'FV&Card')),
(24, _(u'FV&PWD&Card')),
(25, _(u'Palm')),
(26, _(u'Palm&Card')),
(27,_(u'Palm&FACE')),
(28,_(u'Palm&FP')),
(29,_(u'Palm&FACE&FP')),
)

ATTRIBUTE_TYPE = (
    (0,_(u'input box')),
    (1,_(u'Multiple Choice')),
    (2,_(u'Multiple Choices')),
    (3,_(u'The drop-down list')),
)

class UserProperty(models.Model):
    name = models.CharField(_(u'Attribute name'),null=False,max_length=50)  # 限定属性名不可重复
    type = models.IntegerField(_(u'Input Type'),null=False,choices=ATTRIBUTE_TYPE,default=0)
    preset_attr_values = models.CharField(_(u'Attribute value'),max_length=500,null=True,blank=True,help_text=_(u'Please use English;separated'))


    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'name','width':200,'label': u"%s" % (_(u'Attribute name'))},
            {'name':'type','width':100,'label':u'%s' % (_(u'Input Type'))},
            {'name':'preset_attr_values','width':500,'label':u'%s' % (_(u'Attribute value'))},
        ]

    @staticmethod
    def objByID(id):
        upobj = None
        cache_key = "%s_iclock_userproperty_%s"%(settings.UNIT, id)
        upobj = cache.get(cache_key)
        if upobj is not None:
            if upobj != '':
                return upobj
            else:
                return None
        try:
            upobj = UserProperty.objects.get(id=id)
        except:
            pass
        if upobj:
            cache.set(cache_key, upobj)
        else:
            cache.set(cache_key, '')
        return upobj

    @staticmethod
    def get_all_objs():
        objs = []
        all_objs = cache.get("%s_iclock_userproperty_all"%settings.UNIT)
        if all_objs is not None:
            if all_objs != '':
                return all_objs
            else:
                return []
        try:
            objs = UserProperty.objects.all().order_by('id')
        except:
            pass
        if objs:
            cache.set("%s_iclock_userproperty_all"%settings.UNIT, objs)
        else:
            cache.set("%s_iclock_userproperty_all"%settings.UNIT, '')
        return objs

    def save(self):
        if self.name.isdigit():
            #为数字的话，导出pdf会异常，另扩展属性为数字，无实际意义
            raise Exception(u"%s" % (_(u'Attribute name cannot be numeric')))
        att_name = UserProperty.objects.filter(name=self.name).count()
        if att_name > 0:
            raise Exception(u"%s" % (_(u'Attribute names must be unique')))
        counts = UserProperty.objects.all().count()
        if counts >= 20:
            raise Exception(u"%s" % (_(u'The maximum number of staff extended attributes is 20')))
        else:
            cache.delete("%s_iclock_userproperty_all"%settings.UNIT)
            super(UserProperty,self).save()

        try:
            employee_template = 'employee_list_%s.js' % datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            file = os.path.dirname(os.path.dirname(os.path.relpath(__file__)))
            f_path = os.path.join(file, 'templates', 'employee_list.js')
            f = open(f_path, 'r')
            f_new_dir = os.path.join(addition_file_root(), 'templates')
            if not os.path.exists(f_new_dir):
                os.makedirs(f_new_dir)
            f_new_path = os.path.join(addition_file_root(), 'templates', employee_template)
            f_new = open(f_new_path, 'w')
            for line in f.readlines():
                if line == '"Address":"{{item.Address|trim}}"}\n':
                    userproperty = UserProperty.objects.all()
                    if userproperty.count() > 0:
                        names = userproperty.values_list('id')
                        for name in names:
                            # 示例 "1":"{{ 1|get_emp_roster:item.id }}",
                            line_data = '"' + str(name[0]) + '"' + ':"{{' + str(
                                name[0]) + '|get_emp_roster:item.id }}",\n'
                            f_new.writelines(line_data)
                    f_new.writelines(line)
                else:
                    f_new.writelines(line)
            f.close()
            f_new.close()
            SetParamValue('employee_template_name', employee_template)
        except:
            if settings.DEBUG:
                import traceback;
                traceback.print_exc()
            print('write extend_attribute_js failed !!!')
    class Admin:
        search_fields = ['name',]

    class Meta:
        db_table = 'user_property'
        verbose_name = _(u"Extended Attributes")
        unique_together = (("name"),)
        default_permissions = ('browse', 'add', 'change', 'delete')


class UserPropertyValue(models.Model):
    userid = models.IntegerField(null=False,blank=False)  # 人员id,不使用外键防止对人员表约束
    PropertyId = models.ForeignKey(UserProperty,db_column='propertyid',null=False, on_delete=models.CASCADE)
    value = models.CharField(max_length=500,null=True,blank=True)

    def __str__(self):
        return self.value

    @staticmethod
    def objByUnique(userid, propertyid):
        upv = None
        cache_key = "%s_iclock_userpropertyvalue_%s_%s"%(settings.UNIT, userid, propertyid)
        upv = cache.get(cache_key)
        if upv is not None:
            if upv != '':  #这边''有业务逻辑，当数据库没有值时，缓存赋值''，避免缓存查不到数据后再次查询数据库
                return upv
            else:
                return None
        try:
            upv = UserPropertyValue.objects.get(userid=userid,PropertyId_id=propertyid)
        except:
            pass
        if upv:
            cache.set(cache_key, upv)
        else:
            cache.set(cache_key, '')
        return upv

    def save(self, *args, **kwargs):
        cache_key = "%s_iclock_userpropertyvalue_%s_%s"%(settings.UNIT,self.userid, self.PropertyId_id)
        cache.delete(cache_key)
        super(UserPropertyValue, self).save()

    def delete(self, *args, **kwargs):
        cache_key = "%s_iclock_userpropertyvalue_%s_%s"%(settings.UNIT,self.userid, self.PropertyId_id)
        cache.delete(cache_key)
        super(UserPropertyValue, self).delete()

    class Meta:
        db_table = 'user_property_value'
        verbose_name = _(u"User Property Value")
        unique_together = (("userid","PropertyId"),)
        default_permissions = ('browse', 'add', 'change', 'delete')


class employee(models.Model):
    id=models.AutoField(db_column="userid", primary_key=True, null=False,editable=False)
    PIN = models.CharField(_('Personnel ID'),db_column="badgenumber",null=False,max_length=24)
    EName = models.CharField(_('Emp Name'),db_column="name",null=True,max_length=24, blank=True, default="")  #First Name
    LastName = models.CharField(_('Emp Last Name'),db_column="lastname",null=True,max_length=24, blank=True, default="")
    LocalName = models.CharField(_('Emp Local Name'),db_column="localname",null=True,max_length=24, blank=True, default="")
    DeptID = models.ForeignKey(department,db_column="defaultdeptid", verbose_name=_(u'Department Number'), editable=True, null=True, on_delete=models.CASCADE)
    Password = models.CharField(_('Password'),max_length=20, null=True, blank=True, editable=False,db_column='password')
    MVerifyPass=models.CharField(_(u'Device password'),max_length=20, null=True, db_column='mverifypass',blank=True,editable=True, help_text=_('1.Admin password of the device;2.Checkinout password'))
    Privilege = models.IntegerField(_(u'Device Permissions'),null=True, blank=True, db_column='privilege',default=0,choices=PRIV_CHOICES,help_text=_(u'Edit By Superuser'))
    Card = models.CharField(_('ID Card'),max_length=20, null=True, db_column='card',blank=True, editable=True)
    AccGroup = models.IntegerField(_('Access Group'),null=True, db_column='accgroup',blank=True,editable=False)#多人开门组使用
    TimeZones = models.CharField(_('Access Timezone'),max_length=20, null=True, db_column='timezones',blank=True,editable=False)
    Gender = models.CharField(_('Sex'),max_length=2, choices=GENDER_CHOICES, db_column='gender',null=True, blank=True)
    Birthday = models.DateTimeField(_('Birthday'),max_length=8, null=True, db_column='birthday',blank=True)#help_text=_('Date format is ')+"ISO;"+u"%s"%(_('for ex_('for example')-1-11')
    Address = models.CharField(_('Address'),db_column="street",max_length=80, null=True, blank=True)
    PostCode = models.CharField(_('Postcode'),db_column="zip",max_length=6, null=True, blank=True)
    Tele = models.CharField(_('Office phone'),db_column="ophone",max_length=20, editable=True,null=True, blank=True)
    FPHONE=models.CharField(_(u'Contact number'),max_length=20, null=True, blank=True,db_column='fphone')
    Mobile = models.CharField(_('Mobile'),db_column="pager",max_length=20, null=True, blank=True)
    National = models.CharField(_('Nationality'),db_column="minzu",max_length=8, null=True, blank=True)
    Title = models.CharField(_('Title'),db_column="title",max_length=20, null=True, blank=True)
    #SN = models.ForeignKey(iclock, db_column='SN', verbose_name=_('registration device'), null=True, blank=True, editable=False, on_delete=models.CASCADE) #屏蔽掉登记设备设置
    SN = models.CharField( db_column='sn', max_length=20,verbose_name=_('SN'), null=True, blank=True, editable=False)#设备序列号，不允许借用他用
    SSN=models.CharField(_('Social Insurance'),max_length=20, null=True, blank=True,db_column='ssn')
    UTime = models.DateTimeField(_('Refresh time'), null=True, blank=True, editable=False,db_column='utime')
    Hiredday=models.DateField(_('Employment date'),max_length=8, null=True, blank=True,db_column='hiredday')
    VERIFICATIONMETHOD=models.SmallIntegerField(_(u'identifying type'),null=True,db_column='verificationmethod',choices=COMVERIFYS,blank=True,editable=True,help_text=_(u'Depending Device Supports'))
    State=models.CharField(_(u'Province'),max_length=6, null=True, db_column='state',blank=True,editable=False)#此字段可做他用
    City=models.CharField(_(u'Area'),max_length=6, null=True, db_column='city',blank=True,editable=True,help_text=_(u'Need Open Options'))#此字段可做用做区域保存区域ID
    SECURITYFLAGS=models.SmallIntegerField(_(u'Admin Flag'),null=True, db_column='securityflags',blank=True,editable=False)
    ATT=models.BooleanField(_('Active AC'),null=True,default=True,  db_column='att',blank=True,editable=True)
    OverTime=models.BooleanField(_('Count OT'),null=True,default=True, db_column='overtime',blank=True,editable=False)
    Holiday=models.BooleanField(_('Rest on Holidays'),null=True,default=True,db_column='holiday', blank=True,editable=False) #备用
    INLATE=models.SmallIntegerField(_('Check Clock In'),null=True,default=0, db_column='inlate',choices=CHECK_CLOCK_IN, blank=True,editable=True)
    OutEarly=models.SmallIntegerField(_('Check Clock Out'),null=True,default=0, db_column='outearly',choices=CHECK_CLOCK_OUT, blank=True,editable=True)
    Lunchduration=models.SmallIntegerField(_(u'Lunch Break'),null=True,default=1, db_column='lunchduration',blank=True,editable=False)
    SEP=models.SmallIntegerField(null=True,default=1,editable=False,db_column='sep')
    OffDuty=models.SmallIntegerField(_("left"), null=True, default=0, editable=False, choices=BOOLEANS,db_column='offduty')
    AutoSchPlan=models.SmallIntegerField(null=True,default=1,editable=False,db_column='autoschplan')
    MinAutoSchInterval=models.IntegerField(null=True,default=24,editable=False,db_column='minautoschinterval')
    RegisterOT=models.IntegerField(null=True,default=1,editable=False,db_column='registerot')
    sysPass = EncryptionCharField(_(u'Self-Service Pwd'),db_column="syspass",max_length=50, null=True, blank=True,editable=True)  #默认无密码 密码即为编号
    # sysPass=models.CharField(_(u'Self-Service Pwd'),db_column="syspass",max_length=20, null=True, blank=True,editable=True)   #默认无密码 密码即为编号
    email = models.EmailField(_('E-mail'), blank=True, null=True)
    OpStamp = models.DateTimeField(_('modify time'),null=True, db_column='opstamp',blank=True,editable=False)                 #保存人员信息的更新时间,不编辑
    Reserved=models.IntegerField(null=True,default=0,blank=True,editable=False,db_column='reserved')       #  泰康约定人员的人员编号修改后将该字段写1    #预留保存一些临时统计信息比如指纹数可实现查询没有指纹的人员
    Annualleave=models.FloatField(_('Annual leave'), null=True, default=0, blank=True,editable=False,db_column='annualleave')                  #暂时不编辑

    #人事合同信息
    Educational = models.CharField(_('Educational'),max_length=2, choices=EDU_CHOICES, null=True, blank=True,db_column='educational')
    Trialstarttime=models.DateField(_('Trialstarttime'),max_length=8, null=True, blank=True,db_column='trialstarttime')
    Trialendtime=models.DateField(_('Trialendtime'),max_length=8, null=True, blank=True,db_column='trialendtime')
#	Startwork=models.DateField(_('Startwork'),max_length=8, null=True, blank=True)
#	Worktime=models.DateField(_('Worktime'),max_length=8, null=True, blank=True)
    #Workyears=models.DateField(_('Workyears'),max_length=8, null=True, blank=True)
    Contractstarttime=models.DateField(_('Contractstarttime'),max_length=8, null=True, blank=True,db_column='contractstarttime')
    Contractendtime=models.DateField(_('Contractendtime'),max_length=8, null=True, blank=True,db_column='contractendtime')
    Employeetype= models.CharField(_('Employee Type'),max_length=2, choices=EMPTYPE_CHOICES, null=True, blank=True,db_column='employeetype')
    DelTag = models.IntegerField(_(u'Delete Tag'),default=0, editable=False, null=True, blank=True,db_column='deltag')
    fingercons = models.IntegerField(_(u'Support biometric'),default=0, editable=True, null=True, blank=True, choices=SUPPORT_BIOMETRIC_POS, db_column='fingercons')
    boundid = models.CharField(_(u'boundid'), max_length=30, null=True, blank=True, editable=False)
    #DevOpFlag用以标记最近一次更新此人员的设备，格式 SN_时间  例 5653190400015_20191031102844
    DevOpFlag = models.CharField(db_column='devopflag', max_length=50, null=True, blank=True, editable=False)
    openid = models.CharField(db_column='openid', max_length=100, null=True, blank=True, editable=False) # 小程序用户的唯一标识，现用于资产消息推送
    clock_in_mobile = models.CharField(_('Mobile terminal clock in'),max_length=2, default=0, choices=CLOCK_IN_MOBILE, null=True, blank=True)
    group_id = models.IntegerField("group", db_column='group_id', null=True, blank=True, editable=False)  # 人员分组，一个人只能在一个组中
    QywxCode = models.CharField(db_column='qywxcode', max_length=50, null=True, blank=True, editable=False) # 对接企业微信的人员的账号





    @staticmethod
    def objByID(id):
        if (id==None) or (id==0) or (id=='0') or (isinstance(id,int) and id<0):
            return None
        emp=cache.get("%s_iclock_emp_%s"%(settings.UNIT, id))
        if emp:
            if emp.Reserved and emp.Reserved==1:
                sql="update userinfo set Reserved=0 where userid=%s"%(emp.id)
                customSql(sql)
            else:
                return emp


        try:
            u=employee.objects.get(id=id)
        except Exception as e:
            print ("objByID===",id,e)
            u=None
        if u:
            u.IsNewEmp=False
            cache.set("%s_iclock_emp_%s"%(settings.UNIT,u.id),u)
            cache.set("%s_iclock_emp_PIN_%s"%(settings.UNIT,u.PIN),u)
        #if not u:
        #	print "==============",id
        return u

    @staticmethod
    def objByMobile(mobile):
        emp=cache.get("%s_iclock_emp_Mobile_%s"%(settings.UNIT, mobile))
        if emp:
            return emp
        try:
            u=employee.objects.get(Mobile=mobile)
        except Exception as e:
            print ("objByMobile===",mobile,e)
            u=None
        if u:
            cache.set("%s_iclock_emp_Mobile_%s"%(settings.UNIT,mobile),u)
        return u

    @staticmethod
    def objByPIN(pin, Device=None):
        if (not pin) or (pin=='0'):return None
        emp=cache.get("%s_iclock_emp_PIN_%s"%(settings.UNIT,pin))
        if emp:
            if emp.Reserved and emp.Reserved==1:#如果客户直接往userinifo更新人员信息，要把Reserved=1
                sql="update userinfo set Reserved=0 where userid=%s"%(emp.id)
                customSql(sql)
            else:
                return emp
        empl=employee.objects.filter(PIN=pin)
        if empl:
            if empl[0].DelTag==1:
                if Device:
                    if (Device.ProductType not in [5,15,11,12,13]) or Device.Style == '12' or Device.Style=='80':
                        IDepts = IclockDept.objects.filter(SN=Device).order_by('id')
                        if IDepts:
                            obj = IDepts[0]
                            dept = obj.dept
                        else:
                            dept=getDefaultDept()
                        empl[0].DeptID=dept
                        empl[0].DelTag=0
                        empl[0].EName=''
                        empl[0].VERIFICATIONMETHOD=None
                        empl[0].save()
                        e=empl[0]
                        if GetParamValue('opt_basic_att_zone', '0') == '1':
                            from mysite.acc.models import IclockZone, empZone
                            izone = IclockZone.objects.filter(SN_id=Device)
                            ezone = empZone.objects.filter(UserID=e,zone=izone[0].zone)
                            if not ezone:
                                empZone(UserID=e, zone=izone[0].zone).save()
                        e.IsNewEmp=True
                    else:
                        return None
                else:
                    cache.delete("%s_iclock_emp_%s"%(settings.UNIT,empl[0].id))
                    cache.delete("%s_iclock_emp_PIN_%s"%(settings.UNIT,empl[0].PIN))
                    raise Exception("Employee PIN %s not found"%pin)
            else:
                e=empl[0]  #about 60ms
                e.IsNewEmp=False
                cache.set("%s_iclock_emp_PIN_%s"%(settings.UNIT,pin),e)
        else:
            if Device:
                try:
                    try:
                        #deptid=Device.DeptID
                        #if not deptid: deptid=getDefaultDept()
                        if Device.ProductType not in [11, 12, 13] or Device.Style=='80':

                            IDepts=IclockDept.objects.filter(SN=Device).order_by('id')
                            if IDepts:
                                obj=IDepts[0]
                                deptid=obj.dept

                            else:
                                #if  Device.ProductType in [4]:
                                deptid = getDefaultDept()
                                #else:
                                    #return None
                       # elif Device.ProductType in [25]:
                        #    deptid = getDefaultDept()
                        else:
                            return None
                    except:
                        return None

                    e=employee(PIN=pin, EName="",DeptID=deptid,SN=Device.SN,VERIFICATIONMETHOD=None)
                    e.save(iscomm=True)  # 通讯中更新
                    e.IsNewEmp=True

                    if GetParamValue('opt_basic_att_zone', '0') == '1':
                        from mysite.acc.models import IclockZone, empZone
                        izone = IclockZone.objects.filter(SN_id=Device)
                        if izone:
                            ezone = empZone.objects.filter(UserID=e,zone=izone[0].zone)
                            if not ezone:
                                empZone(UserID=e, zone=izone[0].zone).save()
                except Exception as er:
                    raise er
            else:
                raise Exception("Employee PIN %s not found"%pin)
        return e
    def Dept(self): #cached user
        return department.objByID(self.DeptID_id)
    def getZone(self):
        from mysite.acc.models import zone
        return zone.objByID(self.City)
    @staticmethod
    def clear():
        emps=employee.objects.all()
        for e in emps:
            e.DelTag=1
            e.save()
    def Device(self):
        return getDevice(self.SN)
    def getUrl(self):
        return settings.UNIT_URL+"iclock/data/employee/%s/"%self.pk
    def getImgUrl(self, default=None):
        if not self.PIN: return default
        if os.path.exists(getStoredFileName("photo", None, devicePIN(self.PIN)+".jpg")):
            url=getStoredFileURL("photo", None, devicePIN(self.PIN)+".jpg")
            import random
            url += '?time=%s'%str(random.random()).split('.')[1]
            return url
        elif self.SSN and os.path.exists(getStoredFileName("photo", None, self.SSN+".jpg")):
            url=getStoredFileURL("photo", None, self.SSN+".jpg")
            import random
            url += '?time=%s'%str(random.random()).split('.')[1]
            return url
        return default
    def rmThumbnail(self):
        tbName=getStoredFileName("photo/thumbnail", None, devicePIN(self.PIN)+".jpg")
        if os.path.exists(tbName):
            os.remove(tbName)
    def delpic(self):
        tbpic=getStoredFileName("photo/thumbnail", None, devicePIN(self.PIN)+".jpg")
        if os.path.exists(tbpic):
            os.remove(tbpic)
        epic=getStoredFileName("photo", None, devicePIN(self.PIN)+".jpg")
        if os.path.exists(epic):
            os.remove(epic)
        dpic=getStoredFileName("photo", None, "D-%s.jpg"%devicePIN(self.PIN))
        if os.path.exists(dpic):
            os.remove(dpic)
        adpic=getStoredFileName("photo", None, "ad_%s.jpg"%devicePIN(self.PIN))
        if os.path.exists(adpic):
            os.remove(adpic)
    def getAdUserPhoto(self):
        if os.path.exists(getStoredFileName("photo/thumbnail", None, devicePIN(self.PIN)+".jpg")):
            return getStoredFileURL("photo", None, self.PIN+".jpg")
        elif os.path.exists(getStoredFileName("photo", None, devicePIN(self.PIN)+".jpg")):
            return getStoredFileURL("photo", None, devicePIN(self.PIN)+".jpg")
        return ""
    def getThumbnailUrl(self, default=None,tag=True):
        import random
        varile = str(random.random()).split('.')[1]
        if tag:
            if GetParamValue('opt_basic_emp_pic','0')!='1':return ''

        if not self.PIN: return default
        # if hasattr(settings,'SHOWEMPPHOTO') and settings.SHOWEMPPHOTO==0:
        #     if facetemp.objects.filter(UserID=self.id).count()==0:
        #         return ""
        if not os.path.exists(getStoredFileName("photo/thumbnail", None, self.pin()+".jpg")) and \
            not os.path.exists(getStoredFileName("photo", None, self.pin()+".jpg")):
            if self.SSN:
                tbName=getStoredFileName("photo", None, self.SSN+".jpg")
                tbUrl=getStoredFileURL("photo", None, self.SSN+".jpg")
                if os.path.exists(tbName):#取路径先判断压缩图是否已经存在
                    return tbUrl+'?'+varile
            else:
                if os.path.exists(getStoredFileName("photo/thumbnail", None, "D-"+self.pin()+".jpg")):
                    return getStoredFileURL("photo/thumbnail", None, "D-"+self.pin()+".jpg")+'?'+varile
        else:
            tbName=getStoredFileName("photo/thumbnail", None, self.pin()+".jpg")
            tbUrl=getStoredFileURL("photo/thumbnail", None, self.pin()+".jpg")
            if os.path.exists(tbName):#取路径先判断压缩图是否已经存在
                return tbUrl+'?'+varile
            else:#压缩图不存在 创建
                fullName=getStoredFileName("photo", None, self.pin()+".jpg")
                if os.path.exists(fullName):
                    if createThumbnail(fullName, tbName):
                        return tbUrl+'?'+varile
        return ''
    def save(self,*args,**kwargs):
        if settings.MULTI_TENANT and not self.id: #  多租户模式下新增人员时先进行人员上限判断
            from mysite.base.multitenant_utils import check_max_user
            if not check_max_user():
                raise Exception(_(u'The count of employees has reached its limit!'))
        if ('iscomm' in kwargs) and kwargs['iscomm']:   #通讯中更新
            super(employee,self).save()
            return self
        self.PIN=str(self.PIN).strip()
        if self.Card:
            self.Card = int(self.Card)
        pin=self.PIN
        if pin in settings.DISABLED_PINS:# or (not pin.isdigit()):
            raise Exception(u"%s" % (_(u"Employee PIN %s is disabled") % pin))
        #if pin_int>MAX_PIN_INT:
        #	raise Exception("Max employee PIN is %d"%MAX_PIN_INT)
        self.PIN=formatPIN(self.PIN)

        if (self.Employeetype == '12') and (self.Privilege in [2,6,14]):
            raise Exception(u"%s"%(_(u'Blacklist can not have management authority')))

        if not self.id: #new employee
            try:
                old=self.objByPIN(self.PIN, None)
            except:
                old=None
            if old:
                emp=employee.objects.filter(PIN=self.PIN)
                if emp and emp[0].DelTag!=1:
                    raise Exception(u"%s"%(_(u'Number repeat')))
            self.OpStamp = datetime.datetime.now()
            cache.set('emp_change_flag',1)

        else: # modify a employee
            old_emp=self.objByID(self.id)
            if old_emp.PIN!=self.PIN: #changed the PIN
                if employee.objects.filter(PIN=self.PIN).count()>0:
                    raise Exception("Duplicated Employee PIN: %s"%self.PIN)
            #已离职人员，卡的操作，仅限删除卡号
            if old_emp.OffDuty and (self.OffDuty == old_emp.OffDuty) and not (old_emp.Card != self.Card and not self.Card):
                raise Exception(_(u"The person has left the company and the operation failed."))

            if old_emp.PIN!=self.PIN or old_emp.OffDuty!=self.OffDuty or old_emp.DeptID_id!=self.DeptID_id or old_emp.EName!=self.EName or old_emp.Card!=self.Card or old_emp.Privilege!=self.Privilege or old_emp.VERIFICATIONMETHOD!=self.VERIFICATIONMETHOD or old_emp.MVerifyPass!=self.MVerifyPass or old_emp.City!=self.City:
                self.OpStamp = datetime.datetime.now()
                cache.set('emp_change_flag',1)

            #员工登录密码 有设置且与旧密码不同 或 员工密码未设置且原来有设置密码情况
            if (self.sysPass and self.sysPass != old_emp.sysPass) or (not self.sysPass and old_emp.sysPass):
                if GetParamValue("opt_basic_strong_password", '0')=='1' and not re.match(
                        '^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])).{8,16}$',
                        str(self.sysPass)):
                    raise Exception(
                        u"%s" % (_(
                            u'The password must contain uppercase letters, lowercase letters, and numbers, with a length of 8 to 16 characters.'))
                    )
         #if self.Card:#isvalidate card
        #	empcard=employee.objects.filter(Card=self.Card).exclude(DelTag=1).exclude(OffDuty=1)
        #	if empcard.count()>0 and empcard[0].PIN!=self.PIN:
        #		raise Exception(_(u"卡号重复: %s")%self.Card)
        self.SN=''

        # 多租户模式不允许人员手机号重复
        if settings.MULTI_TENANT and self.Mobile and employee.objects.filter(Mobile=self.Mobile, DelTag=0, OffDuty=0).exclude(id=self.id).exists():
            raise Exception(u"%s"%(_(u'The mobile phone number has been bound to other personnel')))

        if GetParamValue('opt_basic_ssnnorepeat', '0') == '1' and self.SSN and  employee.objects.filter(SSN = self.SSN.strip(),DelTag=0).exclude(PIN =self.PIN):
            raise  Exception(u"%s"%(_(u'ID number has been registered')))

        if GetParamValue('opt_basic_pwdnorepeat', '0') == '1' and self.MVerifyPass and  employee.objects.filter(MVerifyPass = self.MVerifyPass.strip(),DelTag=0,OffDuty=0).exclude(PIN =self.PIN):
            raise  Exception(u"%s"%(_(u'Device password duplication')))
        if self.Address:
            self.Address = escape(self.Address)

        if not self.id:
            try:
                emp=employee.objects.get(PIN=self.PIN)
                self.id=emp.id
                self.DelTag=0

                super(employee,self).save(force_update=True)
            except:
                super(employee,self).save()
        else:
            super(employee,self).save()


        #if GetParamValue('opt_basic_Auto_iclock','0')=='1':
        #	from iclock.dataproc import autoemptodev
        #	if self.OffDuty!=1:
        #		autoemptodev(self)
        #if GetParamValue('opt_basic_Auto_del_iclock','0')=='1':
        #	from iclock.dataproc import autodelempfromdev
        #	if self.OffDuty==1:
        #		autodelempfromdev(self)

        try:
            cache.delete("%s_iclock_emp_Mobile_%s"%(settings.UNIT,self.Mobile))
            cache.delete("%s_iclock_emp_PIN_%s"%(settings.UNIT,self.PIN))
            cache.delete("%s_iclock_emp_%s"%(settings.UNIT,self.id))
            cache.delete('%s_emp_templatecount_%s' % (settings.UNIT, self.id))
            cache.delete("%s_iclock_emp_zoneids_%s"%(settings.UNIT, self.id))
        except:
            pass
        return self
    def delete(self):
        try:
            cache.delete("%s_iclock_emp_%s"%(settings.UNIT,self.id))
            cache.delete("%s_iclock_emp_PIN_%s"%(settings.UNIT,self.PIN))
            cache.delete("%s_iclock_emp_Mobile_%s"%(settings.UNIT,self.Mobile))
            cache.delete('%s_emp_templatecount_%s' % (settings.UNIT, self.id))  #人员列表页，特征模板数量
            cache.delete("%s_iclock_emp_zoneids_%s"%(settings.UNIT, self.id))
            cache.set('emp_change_flag',1)
            super(employee, self).delete()
        except Exception as e:
            #print "=====%s"%e
            pass
    def pin(self):
        return devicePIN(self.PIN)
    def fpCount(self):
        return BioData.objects.filter(UserID=self,bio_type=bioFinger).count()
    def faceCount(self):
        if BioData.objects.filter(UserID=self,bio_type=bioFace).count()>0:return 1
        return 0
    def	GetCopyFields(self):
        return ["National", "PostCode", "Address", "Gender"]
    def __unicode__(self):
        return self.PIN+(self.EName and " %s"%self.EName or "")
    def __str__(self):
        return self.PIN+(self.EName and " %s"%self.EName or "")
    @staticmethod
    def colModels():
        ret=[{'name':'id','hidden':True,'frozen': False},
            {'name':'PIN','index':'PIN','width':100,'search':True,'label':u'%s'%employee._meta.get_field('PIN').verbose_name,'frozen':False},
            {'name':'EName','sortable':False,'width':80,'label':u'%s'%employee._meta.get_field('EName').verbose_name,'frozen': False},
            {'name':'DeptID','width':75,'label':u"%s"%(employee._meta.get_field('DeptID').verbose_name)},
            {'name':'DeptName','sortable':False,'index':'DeptID__DeptName','width':180,'label':u"%s"%(_('department name'))},
            {'name':'Zone','sortable':False,'index':'City','width':100,'label':u"%s"%(employee._meta.get_field('City').verbose_name)},
            {'name':'Gender','width':50,'search':False,'label':u"%s"%(employee._meta.get_field('Gender').verbose_name)},
            {'name':'Card','sortable':False,'width':80,'label':u"%s"%(employee._meta.get_field('Card').verbose_name)},
            {'name':'templateinfo','sortable':False,'search':False,'width':210,'label':u"%s"%(_(u'Biotemplate number'))},
            {'name': 'group_id', 'search': False, 'width': 70, 'label': u"%s" % (_("group name"))},
            {'name':'SSN','sortable':False,'width':120,'label':u"%s"%(employee._meta.get_field('SSN').verbose_name)},
            {'name':'Privilege','width':90,'search':False,'label':u"%s"%(employee._meta.get_field('Privilege').verbose_name)},
             {'name': 'VERIFICATIONMETHOD', 'width': 90, 'search': False, 'label': u"%s"%(employee._meta.get_field('VERIFICATIONMETHOD').verbose_name)},
             {'name':'Birthday','sortable':False,'search':False,'width':80,'label':u"%s"%(employee._meta.get_field('Birthday').verbose_name)},
            {'name':'National','search':False,'width':50,'sortable':False,'label':u"%s"%(employee._meta.get_field('National').verbose_name)},
            {'name':'Title','width':60,'label':u"%s"%(employee._meta.get_field('Title').verbose_name)},
            {'name':'Tele','search':False,'width':80,'sortable':False,'label':u"%s"%(employee._meta.get_field('Tele').verbose_name)},
            {'name':'Mobile','search':False,'sortable':False,'width':80,'label':u"%s"%(employee._meta.get_field('Mobile').verbose_name)},
            {'name':'Hiredday','sortable':False,'search':False,'width':100,'label':u"%s"%(employee._meta.get_field('Hiredday').verbose_name)},
            {'name':'photo','search':False,'sortable':False,'width':100,'label':u"%s"%(_("Picture"))},
            {'name':'email','sortable':False,'width':100,'label':u"%s"%(employee._meta.get_field('email').verbose_name)},
            {'name':'OpStamp','width':120,'label':u"%s"%(employee._meta.get_field('OpStamp').verbose_name)},
            {'name':'OffDuty','search':False,'width':70,'label':u"%s"%(_("Left"))},
            {'name':'Employeetype','search':False,'width':70,'label':u"%s"%(_("Employeetype"))},
             {'name':'Address','sortable':False,'width':80,'label':u"%s"%(_("Address")),'frozen': False}
            ]
        if GetParamValue('opt_basic_emp_pic','0')!='1':
            for t in ret:
                if t['name']=='photo':
                    t['hidden']=True
                if t['name'] in ['id','PIN','EName']:
                    t['frozen']=True
        emp_extend = GetParamValue('opt_basic_emp_extend', '0')
        if emp_extend == '1':
            upobjs = UserProperty.get_all_objs()
            for obj in upobjs:
                t = {'name': str(obj.id), 'sortable':False, 'width': 100, 'label':u"%s"%(_(obj.name))}
                ret.append(t)
        return ret


    class Admin:
        list_display=('PIN','EName','DeptID','Gender','Title','Tele','Mobile')
        list_filter = ('DeptID','Gender','Birthday','OffDuty',)
        search_fields = ['PIN','EName','Card','Mobile','Title']
        lock_fields=['DeptID']

    class Meta:
        db_table = 'userinfo'
        verbose_name=_("employee")
        verbose_name_plural=verbose_name
        unique_together = (("PIN",),)
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
                    ('empLeave_employee','Employee leave'),
                    ('restoreEmpLeave_employee','restore Employee leave'),
                    ('setBlack','Employee black'),
                    ('restoreEmpBlack','restore Employee black'),
                    ('zoneSetting','Setting Employee Zone'),
                    ('toDepart_employee',"Change employee's department"),
                    # ('enroll_employee','Enroll employee\'s fingerprint'),
                    ('Upload_pictures','Upload pictures'),
                    ('edit_privilege','edit privilege'),
                    ('edit_MVerifyPass','edit MVerifyPass'),
                    ('edit_Card','edit Card'),
                    ('unbind_employee', 'Offical account personnel untying'),  # 公众号人员解绑
                    #('EmpToBioTmp', 'EmpToBioTmp'),#用于新增功能:传送人员到比对服务器
        )


class FirmwareConfig(models.Model):
    feature_name = models.CharField(_('特性项名称'), blank=False, null=True, max_length=100)
    parameter_value = models.TextField(_('参数值'), blank=False, null=True)
    remark = models.CharField(_('备注'), max_length=200, blank=True, null=True)
    sort_order = models.IntegerField(_('排序'),blank=True, null=True)

    def save(self, *args, **kwargs):
        # 新增逻辑：首次保存时自动生成自增排序值
        if not self.pk:  # 仅新建时执行
            # 获取当前最大排序值（若没有则为0）
            max_order = FirmwareConfig.objects.aggregate(models.Max('sort_order'))['sort_order__max'] or 0
            self.sort_order = max_order + 1  # 自增
        else:
            if self.sort_order is None:
                raise ValueError(_('排序字段不能为空'))
            else:
                if self.sort_order < 1:
                    raise ValueError(_('排序字段不能小于1'))
                num = FirmwareConfig.objects.filter(sort_order=self.sort_order).first()
                if num:
                    raise ValueError(_('排序字段不能重复'))
        # 去除parameter_value字段两端的空格
        if self.parameter_value:
            self.parameter_value = self.parameter_value.strip()
            # 处理\r\n两端的多个空格
            self.parameter_value = re.sub(r'\s*\r\n\s*', '\n', self.parameter_value)
            print('保存的参数值是什么',self.parameter_value)
        super(FirmwareConfig, self).save(*args, **kwargs)

    def __unicode__(self):
        if self.remark:
            return f"{self.feature_name}({self.remark})"
        else:
            return self.feature_name

    def __str__(self):
        if self.remark:
            return f"{self.feature_name}({self.remark})"
        else:
            return self.feature_name

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'feature_name','index':'feature_name','width':100,'label':u"特性项名称"},
            {'name':'parameter_value','index':'parameter_value','width':200,'label':u"参数值"},
            {'name':'remark','index':'remark','width':200,'label':u"备注"},
            {'name':'sort_order','index':'sort_order','width':200,'label':u"排序"},
            ]

    class Meta:
        db_table = 'firmware_config'
        verbose_name = _('固件参数配置')
        verbose_name_plural = _('固件参数配置')
        default_permissions = ('browse', 'add', 'change', 'delete')

    class Admin:
        list_display = ('feature_name', 'parameter_value', 'remark')
        list_filter = ['feature_name']
        search_fields = ['feature_name']


class UpgradePackage(models.Model):
    """
    连续序列号升级包制作表
    """
    oa_number = models.CharField(max_length=255, verbose_name='OA单号', null=True)
    start_serial_number = models.CharField(max_length=255, verbose_name='起始序列号', null=True)
    serial_number_count = models.IntegerField(verbose_name='序列号数量', null=True)
    parameter_name = models.CharField(max_length=255, verbose_name='特性项名称',null=True)
    parameter_value = models.TextField(verbose_name='固件参数值',null=True)
    operation_time = models.DateTimeField(_(u'制作时间'), null=True, editable=False)
    audit = models.IntegerField(_('审核'), null=True, blank=True, default=0) # 0 未审核 1 已审核
    user = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('administrator'), null=True, blank=True,
                             db_column='user_id', on_delete=models.CASCADE)

    def __unicode__(self):
        return self.oa_number

    def __str__(self):
        return self.oa_number

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name': 'oa_number', 'index': 'oa_number', 'width': 150, 'label': u'OA单号'},
            {'name':'start_serial_number','index':'start_serial_number','width':150,'label':u'起始序列号'},
            {'name':'serial_number_count','index':'serial_number_count','width':100,'label':u'序列号数量'},
            {'name':'parameter_name','index':'parameter_name','width':150,'label':u'特性项名称'},
            {'name':'operation_time','index':'operation_time','width':150,'label':u'制作时间'},
            {'name':'audit','index':'audit','width':150,'label':u'审核状态'},
            {'name': 'operation', 'width': 40, 'label': u'操作','sortable':False}
        ]

    class Meta:
        db_table = 'upgrade_package'
        verbose_name = '连续序列号设备升级包'
        verbose_name_plural = '连续序列号设备升级包'
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('upgrade_pkg_audit', '审核并打包'),
        )

    class Admin:
        list_display = ('start_serial_number','serial_number_count', 'parameter_name', 'oa_number')
        list_filter = ('parameter_name')
        search_fields = ['oa_number']


class DiscontUpgradePackage(models.Model):
    """
    不连续序列号升级包制作表
    """
    oa_number = models.CharField(max_length=255, verbose_name='OA单号', null=True)
    serial_number = models.TextField(verbose_name='不连续序列号', null=True,help_text='1行一个设备序列号不要有任何标点符号')
    parameter_name = models.CharField(max_length=255, verbose_name='特性项名称',null=True)
    parameter_value = models.TextField(verbose_name='固件参数值', null=True)
    operation_time = models.DateTimeField(_(u'制作时间'), null=True, editable=False)
    audit = models.IntegerField(_('审核'), null=True, blank=True, default=0) # 0 未审核 1 已审核
    user = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('administrator'), null=True, blank=True,
                             db_column='user_id', on_delete=models.CASCADE)

    def __unicode__(self):
        return self.oa_number

    def __str__(self):
        return self.oa_number

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name': 'oa_number', 'index': 'oa_number', 'width': 150, 'label': u'OA单号'},
            {'name':'serial_number','index':'serial_number','width':150,'label':u'不连续序列号'},
            {'name':'parameter_name','index':'parameter_name','width':150,'label':u'特性项名称'},
            {'name':'operation_time','index':'operation_time','width':150,'label':u'制作时间'},
            {'name': 'audit', 'width': 150, 'label': u'审核状态'},
            {'name': 'operation', 'width': 40, 'label': u'操作','sortable':False}
        ]

    class Meta:
        db_table = 'discont_upgrade_package'
        verbose_name = '不连续序列号设备升级包'
        verbose_name_plural = '不连续序列号设备升级包'
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('disupgrade_pkg_audit', '审核并打包'),
        )


    class Admin:
        list_display = ('serial_number','parameter_name', 'oa_number')
        list_filter = ('parameter_name')
        search_fields = ['oa_number']


class UpgradePackageTable(models.Model):
    """
    升级包制作明细报表
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('administrator'), null=True, blank=True,
                             db_column='user_id', on_delete=models.CASCADE)
    upgrade_package_id = models.ForeignKey(UpgradePackage, verbose_name='连续序列号升级包ID', null=True, blank=True,
                                          db_column='upgrad'
                                                    ''
                                                    'e_package_id', on_delete=models.CASCADE)
    discont_upgrade_package_id = models.ForeignKey(DiscontUpgradePackage, verbose_name='不连续序列号升级包ID', null=True, blank=True,
                                          db_column='discont_upgrade_package_id', on_delete=models.CASCADE)
    oa_number = models.CharField(max_length=255, verbose_name='OA单号', null=True)
    serial_number = models.CharField(max_length=255, verbose_name='设备序列号', null=True)
    parameter_name = models.CharField(max_length=255, verbose_name='特性项名称',null=True)
    parameter_value = models.TextField(verbose_name='固件参数值', null=True, editable=False)
    operation_time = models.DateTimeField(_(u'制作时间'), null=True, editable=False)

    def __unicode__(self):
        return self.serial_number

    def __str__(self):
        return self.serial_number

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'user','index':'user','width':150,'label':u'管理员'},
            {'name':'oa_number', 'index': 'oa_number', 'width': 150, 'label': u'OA单号'},
            {'name':'serial_number','index':'serial_number','width':150,'label':u'设备序列号'},
            {'name':'parameter_name','index':'parameter_name','width':150,'label':u'特性项名称'},
            {'name':'operation_time','index':'operation_time','width':150,'label':u'制作时间'}
        ]

    class Meta:
        db_table = 'upgrade_package_table'
        verbose_name = '升级包制作明细报表'
        verbose_name_plural = '升级包制作明细报表'
        default_permissions = ('browse', 'export')

    class Admin:
        search_fields = ['oa_number','serial_number']




def getNormalCard(card):
    if not card: return ""
    try:
        num=int(str(card))
        card="[%02X%02X%02X%02X%02X]"%(num & 0xff, (num >> 8) & 0xff, (num >> 16) & 0xff, (num >> 24) & 0xff, (num >> 32) & 0xff)
    except:
        card=card[:-3]+'00]'
    return card

def getEmpCmdStr(emp,device=None,groupId=None):
    ret=''
    if emp.DelTag==1 or emp.OffDuty==1:return ret
    ename=emp.EName and emp.EName.strip() or ""
    if type(device)==list:
        device=getDevice(device[0])
    if (not device) or (device.ProductType not in [5,15]):
        ret= "DATA USER PIN=%s\t%s\t%s\t%s\t%s\t%s\tGrp=%s"%(emp.pin(),
                ename and ("Name=%s"%ename) or "",
                "Pri=%s"%(emp.Privilege and emp.Privilege or 0),
                "Passwd=%s"%(emp.MVerifyPass or ""),
                "Card=%s"%(getNormalCard(emp.Card) or ""),
                "",
                1)
    elif device.ProductType in [5,15,25]:#为门禁控制器生成命令
        isDisabled=0
        super_auth=0
        starttime=0
        endtime=0
        grp=0
        try:
            from mysite.acc.models import acc_employee
            obj=acc_employee.objByUserID(emp.id)
            if obj.isblacklist:isDisabled=1
            super_auth=obj.acc_super_auth or 0
            if obj.set_valid_time and obj.acc_startdate and obj.acc_enddate:
                starttime=OldEncodeTime(obj.acc_startdate)
                endtime=OldEncodeTime(obj.acc_enddate)
            if obj.morecard_group:
                grp=obj.morecard_group_id

        except:
            pass
        emppin=emp.pin()
        if settings.IDFORPIN==1:
            emppin=emp.id
        if device.ProductType in [5,25]:
            ret="\r\nCardNo=%s\tPin=%s\tPassword=%s\tGroup=%s\tStartTime=%s\tEndTime=%s\tName=%s\tSuperAuthorize=%s\tDisable=%s"%(emp.Card or '',emppin,emp.MVerifyPass or '',grp or 0,starttime,endtime,ename,super_auth,isDisabled)
        else:#PULL设备时间格式YYYYMMDD
            st= OldDecodeTime(starttime)[:10].replace('-','')
            et= OldDecodeTime(endtime)[:10].replace('-','')

            if device.Style not in [DEVICE_C3_100, DEVICE_C3_200, DEVICE_C3_400, DEVICE_C3_400_TO_200]:
                ret="\r\nCardNo=%s\tPin=%s\tPassword=%s\tGroup=%s\tStartTime=%s\tEndTime=%s"%(emp.Card or '',emppin,emp.MVerifyPass or '',grp or 0,st,et)
            else:
                ret="\r\nCardNo=%s\tPin=%s\tPassword=%s\tGroup=%s\tStartTime=%s\tEndTime=%s"%(emp.Card or '',emppin,emp.MVerifyPass or '',grp or 0,st,et)




    return ret

def getBiophoto(pin):
    """
    获取比对照片,base64格式（当前仅在信息屏有用到此方法）
    """
    ls_f = cache.get('%s_BIOPHOTO_BASE64_%s_%s'%(settings.UNIT, 9, pin))
    if ls_f:
        cmdStr = u"DATA UPDATE BIOPHOTO PIN=%s\tType=2\tSize=%d\tContent=%s"%(pin, len(ls_f), ls_f)
        return cmdStr
    import base64
    adfile = getStoredFileName('photo', None, "ad_%s.jpg"%pin)
    if os.path.isfile(adfile):
        try:
            f=open(r'%s'%adfile,'rb')
            data=f.read()
            if data[:10]==b"CRYPT_IMG:":
                data = aes_crypt(data[10:], False)
            ls_f=base64.b64encode(data)
            f.close()
            if sys.version[0] == '3':
                ls_f = ls_f.decode("latin-1")
            cache.set('%s_BIOPHOTO_BASE64_%s_%s'%(settings.UNIT, 9, pin), ls_f, 60*60*2)  #缓存2小时
            cmdStr=u"DATA UPDATE BIOPHOTO PIN=%s\tType=2\tSize=%d\tContent=%s"%(pin, len(ls_f), ls_f)
            return cmdStr
        except Exception as e:
            print ("ad photo=",e)
            pass
    return u""

def get_biophoto_base64(pin, bio_type=9):
    """
    读取比对照片（支持不同类型比对照片，如可见光面部照片、可见光手掌照片）
        生成比对照片命令时，命令中不包含具体的照片信息，设备取命令时才组装成完整的命令
    """
    import PIL.Image as Image
    ls_f = cache.get('%s_BIOPHOTO_BASE64_%s_%s'%(settings.UNIT, bio_type, pin))
    if ls_f:
        return ls_f
    if bio_type == 9:
        biophoto = getStoredFileName("photo", None, "ad_{}.jpg".format(pin))
    else:
        biophoto = getStoredFileName("photo/biophoto_%s" % bio_type, None, "ad_{}.jpg".format(pin))

    if os.path.isfile(biophoto):
        try:
            with open(biophoto, "rb") as f:
                content = f.read()
            if not content:
                data = b''
            # 如果图片已加密，则先解密，再base64
            elif content[:10] == b"CRYPT_IMG:":
                image_data = aes_crypt(content[10:], False)  # 解密照片
                # 以下为异常图片数据处理（如多次加密，或是只有CRYPT_IMG字符的文件）
                if not image_data or b'CRYPT_IMG' in image_data:
                    data = b''
                else:
                    data = image_data
            else:
                data = content
            ls_f = base64.b64encode(data).decode('utf-8')
            cache.set('%s_BIOPHOTO_BASE64_%s_%s'%(settings.UNIT, bio_type, pin), ls_f, 60*60*2)  #缓存2小时
            return ls_f
        except:
            if settings.DEBUG:
                import traceback;traceback.print_exc()
    return u""

def get_user_pic(pin):
    """
    读取人员头像
        生成头像命令时，命令中不包含具体的头像信息，设备取命令时才组装成完整的命令
    """
    import PIL.Image as Image
    ls_f = cache.get('%s_USERPIC_BASE64_%s'%(settings.UNIT, pin))
    if ls_f:
        return ls_f
    userpic = getStoredFileName("photo/thumbnail", None, "{}.jpg".format(pin))
    if os.path.isfile(userpic):
        try:
            with open(userpic, "rb") as f:
                content = f.read()
            if not content:
                data = b''
            # 如果图片已加密，则先解密，再base64
            elif content[:10] == b"CRYPT_IMG:":
                image_data = aes_crypt(content[10:], False)  # 解密照片
                # 以下为异常图片数据处理（如多次加密，或是只有CRYPT_IMG字符的文件）
                if not image_data or b'CRYPT_IMG' in image_data:
                    data = b''
                else:
                    data = image_data
            else:
                data = content
            ls_f = base64.b64encode(data).decode('utf-8')
            cache.set('%s_USERPIC_BASE64_%s'%(settings.UNIT, pin), ls_f, 60*60*2)  #缓存2小时
            return ls_f
        except:
            if settings.DEBUG:
                import traceback;traceback.print_exc()
    return u""


EMPLEAVETYPE = (
        (0, _(u'self-dissociation')),  #自离
        (1, _(u'dismiss')),  #辞退
        (2, _(u'resignation')),  #辞职
        (3, _(u'tune away')),  #调离
        (4, _(u'No pay and stay')),  #停薪留职
        (5, _(u'retire')),  #退休
        (6, _(u'death')),  #死亡
)

EMPLEAVESTATE = (
        (0, _(u'departed')),
        (1, _(u'Resignation Recovery')),
)

def GetLeavetype():
    ret = []
    for leav in EMPLEAVETYPE:
        dleav = {}
        dleav['symbol'] = leav[0]
        dleav['pName'] = str(leav[1])
        ret.append(dleav)
    return ret

class empleavelog(models.Model):
    UserID = models.ForeignKey(employee,  verbose_name=_(u"Employee"),db_column='userid_id', on_delete=models.CASCADE)
    leavedate=models.DateTimeField(verbose_name=_(u'date of departure'),editable=True)
    leavetype=models.IntegerField(verbose_name=_(u'Departure type'),choices=EMPLEAVETYPE,editable=True)
    reason=models.CharField(verbose_name=_(u'Reason for leaving'),max_length=200,null=True,blank=True,editable=True)
    createtime=models.DateTimeField(verbose_name=_(u'Operation time'),editable=True)
    deltag = models.IntegerField(_(u'delete tag'),default=0,choices=EMPLEAVESTATE, editable=False, null=True, blank=True)

    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def __unicode__(self):
        return u"%s" % (self.UserID.__unicode__())
    def __str__(self):
        return u"%s" % (self.UserID.__str__())
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
            {'name':'EName','index':'UserID__EName','width':80,'label':u"%s"%(_('EName'))},
            {'name':'leavedate', 'width':120,'sortable':True,'label':u"%s"%(empleavelog._meta.get_field('leavedate').verbose_name)},
            {'name':'leavetype', 'width':90,'label':u"%s"%(empleavelog._meta.get_field('leavetype').verbose_name)},
            {'name':'reason','sortable':False,'width':120,'label':u"%s"%(_(u'Reason for leaving'))}
            ]

    class Admin:
        list_filter =['UserID','leavedate','leavetype']
        lock_fields=['UserID']
        search_fields = ['UserID__PIN','UserID__EName']
    class Meta:
        db_table = 'user_empleavelog'
        verbose_name = _(u'departed staff')
        verbose_name_plural=verbose_name
        #unique_together = (("UserID", "leavedate","leavetype","deltag", "createtime","id"),)
        default_permissions = ('browse','add', 'change', 'delete')
        permissions = (
            )

class stafflogin(models.Model):
    UserID = models.ForeignKey(employee,  verbose_name=u"%s"%(_(u"Employee")),db_column='userid_id', on_delete=models.CASCADE)
    StaffUsername = models.CharField(_(u'username'),max_length=20, null=True, blank=True,unique=True,db_column='staffusername')
    LoginMethod = models.IntegerField(_(u'Login Method'),null=True, blank=True,editable=False,db_column='loginmethod')

    def __unicode__(self):
        return self.StaffUsername
    def __str__(self):
        return self.StaffUsername

    class Admin:
        pass

    class Meta:
        db_table = 'stafflogin'
        verbose_name = _(u'Login Method')
        verbose_name_plural=verbose_name
        default_permissions = ('browse','add', 'change', 'delete')
        #unique_together = (("StaffUsername",),)




FINGERIDS=(
    (0, _(u'left hand little finger')),
    (1, _(u'Left ring finger')),
    (2, _(u'left hand')),
    (3, _(u'left hand index finger')),
    (4, _(u'left thumb')),
    (5, _(u'right thumb')),
    (6, _(u'right hand index finger')),
    (7, _(u'right middle finger')),
    (8, _(u'Right Ring Finger')),
    (9, _(u'right hand little finger')),
)

#class fptemp(models.Model):
#	id=models.AutoField(primary_key=True)
#	UserID = models.ForeignKey("employee", db_column='userid', verbose_name=u"员工", on_delete=models.CASCADE)
#	Template = models.TextField(u'指纹模板',null=True)
#	FingerID = models.IntegerField(u'手指',default=0, choices=FINGERIDS)
#	Valid = models.IntegerField(u'是否有效',default=1, choices=BOOLEANS)
#	#DelTag = models.IntegerField(u'删除标记',default=0, choices=BOOLEANS,null=True)
#	SN = models.ForeignKey(iclock, db_column='SN', verbose_name=u'登记设备', null=True, blank=True, on_delete=models.CASCADE)
#	UTime = models.DateTimeField(_('refresh time'), null=True, blank=True, editable=False)
#	#BITMAPPICTURE=models.TextField(null=True,editable=False)
#	#BITMAPPICTURE2=models.TextField(null=True,editable=False)
#	#BITMAPPICTURE3=models.TextField(null=True,editable=False)
#	#BITMAPPICTURE4=models.TextField(null=True,editable=False)
#	USETYPE = models.IntegerField(null=True,editable=False)
##	Template2 = models.TextField(u'指纹模板',null=True,editable=False)
##	Template3 = models.TextField(u'指纹模板',null=True,editable=False)
#	AlgVer=models.IntegerField(null=True,default=0,blank=True,editable=False)
#	def __unicode__(self):
#		return "%s, %d"%(self.UserID.__unicode__(),self.FingerID)
#	def template(self):
#		return self.Template.decode("base64")
#	def temp(self):
#		#去掉BASE64编码的指纹模板中的回车
#		return self.Template.replace("\n","").replace("\r","")
#	def employee(self): #cached employee
#		try:
#			return employee.objByID(self.UserID_id)
#		except:
#			return None
#	@staticmethod
#	def colModels():
#		return [{'name':'id','hidden':True},
#			{'name':'DeptName','index':'UserID__DeptID','sortable':False,'width':200,'label':u"%s"%(_('department name'))},
#			{'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
#			{'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('EName'))},
#			{'name':'FingerID','width':100,'label':u"%s"%(_('Finger serial number'))},
#			{'name':'FingerName','width':100,'sortable':False,'label':u"%s"%(fptemp._meta.get_field('FingerID').verbose_name)},
#			{'name':'Fingerprint Images','label':u"%s"%(_('Fingerprint Images')),'hidden':True},
#			{'name':'AlgVer','index':'AlgVer','width':100,'label':u"%s"%(_(u'指纹版本'))},
#			{'name':'UTime','width':120,'label':u"%s"%(_(u'Collection time'))}
#			]
#	class Admin:
#		list_display=('UserID', 'FingerID', 'Valid')
#		list_filter = ('UserID',)
#		search_fields = ['=UserID__PIN','=UserID__EName']
#	class Meta:
#		db_table = 'template'
#		unique_together = (("UserID", "FingerID","AlgVer"),)
#		verbose_name=_("fingerprint")#u"人员指纹"
#		verbose_name_plural=verbose_name

#class iface(models.Model):
#	UserID = models.ForeignKey("employee", db_column='userid', on_delete=models.CASCADE)
#	FaceID = models.CharField(max_length=50, null=True, blank=True)
#	PersonID = models.CharField(max_length=50, null=True, blank=True)
#	Valid = models.SmallIntegerField(default=1)
#
#	class Meta:
#		db_table = 'iface'
#		unique_together = (("UserID", "PersonID"),)
#
#	def save(self, *args, **kwargs):
#		super(iface, self).save(*args, **kwargs)


#class facetemp(models.Model):
#	id=models.AutoField(primary_key=True)
#	UserID = models.ForeignKey("employee", db_column='userid', verbose_name=u"员工", on_delete=models.CASCADE)
#	Template = models.TextField(u'面部模板',max_length=2048,null=True)
#	FaceID = models.SmallIntegerField(u'模板编号',default=0)
#	Valid = models.SmallIntegerField(u'是否有效',default=1, choices=BOOLEANS)
#	#DelTag = models.SmallIntegerField(u'删除标记',default=0, choices=BOOLEANS,null=True)
#	SN = models.ForeignKey(iclock, db_column='SN', verbose_name=u'登记设备', null=True, blank=True, on_delete=models.CASCADE)
#	UTime = models.DateTimeField(_('refresh time'), null=True, blank=True, editable=False)
#	USETYPE = models.SmallIntegerField(null=True,editable=False)
#	AlgVer=models.IntegerField(null=True,default=0,blank=True,editable=False)
#	def __unicode__(self):
#		return "%s, %d"%(self.UserID.__unicode__(),self.FaceID)
#	def template(self):
#		return self.Template.decode("base64")
#	def temp(self):
#		#去掉BASE64编码的模板中的回车
#		return self.Template.replace("\n","").replace("\r","")
#	def employee(self): #cached employee
#		try:
#			return employee.objByID(self.UserID_id)
#		except:
#			return None
#	@staticmethod
#	def colModels():
#		return [{'name':'id','hidden':True},
#			{'name':'DeptName','sortable':False,'index':'UserID__DeptID','width':200,'label':u"%s"%(_('department name'))},
#			{'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
#			{'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('EName'))},
#			{'name':'FaceID','width':100,'label':u"%s"%(_('face serial number')),'hidden':True},
#			#{'name':'FaceName','width':100,'sortable':False,'label':u"%s"%(facetemp._meta.get_field('FaceID').verbose_name)},
#			{'name':'face Images','label':u"%s"%(_('face Images')),'hidden':True},
#			{'name':'AlgVer','width':100,'label':u"%s"%(_('AlgVer'))},
#			{'name':'UTime','width':120,'label':u"%s"%(_(u'Collection time'))}
#			]
#	class Admin:
#		list_display=('UserID', 'FaceID', 'Valid')
#		list_filter = ('UserID',)
#		search_fields = ['=UserID__PIN','=UserID__EName']
#	class Meta:
#		db_table = 'facetemplate'
#		unique_together = (("UserID", "FaceID","AlgVer"),)
#		verbose_name=_("face template")#u"人员面部"
#		verbose_name_plural=verbose_name

def getCacheBioData(id,tid):
    tid=int(tid)
    id=str(int(id))
    tmp=cache.get('%s_TMP_%s_%s'%(settings.UNIT,id,tid))
    if tmp:
        return tmp
    try:
        objs=BioData.objects.filter(id=id)
        if objs:
            obj=objs[0]
            bio_type=obj.bio_type
            if bio_type in [bioVein,bioFace,bioHand,bioFaceVL,bioHandVL]:
                d_tmp = loads(obj.bio_tmp)
                for k, v in d_tmp.items():
                    if int(k)==int(tid):
                        tmp=v.replace("\n","").replace("\r","")
            else:
                tmp=obj.temp()
    except Exception as e:
        print ("getCacheBioData=",e)
    if tmp:
        cache.set('%s_TMP_%s_%s'%(settings.UNIT,id,tid),tmp)
    return tmp



"""大容量协议，暂时未考虑
DATA UPDATE BIODATA Pin=%s\tNo=0\tIndex=%s\tValid=1\tDuress=0\tType=%s\tMajorVer=%s\tMinorVer=0\tFormat=0\tTmp=%s
"""
class BioData(models.Model):
    id=models.AutoField(primary_key=True)
    UserID = models.ForeignKey("employee", db_column='userid', verbose_name=_(u"Employee"), on_delete=models.CASCADE)
    bio_tmp = models.TextField(_(u'Feature Template'))
    bio_no = models.IntegerField(_(u'Numbering'), default=0, null=True)#生物具体个体编号，默认值为0 0为左眼 1为右眼  0为左手 1为右手 指纹0---9
    bio_index = models.IntegerField(_(u'serial number'),default=0, null=True)#生物具体个体模板编号，该值一般都为0，一个指静脉3个模板保存在一行，因此bio_index=0 一张脸12个保存在一行
    bio_type = models.IntegerField(_(u'template type'))# 0 通用 1 指纹 2 面部 3  声纹  4 虹膜 5 视网膜  6 掌纹 7 指静脉  8手掌  9可见光面部 10可见光手掌
    majorver = models.CharField(_(u'Algorithm version'), max_length=30)#指纹版本存10,9 非10.0
    minorver = models.CharField(_(u'Algorithm version'), max_length=30, null=True)
    bio_format = models.IntegerField(_(u'algorithm format'), default=0, null=True)#模板格式，如指纹有ZK=0\PIC=1\ISO=2\ANSI=3等格式
    valid = models.IntegerField(_(u'is it effective'),default=1, choices=BOOLEANS)#是否有效标示，0：无效，1：有效，默认为1
    duress = models.IntegerField(_(u'Is it coercive?'),default=0, choices=BOOLEANS)#是否胁迫标示，0：非胁迫，1：胁迫，默认为0
    UTime = models.DateTimeField(_('refresh time'), null=True, db_column='utime',blank=True, editable=False)
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_(u'registered equipment'), null=True, blank=True, on_delete=models.CASCADE)
    def __unicode__(self):
        return u"%s,%s"%(self.UserID.__unicode__(), u"%s"%(self.bio_type))
    def __str__(self):
        return u"%s,%s"%(self.UserID.__str__(), u"%s"%(self.bio_type))


    def template(self):
        return self.bio_tmp.decode("base64")

    def temp(self):
        #去掉BASE64编码的指纹模板中的回车
        return self.bio_tmp.replace("\n","").replace("\r","")

    def algorithm_version(self, ):
        return "%s.%s" % (self.majorver, self.minorver or '0')

    class Admin:
        list_display=('UserID', 'bio_type', 'valid')
        list_filter = ('UserID',)
        search_fields = ['=UserID__PIN','=UserID__EName']

    class Meta:
        db_table = 'bio_data'
        unique_together = (("UserID", "bio_no", "bio_type", "majorver", "minorver"),)
        verbose_name = _(u"Feature Template")
        verbose_name_plural=verbose_name
        default_permissions=('browse','add','delete','export')

    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def bio_type_name(self, ):
        bio_type_names=(
            _(u'universal'),
            _(u'fingerprint'),
            _(u'human face'),
            _(u'soundprint'),
            _(u'Iris'),
            _(u'retina'),
            _(u'palm print'),
            _(u'Finger vein'),
            _(u'palm'),
            _(u'visible imagery'),
            _(u'LightPalm mode')
        )
        return bio_type_names[self.bio_type]
    def bio_name(self, ):
        if self.bio_type in [bioFinger,bioVein]:
            return FINGERIDS[self.bio_no][1]
        elif self.bio_type==bioFace:
            return ''
        elif self.bio_type==bioIris:
            bio_type_names=(_(u'left eye'),_(u'right eye'))
            return bio_type_names[self.bio_no]
        return u''

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
            {'name':'DeptName','sortable':False,'index':'UserID__DeptID','width':200,'label':u"%s"%(_('department name'))},
            {'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
            {'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('EName'))},
            {'name':'bio_type','sortable':False,'width':80,'label':u"%s"%(BioData._meta.get_field('bio_type').verbose_name)},
            {'name':'bio_no','width':60,'label':u"%s"%(BioData._meta.get_field('bio_no').verbose_name)},
            {'name':'bio_name','sortable':False,'width':80,'label':u"%s"%(_(u'Bio Name'))},
            #{'name':'FaceName','width':100,'sortable':False,'label':u"%s"%(facetemp._meta.get_field('FaceID').verbose_name)},
            {'name':'AlgVer','sortable':False,'width':80,'label':u"%s"%(_('AlgVer'))},
            {'name':'SN','sortable':False,'width':120,'label':u"%s"%(_('Device name'))},
            {'name':'UTime','width':120,'label':u"%s"%(_(u'Collection time'))}
            ]



class OperatorBiodata(models.Model):
    user = models.ForeignKey(MyUser, db_column='admin', verbose_name=_("Administrator"), on_delete=models.CASCADE)
    bio_tmp = models.TextField(_('Feature Template'))
    bio_no = models.IntegerField(_('Numbering'), default=0, null=True) # 手指编号0---9
    bio_index = models.IntegerField(_('serial number'),default=0, null=True) # 生物具体个体模板编号，该值一般都为0，一个指静脉3个模板保存在一行，因此bio_index=0 一张脸12个保存在一行
    majorver = models.CharField(_('Algorithm version'), max_length=30, default='10') # 指纹版本存10,9 非10.0
    minorver = models.CharField(_('Algorithm version'), max_length=30, null=True)
    bio_format = models.IntegerField(_('algorithm format'), default=0, null=True) # 模板格式，如指纹有ZK=0\PIC=1\ISO=2\ANSI=3等格式
    valid = models.IntegerField(_('is it effective'),default=1, choices=BOOLEANS) # 是否有效标示，0：无效，1：有效，默认为1
    UTime = models.DateTimeField(_('refresh time'), null=True, db_column='utime', blank=True, editable=False)
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('registered equipment'), null=True, blank=True, on_delete=models.CASCADE)

    class Admin:
        list_display=('user', 'valid')
        list_filter = ('user',)
        search_fields = ['=user_id', '=user_username']

    class Meta:
        db_table = 'operator_biodata'
        unique_together = (("user", "bio_no", "majorver"),)
        verbose_name = _("Admin Feature Template")
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'add', 'delete', 'export')


VERIFYS=(
(0, _("Password")),
(1, _("Fingerprint")),
(2, _("Card")),
(3, _("Card")),
(4, _("Card")),
(5, _("Add")),

(9, _("Other")),
(11, _(u"card + password")),#为控制器

(15,_("FACE")),
(16,_(u"other")),
)




# COMVERIFYS=(#组合验证
# (0, _("FP_OR_PW_OR_RF")),#指纹或密码或卡
# (1, _("FP")),#指纹
# (2, _("PIN")),#考勤号
# (3, _("PW")),#密码
# (4, _("RF")),#卡
# (5, _("FP_OR_PW")),#指纹或密码
# (6, _("FP_OR_RF")),#指纹或卡
# (7, _("PW_OR_RF")),#密码或卡
# (8, _("PIN_AND_FP")),#考勤号和指纹
# (9, _("FP_AND_PW")),#指纹和密码
# (10, _("FP_AND_RF")),#指纹和卡
# (11, _("PW_AND_RF")),#密码和卡
# (12, _("FP_AND_PW_AND_RF")),#指纹和密码和卡
# (13, _("PIN_AND_FP_AND_PW")),#考勤号和指纹和密码
# (14, _("FP_AND_RF_OR_PIN")),#指纹和卡和考勤号
# (15,_("FACE")),
# (16,_("FACE_AND_FP")),#面部加指纹
# (17, _("FACE_AND_PW")),#面部加密码
# (18, _("FACE_AND_RF")),#面部加卡
# (19, _("FACE_AND_FP_AND_RF")),#面部加指纹加卡
# (20, _("FACE_AND_FP_AND_PW")),#面部加指纹加密码
# )

#ATTSTATES=(
#("I",_("Check in")),
#("O",_("Check out")),
##("8",_("Meal start")),
##("9",_("Meal end")),
##("i",_("Overtime in")),
##("o",_("Overtime out")),
##("0",_("Break out")),
##("1",_("Break in")),
#("2",_("Break out")),
#("3",_("Break in")),
#("4",_("Overtime in")),
#("5",_("Overtime out")),
##("160",_("Test Data")),
#)

#新的默认状态定义,可以通过系统设置进行重新定义,变化是原来的I变成0,O变成1,获得状态名称统一通过getRecordName(state)实现
ATTSTATES=(
("I",_("Check in")),
("O",_("Check out")),
("i",_("Break out")),
("o",_("Break in")),
("V",_("Overtime in")),
("v",_("Overtime out")),
)

def transAttState(state):
    if state:
        for i in range(len(ATTSTATES)):
            if ATTSTATES[i][0] in state:
                return ATTSTATES[i][1].title()
        return ''
    else:
        return ''

def tranAttVerify(code):
    if code:
        for i in range(len(VERIFYS)):
            if VERIFYS[i][0]==code:
                return VERIFYS[i][1].title()
        return ''
    else:
        return ''


def createThumbnail(imgUrlOrg, imgUrl, encrypt=True):
    import PIL.Image as Image

    #压缩缩略图时，如果原图已是加密，则先解密，再重新缩略、加密
    img = open(imgUrlOrg,'rb')
    data = img.read()
    img.close()
    if data[:10] == b"CRYPT_IMG:":
        image_data = aes_crypt(data[10:], False)
        img = open(imgUrlOrg,'wb')
        data = img.write(image_data)
        img.close()

    try:
        im = Image.open(imgUrlOrg)
    except IOError as e:
        return None
    cur_width, cur_height = im.size
    new_width, new_height = 112, 140  #数值参考近红外设备上传的头像分辨率
    if 0: #crop
        if cur_width < cur_height:
            ratio = float(new_width)/cur_width
        else:
            ratio = float(new_height)/cur_height
        x = (cur_width * ratio)
        y = (cur_height * ratio)
        x_diff = int(abs((new_width - x) / 2))
        y_diff = int(abs((new_height - y) / 2))
        box = (x_diff, y_diff, (x-x_diff), (y-y_diff))
        resized = im.resize((x, y), Image.LANCZOS).crop(box)
    else:
        if not new_width == 0 and not new_height == 0:
            if cur_width > cur_height:
                ratio = float(new_width)/cur_width
            else:
                ratio = float(new_height)/cur_height
        else:
            if new_width == 0:
                ratio = float(new_height)/cur_height
            else:
                ratio = float(new_width)/cur_width
        try:
            resized = im.resize((int(cur_width * ratio), int(cur_height * ratio)), Image.LANCZOS)
            try:
                os.makedirs(os.path.split(imgUrl)[0])
            except:pass
            resized.save(imgUrl)
            im.close()
            if encrypt:
                crypt_img(imgUrl)   #加密缩略头像照片
                crypt_img(imgUrlOrg)   #加密原头像照片
                # os.remove(imgUrlOrg)    #删除原照片
            return imgUrl
        except Exception as e:
            pass
    return None




SIGN_TYPE = ((0, _(u'sign in first')),(1, _(u'approval first')))
# 外勤签到表
class outWork(models.Model):
    UserID = models.ForeignKey(employee, db_column='userid', null=False, default=1, verbose_name=_("employee"), on_delete=models.CASCADE)
    CHECKTIME = models.DateTimeField(_('Check time'), null=True, default=0, blank=True, db_column='checktime')
    longitude = models.CharField(_('longitude'), max_length=100, null=True, default='', blank=True)
    latitude = models.CharField(_('latitude'), max_length=100, null=True, default='', blank=True)
    location = models.CharField(_('location'), max_length=100, null=True, blank=True, db_column='location')

    reason = models.CharField(_('reason'), max_length=100, null=True, blank=True, db_column='reason')
    MODIFYBY = models.CharField(_('Modify by'), max_length=50, null=True, blank=True, db_column='modifyby')
    State = models.IntegerField(_('state'), null=True, default=0, blank=True, choices=AUDIT_STATES, editable=False, db_column='state')#0-申请；2-通过；3-拒绝；6-重新申请；>10表示审批中，此时减去10等于管理员id(职务审批）或者人员id(人员审批)
    # ApplyDate = models.DateTimeField(_('apply date'), null=True, blank=True, db_column='applydate')

    roleid = models.IntegerField(_(u'current reviewer'), editable=True, db_column='roleid')# 当前审核人
    process = models.CharField(_(u'Audit Process'), max_length=80, null=True, blank=True, db_column='process')# 审批流程
    oldprocess = models.CharField(_(u'Audited Process'), max_length=80, null=True, blank=True, db_column='oldprocess')# 审核过的流程
    processid = models.IntegerField(_(u'Audit Process id'), editable=True, db_column='processid')# 审核流程id
    procSN = models.IntegerField(_(u'reviewed serial number'), default=0, editable=True, db_column='procsn')# 已审序号

    #签到记录类型，0先签到后审批，1先外出申请审批后再签到
    sign_type = models.IntegerField(_('Sign Type'), null=True, default=0, blank=True, choices=SIGN_TYPE, editable=False, db_column='sign_type')
    processtype = models.IntegerField(_('process type'), default=0, editable=True, choices=PROCESSTYPE,db_column='processtype')
    employeelist = models.CharField(_('current reviewer'), max_length=80, null=True, blank=True)
    reserve1 = models.CharField(_('reserve1'), max_length=250, null=True)  # 预留字段
    reserve2 = models.CharField(_('reserve2'), max_length=250, null=True)  # 预留字段

    def __unicode__(self):
        return u"%s"%(u"%s" % (self.UserID))
    def __str__(self):
        return u"%s"%(u"%s" % (self.UserID))

    def employee(self):
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None

    @staticmethod
    def colModels(request=None):
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'DeptID', 'width': 80, 'index': 'UserID__DeptID', 'label': u"%s" % (_('department number'))},
            {'name': 'DeptName', 'width': 180, 'index': 'UserID__DeptID__DeptName',
             'label': u"%s" % (_('department name'))},
            {'name': 'PIN', 'index': 'UserID__PIN', 'width': 120, 'label': u"%s" % (_('PIN'))},
            {'name': 'EName', 'index': 'UserID__EName', 'width': 80, 'label': u"%s" % (_('EName'))},
            {'name': 'CHECKTIME', 'width': 120,
             'label': u"%s" % (outWork._meta.get_field('CHECKTIME').verbose_name)},
            {'name': 'location', 'width': 120, 'index': 'location', 'label': u"%s" % (_('location'))},
            {'name': 'process', 'sortable': False, 'index': 'process', 'width': 220,
             'label': u"%s" % (_(u'Audit flow'))},
            {'name': 'State', 'width': 80, 'hidden': False, 'search': False,
             'label': u"%s" % (outWork._meta.get_field('State').verbose_name)},
            {'name': 'outPhoto', 'width': 100, 'hidden': False, 'search': False,
             'label': u"%s" % (_(u'Outwork photo'))}
        ]


    class Meta:
        verbose_name=_("Out work")
        verbose_name_plural=verbose_name
        db_table = 'app_out_work'
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
            ('TransAudit_outWork', 'Audit OutWork'),
        )
    class Admin:
        search_fields = ['UserID__PIN','UserID__EName']


# 人员外出申请表
class UserEgress(models.Model):
    UserID = models.ForeignKey(employee, db_column='userid', null=False, default=1, verbose_name=_("employee"), on_delete=models.CASCADE)
    starttime = models.DateTimeField(_('beginning time'), null=False, blank=True, db_column='starttime')
    endtime = models.DateTimeField(_('ending time'), null=True, blank=True, db_column='endtime')
    longitude = models.CharField(_('longitude'), max_length=100, null=True, default='', blank=True)
    latitude = models.CharField(_('latitude'), max_length=100, null=True, default='', blank=True)
    location = models.CharField(_('location'), max_length=100, null=True, blank=True)

    reason = models.CharField(_('reason'), max_length=100, null=True, blank=True)
    MODIFYBY = models.CharField(_('Modify by'), max_length=50, null=True, blank=True, db_column='modifyby')
    State = models.IntegerField(_('state'), null=True, default=0, blank=True, choices=AUDIT_STATES, editable=False, db_column='state')
    ApplyDate = models.DateTimeField(_('apply date'), null=True, blank=True, db_column='applydate')

    roleid = models.IntegerField(_(u'current reviewer'), editable=True, db_column='roleid')# 当前审核人
    process = models.CharField(_(u'Audit Process'), max_length=80, null=True, blank=True, db_column='process')# 审批流程
    oldprocess = models.CharField(_(u'Audited Process'), max_length=80, null=True, blank=True, db_column='oldprocess')# 审核过的流程
    processid = models.IntegerField(_(u'Audit Process id'), editable=True, db_column='processid')# 审核流程id
    procSN = models.IntegerField(_(u'reviewed serial number'), default=0, editable=True, db_column='procsn')# 已审序号

    def __unicode__(self):
        return u"%s"%(u"%s" % (self.UserID))

    def __str__(self):
        return u"%s"%(u"%s" % (self.UserID))

    def employee(self):
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None

    @staticmethod
    def colModels(request=None):
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'DeptID', 'width': 80, 'index': 'UserID__DeptID', 'label': u"%s" % (_('department number'))},
            {'name': 'DeptName', 'width': 180, 'index': 'UserID__DeptID__DeptName','label': u"%s" % (_('department name'))},
            {'name': 'PIN', 'index': 'UserID__PIN', 'width': 120, 'label': u"%s" % (_('PIN'))},
            {'name': 'EName', 'index': 'UserID__EName', 'width': 80, 'label': u"%s" % (_('EName'))},
            {'name': 'starttime', 'index': 'starttime', 'width': 120,'label': u"%s" % (UserEgress._meta.get_field('starttime').verbose_name)},
            {'name': 'endtime', 'index': 'endtime', 'width': 120,'label': u"%s" % (UserEgress._meta.get_field('endtime').verbose_name)},
            {'name': 'ApplyDate', 'width': 160, 'sortable': False,'label': u"%s" % (UserEgress._meta.get_field('ApplyDate').verbose_name)},
            {'name': 'location', 'width': 120, 'index': 'location', 'label': u"%s" % (_('location'))},
            {'name': 'process', 'sortable': False, 'index': 'process', 'width': 220,'label': u"%s" % (_(u'Audit flow'))},
            {'name': 'State', 'width': 80, 'hidden': False, 'search': False,'label': u"%s" % (UserEgress._meta.get_field('State').verbose_name)}
        ]

    class Meta:
        db_table = 'app_user_egress'
        permissions = (
            ('TransAudit_useregress', 'Audit User Egress'),
        )


class transactions(models.Model):
    UserID = models.ForeignKey("employee", db_column='userid', verbose_name=_("employee"), on_delete=models.CASCADE)
    TTime = models.DateTimeField(_('time'), db_column='checktime')
    State = models.CharField(_('state'), db_column='checktype', max_length=5,null=True, default='I')  #原长度为1  门禁功能中表示进出
    Verify = models.IntegerField(_('verification'), db_column='verifycode', null=True,default=0, choices=VERIFYS)#55=补记录
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('device'), null=True, blank=True, on_delete=models.CASCADE)
    #sensorid = models.CharField(db_column='sensorid', verbose_name=u'Sensor ID', null=True, blank=True, max_length=5, editable=False)

    NewType = models.CharField(_('NewType'), db_column='newtype',max_length=3,null=True,blank=True)
    AbNormiteID=models.IntegerField(null=True,blank=True,db_column='abnormiteid')#(raValid, raInvalid, raRepeat, raErrorState,raOut, raOT, raFreeOT, raAutoSch)=range(8)
    SchID=models.IntegerField(_('Schclass'), null=True,blank=True,db_column='schid')
    purpose = models.IntegerField(_(u'use'),null=True)#0考勤记录 1 会议记录    2 ...   3 ...
    WorkCode = models.CharField(_('work code'), max_length=10, null=True, blank=True,db_column='workcode')
    temperature = models.DecimalField(_(u'Temperature'), decimal_places=2, max_digits=4, blank=True, null=True)
    mask_flag = models.IntegerField(_(u'Mask Flag'), null=True, blank=True, choices=BOOLEANS)  #0：表示没有佩戴口罩；1：表示有佩戴
    Reserved = models.CharField(_('Reserved'), max_length=100, null=True, blank=True,db_column='reserved')
    punch_location = models.CharField(_('Punch location'), max_length=200, null=True, blank=True)  #打卡位置，移动端（APP)

    def getComVerifys(self):
        try:
            if self.Verify==55:#补记录
                return u"%s"%_("Add")
            elif self.Verify==60:#APP定位签到
                return u"%s"%_("APP Sign")
            elif self.Verify==61:#APP外勤定位签到
                return u"%s"%_("APP Outwork Sign")
            else:
                if self.SN_id:
                    #iclockObj=getDevice(self.SN_id)#iclock.objects.get(SN=self.SN_id)
                    if 1:#iclockObj.Authentication==2:
                        for i in range(len(COMVERIFYS)):
                            if self.Verify==COMVERIFYS[i][0]:
                                return COMVERIFYS[i][1].title()
                    else:
                        return self.get_Verify_display()
                else:
                    return ""
        except Exception as e:
            print (99999999,e)
            return ""#self.get_Verify_display()
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None

    def format_temperature(self):
        """
        格式化温度显示
        """
        try:
            if self.temperature:
                return '%.1f' % self.temperature
            else:
                return ''
        except:
            return ''

    def Time(self):
        return self.TTime
    def StrTime(self,diffSec=0):
        tm=self.Time()+datetime.timedelta(seconds=diffSec)
        return tm.strftime('%Y%m%d%H%M%S')
    @staticmethod
    def delOld(): return ("TTime", 365)
    def Device(self):
        return getDevice(self.SN_id)
    def getImgUrl(self, user,default=None):
        #from mysite.utils import getUploadFileURL
        if GetParamValue('opt_users_rec_pic','0',user.id)!='1':return ''
        device=self.Device()
        emp=self.employee()

        if emp and device:
            pin=formatPIN(emp.PIN)
            fname="%s-%s.jpg"%(self.StrTime(),pin)
            imgUrl=getUploadFileName("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')),'', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')), '', fname)
            #解决考勤照片与记录时间差的问题
            fname="%s-%s.jpg"%(self.StrTime(-1),pin)
            imgUrl=getUploadFileName("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')),'', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')), '', fname)
            fname="%s-%s.jpg"%(self.StrTime(-2),pin)
            imgUrl=getUploadFileName("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')),'', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')), '', fname)
            fname="%s-%s.jpg"%(self.StrTime(1),pin)
            imgUrl=getUploadFileName("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')),'', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s"%(device.SN,self.TTime.strftime('%Y%m')), '', fname)
        return default
#	def getThumbnailUrl(self, default=None):
#		device=self.Device()
#		emp=self.employee()
#		if device and emp:
#			try:
#				pin=int(emp.PIN)
#			except:
#				pin=emp.PIN
#			fname="%s.jpg"%(self.StrTime())
#			imgUrl=getUploadFileName("thumbnail/"+device.SN, pin, fname)
#			#print imgUrl
#			if not os.path.exists(imgUrl):
#				imgUrlOrg=getUploadFileName(device.SN, pin, fname)
#				if not os.path.exists(imgUrlOrg):
#					#print imgUrlOrg, "is not exists"
#					return default
#				if not createThumbnail(imgUrlOrg, imgUrl):
#					#print imgUrl, "create fail."
#					return default
#			return getUploadFileURL("thumbnail/"+device.SN, pin, fname)
#		#print "device, emp", device, emp
#		return default
    def __unicode__(self):
        return self.UserID.__unicode__()+', '+self.TTime.strftime("%y-%m-%d %H:%M:%S")
    def __str__(self):
        return self.UserID.__str__()+', '+self.TTime.strftime("%y-%m-%d %H:%M:%S")
    @staticmethod
    def colModels(request=None):
        ret= [
            {'name':'id','hidden':True},
            {'name':'DeptID','width':80,'index':'UserID__DeptID','label':u"%s"%(_('department number'))},
            {'name':'DeptName','width':180,'index':'UserID__DeptID__DeptName','label':u"%s"%(_('department name'))},
            {'name':'PIN','index':'UserID__PIN','width':120,'label':u"%s"%(_('PIN'))},
            {'name':'EName','index':'UserID__EName','width':80,'label':u"%s"%(_('EName'))},
            {'name':'TTime','width':120,'search':False,'label':u"%s"%(transactions._meta.get_field('TTime').verbose_name)},
            {'name':'State','width':80,'hidden':False,'search':False,'label':u"%s"%(transactions._meta.get_field('State').verbose_name)},
            {'name':'Verify','width':80,'search':False,'label':u"%s"%(transactions._meta.get_field('Verify').verbose_name),'hidden':False},
            {'name':'temperature','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Temperature')),'hidden':True},
            {'name':'mask_flag','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Mask Flag')),'hidden':True},
            {'name':'Device','index':'SN','width':180,'label':u"%s"%(_('Device'))},
            #{'name':'valid','sortable':False,'width':80,'label':u"%s"%(_(u'时间校验'))},
            {'name':'photo','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Register photo'))},
            {'name':'thumbnailUrl','search':False,'sortable':False,'width':100,'label':u"%s"%(_(u'Attendance Photo'))}
            ]
        #if GetParamValue('opt_basic_checkinouttype','')=='0' or  GetParamValue('opt_basic_checkinouttype','0')==0:
        is_state= int(GetParamValue('opt_basic_checkinouttype','0'))
        is_rec_pic=0
        if request:
            is_rec_pic= int(GetParamValue('opt_users_rec_pic','0',request.user.id))
        is_emp_pic= GetParamValue('opt_basic_emp_pic','0')


        for t in ret:
            if is_state==0 and t['name']=='State':
                t['hidden']=True
            if is_rec_pic==0 and t['name']=='thumbnailUrl':
                t['hidden']=True
            if is_emp_pic!='1' and t['name']=='photo':
                t['hidden']=True
            if t['name'] in ('mask_flag','temperature'):
                if int(GetParamValue('opt_basic_antiepidemic','0')) == 1:
                    t['hidden']=False
        return ret
    class Meta:
        verbose_name=_("transaction")
        verbose_name_plural=verbose_name
        db_table = 'checkinout'
        unique_together = (("UserID", "TTime"),)
        index_together = (('TTime'),)  #索引
        default_permissions=('browse','export')
        permissions = (
#				('clearObsoleteData_transaction','Clear Obsolete Data'),
                ('monitor_oplog', 'Transaction Monitor'),
                ('upload_utransaction', 'Upload u disk transaction file'),
                ('meeting_transaction', 'Participation Record'),
                ('monitor_rollbook','Real Time roll call')
                )

    class Admin:
        list_display=('State','Verify','SN')
        list_filter = ('State','Verify','SN',)
        search_fields = ['UserID__PIN','UserID__EName']


EI_DATA_SOURCE =(
    (0,_("Add")),
    (1,_("Attendance")),
    (2,_("Access"))
)

#疫情排查登记表
class EpidemicInvestigation(models.Model):
    UserID = models.ForeignKey(employee, verbose_name=_(u"personnel"), null=True, blank=True, editable=True,db_column="userid", on_delete=models.CASCADE)
    temperature = models.DecimalField(_(u'Temperature'), decimal_places=2, max_digits=4, blank=True, null=True)
    mask_flag = models.IntegerField(_(u'Mask Flag'), null=True, blank=True, choices=BOOLEANS)  #0：表示没有佩戴口罩；1：表示有佩戴
    TTime = models.DateTimeField(_('time'), db_column='checktime')
    data_source = models.IntegerField(_(u'Data Source'), null=True, blank=True, default=0, choices=EI_DATA_SOURCE)
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('device'), null=True, blank=True, on_delete=models.CASCADE)
    @staticmethod
    def colModels():
        ret= [
            {'name':'id','hidden':True},
            {'name':'DeptID','width':80,'index':'UserID__DeptID','label':u"%s"%(_('department number'))},
            {'name':'DeptName','width':180,'index':'UserID__DeptID__DeptName','label':u"%s"%(_('department name'))},
            {'name':'PIN','index':'UserID__PIN','width':120,'label':u"%s"%(_('PIN'))},
            {'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('EName'))},
            {'name':'TTime','width':150,'search':False,'label':u"%s"%(EpidemicInvestigation._meta.get_field('TTime').verbose_name)},
            {'name':'temperature','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Temperature (℃)'))},
            {'name':'mask_flag','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Mask Flag'))},
            {'name':'data_source','width':150,'label':u"%s"%(_('Data Source'))},
            {'name':'Device','index':'SN','width':150,'label':u"%s"%(_('Device'))},
            ]
        return ret

    def format_temperature(self):
        """
        格式化温度显示
        """
        if self.temperature:
            return '%.1f' % self.temperature

    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def Device(self):
        return getDevice(self.SN_id)
    class Admin:
        search_fields = ['UserID__PIN','UserID__EName']

    class Meta:
        verbose_name = _(u"Epidemic Investigation")
        db_table = 'epidemic_investigation'
        verbose_name_plural = verbose_name
        unique_together = (("UserID","TTime"),)
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
            ('monitor_oplog_epidemic', 'Transaction Monitor'),
            ('epidemic', 'Epidemic Investigation'),
            ('emp_temperature', 'emloyee temperature'),
            ('department_temperature_report', 'department thermometer report'),
            ('detail_temperature', 'detail temperature'),
        )


#签卡位置
class SignLocation(models.Model):
    """移动端签到位置信息表，废弃，直接在原始记录表增加位置信息字段 20211102"""
    transaction = models.ForeignKey(transactions, db_column='transaction_id', default=1, null=False, blank=False, on_delete=models.CASCADE)
    longitude = models.CharField(_('longitude'), max_length=20, null=True, blank=True)
    latitude = models.CharField(_('latitude'), max_length=20, null=True, blank=True)
    location = models.CharField(_('location'), max_length=100, null=True, blank=True)
    coordinate_system = models.CharField(_('coordinate_system'), max_length=20, null=True, blank=True)
    deviceid = models.CharField(_(u'Device ID'), blank=True, null=True, max_length=50)  #设备id，如IMEI

    class Meta:
        db_table = 'app_sign_location'
        default_permissions = ()


def OPName(op):
    OPNAMES = {0: _("start up"),
               1: _("shutdown"),
               2: _("validation failure"),
               3: _("alarm"),
               4: _("enter the menu"),
               5: _("change settings"),
               6: _("registration fingerprint"),
               7: _("registration password"),
               8: _("card registration"),
               9: _("delete User"),
               10: _("delete fingerprints"),
               11: _("delete the password"),
               12: _("delete RF card"),
               13: _("remove data"),
               14: _("MF create cards"),
               15: _("MF registration cards"),
               16: _("MF registration cards"),
               17: _("MF registration card deleted"),
               18: _("MF clearance card content"),
               19: _("moved to the registration card data"),
               20: _("the data in the card copied to the machine"),
               21: _("set time"),
               22: _("restore factory settings"),
               23: _(u"Delete Record"),
               24: _("remove administrator rights"),
               25: _("group set up to amend Access"),
               26: _("modify user access control settings"),
               27: _("access time to amend paragraph"),
               28: _("amend unlock Portfolio"),
               29: _("unlock"),
               30: _("registration of new users"),
               31: _("fingerprint attribute changes"),
               32: _("stress alarm"),
               34: _(u"anti-submarine"),
               35: _(u"Delete attendance photos"),
               36: _(u"Modify user information"),
               37: _(u"Holiday operation"),
               38: _(u"Restore data"),
               39: _(u"Backup data"),
               40: _(u"U disk upload"),
               41: _(u"U disk download"),
               42: _(u"U disk attendance record encryption"),
               43: _(u"Delete record after successful download of U disk"),
               68: _(u"Registered User Photo"),
               69: _(u"Modify User Photo"),
               70: _(u"Modify User Name"),
               71: _(u"Modify User Permissions"),
               76: _(u"Modify Network Settings IP"),
               77: _(u"Modify Network Settings Mask"),
               78: _(u"Modify Network Settings Gateway"),
               79: _(u"Modify Network Settings DNS"),
               80: _(u"Modify Connection Settings Password"),
               81: _(u"Modify Connection Settings Device ID"),
               82: _(u"Modify cloud server address"),
               83: _(u"Modify Cloud Server Port"),
               87: _(u"Modify the attendance parameter flag"),
               88: _(u"Modify face parameter flag"),
               89: _(u"Modify fingerprint parameter flag"),
               90: _(u"Modify the palm print parameter mark"),
               91: _(u"Modify the palm print parameter mark"),
               92: _(u"u disk upgrade logo")

               }
    # {0: u"开机",
    # 1: u"关机",
    # 2: u"验证失败",
    # 3: u"报警",
    # 4: u"进入菜单",
    # 5: u"更改设置",
    # 6: u"登记指纹",
    # 7: u"登记密码",
    # 8: u"登记HID卡",
    # 9: u"删除用户",
    # 10: u"删除指纹",
    # 11: u"删除密码",
    # 12: u"删除射频卡",
    # 13: u"清除数据",
    # 14: u"创建MF卡",
    # 15: u"登记MF卡",
    # 16: u"注册MF卡",
    # 17: u"删除MF卡注册",
    # 18: u"清除MF卡内容",
    # 19: u"把登记数据移到卡中",
    # 20: u"把卡中的数据复制到机器中",
    # 21: u"设置时间",
    # 22: u"恢复出厂设置",
    # 23: u"删除进出记录",
    # 24: u"清除管理员权限}",
    # 25: u"修改门禁组设置",
    # 26: u"修改用户门禁设置",
    # 27: u"修改门禁时间段",
    # 28: u"修改开锁组合设置",
    # 29: u"开锁",
    # 30: u"登记新用户",
    # 31: u"更改指纹属性",
    # 32: u"胁迫报警",
    try:
        return u'%s' % (OPNAMES[op])
    except:
        return op and "%s" % op or ""


#设备操作日志
class oplog(models.Model):
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('device'), null=True, blank=True, on_delete=models.CASCADE)
    #admin = models.IntegeField(_(u'Device Manager'), null=False, blank=False, default=0)
    #OP = models.SmallIntegerField(_('Operation'), null=False, blank=False, default=0)
    admin = models.CharField(_(u'Device Manager'), max_length=24,null=True, blank=False, default=0)
    OP = models.CharField(_('Operation'), max_length=10,null=False, blank=False, default=0,db_column='op')
    OPTime=models.DateTimeField(_('time'),db_column='optime')
    Object=models.CharField(_('Object'),max_length=50, null=True, blank=True,db_column='object')
    Param1=models.IntegerField(_('Parameter1'), null=True, blank=True,db_column='param1')
    Param2=models.IntegerField(_('Parameter2'), null=True, blank=True,db_column='param2')
    Param3=models.IntegerField(_('Parameter3'), null=True, blank=True,db_column='param3')
    def Device(self):
        return getDevice(self.SN_id)
    @staticmethod
    def delOld(): return ("OPTime", 200)
    def OpName(self):
        try:
            return OPName(int(self.OP))
        except:
            return u'%s'%self.OP
    def ObjName(self):
        if self.OP==3:
            return AlarmName(self.Object)
        return self.Object or ""
    def __unicode__(self):
        return u"%s,%s,%s"%(self.Device(), self.OP, self.OPTime.strftime("%y-%m-%d %H:%M:%S"))

    def __str__(self):
        return u"%s,%s,%s" % (self.Device(), self.OP, self.OPTime.strftime("%y-%m-%d %H:%M:%S"))

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'SN','width':200,'label':u"%s"%(oplog._meta.get_field('SN').verbose_name)},
            #{'name':'admin','width':150,'label':u"%s"%(oplog._meta.get_field('admin').verbose_name)},
            {'name':'OpName','index':'OP','width':220,'label':u"%s"%(oplog._meta.get_field('OP').verbose_name)},
            {'name':'OPTime','width':120,'label':u"%s"%(oplog._meta.get_field('OPTime').verbose_name)},
            {'name':'ObjName','index':'Object','width':200,'label':u"%s"%(oplog._meta.get_field('Object').verbose_name)}

            ]
    class Meta:
        verbose_name=_("Device Operation Log")
        verbose_name_plural=verbose_name
        unique_together = ("SN","OP", "OPTime",)
        default_permissions=('browse','delete','export')

        permissions = (
#			('monitor_oplog', 'Transaction Monitor'),
            )
    class Admin:
        list_display=('SN','admin','OP','OPTime', 'Object',)
        list_filter = ('SN','admin','OP','OPTime')
        search_fields=['SN__SN','SN__Alias']
class iaccessoplog(models.Model):
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('device'), null=True, blank=True, on_delete=models.CASCADE)
    Even = models.CharField(_('Operation'), max_length=20,null=False, blank=False, default=0)#OP
    OPTime=models.DateTimeField(_('time'))
    Object=models.CharField(_('Object'),  max_length=20,null=True, blank=True)
    Message=models.CharField(_(u'event'),  max_length=20,null=True, blank=True)#object
    Param1=models.IntegerField(_('Parameter1'), null=True, blank=True)
    Param2=models.IntegerField(_('Parameter2'), null=True, blank=True)
    Param3=models.CharField(_('Parameter3'),  max_length=20,null=True, blank=True)
    def Device(self):
        return getDevice(self.SN_id)
    @staticmethod
    def delOld(): return ("OPTime", 200)
    def OpName(self):
        #print 883332,OpName(int(self.Even))
        return OpName(int(self.Even))
    def ObjName(self):
#		if self.OP==3:
        #print 33333,AlarmName(int(self.Message))
        return AlarmName(int(self.Message))
#		return self.Object or ""
    def __unicode__(self):
        return "%s,%s,%s"%(self.Device(), self.Even, self.OPTime.strftime("%y-%m-%d %H:%M:%S"))
    def __str__(self):
        return "%s,%s,%s"%(self.Device(), self.Even, self.OPTime.strftime("%y-%m-%d %H:%M:%S"))
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'SN','width':200,'label':u"%s"%(iaccessoplog._meta.get_field('SN').verbose_name)},
            {'name':'ObjName','index':'Object','width':80,'label':u"%s"%(iaccessoplog._meta.get_field('Object').verbose_name)},
            {'name':'OPTime','width':120,'label':u"%s"%(iaccessoplog._meta.get_field('OPTime').verbose_name)},
            {'name':'OpName','index':'Even','width':150,'label':u"%s"%(iaccessoplog._meta.get_field('Even').verbose_name)},
            {'name':'Message','width':150,'label':u"%s"%(iaccessoplog._meta.get_field('Message').verbose_name)}
            ]
    class Meta:
        verbose_name=_("iaccessoplog")
        verbose_name_plural=verbose_name
        unique_together = (("SN", "OPTime"),)
        default_permissions = ()
        permissions = (
            ('monitor_iaccessoplog', 'Transaction Monitor'),
            )
    class Admin:
        list_display=('SN','Message','Even','OPTime', 'Object',)
        list_filter = ('SN','Message','Even','OPTime')
        search_fields=['SN__SN','SN__Alias']
#设备上传数据日志
class devlog(models.Model):
    SN = models.ForeignKey(iclock, verbose_name=_('device'),db_column='sn_id', on_delete=models.CASCADE)
    OP = models.CharField(_('data'),max_length=80, default="TRANSACT",db_column='op')
    Object = models.CharField(_('object'),max_length=60, null=True, blank=True,db_column='object')
    Cnt = models.IntegerField(_('record count'),default=1, blank=True,db_column='cnt')
    ECnt = models.IntegerField(_('error count'),default=0, blank=True,db_column='ecnt')
    OpTime = models.DateTimeField(_('Upload Time'),db_column='optime')
    remark = models.CharField(_('Remarks'), max_length=100, null=True, blank=True)
    def Device(self):
        return getDevice(self.SN_id)
    @staticmethod
    def delOld(): return ("OpTime", 30)
    def save(self, *args, **kwargs):
        if not self.id:
            self.OpTime=datetime.datetime.now()
        models.Model.save(self, *args, **kwargs)
    def __unicode__(self): return u"%s, %s, %s"%(self.SN, self.OpTime.strftime('%y-%m-%d %H:%M'), self.OP)
    def __str__(self): return u"%s, %s, %s"%(self.SN, self.OpTime.strftime('%y-%m-%d %H:%M'), self.OP)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'Device','index':'SN__SN','width':200,'label':u"%s"%(_('device'))},
            {'name':'OpTime','width':150,'label':u"%s"%(devlog._meta.get_field('OpTime').verbose_name)},
            {'name':'OP','width':220,'label':u"%s"%(devlog._meta.get_field('OP').verbose_name)},
            {'name':'Object','width':220,'label':u"%s"%(devlog._meta.get_field('Object').verbose_name)},
            {'name':'Cnt','width':90,'label':u"%s"%(devlog._meta.get_field('Cnt').verbose_name)},
            {'name':'ECnt','width':40,'label':u"%s"%(devlog._meta.get_field('ECnt').verbose_name)},
            {'name':'remark','width':180,'label':u"%s"%(devlog._meta.get_field('remark').verbose_name)},
            {'name':'DeviceSN','hidden':True}

            ]

    class Admin:
        list_display=('SN','OpTime','OP','Cnt','Object',)
        list_filter=("SN",'OpTime')
        search_fields = ["=SN__SN","=SN__Alias","OP","Object"]
    class Meta:
        verbose_name=_("data from device")
        verbose_name_plural=verbose_name
        db_table = 'devlog'
        default_permissions=('browse','delete', 'export')

class errorlog(models.Model):
    SN = models.ForeignKey("iclock", verbose_name=_('device'),db_column='sn_id', on_delete=models.CASCADE)
    cmdid = models.IntegerField(_('Cmd ID'))
    errcode = models.TextField(_('ErrCode'))
    errmsg = models.TextField(_('ErrMsg'))
    dataorigin = models.TextField(_('Data Origin'))
    additional = models.TextField(_('Additional'),null=True, blank=True)
    uptime = models.DateTimeField(_('Upload Time'))

    def get_error_msg(self):
        ERRCODE = {
            '00000000': _('success'),
            'D01E0001': _('Facial Detection Failure'),
            'D01E0002': _('Face occlusion'),
            'D01E0003': _('Lack of clarity'),
            'D01E0004': _('Face angle is too big'),
            'D01E0005': _('Failure of biopsy'),
            'D01E0006': _('Failure to extract template')
        }
        if not self.errmsg and self.errcode in ERRCODE:
            self.errmsg = ERRCODE[self.errcode]
        return u'%s'%self.errmsg

    @staticmethod
    def colModels():
        return [
            {'name':'cmdid','width':70,'label':'ID'},
            {'name':'SN','width':120,'label':u"%s"%(errorlog._meta.get_field('SN').verbose_name)},
            {'name':'errcode','width':120,'label':u"%s"%(errorlog._meta.get_field('errcode').verbose_name)},
            {'name':'errmsg','width':120,'label':u"%s"%(errorlog._meta.get_field('errmsg').verbose_name)},
            {'name':'dataorigin','width':120,'label':u"%s"%(errorlog._meta.get_field('dataorigin').verbose_name)},
            {'name':'additional','width':400,'label':u"%s"%(errorlog._meta.get_field('additional').verbose_name)},
            {'name':'uptime','sortable':True,'width':180,'label':u"%s"%(errorlog._meta.get_field('uptime').verbose_name)}
            ]
    class Admin:
        list_display=('cmdid','SN','errcode','errmsg','dataorigin','additional','uptime')
        search_fields = ['cmdid', 'errcode']

    class Meta:
        db_table = 'errorlog'
        verbose_name=_("Commands Error Log")
        verbose_name_plural=verbose_name
        default_permissions=('browse','delete', 'export')

class devcmds(models.Model):
    SN = models.ForeignKey("iclock", verbose_name=_('device'),db_column='sn_id', on_delete=models.CASCADE)
    #UserName = models.CharField('提交用户',max_length=20,null=True, blank=True)
    CmdContent = models.TextField(_('command content'),db_column='cmdcontent')
    CmdCommitTime = models.DateTimeField(_('submit time'),db_column='cmdcommittime')
    CmdTransTime = models.DateTimeField(_('transfer time'),null=True, blank=True,db_column='cmdtranstime')
    CmdOverTime = models.DateTimeField(_('return time'),null=True, blank=True,db_column='cmdovertime')
    CmdReturn = models.IntegerField(_('return value'), null=True, blank=True,db_column='cmdreturn')
    Reserved = models.CharField(_('Reserved'), max_length=100, null=True, blank=True,db_column='reserved')

    def Device(self):
        return getDevice(self.SN_id)
    def __unicode__(self):
        return u"%s, %s" % (self.SN, self.CmdCommitTime.strftime('%y-%m-%d %H:%M'))
    def __str__(self):
        return u"%s, %s" % (self.SN, self.CmdCommitTime.strftime('%y-%m-%d %H:%M'))
    def save(self, *args, **kwargs):
        super(devcmds, self).save(*args, **kwargs)



    def fileURL(self):
        if self.CmdContent.find("GetFile ")==0:
            fname=self.CmdContent[8:]
        elif self.CmdContent.find("Shell ")==0:
            fname="shellout.txt"
        else:
            return ""
        return getUploadFileURL(self.SN.SN, self.id, fname)
    @staticmethod
    def delOld(): return ("CmdOverTime", 10)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':False,'width':70,'label':'ID'},
            {'name':'Device','index':'SN__SN','width':180,'label':u"%s"%(_('device'))},
            {'name':'CmdContent','sortable':False,'width':500,'label':u"%s"%(devcmds._meta.get_field('CmdContent').verbose_name)},
            {'name':'CmdCommitTime','width':120,'label':u"%s"%(devcmds._meta.get_field('CmdCommitTime').verbose_name)},
            {'name':'CmdTransTime','width':120,'label':u"%s"%(devcmds._meta.get_field('CmdTransTime').verbose_name)},
            {'name':'CmdOverTime','width':120,'label':u"%s"%(devcmds._meta.get_field('CmdOverTime').verbose_name)},
            {'name':'CmdReturn','width':70,'label':u"%s"%(devcmds._meta.get_field('CmdReturn').verbose_name)},
            {'name':'Reserved','sortable':False,'width':80,'label':u"%s"%(_(u'schedule'))},
            {'name':'fileURL','hidden':True}

            ]

    class Admin:
        list_display=('SN','CmdCommitTime','CmdTransTime','CmdOverTime','CmdContent',)
        search_fields = ["SN__SN", "CmdContent"]
        list_filter =['SN', 'CmdCommitTime', 'CmdOverTime']

    class Meta:
        db_table = 'devcmds'
        verbose_name=_("command to device")
        verbose_name_plural=verbose_name
        index_together = (('SN', 'CmdCommitTime', 'CmdTransTime','CmdOverTime'),)  #联合索引（不唯一）
        default_permissions=('browse','delete', 'export')


class AttDataProofCmd(models.Model):
    SN = models.ForeignKey("iclock", db_column='sn',verbose_name=_('device'), on_delete=models.CASCADE)
    OperateTime = models.DateTimeField(_('Operate time'),db_column='operatetime')
    StartTime = models.DateTimeField(_('Begin time'),null=True, db_column='starttime',blank=True)
    EndTime = models.DateTimeField(_('End time'),null=True, db_column='endtime',blank=True)
    DevCount = models.IntegerField(_('DevCount'), null=True, db_column='devcount',blank=True)
    SerCount = models.IntegerField(_('SerCount'), null=True,db_column='sercount', blank=True)
    flag=models.IntegerField(_('state'),null=True,default=0,blank=True)#状态0正常，-1异常
    Reserved=models.IntegerField(null=True,default=0,db_column='reserved',blank=True,editable=False)
    Reserved1=models.FloatField(null=True,default=0,blank=True,db_column='reserved1',editable=False)
    Reserved2=models.CharField(max_length=30,null=False,db_column='reserved2',editable=False)

    def Device(self):
        return getDevice(self.SN_id)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'Device','index':'SN__SN','width':200,'label':u"%s"%(_('device'))},
            {'name':'OperateTime','width':120,'label':u"%s"%(AttDataProofCmd._meta.get_field('OperateTime').verbose_name)},
            {'name':'StartTime','width':120,'label':u"%s"%(AttDataProofCmd._meta.get_field('StartTime').verbose_name)},
            {'name':'EndTime','width':120,'label':u"%s"%(AttDataProofCmd._meta.get_field('EndTime').verbose_name)},
            {'name':'flag','width':60,'label':u"%s"%(AttDataProofCmd._meta.get_field('flag').verbose_name)},
            {'name':'DevCount','width':100,'label':u"%s"%(AttDataProofCmd._meta.get_field('DevCount').verbose_name)},
            {'name':'SerCount','width':100,'label':u"%s"%(AttDataProofCmd._meta.get_field('SerCount').verbose_name)}
            ]

    class Admin:
        list_display=('SN__SN','OperateTime','StartTime','EndTime',)
        search_fields = ["SN__SN"]
        list_filter =['SN', 'OperateTime', 'StartTime','EndTime']
    class Meta:
        db_table = 'attdataproofcmd'
        verbose_name=_("attdata proof cmd")
        verbose_name_plural=verbose_name
        default_permissions = ('browse',)

def isUpdatingFW(device):
    return devcmds.objects.filter(SN=device, CmdReturn__isnull=True, CmdContent__startswith='PutFile ', CmdContent__endswith='main.gz.tmp',).count()

def clearData():
    for obj in employee.objects.all(): obj.delete()
    for obj in devcmds.objects.all():
        obj.CmdOverTime=None
        obj.CmdTransTime=None
        obj.save()
    for obj in iclock.objects.all():
        obj.LogStamp=0
        obj.OpLogStamp=0
        cache.delete("iclock_"+obj.SN)
        obj.DelTag=1
        obj.save()


#Iclock 信息订阅服务

M_WEATHER=1
M_NEWS=2
M_DEPT_NOTES=3
M_SYS_NOTES=4
M_DEPT_SMS=6
M_PRIV_SMS=5

PUBMSGSERVICES=(
(M_NEWS, _("News Channel")),
(M_DEPT_NOTES, _("Company Notice")),
(M_SYS_NOTES, _("Notice System")),
(M_DEPT_SMS, _("Companies short message")),
)

MSGSERVICES=(
(M_WEATHER, _("Weather Forecast")),
(M_PRIV_SMS, _("Employee SMS")),
(M_NEWS, _("News Channel")),
(M_DEPT_NOTES, _("Company Notice")),
(M_SYS_NOTES, _("System Notice")),
(M_DEPT_SMS, _("Companies SMS")),
)

class messages(models.Model):
    MsgType = models.IntegerField(_("type"), null=False, blank=False, default=5, choices=PUBMSGSERVICES,db_column='msgtype')
    StartTime = models.DateTimeField(_("take effect"), null=False, blank=False,db_column='starttime')
    EndTime	= models.DateTimeField(_("out-of-service"), null=True, blank=True,db_column='endtime')
    MsgContent = models.TextField(_("Content"), max_length=2048, null=True, blank=True,db_column='msgcontent')
    MsgImage = models.CharField(_("picture"), max_length=64, null=True, blank=True,db_column='msgimage')
    DeptID = models.ForeignKey(department, verbose_name=_('company/department'), null=True, blank=True,db_column='deptid_id', on_delete=models.CASCADE)
    MsgParam = models.CharField(_("parameter"), max_length=32, null=True, blank=True,db_column='msgparam')
    def __unicode__(self):
        return u"%s"%(u"%s[%s]: %s"%(self.get_MsgType_display(), self.StartTime, self.MsgContent and self.MsgContent[:40] and ''))
    def __str__(self):
        return u"%s"%(u"%s[%s]: %s"%(self.get_MsgType_display(), self.StartTime, self.MsgContent and self.MsgContent[:40] and ''))
    class Admin:
        list_filter =['StartTime','MsgType', 'MsgParam', 'DeptID']
    class Meta:
        verbose_name=_("public information")
        verbose_name_plural=verbose_name
        default_permissions = ('browse','delete')


class IclockMsg(models.Model):
    SN = models.ForeignKey(iclock, verbose_name=_('device'), null=False, blank=False,db_column='sn_id', on_delete=models.CASCADE)
    MsgType = models.IntegerField(_("type"), null=False, blank=False, default=5, choices=MSGSERVICES,db_column='msgtype')
    StartTime = models.DateTimeField(_("take effect"), null=False, blank=False,db_column='starttime')
    EndTime	= models.DateTimeField(_("out-of-service"), null=True, blank=True,db_column='endtime')
    MsgParam = models.CharField(_("parameter"), max_length=32, null=True, blank=True,db_column='msgparam')
    MsgContent = models.CharField(_("content"), max_length=200, null=True, blank=True,db_column='msgcontent')
    LastTime = models.DateTimeField(_("recently service"), null=True, blank=True, editable=False,db_column='lasttime')
    def Device(self):
        return getDevice(self.SN_id)
    def __unicode__(self):
        return u"%s"%(u"%s"%(self.SN))
    def __str__(self):
        return u"%s"%(u"%s"%(self.SN))
    class Admin:
        list_filter =['SN','MsgType','StartTime','EndTime']
    class Meta:
        verbose_name=_("information subscription")
        verbose_name_plural=verbose_name
        default_permissions = ('browse','add', 'change', 'delete')

class adminLog(models.Model):
    time = models.DateTimeField(_('time'), null=False, blank=False)
    User = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('administrator'), null=True, blank=True, db_column='user_id', on_delete=models.CASCADE)
    model = models.CharField(_('data'), max_length=40, null=True, blank=True)
    action = models.CharField(_('operation'), max_length=40, default=_("Modify"), null=False, blank=False)
    object = models.CharField(_('object'), max_length=100, null=True, blank=True)
    count = models.IntegerField(_('amount'), default=1, null=False, blank=False)
    loginIP = models.CharField(_('Login IP'), max_length=100, null=True, blank=True,db_column='loginip')
    @staticmethod
    def delOld(): return ("time", 200)
    #def save(self):
    #	if not self.id: self.time=datetime.datetime.now()
    #	logFile(self.time.strftime("%Y-%m-%d %H:%M:%S")+'   '+self.User.username+'  '+self.action)
    #	models.Model.save(self)
    def __unicode__(self):
        if self.User:
            return u"[%s]%s, %s"%(self.time, self.User.username, self.action)
        else:
            return u"[%s]%s, %s"%(self.time, '', self.action)
    def __str__(self):
        if self.User:
            return u"[%s]%s, %s"%(self.time, self.User.username, self.action)
        else:
            return u"[%s]%s, %s"%(self.time, '', self.action)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'time','width':120,'label':u"%s"%(adminLog._meta.get_field('time').verbose_name)},
            {'name':'User','width':80,'label':u"%s"%(adminLog._meta.get_field('User').verbose_name)},
            {'name':'action','width':220,'label':u"%s"%(adminLog._meta.get_field('action').verbose_name)},
            {'name':'model','width':220,'label':u"%s"%(adminLog._meta.get_field('model').verbose_name)},
            {'name':'object','width':220,'label':u"%s"%(adminLog._meta.get_field('object').verbose_name)},
            {'name':'loginIP','width':220,'label':u"%s"%(adminLog._meta.get_field('loginIP').verbose_name)},
            {'name':'count','width':80,'label':u"%s"%(adminLog._meta.get_field('count').verbose_name)}

            ]

    class Admin:
        list_filter =['time','User','model']
        search_fields = ['User__username','action','model','object']

    class Meta:
        verbose_name=_("Admin Action Log")
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'delete', 'export')
        #unique_together = (("time", "User"),)

    def save(self, *args, **kwargs):
        from mysite.base.threadlocals.threadlocals import get_current_request
        from mysite.utils import escape
        if self.model=='level_door' or self.model=='level_emp':
            self.model='level'
        request = get_current_request()
        if request:
            if 'HTTP_X_FORWARDED_FOR' in request.META:
              self.loginIP = request.META['HTTP_X_FORWARDED_FOR'].split(',')[0]
            else:
              self.loginIP = request.META['REMOTE_ADDR']
        self.object = str(self.object)
        self.object = escape(self.object)
        if len(self.object) >= 100:
            self.object = self.object[:100]
        super(adminLog, self).save(*args, **kwargs)


SUCCESS_STATUS = 0
FAIL_STATUS = 1

INTERFACE_STATUS=(
    (SUCCESS_STATUS,_('success')),
    (FAIL_STATUS,_('failure'))
)

class InterfaceLog(models.Model):
    op_ip = models.GenericIPAddressField(_('Request IP'), null=True)
    op_url = models.CharField(_('Request URL'), max_length=255)
    op_method = models.CharField(_('Request Method'), max_length=11)
    op_model = models.CharField(_('Operating Model'), max_length=40)
    op_type = models.CharField(_('operation'), max_length=40, default=_("Modify"))
    request_params = models.TextField(_('Request Parameters'), null=True)
    response_content = models.TextField(_('Response Content'), null=True)
    result_status = models.SmallIntegerField(_('Operating State'), default=0, choices=INTERFACE_STATUS)
    result_msg = models.TextField(_('Operating Result'), null=True)
    op_time = models.DateTimeField(_('Operation time'))

    def __str__(self):
        return '%s_%s_%s_%s'%(self.op_url,self.op_method,self.op_model,self.op_type)

    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'op_ip','sortable':False, 'width': 120, 'label': u"%s" % (InterfaceLog._meta.get_field('op_ip').verbose_name)},
            {'name': 'op_url', 'sortable':False,'width': 220, 'label': u"%s" % (InterfaceLog._meta.get_field('op_url').verbose_name)},
            {'name': 'op_method', 'sortable':False,'width': 80, 'label': u"%s" % (InterfaceLog._meta.get_field('op_method').verbose_name)},
            {'name': 'op_model','sortable':False, 'width': 80, 'label': u"%s" % (InterfaceLog._meta.get_field('op_model').verbose_name)},
            {'name': 'op_type','sortable':False, 'width': 80, 'label': u"%s" % (InterfaceLog._meta.get_field('op_type').verbose_name)},
            {'name': 'request_params','sortable':False, 'width': 220, 'label': u"%s" % (InterfaceLog._meta.get_field('request_params').verbose_name)},
            {'name': 'response_content', 'sortable':False,'width': 220, 'label': u"%s" % (InterfaceLog._meta.get_field('response_content').verbose_name)},
            {'name': 'result_status', 'width': 80, 'label': u"%s" % (InterfaceLog._meta.get_field('result_status').verbose_name)},
            {'name': 'result_msg', 'sortable':False,'width': 220,'label': u"%s" % (InterfaceLog._meta.get_field('result_msg').verbose_name)},
            {'name': 'op_time', 'width': 120,'label': u"%s" % (InterfaceLog._meta.get_field('op_time').verbose_name)}
        ]
    class Admin:
        list_filter =[]
        search_fields = ['op_model','request_params']

    class Meta:
        verbose_name = _(u'API Interface Log')
        verbose_name_plural = verbose_name
        db_table = 'interface_log'
        default_permissions = ('browse', 'delete')

#员工登陆日志
class employeeLog(models.Model):
    LTime = models.DateTimeField(_('time'),  null=False, blank=False,db_column='ltime')
    PIN = models.CharField(_('PIN'), max_length=24, null=False, blank=False,db_column='pin')
    Name = models.CharField(_('EName'), max_length=24, null=True, blank=False,db_column='name')
    DeptID = models.IntegerField(_('department'),  null=True, blank=False,db_column='deptid')
    action = models.CharField(_('operation'), max_length=20, default="Login", null=True, blank=True)
    loginIP = models.CharField(_('Login IP'), max_length=100, null=True, blank=True,db_column='loginip')
#	count = models.IntegerField(_('amount'), default=1, null=False, blank=False)
    def __unicode__(self):
        return u"%s"%(u"[%s]%s, %s"%(self.LTime, self.PIN, self.action))
    def __str__(self):
        return u"%s"%(u"[%s]%s, %s"%(self.LTime, self.PIN, self.action))


    def Dept(self):
        return department.objByID(self.DeptID)

    def save(self, *args, **kwargs):
        super(employeeLog, self).save(*args, **kwargs)

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'LTime','width':120,'label':u"%s"%(employeeLog._meta.get_field('LTime').verbose_name)},
            {'name':'PIN','width':120,'label':u"%s"%(_('PIN'))},
            {'name':'Name','width':120,'label':u"%s"%(_('EName'))},
            {'name':'DeptName','width':180,'sortable':False,'label':u"%s"%(_('department name'))},
            {'name':'action','width':220,'label':u"%s"%(employeeLog._meta.get_field('action').verbose_name)},
            {'name':'loginIP','width':120,'label':u"%s"%(_(u'Login IP'))},
            ]
    class Admin:
        list_filter =[]
        search_fields = ['PIN','Name']
    class Meta:
        verbose_name=_(u"Employee Login Log")
        verbose_name_plural=verbose_name
        unique_together = (("LTime", "PIN"),)
        default_permissions = ('browse','delete', 'export')

def delOldRecords(model, field, days):
    table=model._meta.db_table
    field=model._meta.get_field(field).column
    #cursor = connection.cursor()
    if 'oracle' in settings.DATABASE_ENGINE:
        sql="DELETE FROM %s WHERE %s < TIMESTAMP '%s 00:00:00'"%(table, field,days )#str((datetime.datetime.today())-datetime.timedelta(days))
    else:
        sql="DELETE FROM %s WHERE %s < '%s'"%(table, field, days)#str((datetime.datetime.today()).date()-datetime.timedelta(days))
    customSql(sql)





##员工考勤修改日志##
##补签记录##
class checkexact(models.Model):
    UserID = models.ForeignKey(employee, db_column='userid', null=False, default=1, verbose_name=_("employee"), on_delete=models.CASCADE)
    CHECKTIME = models.DateTimeField(_('Check time'),null=True,default=0, blank=True,db_column='checktime')
    CHECKTYPE=models.CharField(_('Check type'),max_length=2, null=True, default='I',blank=True, db_column='checktype',choices=ATTSTATES)
    ISADD=models.SmallIntegerField(null=True, blank=True,editable=False,db_column='isadd')
    YUYIN=models.CharField(_('reson'),max_length=100, null=True, blank=True,db_column='yuyin')
    ISMODIFY=models.SmallIntegerField(null=True, default=0,blank=True,editable=False,db_column='ismodify')
    ISDELETE=models.SmallIntegerField(null=True,default=0, blank=True,editable=False,db_column='isdelete')
    INCOUNT=models.SmallIntegerField(null=True,default=0, blank=True,editable=False,db_column='incount')
    ISCOUNT=models.SmallIntegerField(null=True, default=0,editable=False,db_column='iscount')
    MODIFYBY = models.CharField(_('Modify by'),max_length=50,null=True, blank=True,db_column='modifyby')
    SaveStamp = models.CharField(_('Modify date'),max_length=20,null=True, blank=True,db_column='savestamp')
    #DATE=models.DateTimeField(_('Modify date'),null=True, blank=True)
    State=models.IntegerField(_('state'), null=True, default=0, blank=True, choices=AUDIT_STATES, editable=False,db_column='state')#0-申请；2-通过；3-拒绝；6-重新申请；>10表示审批中，此时减去10等于管理员id(职务审批）或者人员id(人员审批)
    ApplyDate=models.DateTimeField(_('apply date'), null=True,  blank=True,db_column='applydate')
    SN = models.ForeignKey(iclock,  verbose_name=_('device'), null=True, blank=True,db_column='sn', on_delete=models.CASCADE)
    roleid = models.IntegerField(_(u'current reviewer'),editable=True,db_column='roleid')
    process=models.CharField(_(u'Audit Process'),max_length=80,null=True,blank=True,db_column='process')
    oldprocess=models.CharField(_(u'Audited Process'),max_length=80,null=True,blank=True,db_column='oldprocess')
    processid=models.IntegerField(_(u'Audit Process id'),editable=True,db_column='processid')
    procSN=models.IntegerField(_(u'reviewed serial number'),default=0,editable=True,db_column='procsn')
    processtype = models.IntegerField(_('process type'), default=0, editable=True, choices=PROCESSTYPE,db_column='processtype')
    employeelist = models.CharField(_('current reviewer'), max_length=80, null=True, blank=True)

    def __unicode__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def __str__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def Device(self):
        return getDevice(self.SN_id)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True,'frozen':True},
            {'name':'PIN','index':'UserID__PIN','width':120,'label':u"%s"%(_('PIN')),'frozen':True},
            {'name':'EName','index':'','sortable':False,'width':80,'label':u"%s"%(_('EName')),'frozen':True},
            {'name':'DeptName','index':'UserID__DeptID','sortable':False,'width':120,'label':u"%s"%(_('department name'))},
            {'name':'CHECKTIME','width':120,'label':u"%s"%(checkexact._meta.get_field('CHECKTIME').verbose_name)},
            {'name':'CHECKTYPE','width':80,'sortable':False,'label':u"%s"%(checkexact._meta.get_field('CHECKTYPE').verbose_name)},
            {'name':'process','sortable':False,'index':'process','width':220,'label':u"%s"%(_(u'Audit flow'))},
            {'name':'YUYIN','width':80,'sortable':False,'label':u"%s"%(checkexact._meta.get_field('YUYIN').verbose_name)},
            {'name':'MODIFYBY','width':120,'sortable':False,'label':u"%s"%(checkexact._meta.get_field('MODIFYBY').verbose_name)},
            {'name':'State','index':'State','width':80,'label':u"%s"%(checkexact._meta.get_field('State').verbose_name)},
            {'name':'ApplyDate','width':160,'sortable':False,'label':u"%s"%(checkexact._meta.get_field('ApplyDate').verbose_name)},
            {'name':'Device','index':'SN','width':180,'label':u"%s"%(_('Device name'))},
            #{'name':'SaveStamp','width':160,'sortable':False,'label':u"%s"%(checkexact._meta.get_field('SaveStamp').verbose_name)}
            ]
    class Admin:
        list_filter =['UserID','CHECKTIME']
        search_fields = ['UserID__PIN','UserID__EName']
        lock_fields=['UserID']
    class Meta:
        db_table = 'checkexact'
        verbose_name=_(u"Replenish record data")
        verbose_name_plural=verbose_name
        unique_together = (("UserID", "CHECKTIME","State"),)
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
                        ('TransAudit_checkexact','Audit checkexact'),
            )

class EXCNOTES(models.Model):
    UserID = models.IntegerField(null=True, editable=False,db_column='userid')
    AttDate = models.DateTimeField(_('Attendance date'),null=True, blank=True,db_column='attdate')
    Notes=models.CharField(_('Notes'),max_length=200, null=True, blank=True,db_column='notes')
    class Admin:
        pass
    class Meta:
        db_table = 'excnotes'
        unique_together = (("UserID", "AttDate"),)
        default_permissions = ()


HOLIDAY_NATIONS=(
    ('01',_(u'Han')),
    ('02',_(u'Mongolian')),
    ('03',_(u'Hui')),
    ('04',_(u'Tibetan')),
    ('05',_(u'Uighur')),
    ('06',_(u'Miao Nationality')),
    ('07',_(u'Yizu')),
    ('08',_(u'Zhuang Nationality')),
    ('09',_(u'Buyi')),
    ('10',_(u'Korean Nationality')),
    ('11',_(u'Manchu')),
    ('12',_(u'Dongzu')),
    ('13',_(u'Yao people')),
    ('14',_(u'White People')),
    ('15',_(u'Tujia')),
    ('16',_(u'Hani')),
    ('17',_(u'Kazakh')),
    ('18',_(u'Daizu')),
    ('19',_(u'Li Nationality')),
    ('20',_(u'Lisuzu')),
    ('21',_(u'Wazu')),
    ('22',_(u'Shezu')),
    ('23',_(u'Alpine Nationality')),
    ('24',_(u'Lahu')),
    ('25',_(u'aquarium')),
    ('26',_(u'Dongxiang')),
    ('27',_(u'Nasi')),
    ('28',_(u'Jingpo')),
    ('29',_(u'Korke Dai')),
    ('30',_(u'Tu Nationality')),
    ('31',_(u'Damu')),
    ('32',_(u'Mulaozu')),
    ('33',_(u'Qiangzu')),
    ('34',_(u'Blan')),
    ('35',_(u'Sarah')),
    ('36',_(u'Mao Nan')),
    ('37',_(u'Gelaozu')),
    ('38',_(u'Sibo')),
    ('39',_(u'Achang Clan')),
    ('40',_(u'Pumi')),
    ('41',_(u'Tajik')),
    ('42',_(u'Rage Nationality')),
    ('43',_(u'Uzbek')),
    ('44',_(u'Russian')),
    ('45',_(u'Ewenki')),
    ('46',_(u'Derg Nationality')),
    ('47',_(u'Security Family')),
    ('48',_(u'Yugu')),
    ('49',_(u'Kyoto')),
    ('50',_(u'Tatar')),
    ('51',_(u'Dragon')),
    ('52',_(u'Olunchun')),
    ('53',_(u'Herzhe')),
    ('54',_(u'Menba')),
    ('55',_(u'Lamba')),
    ('56',_(u'Kinuo')),
)

HolidayType_CHOICES=(

    (1,_(u'Festival Type 1')),
    (2,_(u'Festival Type 2')),
    (3,_(u'Festival Type 3')),


)


    ##节假日表##
class holidays(models.Model):
    HolidayID=models.AutoField(primary_key=True,null=False, editable=False,db_column='holidayid')
    HolidayName=models.CharField(_('Holiday name'),max_length=20, null=False, blank=False,db_column='holidayname')
    HolidayYear=models.SmallIntegerField(_('Year'), null=True, blank=True, editable=False,db_column='holidayyear')
    HolidayMonth=models.SmallIntegerField(_('Month'), null=True, blank=True, editable=False,db_column='holidaymonth')
    HolidayDay=models.SmallIntegerField(_('Day'), null=True, default=1,blank=True, editable=False,db_column='holidayday')
    StartTime = models.DateField(_('Beginning date'), null=False, blank=False,db_column='starttime')#help_text=_('Date format is ')+"ISO;"+ _('for example')+':1999-01-10/1999-1-11')     #请假时间为日期型
    Duration=models.SmallIntegerField(_('Duration times'), null=False, blank=False,help_text=_(u'day'),db_column='duration')
    HolidayType=models.SmallIntegerField(_('Holiday type'), null=True, db_column='holidaytype',blank=True,choices=HolidayType_CHOICES, editable=True,help_text=_(u'This option is only used for access time period settings, up to 32 holidays per holiday type'))
    XINBIE=models.CharField(_('Holiday sex'), max_length=4, null=True, db_column='xinbie',blank=True, choices=GENDER_CHOICES, editable=False)
    MINZU=models.CharField(_('Holiday nation'), max_length=50, null=True, db_column='minzu',blank=True,choices=HOLIDAY_NATIONS, editable=False)
    DelTag = models.IntegerField(_(u'delete tag'),default=0, editable=False, db_column='deltag',null=True, blank=True)
    def __unicode__(self):
        return u"%s"%(u"%s"%(self.HolidayName))
    def __str__(self):
        return u"%s"%(u"%s"%(self.HolidayName))
    def save(self):
        try:
            h = holidays.objects.get(HolidayName=self.HolidayName)
            if h.DelTag!=0:
                h.DelTag=0
            h.StartTime=self.StartTime
            h.Duration=self.Duration
            h.HolidayType=self.HolidayType
            super(holidays,h).save()
        except:
            super(holidays,self).save()
    @staticmethod
    def colModels():
        table = [
            {'name':'id','hidden':True},
            {'name':'HolidayName','width':200,'label':u"%s"%(holidays._meta.get_field('HolidayName').verbose_name)},
            {'name':'StartTime','width':120,'label':u"%s"%(holidays._meta.get_field('StartTime').verbose_name)},
            {'name':'Duration','width':120,'label':u"%s"%(holidays._meta.get_field('Duration').verbose_name)},
            {'name':'HolidayType','width':120,'label':u"%s"%(holidays._meta.get_field('HolidayType').verbose_name)}
            ]
        if settings.PRODUCTCODE == 14:
            if len(settings.SALE_MODULE) == 1 and settings.SALE_MODULE[0] == 'adms':
                pass
            else:
                for i in table:
                    if i['name'] == 'HolidayType':
                        table.remove(i)
        return table
    class Admin:
        search_fields = ['HolidayName']
    class Meta:
        db_table = 'holidays'
        verbose_name=_('holidays')
        verbose_name_plural=verbose_name
        unique_together = (("HolidayName", "StartTime"),)
        default_permissions = ('browse', 'add', 'change', 'delete')


##班次排版时段表##

class NUM_RUN_DEIL(models.Model):
    Num_runID=models.ForeignKey("NUM_RUN", verbose_name=_("shift"), db_column='num_runid', null=False, blank=False, on_delete=models.CASCADE)
    StartTime = models.TimeField(_('beginning time'),null=False, blank=False,db_column='starttime')
    EndTime = models.TimeField(_('ending time'),null=True, blank=False,db_column='endtime')
    Sdays=models.SmallIntegerField(_('start day'),null=False, blank=False,db_column='sdays')
    Edays=models.SmallIntegerField(_('finish day'),null=True,blank=True,db_column='edays')
    SchclassID=models.ForeignKey("SchClass", verbose_name=_('time-table class'), db_column='schclassid', null=True,default=-1,blank=True, on_delete=models.CASCADE)
    OverTime = models.IntegerField(_('Over time'),null=True,default=0,blank=True,db_column='overtime')
    def __unicode__(self):
        return u"%s"%(u"%s,%s"%(self.Num_runID,self.SchclassID))
    def __str__(self):
        return u"%s"%(u"%s,%s"%(self.Num_runID,self.SchclassID))
    class Admin:
        pass
    class Meta:
        db_table = 'num_run_deil'
        verbose_name = _('shift detail')
        verbose_name_plural=verbose_name
        unique_together = (("Num_runID", "Sdays","StartTime"),)
        default_permissions = ()


CYCLE_UNITS=(
    (0, _('Day')),
    (1, _('Week')),
    (2, _('Month')),
)

    ##班次表##
class NUM_RUN(models.Model):
    Num_runID=models.AutoField(primary_key=True,null=False, editable=False,db_column='num_runid')
    OLDID=models.IntegerField(null=True,default=-1,blank=True, editable=False,db_column='oldid')
    Name=models.CharField(_('Sch name'),max_length=30,null=False,db_column='name')
    StartDate = models.DateField(_('Beginning date'),null=True,default='2017-01-01', blank=True,db_column='startdate') #日期型
    EndDate = models.DateField(_('Ending date'),null=True,default='2020-12-31', blank=True,db_column='enddate')    #日期型
    Cycle=models.SmallIntegerField(_('Cycle number'),null=True, db_column='cyle', default=1,blank=True,editable=True)
    Units=models.SmallIntegerField(_('Cycle unit'),null=True, default=1,blank=True,editable=True, choices=CYCLE_UNITS,db_column='units')
    Num_RunOfDept=models.IntegerField(_(u'Home unit'),null=True,blank=True, default=0,editable=True,db_column='num_runofdept')
    DelTag = models.IntegerField(_(u'logout mark'),default=0, editable=False, null=True, blank=True,db_column='deltag')

    def __unicode__(self):
        return u"%s"%(u"%s"%(self.Name))
    def __str__(self):
        return u"%s"%(u"%s"%(self.Name))
    @staticmethod
    def objByID(id):
        if id==None: return None
        d=cache.get("%s_iclock_num_run_%s"%(settings.UNIT,id))
        if d: return d
        try:
            d=NUM_RUN.objects.get(Num_runID=id)
        except:
            d=None
        if d:
            cache.set("%s_iclock_num_run_%s"%(settings.UNIT,id),d)
        return d


    @staticmethod
    def colModels():
        return [
            {'name':'Num_runID','hidden':True},
            {'name':'Name','width':140,'label':u"%s"%(NUM_RUN._meta.get_field('Name').verbose_name)},
            {'name':'StartDate','width':120,'label':u"%s"%(NUM_RUN._meta.get_field('StartDate').verbose_name)},
            {'name':'EndDate','width':120,'label':u"%s"%(NUM_RUN._meta.get_field('EndDate').verbose_name)},
            {'name':'Cycle','width':80,'label':u"%s"%(NUM_RUN._meta.get_field('Cycle').verbose_name)},
            {'name':'Units','width':80,'label':u"%s"%(NUM_RUN._meta.get_field('Units').verbose_name)},
            {'name':'Num_RunOfDept','sortable':False,'width':120,'label':u"%s"%(SchClass._meta.get_field('TimeZoneOfDept').verbose_name)},
            # {'name':'shift_detail','sortable':False,'width':100,'label':u"%s"%(_('operation'))},
            {'name':'h_unit','hidden':True}

            ]
    class Admin:
        search_fields = ['Name']
    class Meta:
        db_table = 'num_run'
        verbose_name=_('shift')
        verbose_name_plural=verbose_name
        default_permissions = ('browse','add', 'change', 'delete')
        permissions = (
                    ('addShiftTimeTable_num_run','Add time-table'),
                    ('deleteAllShiftTimeTbl_num_run','Delete time-table'),
        )
    def Dept(self): #cached employee
        d=department.objByID(self.Num_RunOfDept)
        if d:return d
        else:return _(u'All departments')

    def save(self):
        super(NUM_RUN,self).save()
        try:
            cache.delete("%s_iclock_num_run_%s"%(settings.UNIT,self.Num_runID))
        except:
            pass
        return self


    ##管理员权限设置表##
# class SECURITYDETAILS(models.Model):
#     SecuritydetailID=models.AutoField(primary_key=True,null=False, editable=False)
#     UserID=models.SmallIntegerField(null=True,blank=True)
#     DeptID=models.SmallIntegerField(null=True,blank=True)
#     Schedule=models.SmallIntegerField(null=True,blank=True)
#     UserInfo=models.SmallIntegerField(null=True,blank=True)
#     EnrollFingers=models.SmallIntegerField(null=True,blank=True)
#     ReportView=models.SmallIntegerField(null=True,blank=True)
#     Report=models.CharField(max_length=10,null=True)
#     class Admin:
#         pass
#     class Meta:
#         db_table = 'SECURITYDETAILS'



 ##轮班表##
# class SHIFT(models.Model):
#     ShiftID=models.AutoField(primary_key=True,null=False, editable=False)
#     Name=models.CharField(_('Sch name'),max_length=20,null=True)
#     UshiftID=models.IntegerField(null=True,default=-1,blank=True,editable=False)
#     StartDate = models.DateField(_('Beginning date'),null=False,default='1900-1-1', blank=True)
#     EndDate = models.DateField(_('Ending date'),null=True,default='1900-12-31', blank=True)   #日期型
#     RunNum=models.SmallIntegerField(_('Run number'),null=True,default=1,blank=True)         #日期型
#     SCH1=models.IntegerField(_('SCH1'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH2=models.IntegerField(_('SCH2'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH3=models.IntegerField(_('SCH3'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH4=models.IntegerField(_('SCH4'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH5=models.IntegerField(_('SCH5'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH6=models.IntegerField(_('SCH6'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH7=models.IntegerField(_('SCH7'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH8=models.IntegerField(_('SCH8'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH9=models.IntegerField(_('SCH9'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH10=models.IntegerField(_('SCH10'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH11=models.IntegerField(_('SCH11'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     SCH12=models.IntegerField(_('SCH12'), editable=False,null=True,default=0,blank=True,choices=BOOLEANS)
#     Cycle=models.SmallIntegerField( _('Shift Cycle'),null=True,default=0,blank=True)
#     UnitS=models.SmallIntegerField(_('Cycle unit'),null=True,default=0,blank=True,choices=CYCLE_UNITS)
#     class Admin:
#         pass
#     class Meta:
#         db_table = 'SHIFT'
#         verbose_name = _('Scheduled shift')
#         verbose_name_plural=verbose_name
#         default_permissions = ()

    ##员工排班表##
class USER_OF_RUN(models.Model):
    ScheType = models.IntegerField(null=False,db_column='schetype',default=0,editable=False)  # 0-人员排班,1-部门排班
    UserID=models.ForeignKey(employee, verbose_name=_("employee"), db_column='userid', null=True, blank=True, on_delete=models.CASCADE)  # 人员外键可为空
    DeptID=models.ForeignKey(department, verbose_name=_("department"), db_column='deptid', null=True, blank=True, on_delete=models.CASCADE)  # 部门外键可为空
    group_id = models.IntegerField("group", db_column='group_id', null=True, blank=True)
    StartDate = models.DateField(_('Beginning date'),null=False, blank=True,db_column='startdate')
    EndDate = models.DateField(_('Ending date'),null=False,default='2099-12-31', blank=True,db_column='enddate')
    NUM_OF_RUN_ID=models.ForeignKey(NUM_RUN, verbose_name=_('Shift'), db_column='num_of_run_id', null=False, blank=False, on_delete=models.CASCADE)
    ISNOTOF_RUN=models.IntegerField(null=True,default=0,blank=True ,editable=False,db_column='isnotof_run')
    ORDER_RUN=models.IntegerField(null=True,blank=True,editable=False,db_column='order_run')
    datest=models.DateTimeField(_(u'datest'), db_column='datest', null=True)
#	def save(self):
#		self.EndDate=datetime.datetime(self.EndDate.year,self.EndDate.month,self.EndDate.day,23,59,59)
#		return models.Model.save(self)
    def get_all_Shift_Name(self):
        datas = NUM_RUN.objects.all()
        s = []
        for row in datas:
            s.append (row["Name"])
        return s
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels(schetype=0):
        if schetype == 0:
            return [
                {'name':'id','hidden':True},
                {'name':'PIN','index':'UserID__PIN','width':120,'label':u"%s"%(_('PIN'))},
                {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
                {'name': 'DeptNumber', 'index': 'UserID__DeptID__DeptNumber', 'width': 100,'label': u"%s"%(_(u'Department Number'))},
                {'name': 'DeptName', 'index': 'UserID__PIN', 'sortable': False, 'width': 140,'label': u"%s"%(_('department name'))},
                {'name':'StartDate','sortable':False,'index':'StartSpecDay','width':120,'label':u"%s"%(USER_OF_RUN._meta.get_field('StartDate').verbose_name)},
                {'name':'EndDate','sortable':False,'index':'EndSpecDay','width':120,'label':u"%s"%(USER_OF_RUN._meta.get_field('EndDate').verbose_name)},
                {'name':'NUM_OF_RUN_ID','sortable':False,'width':140,'label':u"%s"%(_('Sch name'))}
                ]
        else:
            return [
                {'name':'id','hidden':True},
                {'name':'DeptNumber','index':'UserID__DeptID__DeptNumber','width':100,'label':u"%s"%(_(u'Department Number'))},
                {'name':'DeptName','index':'UserID__PIN','sortable':False,'width':140,'label':u"%s"%(_('department name'))},
                {'name':'StartDate','sortable':False,'index':'StartSpecDay','width':120,'label':u"%s"%(USER_OF_RUN._meta.get_field('StartDate').verbose_name)},
                {'name':'EndDate','sortable':False,'index':'EndSpecDay','width':120,'label':u"%s"%(USER_OF_RUN._meta.get_field('EndDate').verbose_name)},
                {'name':'NUM_OF_RUN_ID','sortable':False,'width':140,'label':u"%s"%(_('Sch name'))}
                ]


    class Admin:
        list_filter = ['UserID','NUM_OF_RUN_ID','StartDate','EndDate']
        search_fields = ['UserID__PIN','UserID__EName']

    class Meta:
        db_table = 'user_of_run'
        verbose_name = _('empoyee shift')
        verbose_name_plural=verbose_name
        unique_together = (("ScheType","UserID","DeptID","NUM_OF_RUN_ID", "StartDate","EndDate", "group_id"),)
        default_permissions = ('browse','add', 'change', 'delete' ,'export')
        permissions=(

                   ('Employee_shift_details','Employee shift details'),



        )


    ##员工考勤例外（请假/公出）表##
class USER_SPEDAY(models.Model):
    #id=models.AutoField(primary_key=True,null=False, editable=False)
    UserID=models.ForeignKey(employee, db_column='userid',verbose_name=_('employee'), default=1,null=False, blank=False, on_delete=models.CASCADE)
    StartSpecDay= models.DateTimeField(_('beginning time'), null=False, blank=True,db_column='startspecday')
    EndSpecDay = models.DateTimeField(_('ending time'), null=True, blank=True,db_column='endspecday')
    DateID=models.IntegerField(_('Leave Class'),db_column='dateid', null=False,default=-1,blank=True, editable=True)
    YUANYING=models.CharField(_('reson'), max_length=200,null=True,blank=True,db_column='yuanying')
    ApplyDate=models.DateTimeField(_('apply date'), null=True,  blank=True,db_column='applydate')
    State=models.IntegerField(_('state'), null=True,db_column='state', default=0, blank=True, choices=AUDIT_STATES, editable=False)#0-申请；2-通过；3-拒绝；6-重新申请；>10表示审批中，此时减去10等于管理员id(职务审批）或者人员id(人员审批)
    clearance=models.IntegerField(_('Leave clearance'),db_column='clearance', null=True,default=0,blank=True, editable=True)   #是否自动销假
    roleid = models.IntegerField(_('current reviewer'),editable=True)
    process=models.CharField(_('Audit Process'),max_length=80,null=True,blank=True)
    oldprocess=models.CharField(_('Audited Process'),max_length=80,null=True,blank=True)
    processid=models.IntegerField(_('Audit Process id'),editable=True)
    procSN=models.IntegerField(_('reviewed serial number'),default=0,editable=True,db_column='procsn')
    processtype = models.IntegerField(_('process type'), default=0, editable=True, choices=PROCESSTYPE,db_column='processtype')
    employeelist = models.CharField(_('current reviewer'),max_length=80,null=True,blank=True)
#	operator = models.CharField(_(u'operating'), max_length=20, null=True, blank=True, editable=False)

    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def __unicode__(self):
        return u"%s" % (self.UserID.__unicode__())
    def __str__(self):
        return u"%s" % (self.UserID.__str__())
    def USER_SPEDAY_DETAILS(self):
        try:
            return USER_SPEDAY_DETAILS.objByID(self.id)
        except:
            return None
    @staticmethod
    def colModels():
        ann_leave=LeaveClass.objects.filter(LeaveType=5,DelTag=0)
        annTimeType=3
        if ann_leave:
            annTimeType=ann_leave[0].Unit
        ret = [
            {'name':'id','hidden':True,'frozen':True},
            {'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN')),'frozen':True},
            {'name':'EName','index':'UserID__EName','width':80,'label':u"%s"%(_('EName')),'frozen':True},
            {'name':'StartSpecDay','index':'StartSpecDay','width':120,'label':u"%s"%(USER_SPEDAY._meta.get_field('StartSpecDay').verbose_name)},
            {'name':'EndSpecDay','index':'EndSpecDay','width':120,'label':u"%s"%(USER_SPEDAY._meta.get_field('EndSpecDay').verbose_name)},
            {'name':'DateID','width':80,'label':u"%s"%(USER_SPEDAY._meta.get_field('DateID').verbose_name)},
            {'name':'DeptID','index':'UserID__DeptID','width':80,'label':u"%s"%(_('department number'))},
            {'name':'DeptName','index':'UserID__DeptID__DeptName','width':120,'label':u"%s"%(_('department name'))},
            {'name':'Title','index':'UserID__Title','width':60,'label':u"%s"%(employee._meta.get_field('Title').verbose_name)},
            {'name':'State','index':'State','width':80,'label':u"%s"%(USER_SPEDAY._meta.get_field('State').verbose_name)},
            {'name':'clearance','index':'clearance','width':100,'label':u"%s"%(USER_SPEDAY._meta.get_field('clearance').verbose_name)},
            {'name':'process','sortable':False,'index':'process','width':220,'label':u"%s"%(_(u'Audit Process'))},
            {'name':'operate','sortable':False,'width':50,'label':u"%s"%(_(u'operating'))},
            {'name':'YUANYING','sortable':False,'width':120,'label':u"%s"%(USER_SPEDAY._meta.get_field('YUANYING').verbose_name)},
            {'name':'Place','sortable':False,'index':'Place','width':120,'label':u"%s"%(_(u'outing place'))},
            {'name':'mobile','sortable':False,'index':'mobile','width':120,'label':u"%s"%(_(u'contact number'))},
            {'name':'successor','sortable':False,'width':120,'label':u"%s"%(_(u'Work undertaker'))},
            {'name':'file','sortable':False,'width':80,'label':u"%s"%(_(u'annex'))},
            {'name':'remarks','sortable':False,'width':120,'label':u"%s"%(_(u'Remarks'))},
            {'name':'ApplyDate','index':'ApplyDate','width':120,'label':u"%s"%(USER_SPEDAY._meta.get_field('ApplyDate').verbose_name)},
            {'name':'empid','sortable':False,'width':80,'label':u"%s"%(_(u'Annual leave (hours)'))},
            {'name':'empids','sortable':False,'width':100,'label':u"%s"%(_(u'The annual leave (hours)'))}
            ]
        for r in ret:
            if r['name'] == 'empid' and annTimeType==3:
                r['label'] = u"%s"%(_(u'Annual leave (days)'))
            elif r['name'] == 'empids' and annTimeType==3:
                r['label'] = u"%s"%(_(u'After the annual leave (days)'))
        return ret
    class Admin:
        list_filter =['State','UserID','StartSpecDay','DateID','ApplyDate']
        lock_fields=['UserID']
        search_fields = ['UserID__PIN','UserID__EName']
    class Meta:
        db_table = 'user_speday'
        verbose_name = _('special leave')
        verbose_name_plural=verbose_name
        unique_together = (("UserID", "StartSpecDay","DateID", "State"),)
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
                        ('leaveAudit_user_speday','Audit Sepcial Leave'),
                        ('definedReport_user_speday','user-defined report'),
                        ('setprocess','setprocess'),


            )

class USER_SPEDAY_DETAILS(models.Model):
    USER_SPEDAY_ID = models.ForeignKey(USER_SPEDAY,db_column='user_speday_id',default=1,null=False,blank=False, on_delete=models.CASCADE)
    Place = models.CharField(_(u'outing place'),db_column="place",max_length=120, null=True, blank=True)
    mobile = models.CharField(_(u'contact number'),db_column="mobile",max_length=20, null=True, blank=True)
    successor = models.CharField(_(u'Work undertaker'),db_column="successor",null=True,max_length=40, blank=True, default="")
    remarks=models.CharField(_(u'Remarks'),max_length=200,null=True,blank=True)
    file=models.CharField(_(u'annex'),max_length=200,null=True,blank=True,default="")
    @staticmethod
    def objByID(id):
        if id==None: return None
        try:
            d=USER_SPEDAY_DETAILS.objects.get(USER_SPEDAY_ID=id)
        except:
            d=None
        return d
    class Meta:
        default_permissions = ()

class USER_SPEDAY_PROCESS(models.Model):
    USER_SPEDAY_ID = models.ForeignKey(USER_SPEDAY,db_column='user_speday_id',default=1,null=False,blank=False, on_delete=models.CASCADE)
    User = models.ForeignKey(settings.AUTH_USER_MODEL,db_column='user_id', verbose_name=_('administrator'), null=True, blank=True, on_delete=models.CASCADE)
    comments=models.CharField(_(u'Approval comments'),max_length=400,null=True,blank=True)
    ProcessingTime = models.DateTimeField(_(u'Processing time'), null=True, blank=True,db_column='processingtime')
    procSN = models.IntegerField(_(u'Approval level'),db_column='procsn', null=True,blank=True, editable=True)
    State=models.SmallIntegerField(_('state'), null=True, db_column='state',default=0, blank=True, choices=AUDIT_STATES_PROCESS, editable=False)
    employeeid = models.ForeignKey(employee, db_column='userid', verbose_name=_('employee'), null=True,blank=False, on_delete=models.CASCADE)
    class Meta:
        default_permissions = ()

CONTRACT_STATES=(
    (0, _(u'Yes')),
    (1, _(u'No')),
)
CONTRACT_TYPE=(
    (0, _(u'Contract workers')),
    (1, _(u'try out')),
)

    ##用户合同 暂时未开放
class USER_CONTRACT(models.Model):
    id=models.AutoField(primary_key=True,null=False, editable=False)
    UserID=models.ForeignKey(employee, db_column='userid',verbose_name=_('employee'), default=1,null=False, blank=False, on_delete=models.CASCADE)
    StartContractDay= models.DateField(_('beginning time'), null=False, blank=True,db_column='startcontractday')
    EndContractDay = models.DateField(_('ending time'), null=True, blank=True,db_column='endcontractday')
    Notes=models.TextField(_('Notes'), max_length=200,null=True,blank=True,db_column='notes')
    ApplyDate=models.DateTimeField(_(u'application time'),  null=True,  blank=True,db_column='applydate')
    State=models.SmallIntegerField(_(u'Contract status'), null=True, default=0, db_column='state',blank=True, choices=CONTRACT_STATES, editable=True)
    Type=models.SmallIntegerField(_(u'Types of'), null=True, db_column='type',default=0, blank=True, choices=CONTRACT_TYPE, editable=True)
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'PIN','index':'UserID__PIN','width':80,'label':u"%s"%(_('PIN'))},
            {'name':'EName','index':'UserID__EName','width':80,'label':u"%s"%(_('EName'))},
            {'name':'StartContractDay','index':'StartContractDay','width':120,'label':u"%s"%(USER_CONTRACT._meta.get_field('StartContractDay').verbose_name)},
            {'name':'EndContractDay','index':'EndContractDay','width':120,'label':u"%s"%(USER_CONTRACT._meta.get_field('EndContractDay').verbose_name)},
            {'name':'DeptID','index':'UserID__DeptID','width':60,'label':u"%s"%(_('department number'))},
            {'name':'DeptName','index':'UserID__DeptID__DeptName','width':120,'label':u"%s"%(_('department name'))},
            {'name':'State','index':'State','width':80,'label':u"%s"%(USER_CONTRACT._meta.get_field('State').verbose_name)},
            {'name':'Notes','sortable':False,'width':120,'label':u"%s"%(USER_CONTRACT._meta.get_field('Notes').verbose_name)},
            #{'name':'ApplyDate','index':'ApplyDate','width':120,'label':u"%s"%(USER_CONTRACT._meta.get_field('ApplyDate').verbose_name)},
            {'name':'Type','index':'Type','width':80,'label':u"%s"%(USER_CONTRACT._meta.get_field('Type').verbose_name)}
            ]
    class Admin:
        list_filter =['State','UserID','StartContractDay','ApplyDate']
        lock_fields=['UserID']
        search_fields = ['UserID__PIN','UserID__EName']
    class Meta:
        db_table = 'user_contract'
        verbose_name = _('USER_CONTRACT')
        verbose_name_plural=verbose_name
        unique_together = (("UserID", "StartContractDay"),)
        default_permissions = ()


#		permissions = (
#						('leaveAudit_user_speday','Audit Sepcial Leave'),
#			)

WORK_TYPE=(
    (0, _(u'SSpeDayNormal')),#正常工作，平日
    (1, _(u'NormalDayOT')),#平日加班
    (2, _(u'WeekendDayOT')),#周末
    (3, _(u'HolidayDayOT')),#节假日加班
)
    ##员工临时排班表##
class USER_TEMP_SCH(models.Model):
    ScheType = models.IntegerField(null=False,db_column='schetype',default=0,editable=False)  # 0-人员排班,1-部门排班
    DeptID=models.ForeignKey(department, verbose_name=_("department"), db_column='deptid', null=True, blank=True, on_delete=models.CASCADE)  # 部门外键可为空
    UserID=models.ForeignKey(employee,db_column='userid',verbose_name=_('employee'), default=1,null=True, blank=True, on_delete=models.CASCADE)  # 人员外键可为空
    ComeTime = models.DateTimeField(_('Beginning time'),null=False, blank=False,db_column='cometime')
    LeaveTime = models.DateTimeField(_('Ending time'),null=False, blank=False,db_column='leavetime')
    OverTime = models.IntegerField(_('Over time'),null=False,default=0,blank=True,db_column='overtime')
    Type=models.SmallIntegerField(_('Type'),null=True,default=0,blank=True,db_column='type')
    Flag=models.SmallIntegerField(null=True,default=1,blank=True,editable=False,db_column='flag')
    SchclassID=models.IntegerField(null=True,default=1,db_column='schclassid',blank=True,editable=False)
    worktype=models.SmallIntegerField(_('Overtime type'),null=True,default=0,blank=True,db_column='worktype',choices=WORK_TYPE)
#	SchclassID=models.ForeignKey("SchClass",db_column='SchclassID',verbose_name=_('shift time-table'),null=False,default=-1,blank=True, on_delete=models.CASCADE)
    def get_all_Sch_Name(self):
        datas = SchClass.objects.all()
        sch = []
        for row in datas:
            sch.append(row["SchName"])
        return sch
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels(schetype=0):
        if schetype==0:
            return [
                {'name':'id','hidden':True},
                {'name':'DeptName','index':'UserID__DeptID','width':100,'label':u"%s"%(_('department name'))},
                {'name':'PIN','index':'UserID__PIN','width':80,'label':u"%s"%(_('PIN'))},
                {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
                {'name':'ComeTime','index':'ComeTime','width':120,'label':u"%s"%(USER_TEMP_SCH._meta.get_field('ComeTime').verbose_name)},
                {'name':'LeaveTime','index':'LeaveTime','width':120,'label':u"%s"%(USER_TEMP_SCH._meta.get_field('LeaveTime').verbose_name)},
                {'name':'SchclassID','sortable':False,'width':80,'label':u"%s"%(_('time-table name'))}
                ]
        else:
            return [
                {'name':'id','hidden':True},
                {'name':'DeptNumber','index':'UserID__DeptID__DeptNumber','width':100,'label':u"%s"%(_(u'Department Number'))},
                {'name':'DeptName','index':'UserID__DeptID','width':100,'label':u"%s"%(_('department name'))},
                {'name':'ComeTime','index':'ComeTime','width':120,'label':u"%s"%(USER_TEMP_SCH._meta.get_field('ComeTime').verbose_name)},
                {'name':'LeaveTime','index':'LeaveTime','width':120,'label':u"%s"%(USER_TEMP_SCH._meta.get_field('LeaveTime').verbose_name)},
                {'name':'SchclassID','sortable':False,'width':80,'label':u"%s"%(_('time-table name'))}
                ]

    class Admin:
        list_filter = ('UserID','SchclassID','LeaveTime','ComeTime','OverTime')
        search_fields = ['UserID__PIN','UserID__EName','SchclassID']
    class Meta:
        db_table = 'user_temp_sch'
        verbose_name=_('temporary schedule')
        verbose_name_plural=verbose_name
        unique_together = (("UserID","ComeTime", "LeaveTime","DeptID","ScheType"),)
        default_permissions = ()
        permissions=(

                   ('Data_Management','Data Management'),#数据管理
                   ('Init_database','Init database'),#初始化系统
                   ('Clear_Obsolete_Data','Clear Obsolete Data'),#清除过期数据
                   ('Backup_Database','Backup Database'),#备份数据库
                   ('import_department_data','import department data'),#导入部门数据
                   ('import_employee_data','import employee data'),#导入人员数据
                   ('Import_Finger_data','Import Finger data'),#导入指纹数据
                   ('U_Disk_Data_Manager','U_Disk Data Manager'),#U盘数据管理
                   ('Database_Options','Database Options'),#数据库设置
                   ('System_Options','System Options'),#系统设置
                   ('preferences_user','user-Preferences'),#自定义显示字段
                   ('setprocess','set process'),
                   ('user_temp_sch_add','add user_temp_sch'),#系统选项的增加
                   ('user_temp_sch_modify','modify user_temp_sch'),#系统选项的修改
                   ('user_temp_sch_delete','delete user_temp_sch'),#系统选项删除

        )

LEAVE_UNITS=(
    (1, _('Hour')),
    (2, _('Minute')),
    (3, _('Workday')),
)
LEAVETYPE=(
    (1, _('Sick Leave')),#病假
    (2, _('Private Affair Leave')),#事假
    (3, _('Home Leave')),#探亲假
    (4, _('Maternity Leave')),#产假
    (5, _('Annual leave')),#年假
    (6, _('business trip')),#出差
)

    ##假类表##
class LeaveClass(models.Model):
    LeaveID=models.AutoField(_('Leave ID'),primary_key=True,null=False, editable=False,db_column='leaveid')
    LeaveName=models.CharField(_('Leave Class name'),max_length=20,null=False,db_column='leavename')
    MinUnit=models.FloatField(_('Min.unit'),null=False,default=1,blank=True,db_column='minunit')
    Unit=models.SmallIntegerField(_('Unit'),null=False,default=1,blank=True,choices=LEAVE_UNITS,db_column='unit')
    RemaindProc=models.SmallIntegerField(_('Round at Acc.'),null=False,default=1,blank=True,choices=BOOLEANS,db_column='remaindproc')
    RemaindCount=models.SmallIntegerField(_('Acc. by times'),null=False,default=1,blank=True,choices=BOOLEANS,db_column='remaindcount')
    ReportSymbol=models.CharField(_('Symbol in report'),max_length=4,null=False,default='-',db_column='reportsymbol')
    Deduct=models.FloatField(_('Min. unit deduction'),null=True,default=0,blank=True,editable=False,db_column='deduct')
    Classify=models.SmallIntegerField(_('Count to leave'),null=False,default=0,blank=True,choices=BOOLEANS_Classify,db_column='classify')
    clearance=models.IntegerField(_('Leave clearance'),db_column='clearance', null=True,default=0,blank=True, editable=True)   #是否自动销假
    LeaveType=models.IntegerField(_('Leave Class Type'), null=True,default=0,blank=True, editable=True,choices=LEAVETYPE,db_column='leavetype') #0 未知  1 病假 2事假 3产假 4探亲假 5年假
    Color=models.IntegerField(_('display color'),null=True,default=16715535,blank=True,editable=True,db_column='color')
    must_upload_attachment = models.BooleanField(_('must upload the attachment'),choices=BOOLEANS, default=False, db_column='must_upload_attachment')
    must_leave_beforehand = models.BooleanField(_('must ask for leave beforehand'),choices=BOOLEANS, default=False, db_column='must_leave_beforehand')
    DelTag = models.IntegerField(default=0, editable=False, null=True, blank=True,db_column='deltag')

    def __unicode__(self):
        return u"%s"%(self.LeaveName)
    def __str__(self):
        return u"%s"%(self.LeaveName)
    def save(self):
        if self.LeaveType == 6 and LeaveClass.objects.filter(LeaveType=6, DelTag=0).exclude(LeaveID=self.LeaveID):
            raise Exception(f"{_('Only one leave class of type business trip can be set')}")
        if LeaveClass.objects.filter(LeaveName=self.LeaveName,DelTag=0).count()>0:
            raise Exception(u"%s"%(_(u'Fake class name repeat')))
        elif LeaveClass.objects.filter(ReportSymbol=self.ReportSymbol,DelTag=0).count()>0:
            raise Exception(u"%s"%(_(u'report symbol repeat')))
        elif LeaveClass.objects.filter(LeaveName=self.LeaveName,DelTag=1).count()>0:
            leave = LeaveClass.objects.filter(LeaveName=self.LeaveName,DelTag=1)
            self.LeaveID = leave[0].LeaveID
        super(LeaveClass,self).save()
    class Admin:
        @staticmethod
        def initial_data():
            if LeaveClass.objects.all().count()==0:
                LeaveClass(LeaveName=_(u'Business Leave'),MinUnit=0.5,Unit=3,RemaindProc=1,RemaindCount=1,ReportSymbol='G',Classify=128,DelTag=0).save()
                LeaveClass(LeaveName=_(u'Sick Leave'),Unit=1,ReportSymbol='B',LeaveType=1,Color=3398744,DelTag=0).save()
                LeaveClass(LeaveName=_(u'Private Affair Leave'),Unit=1,ReportSymbol='S',LeaveType=1,Color=8421631,DelTag=0).save()
                LeaveClass(LeaveName=_(u'Home Leave'),Unit=1,ReportSymbol='T',Color=16744576,DelTag=0).save()
                LeaveClass(LeaveName=_(u'Annual Leave'),Unit=1,ReportSymbol='N',LeaveType=5,Color=16744576,DelTag=0).save()

    class Meta:
        db_table = 'leaveclass'
        verbose_name=_('Leave Class')
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')


    ##统计项目表##
class LeaveClass1(models.Model):
    LeaveID=models.IntegerField(primary_key=True,null=False, editable=False,db_column='leaveid')
    LeaveName=models.CharField(_('Leave Class name'),max_length=20,null=False,db_column='leavename')
    MinUnit=models.FloatField(_('Min.unit'),null=False,default=1,blank=True,db_column='minunit')
    Unit=models.SmallIntegerField(_('Unit'),null=False,default=0,blank=True,choices=LEAVE_UNITS,db_column='unit')
    RemaindProc=models.SmallIntegerField(_('Round at Acc.'),null=False,default=1,blank=True,choices=BOOLEANS,db_column='remaindproc')
    RemaindCount=models.SmallIntegerField(_('Acc. by times'),null=False,default=1,blank=True,choices=BOOLEANS,db_column='remaindcount')
    ReportSymbol=models.CharField(_('Symbol in report'),max_length=4,null=False,default='_',db_column='reportsymbol')
    Deduct=models.FloatField(_('Min. unit deduction'),null=False,default=0,blank=True,db_column='deduct')
    Color=models.IntegerField(_('display color'),null=False,default=0,blank=True,editable=False,db_column='color')
    Classify=models.SmallIntegerField(null=False,default=0,blank=True,editable=False,db_column='classify')
    LeaveType=models.SmallIntegerField(_('Leave Class Type'),null=False,default=0,blank=True,db_column='leavetype')
#	Calc = models.TextField(max_length=2048,null=True,editable=False)
    class Admin:
        @staticmethod
        def initial_data():

            if LeaveClass1.objects.all().count()==0:
                """ 将公出999从leaveclass1中移走,归到假类设置中"""
        #		LeaveClass1(LeaveID=999,LeaveName="%s"%'BL',MinUnit=0.5,Unit=3,RemaindProc=1,RemaindCount=1,ReportSymbol='G',LeaveType="3",Calc='if(AttItem(LeaveType1)=999,AttItem(LeaveTime1),0)+if(AttItem(LeaveType2)=999,AttItem(LeaveTime2),0)+if(AttItem(LeaveType3)=999,AttItem(LeaveTime3),0)+if(AttItem(LeaveType4)=999,AttItem(LeaveTime4),0)+if(AttItem(LeaveType5)=999,AttItem(LeaveTime5),0)').save()
                LeaveClass1(LeaveID=1000,LeaveName="%s"%'OK',MinUnit=0.5,Unit=1,RemaindProc=1,RemaindCount=0,ReportSymbol='/',LeaveType="3").save()
                LeaveClass1(LeaveID=1001,LeaveName="%s"%'Late',MinUnit=10,Unit=2,RemaindProc=2,RemaindCount=1,ReportSymbol='>',LeaveType="3").save()
                LeaveClass1(LeaveID=1002,LeaveName="%s"%'Early',MinUnit=10,Unit=2,RemaindProc=2,RemaindCount=1,ReportSymbol='<',LeaveType="3").save()
                LeaveClass1(LeaveID=1003,LeaveName="%s"%'ALF',MinUnit=1,Unit=1,RemaindProc=1,RemaindCount=1,ReportSymbol='V',LeaveType="3").save()
                LeaveClass1(LeaveID=1004,LeaveName="%s"%'Absent',MinUnit=0.5,Unit=3,RemaindProc=1,RemaindCount=0,ReportSymbol='A',LeaveType="3").save()
                LeaveClass1(LeaveID=1005,LeaveName="%s"%'OT',MinUnit=1,Unit=1,RemaindProc=1,RemaindCount=1,ReportSymbol='+',LeaveType="3").save()
                LeaveClass1(LeaveID=1006,LeaveName="%s"%'OTH',MinUnit=1,Unit=1,RemaindProc=0,RemaindCount=1,ReportSymbol='=',LeaveType="0").save()
                LeaveClass1(LeaveID=1007,LeaveName="%s"%'Hol.',MinUnit=0.5,Unit=3,RemaindProc=2,RemaindCount=1,ReportSymbol='-',LeaveType="2").save()
                LeaveClass1(LeaveID=1008,LeaveName="%s"%'NoIn',MinUnit=1,Unit=4,RemaindProc=2,RemaindCount=1,ReportSymbol='[',LeaveType="2").save()
                LeaveClass1(LeaveID=1009,LeaveName="%s"%'NoOut',MinUnit=1,Unit=4,RemaindProc=2,RemaindCount=1,ReportSymbol=']',LeaveType="2").save()
                LeaveClass1(LeaveID=1010,LeaveName="%s"%'ROT',MinUnit=1,Unit=4,RemaindProc=2,RemaindCount=1,ReportSymbol='{',LeaveType="3").save()
                LeaveClass1(LeaveID=1011,LeaveName="%s"%'BOUT',MinUnit=1,Unit=4,RemaindProc=2,RemaindCount=1,ReportSymbol='}',LeaveType="3").save()
                LeaveClass1(LeaveID=1012,LeaveName="%s"%'OUT',MinUnit=1,Unit=1,RemaindProc=2,RemaindCount=1,ReportSymbol='L',LeaveType="3").save()
                LeaveClass1(LeaveID=1013,LeaveName="%s"%'FOT',MinUnit=1,Unit=1,RemaindProc=2,RemaindCount=1,ReportSymbol='F',LeaveType="3").save()



    class Meta:
        db_table = 'leaveclass1'
        verbose_name=_('report item')
        verbose_name_plural=verbose_name
        default_permissions = ()



ENDTIME_DAYS=(
    (1, _(u'the same day')),
    (2, _(u'the next day')),
)

    ##班次时段类别设置表##
class SchClass(models.Model):
    SchclassID=models.AutoField(primary_key=True,null=False, editable=False,db_column='schclassid')
    SchName=models.CharField(_('time-table name'), max_length=20,null=False,db_column='schname')
    StartTime = models.TimeField(_('on duty time'),null=False, blank=False,db_column='starttime')
    EndTime = models.TimeField(_('off duty time'),null=False, blank=False,db_column='endtime')
    LateMinutes=models.IntegerField(_(u'Allow late'),null=True,blank=True, default=0,help_text=_(u'minutes (default 0)'),db_column='lateminutes')
    EarlyMinutes=models.IntegerField(_(u'Allow early leave'),null=True,blank=True, default=0,help_text=_(u'minutes (default 0)'),db_column='earlyminutes')
    CheckIn = models.SmallIntegerField(_('must C/In'),null=True,default=1,blank=True,choices=BOOLEANS,db_column='checkin')
    CheckOut = models.SmallIntegerField(_('must C/Out'),null=True,default=1,blank=True,choices=BOOLEANS,db_column='checkout')
    CheckInTime1 = models.TimeField(_('beginning in'),null=True, blank=True,editable=False,db_column='checkintime1')
    CheckInTime2 = models.TimeField(_('ending in'),null=True, blank=True,editable=False,db_column='checkintime2')
    CheckOutTime1 = models.TimeField(_('begin out'),null=True, blank=True,editable=False,db_column='checkouttime1')
    CheckOutTime2 = models.TimeField(_('end out'),null=True, blank=True,editable=False,db_column='checkouttime2')
    Color=models.IntegerField(_('display color'),null=True,default=16715535,blank=True,editable=True,db_column='color')
    AutoBind=models.SmallIntegerField(null=True,default=1,blank=True,choices=BOOLEANS,editable=False,db_column='autobind')
    WorkDay=models.FloatField(_(u'translation workday'), null=False, default=1, blank=True,help_text=_(u'Unit: Day'),db_column='workday')
    IsCalcRest=models.BooleanField(_(u'Deduction of rest'),null=True,default=0,  blank=True,editable=True,db_column='iscalcrest',help_text=_(u'Invalid if the total rest time exceeds 240 minutes')) #是否扣减休息时间
    StartRestTime = models.TimeField(_('beginning Rest'),null=True, blank=True,editable=True,db_column='startresttime')             #暂时不编辑,
    EndRestTime = models.TimeField(_('ending rest'),null=True, blank=True,editable=True,db_column='endresttime')                  #暂时不编辑
    StartRestTime1 = models.TimeField(_('beginning Rest2'),null=True, blank=True,editable=True,db_column='startresttime1')             #暂时不编辑,
    EndRestTime1= models.TimeField(_('ending rest2'),null=True, blank=True,editable=True,db_column='endresttime1')                  #暂时不编辑
    CheckInMins1=models.IntegerField(_(u'Before work'),null=True,blank=True, default=120,db_column='checkinmins1',help_text=_(u'Checkin in min(default 120 min)'))
    CheckInMins2=models.IntegerField(_(u'After work'),null=True,blank=True, default=120,db_column='checkinmins2',help_text=_(u'Checkin in min(default 120 min)'))
    CheckOutMins1=models.IntegerField(_(u'Before off work'),null=True,blank=True, default=120,db_column='checkoutmins1',help_text=_(u'Checkout in min(default 120 min)'))
    CheckOutMins2=models.IntegerField(_(u'After off work'),null=True,blank=True, default=120,db_column='checkoutmins2',help_text=_(u'Checkout in min(default 120 min)'))
#	RestMins=models.IntegerField(_(u'扣除休息时间(分钟)'),null=True,blank=True, default=0,help_text=_(u'如不填写按下面的设置计算'))
    TimeZoneType=models.IntegerField(_(u'Flexible work'),null=True,default=0,  blank=True,editable=True,db_column='timezonetype',choices=BOOLEANS) #暂时不编辑,是否扣减休息时间
    WorkTimeType=models.IntegerField(_(u'Working time calculation type'),null=True,default=0,  blank=True,editable=False,db_column='worktimetype') #暂时不编辑

    BeforeInMins=models.IntegerField(_(u'Work in advance'),null=True,blank=True, db_column='beforeinmins',default=120,help_text=_(u'min(default 120 min)'))
    AfterInMins=models.IntegerField(_(u'Postpone to work'),null=True,blank=True, default=120,db_column='afterinmins',help_text=_(u'min(default 120 min)'))
    IsCalcOverTime=models.IntegerField(_(u'After the class, work overtime'),null=True,default=0,  blank=True,editable=True,db_column='iscalcovertime')
    OverTimeMins=models.IntegerField(_(u'off work'),null=True,blank=True, default=60,db_column='overtimemins',help_text=_(u'Min Are overtime2'))
    IsCalcComeOverTime=models.IntegerField(_(u'Overtime in class'),null=True,default=0, db_column='iscalccomeovertime', blank=True,editable=True)
    ComeOverTimeMins=models.IntegerField(_(u'Working'),null=True,blank=True, default=60,db_column='comeovertimemins',help_text=_(u'Min Are overtime1'))
    AsOverTimeMins=models.IntegerField(_(u'Interval notes overtime'),null=True,blank=True, db_column='asovertimemins',default=60,help_text=_(u'Min'))
    AsComeOverTimeMins=models.IntegerField(_(u'Interval notes overtime'),null=True,blank=True,db_column='ascomeovertimemins', default=60,help_text=_(u'Min'))
    MaxComeOverTimeMins=models.IntegerField(_(u'Limit the maximum overtime before work'),null=True,blank=True, db_column='maxcomeovertimemins',default=0,help_text=_(u'min (0 means no limit)'))
    MaxOverTimeMins=models.IntegerField(_(u'Limiting the maximum overtime after work'),null=True,blank=True, db_column='maxovertimemins',default=0,help_text=_(u'min (0 means no limit)'))
    TotalOverTimeMins=models.IntegerField(_(u'Limiting the total overtime hours'),null=True,blank=True, db_column='totalovertimemins',default=0,help_text=_(u'min (0 means no limit)'))


    NextDay=models.IntegerField(_(u'Days'),null=True,blank=True, db_column='nextday',default=1,choices=ENDTIME_DAYS,help_text=_(u'Default </br>the same day'))
    TimeZoneOfDept=models.IntegerField(_(u'Home unit'),null=True,blank=True,db_column='timezoneofdept', default=0,editable=True,help_text=_(u'Unless the large enterprise </br>does not recommend using this configuration'))
    isHidden=models.IntegerField(_(u'Is it hidden?'),null=True,blank=True, db_column='ishidden',default=0,editable=True)  #由于时段不能删除，此字段可用于隐藏
#	TimeZoneMins=models.IntegerField(_(u'Time (minutes)'),null=True,blank=True, default=0,editable=False)
    DelTag = models.IntegerField(_(u'logout mark'),default=0, editable=False, db_column='deltag',null=True, blank=True)
    Holiday=models.BooleanField(_('Rest on Holidays'),null=True,default=True, db_column='holiday',blank=True,editable=True,help_text=_(u'temporary scheduling is not subject </br>to this control'))





    def __unicode__(self):
        return u"%s"%(u"%s"%(self.SchName))
    def __str__(self):
        return u"%s"%(u"%s"%(self.SchName))
    def save(self):
        cache.delete("%s_schclasses"%(settings.UNIT))
        if self.SchclassID:
            cache.delete("%s_iclock_schclass_%s"%(settings.UNIT,self.SchclassID))

        super(SchClass,self).save()

    def Dept(self): #cached employee
        d=department.objByID(self.TimeZoneOfDept)
        if d:return d
        else:return _(u'All departments')

    @staticmethod
    def objByID(id):
        sch=cache.get("%s_iclock_schclass_%s"%(settings.UNIT, id))
        if sch: return sch
        try:
            schClasses=SchClass.GetSchClasses(id)
        except Exception as e:
            print ("schclass=",id,e)
            schClasses=[]
        e={}
        if schClasses:
            e=schClasses[0]
            cache.set("%s_iclock_schclass_%s"%(settings.UNIT,id),e)
        return e



    @staticmethod
    def FindSchClassByID(SchId):

        #sch=SchClass.GetSchClasses()
        #for i in range(len( sch)):
        #	if sch[i]['schClassID']==SchId:
        #		return i
        #return -1
        return SchClass.objByID(SchId)
    def GetTimeZoneMins(self):
        schid=self.SchclassID
        #try:
        #	j=self.FindSchClassByID(schid)
        #except Exception as e:
        #	print "99999",e
        #if j==-1:return 0
        #sch=self.GetSchClasses()[j]
        sch=SchClass.FindSchClassByID(schid)
        r=sch['TimeZoneMins']
        if sch['IsCalcRest']:
            r=r-sch['RestMins']
        return r

    @staticmethod
    def GetSchClasses(schID=None,User=None):
        #AttRule=AttParam.LoadAttRule()
        if not schID:
            #sch=cache.get("%s_schclasses"%(settings.UNIT))
            #if sch:	return sch
            if User and (not User.is_superuser):
                schClass=SchClass.objects.filter(Q(TimeZoneOfDept=0)|Q(TimeZoneOfDept__isnull=True)|Q(TimeZoneOfDept=User.AutheTimeDept)).exclude(DelTag=1).order_by('-SchclassID')
            else:
                schClass=SchClass.objects.all().exclude(DelTag=1).order_by('-SchclassID')
        else:
            schClass=SchClass.objects.filter(SchclassID=schID)
        ss={}
        re=[]
        for sch in schClass:
            sch_st = sch.StartTime
            sch_et = sch.EndTime
            # 解决StartTime，EndTime正常保存为09:00:00，sql server保存为1900-01-01 09:00:00.000，导致checkTime计算的时间不同一影响班次保存结果的问题。
            if type(sch_st)==datetime.datetime:
                sch_st = sch_st.time()
            if type(sch_et)==datetime.datetime:
                sch_et = sch_et.time()
            sch.StartTime=checkTime(sch_st)
            sch.EndTime=checkTime(sch_et)
            if sch.CheckInTime1!=None:
                sch.CheckInTime1=checkTime(sch.CheckInTime1)
            if sch.CheckInTime2!=None:
                sch.CheckInTime2=checkTime(sch.CheckInTime2)
            if sch.CheckOutTime1!=None:
                sch.CheckOutTime1=checkTime(sch.CheckOutTime1)
            if sch.CheckOutTime2!=None:
                sch.CheckOutTime2=checkTime(sch.CheckOutTime2)

            ss={'TimeZone':{'StartTime':sch.StartTime,'EndTime':sch.EndTime},
                'schClassID':sch.SchclassID,
                'SchName':sch.SchName,
                'MustClockIn':0,
                'MustClockOut':0,
                'MinsLate':sch.LateMinutes,
                'MinsEarly':sch.EarlyMinutes,
                'Color':16715535,
                'WorkDay':1,#折算工作日
                'WorkMins':0,               #sch.WorkMins,\                          #not used
                'OverTime':0,
                'Intime':{'StartTime':0,'EndTime':0,'CheckInTime1':120,'CheckInTime2':120},
                'Outtime':{'StartTime':0,'EndTime':0,'CheckOutTime1':120,'CheckOutTime2':120},
                'SchID':sch.SchclassID,
                'IsCalcRest':0,
                'StartRestTime':checkTime(datetime.time(0,0,0)),
                'EndRestTime':checkTime(datetime.time(0,0,0,)),
                'StartRestTime1':checkTime(datetime.time(0,0,0)),
                'EndRestTime1':checkTime(datetime.time(0,0,0,)),
                'RestTime':0,
                'TimeZoneType':0,
                'BeforeInMins':120,
                'AfterInMins':120,
                'IsCalcOverTime':0,
                'OverTimeMins':60,
                'IsCalcComeOverTime':0,
                'ComeOverTimeMins':60,

                'AsComeOverTimeMins': sch.AsComeOverTimeMins or 0,#该间隔记加班时长
                'AsOverTimeMins': sch.AsOverTimeMins or 0,#
                'MaxComeOverTimeMins': sch.MaxComeOverTimeMins or 0,#最大加班时长
                'MaxOverTimeMins': sch.MaxOverTimeMins or 0,#
                'TotalOverTimeMins': sch.TotalOverTimeMins or 0,#总最大加班时长

                #'NextDay':1,
                'NextDay':0,   #与数据库的数据差1
                'TimeZoneMins':0,
                'RestMins':0,
                'TimeZoneOfDept':0,
                'Holiday':sch.Holiday,
                'DelTag':sch.DelTag
            }

            if sch.CheckIn==1:
                ss['MustClockIn']=1
            if sch.CheckOut==1:
                ss['MustClockOut']=1

            #if sch.LateMinutes!=None:	#以时段为准
            #	ss['MinsLate']=sch.LateMinutes
            #else:
            #	ss['MinsLate']=AttRule['MinsLate']
            #if sch.EarlyMinutes!=None:
            #	ss['MinsEarly']=sch.EarlyMinutes
            #else:
            #	ss['MinsEarly']=AttRule['MinsEarly']

            if sch.Color!=None:
                ss['Color']=sch.Color


            try:
                if sch.IsCalcRest!=None:
                    ss['IsCalcRest']=sch.IsCalcRest

                if ss['IsCalcRest']==1:
                    if sch.StartRestTime!=None:                              #add 2009.08.05
                        sch.StartRestTime=checkTime(sch.StartRestTime)
                        ss['StartRestTime']=sch.StartRestTime
                    if sch.EndRestTime!=None:
                        sch.EndRestTime=checkTime(sch.EndRestTime)

                        ss['EndRestTime']=sch.EndRestTime
                        if ss['EndRestTime']<ss['StartRestTime']:
                            ss['EndRestTime']=ss['EndRestTime']+datetime.timedelta(days=1)
                    try:
                        if sch.StartRestTime1!=None:
                            sch.StartRestTime1=checkTime(sch.StartRestTime1)
                            ss['StartRestTime1']=sch.StartRestTime1

                        if sch.EndRestTime1!=None:
                            sch.EndRestTime1=checkTime(sch.EndRestTime1)
                            ss['EndRestTime1']=sch.EndRestTime1

                    except:
                        pass

                    if (sch.StartRestTime==checkTime(datetime.time(0,0,0))) and (sch.EndRestTime==checkTime(datetime.time(0,0,0))):
                        ss['IsCalcRest']=0
            except:
                pass

            if ss['IsCalcRest']==1:
                ms=ss['EndRestTime']-ss['StartRestTime']+(ss['EndRestTime1']-ss['StartRestTime1'])
                ss['RestMins']=ms.days*24*60+ms.seconds/60
                if ss['RestMins']>240:	#说明设置有问题
                    ss['IsCalcRest']=0
                    ss['RestMins']=0
            try:
                if sch.NextDay!=None:
                    ss['NextDay']=int(sch.NextDay)-1
            except:
                pass
            if ss['NextDay']<0 or ss['NextDay']>2:
                ss['NextDay']=0

            if ss['TimeZone']['EndTime']<ss['TimeZone']['StartTime']:
                if ss['NextDay']<1:
                    ss['NextDay']=1
            ss['TimeZone']['EndTime']=ss['TimeZone']['EndTime']+datetime.timedelta(days=ss['NextDay'])
            ms=ss['TimeZone']['EndTime']-ss['TimeZone']['StartTime']
            ss['TimeZoneMins']=ms.days*24*60+ms.seconds/60
            ss['WorkMins']=ss['TimeZoneMins']-ss['RestMins']
            if sch.WorkDay!=None:
                ss['WorkDay']=sch.WorkDay
            try:
                if sch.CheckInMins1!=None:
                    ss['Intime']['StartTime']=ss['TimeZone']['StartTime']-datetime.timedelta(minutes=sch.CheckInMins1)
                    ss['Intime']['CheckInTime1']=sch.CheckInMins1
                else:
                    ss['Intime']['StartTime']=ss['TimeZone']['StartTime']-datetime.timedelta(hours=2)
            except:
                ss['Intime']['StartTime']=ss['TimeZone']['StartTime']-datetime.timedelta(hours=2)

            try:
                if sch.CheckInMins2!=None:
                    ss['Intime']['EndTime']=ss['TimeZone']['StartTime']+datetime.timedelta(minutes=sch.CheckInMins2)
                    ss['Intime']['CheckInTime2']=sch.CheckInMins2
                else:
                    ss['Intime']['EndTime']=ss['TimeZone']['StartTime']+(ss['TimeZone']['EndTime']-ss['TimeZone']['StartTime'])/2
            except:
                ss['Intime']['EndTime']=ss['TimeZone']['StartTime']+(ss['TimeZone']['EndTime']-ss['TimeZone']['StartTime'])/2
            try:
                if sch.CheckOutMins1!=None:
                    ss['Outtime']['StartTime']=ss['TimeZone']['EndTime']-datetime.timedelta(minutes=sch.CheckOutMins1)
                    ss['Outtime']['CheckOutTime1']=sch.CheckOutMins1
                else:
                    ss['Outtime']['StartTime']=ss['TimeZone']['EndTime']-(ss['TimeZone']['EndTime']-ss['TimeZone']['StartTime'])/2
            except:
                ss['Outtime']['StartTime']=ss['TimeZone']['EndTime']-(ss['TimeZone']['EndTime']-ss['TimeZone']['StartTime'])/2
            try:
                if sch.CheckOutMins2!=None:
                    ss['Outtime']['EndTime']=ss['TimeZone']['EndTime']+datetime.timedelta(minutes=sch.CheckOutMins2)
                    ss['Outtime']['CheckOutTime2']=sch.CheckOutMins2
                else:
                    ss['Outtime']['EndTime']=ss['TimeZone']['EndTime']+datetime.timedelta(hours=6)
            except:
                ss['Outtime']['EndTime']=ss['TimeZone']['EndTime']+datetime.timedelta(hours=6)
            try:
                if sch.IsCalcOverTime:
                    ss['IsCalcOverTime']=sch.IsCalcOverTime
                    if sch.OverTimeMins!=None:
                        ss['OverTimeMins']=sch.OverTimeMins

                #else:
                #	ss['IsCalcOverTime']=AttRule['OutOverTime']
                #	ss['OverTimeMins']=AttRule['MinsOutOverTime']
            except:
                pass
            try:
                if sch.IsCalcComeOverTime:
                    ss['IsCalcComeOverTime']=sch.IsCalcComeOverTime
                    if sch.ComeOverTimeMins!=None:
                        ss['ComeOverTimeMins']=sch.ComeOverTimeMins
                #else:
                #	ss['IsCalcComeOverTime']=AttRule['ComeOverTime']
                #	ss['ComeOverTimeMins']=AttRule['MinsComeOverTime']
            except:
                pass
            try:
                if sch.TimeZoneOfDept!=None:
                    ss['TimeZoneOfDept']=sch.TimeZoneOfDept
            except:
                pass
            try:
                if sch.TimeZoneType!=None:
                    ss['TimeZoneType']=sch.TimeZoneType
            except:
                pass

            try:
                if sch.BeforeInMins!=None:
                    ss['BeforeInMins']=sch.BeforeInMins
            except:
                pass
            try:
                if sch.AfterInMins!=None:
                    ss['AfterInMins']=sch.AfterInMins
            except:
                pass
            #if ss['TimeZoneType']:
            #	ss['Intime']['StartTime']=ss['TimeZone']['StartTime']-datetime.timedelta(minutes=ss['BeforeInMins'])
            #	ss['Intime']['EndTime']=ss['TimeZone']['StartTime']+datetime.timedelta(minutes=ss['AfterInMins'])
            #	ss['Outtime']['StartTime']=ss['TimeZone']['StartTime']
            #	ss['Outtime']['EndTime']=ss['TimeZone']['EndTime']+datetime.timedelta(hours=6)
            #


            t=ss.copy()
            re.append(t)
        schClasses=copy.deepcopy(re)
#		if not schID:
#		    cache.set("%s_schclasses"%(settings.UNIT),schClasses)
        return schClasses




    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True,'frozen': True},
            {'name':'SchID','index':'SchclassID','width':60,'sortable':True,'label':u"%s"%(u"%s"%(_(u'Numbering')))},
            {'name':'SchName','width':120,'index':'SchName','sortable':True,'label':u"%s"%(SchClass._meta.get_field('SchName').verbose_name),'frozen': True},
            {'name':'Data','width':60,'sortable':False,'label':u"%s"%(u"%s"%(_(u'data')))},
            {'name':'StartTime','width':70,'label':u"%s"%(SchClass._meta.get_field('StartTime').verbose_name)},
            {'name':'EndTime','width':70,'label':u"%s"%(SchClass._meta.get_field('EndTime').verbose_name)},
            {'name':'LateMinutes','width':60,'label':u"%s"%(_(u'minute'))},
            {'name':'EarlyMinutes','width':80,'label':u"%s"%(_(u'minute'))},
            {'name':'CheckIn','width':70,'label':u"%s"%(SchClass._meta.get_field('CheckIn').verbose_name)},
            {'name':'CheckOut','width':70,'label':u"%s"%(SchClass._meta.get_field('CheckOut').verbose_name)},
            {'name':'CheckInMins1','width':60,'label':u"%s"%(SchClass._meta.get_field('CheckInMins1').verbose_name)},
            {'name':'CheckInMins2','width':60,'label':u"%s"%(SchClass._meta.get_field('CheckInMins2').verbose_name)},
            {'name':'CheckOutMins1','width':60,'label':u"%s"%(SchClass._meta.get_field('CheckOutMins1').verbose_name)},
            {'name':'CheckOutMins2','width':60,'label':u"%s"%(SchClass._meta.get_field('CheckOutMins2').verbose_name)},
            {'name':'IsCalcComeOverTime','width':80,'label':u"%s"%(SchClass._meta.get_field('IsCalcComeOverTime').verbose_name)},
            {'name':'IsCalcOverTime','width':80,'label':u"%s"%(SchClass._meta.get_field('IsCalcOverTime').verbose_name)},
            {'name':'Holiday','width':80,'label':u"%s"%(SchClass._meta.get_field('Holiday').verbose_name)},

            {'name':'AutoBind','hidden':True},
            {'name':'IsCalcRest','width':80,'label':u"%s"%(SchClass._meta.get_field('IsCalcRest').verbose_name)},
            {'name':'StartRestTime','width':100,'label':u"%s"%(SchClass._meta.get_field('StartRestTime').verbose_name)},
            {'name':'EndRestTime','width':100,'label':u"%s"%(SchClass._meta.get_field('EndRestTime').verbose_name)},
            {'name':'StartRestTime1','width':100,'label':u"%s"%(SchClass._meta.get_field('StartRestTime1').verbose_name)},
            {'name':'EndRestTime1','width':100,'label':u"%s"%(SchClass._meta.get_field('EndRestTime1').verbose_name)},
            {'name':'TimeZoneType','width':80,'label':u"%s"%(SchClass._meta.get_field('TimeZoneType').verbose_name)},
            #{'name':'WorkDay','width':100,'label':u"%s"%(SchClass._meta.get_field('WorkDay').verbose_name)},
            {'name':'TimeZoneMins','sortable':False,'width':80,'label':u"%s"%(_(u'Time (minutes)'))},
            {'name':'Color','sortable':False,'width':100,'label':u"%s"%(SchClass._meta.get_field('Color').verbose_name)},
            {'name':'TimeZoneOfDept','sortable':False,'width':100,'label':u"%s"%(SchClass._meta.get_field('TimeZoneOfDept').verbose_name)},
            ]
    @staticmethod
    def HeaderModels():
        return [
            {'startColumnName': 'StartTime', 'numberOfColumns': 2, 'titleText': '<em>%s</em>'%u"%s"%(_(u'Work Time'))},
            {'startColumnName': 'LateMinutes', 'numberOfColumns': 1, 'titleText': '<em>'+u"%s"%(SchClass._meta.get_field('LateMinutes').verbose_name)+'</em>'},
            {'startColumnName': 'EarlyMinutes', 'numberOfColumns': 1, 'titleText': '<em>'+u"%s"%(SchClass._meta.get_field('EarlyMinutes').verbose_name)+'</em>'},
            {'startColumnName': 'CheckInMins1', 'numberOfColumns': 2, 'titleText': '<em>%s(%s)</em>'%(u"%s"%(_(u'Sign In Time range')),u"%s"%(_(u'Minute')))},
            {'startColumnName': 'CheckOutMins1', 'numberOfColumns': 2, 'titleText': '<em>%s(%s)</em>'%(u"%s"%(_(u'Sign Out Time range')),u"%s"%(_(u'Minute')))},

               ]
    class Admin:
        search_fields = ['SchName']
    class Meta:
        db_table = 'schclass'
        verbose_name=_('shift time-table')
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')


#class SchClassForm(forms.Form):
#    TimeZoneType = forms.ChoiceField(label='弹性上班类型', choices=(('0', '不是弹性上班'), ('1', '是弹性上班')), widget=forms.RadioSelect(), initial='0')
#    class Meta:
#        model = SchClass
#
#
#
#class SchClassAdmin(admin.ModelAdmin):
#    form = SchClassForm
#
#admin.site.register(SchClass, SchClassAdmin)
#

def GetSchClassByID(SchId,  StartTime, EndTime):
    #schi=FindSchClassByID(SchId)
    #schClasses=GetSchClasses()
    #AttRule=LoadAttRule()
    sch=SchClass.objByID(SchId)
    if not sch:
        print ("schid=%s not found"%(SchId))    #should throw exceptino
        return {}
    d=trunc(StartTime)
#	if AttRule['TwoDay']==1:
#		d=trunc(EndTime)

#	result=copy.deepcopy(schClasses[schi])
    result=copy.deepcopy(sch)
    d2=result['TimeZone']['StartTime']
    d=d-datetime.datetime(d2.year,d2.month,d2.day)
    result['TimeZone']['StartTime']=result['TimeZone']['StartTime']+d
    result['TimeZone']['EndTime']=result['TimeZone']['EndTime']+d
    if result['TimeZone']['EndTime']<=result['TimeZone']['StartTime']:
        result['TimeZone']['EndTime']=result['TimeZone']['EndTime']+datetime.timedelta(days=1)
    result['Intime']['StartTime']=result['TimeZone']['StartTime']-datetime.timedelta(minutes=result['Intime']['CheckInTime1'])
    result['Intime']['EndTime']=result['TimeZone']['StartTime']+datetime.timedelta(minutes=result['Intime']['CheckInTime2'] + 1)
    result['Outtime']['StartTime']=result['TimeZone']['EndTime']-datetime.timedelta(minutes=result['Outtime']['CheckOutTime1'])
    result['Outtime']['EndTime']=result['TimeZone']['EndTime']+datetime.timedelta(minutes=result['Outtime']['CheckOutTime2'],seconds=59)
    # result['Intime']['StartTime']=result['Intime']['StartTime']+d
    # result['Intime']['EndTime']=result['Intime']['EndTime']+d
    # if result['Intime']['StartTime']>result['TimeZone']['StartTime']:
    #     result['Intime']['StartTime']=result['Intime']['StartTime']-datetime.timedelta(days=1)
    # if result['Intime']['EndTime']<result['Intime']['StartTime']:
    #     result['Intime']['EndTime']=result['Intime']['EndTime']+datetime.timedelta(days=1)
    # result['Outtime']['StartTime']=result['Outtime']['StartTime']+d
    # result['Outtime']['EndTime']=result['Outtime']['EndTime']+d
    if result['IsCalcRest']==1:
        result['StartRestTime']=result['StartRestTime']+d
        result['EndRestTime']=result['EndRestTime']+d
        if result['EndRestTime']<=result['StartRestTime']:
            result['EndRestTime']=result['EndRestTime']+datetime.timedelta(days=1)
        if result['StartRestTime']<result['TimeZone']['StartTime']:
            result['StartRestTime']=result['StartRestTime']+datetime.timedelta(days=1)
        if result['StartRestTime']+datetime.timedelta(days=1)<result['TimeZone']['StartTime']:
            result['StartRestTime']=result['StartRestTime']+datetime.timedelta(days=1)
        if result['EndRestTime']+datetime.timedelta(days=1)<result['TimeZone']['EndTime']:
            result['EndRestTime']=result['EndRestTime']+datetime.timedelta(days=1)
        if result['EndRestTime']<result['TimeZone']['StartTime'] or result['StartRestTime']<result['TimeZone']['StartTime'] or result['StartRestTime']>result['TimeZone']['EndTime'] or result['EndRestTime']>result['TimeZone']['EndTime']:
            result['IsCalcRest']=0

        result['StartRestTime1']=result['StartRestTime1']+d
        result['EndRestTime1']=result['EndRestTime1']+d
        if result['EndRestTime1']<=result['StartRestTime1']:
            result['EndRestTime1']=result['EndRestTime1']+datetime.timedelta(days=1)
        if result['StartRestTime1']<result['TimeZone']['StartTime']:
            result['StartRestTime1']=result['StartRestTime1']+datetime.timedelta(days=1)
        if result['StartRestTime1']+datetime.timedelta(days=1)<result['TimeZone']['StartTime']:
            result['StartRestTime1']=result['StartRestTime1']+datetime.timedelta(days=1)
        if result['EndRestTime1']+datetime.timedelta(days=1)<result['TimeZone']['EndTime']:
            result['EndRestTime1']=result['EndRestTime1']+datetime.timedelta(days=1)
        if result['EndRestTime1'] < result['StartRestTime1']:
            result['EndRestTime1'] = result['EndRestTime1']+datetime.timedelta(days=1)


    #if result['Outtime']['EndTime']<=result['Outtime']['StartTime']:
    #    result['Outtime']['EndTime']=result['Outtime']['EndTime']+datetime.timedelta(days=1)

    t=result['TimeZone']['EndTime']-result['TimeZone']['StartTime']
    if result['IsCalcRest']==1:
        #rtm=(result['EndRestTime']-result['StartRestTime'])+(result['EndRestTime1']-result['StartRestTime1'])
        #t=t-rtm
        result['RestTime']=result['RestMins']
    return result




class UserUsedSClasses(models.Model):
#	ID=models.AutoField(primary_key=True,null=False, editable=False)
    UserID = models.IntegerField("employee", db_column='userid',null=True,blank=True)
    DeptID = models.IntegerField("department", db_column='deptid',null=True,blank=True)
    group_id = models.IntegerField("group", db_column='group_id', null=True, blank=True)
    ScheType = models.IntegerField(null=False,db_column='schetype',default=0,editable=False)  # 0-人员排班,1-部门排班
    SchId=models.IntegerField(null=True,db_column='schid',editable=False)
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID)
        except:
            return None
    def department(self): #cached employee
        try:
            return department.objByID(self.DeptID)
        except:
            return None

    def schclass_name(self):
        try:
            sch = SchClass.objByID(self.SchId)
            return sch['SchName']
        except:
            return ''

    @staticmethod
    def colModels(schetype=0):
        if schetype == 0:
            return [
                {'name':'id','hidden':True},
                {'name':'PIN','sortable':False,'width':120,'label':u"%s"%(_('PIN'))},
                {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
                {'name':'DeptNumber','sortable':False,'width':100,'label':u"%s"%(_(u'Department Number'))},
                {'name':'DeptName','sortable':False,'width':140,'label':u"%s"%(_('department name'))},
                {'name':'SchId','sortable':False,'width':80,'label':u"%s"%(_('SchId'))}
                ]
        else:
            return [
                {'name':'id','hidden':True},
                {'name':'DeptNumber','sortable':False,'width':100,'label':u"%s"%(_(u'Department Number'))},
                {'name':'DeptName','sortable':False,'width':140,'label':u"%s"%(_('department name'))},
                {'name':'SchId','sortable':False,'width':80,'label':u"%s"%(_('SchId'))}
                ]

    class Admin:
        pass

    class Meta:
        db_table = 'userusedsclasses'
        verbose_name = _('intelligent scheduling')
        unique_together = (("UserID", "DeptID", "ScheType", "SchId", 'group_id'),)
        default_permissions = ()


class AuditedExc(models.Model):
    AEID=models.AutoField(primary_key=True,db_column='aeid',null=False, editable=False)
    UserID = models.ForeignKey("employee", db_column='userid', verbose_name=_('employee'),blank=True, on_delete=models.CASCADE)
    CheckTime = models.DateTimeField(_('CheckTime'), db_column='checktime',blank=False)
    UTime = models.DateTimeField(_('UTime'), db_column='utime',blank=False)
    NewExcID=models.SmallIntegerField(blank=True,db_column='newexcid',default=0)
    IsLeave=models.SmallIntegerField(db_column='isleave',blank=True)
    UName=models.CharField( max_length=20,db_column='uname',blank=True)
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    class Admin:
        pass
    class Meta:
        db_table = 'auditedexc'
        default_permissions = ()


class attCalcLog(models.Model):
    DeptID=models.IntegerField(db_column='deptid',null=True,blank=True, default=0)
    UserID = models.IntegerField(db_column='userid', blank=True)
    StartDate = models.DateTimeField( db_column='startdate',blank=True,null=True)
    EndDate = models.DateTimeField(db_column='enddate',blank=True)
    OperTime = models.DateTimeField(db_column='opertime',blank=True)
    Type=models.IntegerField(null=True,default=0,db_column='type',blank=True, editable=False)
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID)
        except:
            return None
    class Admin:
        pass
    class Meta:
        db_table = 'attcalclog'
        unique_together = (("DeptID","UserID","StartDate", "EndDate","Type"),)
        default_permissions=()

        permissions = (
                        ('sys_personal_setting', 'sys_personal_setting'),
                        ('sys_basic_setting','sys_basic_setting'),
                        ('sys_email_setting','sys_email_setting'),
                        ('sys_state_setting','sys_state_setting'),
                        ('sys_api_setting','sys_api_setting'),
                        ('sys_sms_setting','sys_sms_setting'),
                        ('sys_iapp_setting','sys_iapp_setting'),
                        ('sys_iapp_sign_setting', 'sys_iapp_sign_setting'),
                        ('sys_calc_setting','sys_calc_setting'),
                        ('sys_del_setting','sys_delete_setting'),
                        ('sys_pos_setting','sys_pos_setting'),
                        ('sys_sap_setting','sys_sap_setting'),
                        ('sys_acc_setting','sys_acc_setting'),
                        ('sys_common_setting','sys_common_setting'),
                        ('sys_autoexport_setting', 'sys_autoexport_setting'),
                        ('sys_backup_setting', 'sys_backup_setting'),
                        ('sys_antiepidemic_setting', 'sys_antiepidemic_setting'),
                        ('sys_visitors_setting', 'sys_visitors_setting'),
                        ('group_export', 'group export'),
                        #('sys_app_setting','sys_app_setting'),


            )


#统计结果用表-异常记录表
class AttException(models.Model):
    UserID = models.ForeignKey(employee, db_column='userid', verbose_name=_('employee'),blank=True, on_delete=models.CASCADE)
    StartTime = models.DateTimeField(_('StartTime'), db_column='starttime')
    EndTime = models.DateTimeField(_('EndTime'), db_column='endtime')
    ExceptionID=models.IntegerField(_('Exception'),null=True,db_column='exceptionid',blank=True,default=0)
    UserSpedayID=models.IntegerField(_('UserSpeday'),null=True,blank=True,db_column='userspedayid',default=0)
    AuditExcID=models.IntegerField(_('AuditExc'),null=True,blank=True,db_column='auditexcid',default=0)
    OldAuditExcID=models.IntegerField(_('OldAuditExc'),null=True,blank=True,db_column='oldauditexcid',default=0)
    OverlapTime=models.IntegerField(db_column='overlaptime',null=True,blank=True,default=0)        #排班时长
    TimeLong=models.IntegerField(_('TotalTimeLong'), db_column='timelong',null=True,blank=True,default=0)          #总时长
    InScopeTime=models.IntegerField(_('ValidateTimeLong'), db_column='inscopetime',null=True,blank=True,default=0)   #有效时长
    AttDate=models.DateTimeField(_('AttDate'),db_column='attdate',null=True,blank=True)
    OverlapWorkDayTail=models.IntegerField( db_column='overlapworkdaytail')
    OverlapWorkDay=models.FloatField(db_column='overlapworkday',null=True,default=1,blank=True)     #排班工作日
    schindex=models.IntegerField(db_column='schindex',null=True,blank=True,default=0)
    Minsworkday=models.IntegerField(db_column='minsworkday',null=True,blank=True,default=0)
    schid=models.IntegerField(db_column='schid',null=True,blank=True,default=0)

    def __unicode__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def __str__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'UserID','hidden':True},
            {'name':'DeptName','width':180,'sortable':False,'label':u"%s"%(_('department name'))},
            {'name':'PIN','index':'UserID__PIN','width':60,'label':u"%s"%(_('PIN'))},
            {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
            {'name':'AttDate','index':'AttDate','width':120,'label':u"%s"%(_('AttDate'))},
            {'name':'StartTime','sortable':False,'width':80,'label':u"%s"%(_('StartTime'))},
            {'name':'EndTime','sortable':False,'width':80,'label':u"%s"%(_('EndTime'))},
            {'name':'ExceptionID','sortable':False,'width':180,'label':u"%s"%(_('Exception'))},
            {'name':'TimeLong','sortable':False,'width':100,'label':u"%s"%(_('TotalTimeLong(min.)'))},
            {'name':'InScopeTime','sortable':False,'width':100,'label':u"%s"%(_('ValidateTimeLong(min.)'))},
            ]
    class Admin:
        search_fields = ['UserID__PIN']
    class Meta:
        verbose_name=_(u'Personal leave details')
        db_table='attexception'
        unique_together = (("UserID","AttDate", "StartTime"),)
        default_permissions = ()


#统计结果用表-记录状态表 作废
class attRecAbnormite(models.Model):
    UserID = models.ForeignKey(employee, db_column='userid', verbose_name=_("employee"), on_delete=models.CASCADE)
    checktime = models.DateTimeField(_('CheckTime'), db_column='checktime')
    CheckType = models.CharField(_('CheckType'),db_column='checktype', max_length=2)
    NewType = models.CharField(_('NewType'), db_column='newtype', max_length=2,null=True,blank=True)
    AbNormiteID=models.IntegerField(db_column='abnormiteid',  null=True,blank=True)
    SchID=models.IntegerField(_('Schclass'),db_column='schid',  null=True,blank=True)
    OP=models.IntegerField(_('Operation'),db_column='op',  null=True,blank=True)
    AttDate=models.DateTimeField(_('AttDate'),db_column='attdate',null=True,blank=True)
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('device'), null=True, blank=True, on_delete=models.CASCADE)
    Verify = models.IntegerField(_('verification'), db_column='verifycode', default=0, choices=VERIFYS)

    def getComVerifys(self):
        try:
            if self.SN:
                iclockObj=iclock.objects.get(SN=self.SN_id)
                if iclockObj.Authentication==2:
                    for i in range(len(COMVERIFYS)):
                        if self.Verify==COMVERIFYS[i][0]:
                            return COMVERIFYS[i][1].title()
                else:
                    return self.get_Verify_display()
            else:
                return ""
        except Exception as e:
            print (99999999,e)
            return ""#self.get_Verify_display()
    def __unicode__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def __str__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def Device(self):
        return getDevice(self.SN_id)
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'UserID','hidden':True},
            {'name':'DeptName','width':180,'sortable':False,'label':u"%s"%(_('department name'))},
            {'name':'PIN','sortable':False,'index':'UserID_PIN','width':120,'label':u"%s"%(_('PIN'))},
            {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
            {'name':'checktime','sortable':False,'index':'checktime','width':120,'label':u"%s"%(_('CheckTime'))},
            {'name':'Verify','width':80,'label':u"%s"%(_('Verification'))},
            {'name':'CheckType','width':80,'label':u"%s"%(_('CheckType'))},
            {'name':'NewType','sortable':False,'width':180,'label':u"%s"%(_('NewType'))},
            {'name':'AbNormiteID','sortable':False,'width':100,'label':u"%s"%(_('Memo.'))},
            {'name':'Device','sortable':False,'width':100,'label':u"%s"%(_('Device name'))},
            ]

    class Admin:
        list_display=('UserID','checktime','CheckType','AbNormiteID','NewType')
        search_fields = ['UserID__PIN']
    class Meta:
        verbose_name=_("attRecAbnormite")
        verbose_name_plural=verbose_name
        db_table='attrecabnormite'
        unique_together = (("UserID","checktime"),)
        default_permissions = ()


#		unique_together = (("UserID","AttDate", "checktime"),)


class attShifts(models.Model):
    UserID = models.ForeignKey("employee", db_column='userid',null=False, verbose_name=_("employee"), on_delete=models.CASCADE)
    SchIndex=models.IntegerField(db_column='schindex',  null=True,blank=True)
    AutoSch=models.SmallIntegerField(db_column='autosch',null=True,default=0,editable=False)
    AttDate = models.DateTimeField(_('AttDate'), db_column='attdate')
    #SchId=models.IntegerField(_('SchName'),db_column='SchId',  null=True,blank=True)
    SchId=models.ForeignKey("SchClass", verbose_name=_('time-table class'), db_column='schid', null=True,default=-1,blank=True, on_delete=models.CASCADE)

    ClockInTime = models.DateTimeField(_('on duty time'), db_column='clockintime')
    ClockOutTime = models.DateTimeField(_('off duty time'), db_column='clockouttime')
    StartTime = models.DateTimeField(_('on duty'), db_column='starttime',null=True,blank=True)
    EndTime = models.DateTimeField(_('off duty'), db_column='endtime',null=True,blank=True)
    WorkDay=models.FloatField(_('WorkDay'),db_column='workday',null=True,blank=True)
    RealWorkDay=models.FloatField(_('RealWorkDay'),db_column='realworkday',null=True,default=0,blank=True)
    NoIn=models.SmallIntegerField(_('NoIn'),db_column='noin',null=True,blank=True)
    NoOut=models.SmallIntegerField(_('NoOut'),db_column='noout',null=True,blank=True)
    Late = models.FloatField(_('LateTimes'), db_column='late',null=True,blank=True)
    Early = models.FloatField(_('EarlyTimes'), db_column='early',null=True,blank=True)
    Absent = models.IntegerField(_('Absent'), db_column='absent',null=True,blank=True)
    OverTime = models.FloatField(_('OverTime'), db_column='overtime',null=True,blank=True)
    WorkTime = models.IntegerField(_('WorkTime'), db_column='worktime',null=True,blank=True)
    ExceptionID=models.CharField(_('Exception'),db_column='exceptionid',  null=True,blank=True,max_length=10)
    Symbol = models.CharField(_('Symbol'), db_column='symbol', max_length=20,null=True,blank=True)
    MustIn=models.SmallIntegerField(_('MustIn'),db_column='mustin',null=True,blank=True)
    MustOut=models.SmallIntegerField(_('MustOut'),db_column='mustout',null=True,blank=True)
    OverTime1=models.IntegerField(_('OverTime1'),db_column='overtime1',  null=True,blank=True)
    WorkMins = models.IntegerField(_('WorkMins'), db_column='workmins',null=True,blank=True)
    SSpeDayNormal=models.FloatField(_('SSpeDayNorma'),db_column='sspedaynormal',null=True,blank=True)
    SSpeDayWeekend=models.FloatField(_('SSpeDayWeekend'),db_column='sspedayweekend',null=True,blank=True)
    SSpeDayHoliday=models.FloatField(_('SSpeDayHoliday'),db_column='sspedayholiday',null=True,blank=True)
    AttTime = models.IntegerField(_('AttTime'), db_column='atttime',null=True,blank=True)
    SSpeDayNormalOT=models.FloatField(_('SSpeDayNormalOT'),db_column='sspedaynormalot',null=True,blank=True)
    SSpeDayWeekendOT=models.FloatField(_('SSpeDayWeekendOT'),db_column='sspedayweekendot',null=True,blank=True)
    SSpeDayHolidayOT=models.FloatField(_('SSpeDayHolidayOT'),db_column='sspedayholidayot',null=True,blank=True)
    AbsentMins=models.IntegerField(db_column='absentmins',  null=True,blank=True)
    AttChkTime = models.CharField(db_column='attchktime', max_length=10,null=True,blank=True)
    AbsentR=models.FloatField(db_column='absentr',null=True,blank=True)
    ScheduleName = models.CharField( db_column='schedulename', max_length=20,null=True,blank=True)
    MinsDay = models.IntegerField(null=True,db_column='minsday',blank=True) #因为时段中取消了记为多少工作日，在这里保存一天的排班时长，单位分钟，折算工作日用

    IsConfirm=models.SmallIntegerField(db_column='isconfirm',null=True,blank=True)
    IsRead=models.SmallIntegerField(db_column='isread',null=True,blank=True)

    overtime_id = models.IntegerField(db_column='overtime_id', null=True, blank=True, default=0) # 区分同一天的多条加班数据，避免sqlserver下SchId值出现多个Null触发联合约束的问题

    def __unicode__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def __str__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'UserID','hidden':True},
            {'name':'DeptName','width':180,'sortable':False,'label':u"%s"%(_('department name'))},
            {'name':'PIN','index':'UserID_PIN','width':120,'label':u"%s"%(_('PIN'))},
            {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
            {'name':'AttDate','index':'AttDate','width':120,'label':u"%s"%(_('AttDate'))},
            {'name':'SchId','width':80,'label':u"%s"%(_('SchId'))},
            {'name':'Late','width':80,'label':u"%s"%(_('Late'))},
            {'name':'Early','sortable':False,'width':180,'label':u"%s"%(_('Early'))},
            {'name':'StartTime','sortable':False,'width':100,'label':u"%s"%(_('noin'))},
            {'name':'EndTime','sortable':False,'width':100,'label':u"%s"%(_('noout'))},
            {'name':'Absent','sortable':False,'width':100,'label':u"%s"%(_('Absent'))},
            ]

    class Admin:
        pass
    class Meta:
        db_table='attshifts'
        unique_together = (("UserID","AttDate", "SchId", "overtime_id"),)
        default_permissions=()

class attpriReport(models.Model):
    UserID = models.ForeignKey(employee, db_column='userid', verbose_name=_("employee"), on_delete=models.CASCADE)
    AttDate = models.DateTimeField(_('AttDate'), db_column='attdate',null=False,blank=False)
    AttChkTime = models.CharField( max_length=100,null=True,db_column='attchktime',blank=True)
    AttAddChkTime = models.CharField( max_length=100,null=True,db_column='attaddchktime',blank=True)
    AttLeaveTime = models.CharField( max_length=100,null=True,db_column='attleavetime',blank=True)
    SchName = models.CharField( max_length=100,null=True,db_column='schname',blank=True)
    OP=models.IntegerField(_('Operation'),db_column='op',  null=True,blank=True)
    Reserved = models.CharField( max_length=50,db_column='reserved',null=True,blank=True)

    def __unicode__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def __str__(self):
        return u"%s"%(u"%s"%(self.UserID))
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'UserID','hidden':True},
            {'name':'DeptName','width':180,'sortable':False,'label':u"%s"%(_('department name'))},
            {'name':'PIN','index':'UserID_PIN','width':120,'label':u"%s"%(_('PIN'))},
            {'name':'EName','sortable':False,'width':80,'label':u"%s"%(_('EName'))},
            {'name':'AttDate','index':'AttDate','width':120,'label':u"%s"%(_('AttDate'))},
            {'name':'SchName','width':80,'label':u"%s"%(_('SchName'))},
            {'name':'AttChkTime','width':80,'label':u"%s"%(_('Original Attendance'))},
            {'name':'AttAddChkTime','sortable':False,'width':180,'label':u"%s"%(_('Add Attendance'))},
            {'name':'AttLeaveTime','sortable':False,'width':100,'label':u"%s"%(_('Leave'))},
            ]

    class Admin:
        list_display=('UserID','AttDate')
    class Meta:
        verbose_name=_("attpriReport")
        verbose_name_plural=verbose_name
        db_table='attprireport'
        unique_together = (("UserID","AttDate"),)
        default_permissions = ()


#
#ACCOUNTS_TYPE=(
#	(99,_('All')),
#	(1,_('ReCaluate Report')),
#	(2,_('special leave')),
#	(3,_('Forget Checkin/out')),
#	(4,_('OverTime')),
#	(5,_('Shifts for Employee')),
#	)
#STATUS=(
#	(99,_('No Locking')),
#	(1,_('Locked')),
#	(2,_('UnLocked')),
#	)
#此表建议作废
#class accounts(models.Model):
#	StartTime= models.DateTimeField(_('beginning time'), null=False,default=nextDay(), blank=True,editable=True)
#	EndTime = models.DateTimeField(_('ending time'), null=False, default=endOfDay(nextDay()),blank=True,editable=True)
#	Type=models.IntegerField(_('Posting Type'), null=False,default=0,blank=True, editable=True,choices=ACCOUNTS_TYPE)         #如99表示全部 1表示重新统计报表,2请假,3忘签到签退,4加班单,5员工排班:(正常排班、临时排班、清除临时排班)
#	Status=models.IntegerField(_('Status'),null=True,blank=True,default=0,choices=STATUS)                     #99表示未锁,1表示锁定 2表示解锁
#	Reserved = models.CharField(_('Reserved'), max_length=20, null=True, blank=True, editable=False)
#	User = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('administrator'), null=True, blank=True, editable=False, on_delete=models.CASCADE)
#	@staticmethod
#	def colModels():
#		return [
#			{'name':'id','hidden':True},
#			{'name':'StartTime','width':150,'label':u"%s"%(accounts._meta.get_field('StartTime').verbose_name)},
#			{'name':'EndTime','width':150,'label':u"%s"%(accounts._meta.get_field('EndTime').verbose_name)},
#			{'name':'Type','sortable':False,'width':80,'label':u"%s"%(accounts._meta.get_field('Type').verbose_name)},
#			{'name':'Status','sortable':False,'width':80,'label':u"%s"%(accounts._meta.get_field('Status').verbose_name)},
#			{'name':'Reserved','hidden':True},
#			{'name':'User','hidden':True}
#			]
#	class Meta:
#		verbose_name=_("accounts")
#		verbose_name_plural=verbose_name
#		db_table='accounts'
#		unique_together = (("StartTime","EndTime","Type","Status"),)



PUBLISHED=(
(0, "NOT SHARE"),
(1, "SHARE READ"),
(2, "SHARE READ/WRITE")
)
#用于保存每个人的统计日期
class calcDate(models.Model):
    UserID = models.ForeignKey("employee",db_column='userid_id', on_delete=models.CASCADE)
    ItemValue=models.TextField(null=True,db_column='itemvalue')
    class Admin:
        pass
    class Meta:
        verbose_name=_("item define")
        verbose_name_plural=verbose_name
        db_table='calcdate'
        default_permissions = ()
        #unique_together = ("UserID",)
    def save(self, *args, **kwargs):
        super(calcDate, self).save(*args, **kwargs)


#用以保存不同管理员设置的个性配置
class ItemDefine(models.Model):
    ItemName=models.CharField(max_length=100,null=False,db_column='itemname')
    ItemType=models.CharField(max_length=20,null=True,db_column='itemtype')
    Author=models.ForeignKey(settings.AUTH_USER_MODEL, null=True,db_column='author_id', on_delete=models.CASCADE)
    ItemValue=models.TextField(max_length=100*1024,null=True,db_column='itemvalue')
    Published=models.IntegerField(null=True, choices=PUBLISHED, default=0,db_column='published')
    class Admin:
        pass
    class Meta:
        verbose_name=_("item define")
        verbose_name_plural=verbose_name
        db_table='itemdefine'
        default_permissions=()

        #unique_together = (("ItemName","Author","ItemType"),)
#		permissions = (
#					('Forget_transaction','operate Forget to clock in and out'),
#					('report_transaction','operate reports'),
#					('reCalcaluteReport_transaction','ReCalculate Reports'),
#					('definedReport_user_speday','user-defined report'),
#		)


#	##开门时间段类别设置表##
#class ACTimeZones(models.Model):
#	TimeZoneID=models.IntegerField(_('TimeZoneID'),primary_key=True,null=False,editable=True,choices=TIMEZONES_CHOICES)
#	Name=models.CharField(_('ACTimeZones name'), max_length=30,null=False,blank=True)
#	SunStart = models.TimeField(_('Sunday StartTime'),null=True, blank=True)
#	SunEnd = models.TimeField(_('Sunday EndTime'),null=True, blank=True)
#	MonStart=models.TimeField(_('Monday StartTime'),null=True,blank=True)
#	MonEnd=models.TimeField(_('Monday EndTime'),null=True,blank=True)
#	TuesStart=models.TimeField(_('Tuesday StartTime'),null=True,blank=True)
#	TuesEnd=models.TimeField(_('Tuesday EndTime'),null=True,blank=True)
#	WedStart=models.TimeField(_('Wednesday StartTime'),null=True,blank=True)
#	WedEnd=models.TimeField(_('Wednesday EndTime'),null=True,blank=True)
#	ThursStart=models.TimeField(_('Thursday StartTime'),null=True,blank=True)
#	ThursEnd=models.TimeField(_('Thursday EndTime'),null=True,blank=True)
#	FriStart=models.TimeField(_('Friday StartTime'),null=True,blank=True)
#	FriEnd=models.TimeField(_('Friday EndTime'),null=True,blank=True)
#	SatStart=models.TimeField(_('Saturday StartTime'),null=True,blank=True)
#	SatEnd=models.TimeField(_('Saturday EndTime'),null=True,blank=True)
#	def __unicode__(self):
#		return u"%s"%(u"%s"%(self.Name))
#	@staticmethod
#	def objByID(id):
#		try:
#			act=ACTimeZones.objects.get(TimeZoneID=id)
#		except:
#			act=''
#		return act
#
#	@staticmethod
#	def colModels():
#		return [
#			#{'name':'TimeZoneID','hidden':False},
#			{'name':'TimeZoneID','width':80,'label':u"%s"%(ACTimeZones._meta.get_field('TimeZoneID').verbose_name)},
#			{'name':'Name','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('Name').verbose_name)},
#			{'name':'SunStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('SunStart').verbose_name)},
#			{'name':'SunEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('SunEnd').verbose_name)},
#			{'name':'MonStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('MonStart').verbose_name)},
#			{'name':'MonEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('MonEnd').verbose_name)},
#			{'name':'TuesStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('TuesStart').verbose_name)},
#			{'name':'TuesEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('TuesEnd').verbose_name)},
#			{'name':'WedStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('WedStart').verbose_name)},
#			{'name':'WedEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('WedEnd').verbose_name)},
#			{'name':'ThursStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('ThursStart').verbose_name)},
#			{'name':'ThursEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('ThursEnd').verbose_name)},
#			{'name':'FriStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('FriStart').verbose_name)},
#			{'name':'FriEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('FriEnd').verbose_name)},
#			{'name':'SatStart','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('SatStart').verbose_name)},
#			{'name':'SatEnd','width':100,'label':u"%s"%(ACTimeZones._meta.get_field('SatEnd').verbose_name)}
#			]
#	class Admin:
#		search_fields=['Name']
#	class Meta:
#		db_table = 'ACTimeZones'
#		verbose_name=_('ACTimeZones-table')
#		verbose_name_plural=verbose_name
#

    ##门禁组类别设置表##
#class ACGroup(models.Model):
#	#GroupID=models.AutoField(primary_key=True,null=False, editable=False)
#	GroupID=models.IntegerField(_('GroupID'),primary_key=True, editable=True,choices=ACGroupS_CHOICES)
#	Name=models.CharField(_('ACGroup name'), max_length=30,null=True,blank=True)
#	TimeZone1 = models.IntegerField(_('TimeZone1'),null=True, blank=True,default=0)
#	TimeZone2 = models.IntegerField(_('TimeZone2'),null=True, blank=True,default=0)
#	TimeZone3 = models.IntegerField(_('TimeZone3'),null=True,blank=True,default=0)
#	VerifyType=models.IntegerField(_('VerifyType'), null=True,editable=True,default=0,choices=ACGroup_VerifyType)
#	HolidayValid=models.SmallIntegerField(_("HolidayValid"), null=True,default=0, editable=True, choices=BOOLEANS)
#	def __unicode__(self):
#		return u"%s"%(u"%s"%(self.Name))
#	@staticmethod
#	def objByID(id):
#		if id==None: return None
#		g=cache.get("%s_iclock_acgroup_%s"%(settings.UNIT,id))
#		if g: return g
#		g=ACGroup.objects.get(GroupID=id)
#		if g:
#			cache.set("%s_iclock_acgroup_%s"%(settings.UNIT,id),g)
#		return g
#	def save(self):
#		cache.delete("%s_iclock_acgroup_%s"%(settings.UNIT,self.GroupID))
#		super(ACGroup,self).save()
#	def delete(self):
#		cache.delete("%s_iclock_acgroup_%s"%(settings.UNIT,self.GroupID))
#		super(ACGroup, self).delete()
#
#	@staticmethod
#	def colModels():
#		return [
#			#{'name':'GroupID','hidden':True},
#			{'name':'GroupID','width':80,'label':u"%s"%(ACGroup._meta.get_field('GroupID').verbose_name)},
#			{'name':'Name','width':100,'label':u"%s"%(ACGroup._meta.get_field('Name').verbose_name)},
#			{'name':'TimeZone1','width':100,'label':u"%s"%(ACGroup._meta.get_field('TimeZone1').verbose_name)},
#			{'name':'TimeZone2','width':100,'label':u"%s"%(ACGroup._meta.get_field('TimeZone2').verbose_name)},
#			{'name':'TimeZone3','width':100,'label':u"%s"%(ACGroup._meta.get_field('TimeZone3').verbose_name)},
#			{'name':'VerifyType','width':100,'label':u"%s"%(ACGroup._meta.get_field('VerifyType').verbose_name)},
#			{'name':'HolidayValid','width':100,'label':u"%s"%(ACGroup._meta.get_field('HolidayValid').verbose_name)}
#			]
#	class Admin:
#		search_fields=['Name']
#	class Meta:
#		db_table = 'ACGroup'
#		verbose_name=_('ACGroup-table')
#		verbose_name_plural=verbose_name
#
#
#	##开锁组合类别设置表##
#class ACUnlockComb(models.Model):
#	#UnlockCombID=models.AutoField(primary_key=True,null=False, editable=False)
#	UnlockCombID=models.IntegerField(_('UnlockCombID'),primary_key=True,null=False, editable=True,choices=ACUnlockCombS_CHOICES)
#	Name=models.CharField(_('ACUnlockComb name'), max_length=30,null=True,blank=True)
#	Group01 = models.IntegerField(_('Group1'),null=True, blank=True)
#	Group02 = models.IntegerField(_('Group2'),null=True, blank=True)
#	Group03=models.IntegerField(_('Group3'),null=True,blank=True)
#	Group04=models.IntegerField(_('Group4'),null=True,blank=True)
#	Group05=models.IntegerField(_('Group5'),null=True,blank=True)
#
#	def __unicode__(self):
#		return u"%s"%(u"%s"%(self.Name))
#	@staticmethod
#	def colModels():
#		return [
#			#{'name':'UnlockCombID','hidden':True},
#			{'name':'UnlockCombID','width':80,'label':u"%s"%(ACUnlockComb._meta.get_field('UnlockCombID').verbose_name)},
#			{'name':'Name','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Name').verbose_name)},
#			{'name':'Group01','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group01').verbose_name)},
#			{'name':'Group02','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group02').verbose_name)},
#			{'name':'Group03','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group03').verbose_name)},
#			{'name':'Group04','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group04').verbose_name)},
#			{'name':'Group05','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group05').verbose_name)}
#			]
#	class Admin:
#		search_fields=['Name']
#	class Meta:
#		db_table = 'ACUnlockComb'
#		verbose_name=_('ACUnlockComb-table')
#		verbose_name_plural=verbose_name


#用以保存人员在哪台设备 未用
#class UserInMachines(models.Model):
#	UserID = models.ForeignKey(employee, db_column='userid', verbose_name=u"员工", on_delete=models.CASCADE)
#	SN = models.CharField(_('serial number'), max_length=20 )
#	optime=models.DateTimeField(default=datetime.datetime.now)
#	flag = models.IntegerField(null=True, blank=True,default=0)
#
#
#
#	def __unicode__(self):
#		return u"%s"%(u"%s"%(self.UserID))
#	def Device(self):
#		return getDevice(self.SN_id)
#	def employee(self): #cached employee
#		try:
#			return employee.objByID(self.UserID_id)
#		except:
#			return None
#
#	class Admin:
#		pass
#	class Meta:
#		db_table = 'userinmachines'
#		verbose_name=_('userinmachines')
#		verbose_name_plural=verbose_name
#		unique_together = (('UserID', 'SN'),)

ISTOP_TYPE =(
    (0, _("unknown")),
    (1, _("NO")),
    (2, _("Yes")),
)

ANN_TYPE = (
    (0, _("Other")),
    (1, _("Maintenance")),
)
    ##公告类别设置表##
class Announcement(models.Model):
    id=models.AutoField(primary_key=True,null=False, editable=False)
    Title=models.CharField(_('headline'), db_column='title',max_length=80,null=False)
    Content = models.TextField(_('Content'),db_column='content',null=True,editable=True)
    PIN = models.CharField(_('number'),max_length=24,db_column='pin',null=True,blank=True)
    Author=models.CharField(_('Author'),max_length=30,db_column='author',null=False)
    Pubdate=models.DateTimeField(_('Pubdate'),db_column='pubdate',default=datetime.datetime.now,editable=True)
    Entrydate=models.DateTimeField(_(u'write time'),db_column='entrydate',default=datetime.datetime.now,null=True,editable=False)
    Channel=models.IntegerField(_('Channel'),null=True,db_column='channel',default=0)
    admin=models.IntegerField(_('admin'),null=True,default=0)
    IsTop = models.IntegerField(_(u'Is it top?'), default=0, db_column='istop',editable=True,choices=ISTOP_TYPE)
    ann_type = models.IntegerField(_(u'Announcement type'), blank=True, null=True, default=0, editable=True, choices=ANN_TYPE)

    def __unicode__(self):
        return u"%s"%(u"%s,%s"%(self.Title,self.Author))
    def __str__(self):
        return u"%s"%(u"%s,%s"%(self.Title,self.Author))

    def check_illegal_str(self, strs):
        illegal_str = ['onerror', 'alert', 'prompt']
        if any(keyword in strs for keyword in illegal_str):
            return False
        return True

    def save(self, *args, **kwargs):
        if not self.check_illegal_str(self.Title):
            raise Exception(_(u"Contains illegal characters"))
        if not self.check_illegal_str(self.Content):
            raise Exception(_(u"Contains illegal characters"))
        super(Announcement, self).save(*args, **kwargs)

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'Title','width':400,'label':u"%s"%(Announcement._meta.get_field('Title').verbose_name)},
            #{'name':'Content','width':500,'label':u"%s"%(Announcement._meta.get_field('Content').verbose_name)},
            #{'name':'PIN','width':100,'label':u"%s"%(Announcement._meta.get_field('PIN').verbose_name)},
            {'name':'Author','width':100,'label':u"%s"%(Announcement._meta.get_field('Author').verbose_name)},
            {'name':'Pubdate','width':150,'label':u"%s"%(Announcement._meta.get_field('Pubdate').verbose_name)}
            #{'name':'Channel','width':100,'label':u"%s"%(Announcement._meta.get_field('Channel').verbose_name)},
            #{'name':'admin','width':100,'label':u"%s"%(Announcement._meta.get_field('admin').verbose_name)}
            ]
    class Admin:
        search_fields=['Title']
    class Meta:
        db_table = 'announcement'
        verbose_name=_(u'announcement')
        verbose_name_plural=verbose_name
        default_permissions = ('browse','add', 'change', 'delete')
        permissions = (
            ('send_Announcement_Email', 'Send Announcement Email'),
        )


MSG_TYPE=(
	(0,_('item0')),
	(1,_('item1')),
	(2,_('item2')),
    (3,_('item3')),
	)
# 消息列表
class AppMessage(models.Model):
    id = models.AutoField(primary_key=True, null=False, editable=False)
    # UserID = models.ForeignKey(employee, db_column="userid", verbose_name=_(u'employee'), null=True, blank=True, on_delete=models.CASCADE) # 消息申请人
    UserID = models.ManyToManyField(employee, through='AppMessageEmployee')

    msg_type = models.CharField(_('Message Type'), choices=MSG_TYPE, default=0, max_length=100)  # 消息类型
    title = models.CharField(_('Message Title'), max_length=250, blank=True, default='')  # 消息标题
    content = models.TextField(_('Message Content'),null=True,blank=True)  # 消息内容
    createtime = models.DateTimeField(_('Create Time'), auto_now_add=True)  # 消息创建时间
    is_send = models.BooleanField(_('Is Send'), default=False)  # 是否已发 app
    is_read = models.BooleanField(_('Is Read'), default=False)  # 是否已读 小程序

    reserve1 = models.CharField(_('reserve1'), max_length=250, null=True)  # 预留字段
    reserve2 = models.CharField(_('reserve2'), max_length=250, null=True)  # 预留字段

    class Meta:
        db_table = 'app_message'
        ordering = ("createtime", )


class AppMessageEmployee(models.Model):
    message = models.ForeignKey(AppMessage, on_delete=models.CASCADE)
    UserID = models.ForeignKey(employee, on_delete=models.CASCADE, db_column='userid', null=True)  #为空表示给所有人发

    class Meta:
        db_table = 'app_message_employee'

##加班单类别设置表##
class USER_OVERTIME(models.Model):
    UserID=models.ForeignKey(employee, db_column='userid',verbose_name=_('employee'), default=1,null=False, blank=False, on_delete=models.CASCADE)
    StartOTDay= models.DateTimeField(_(u'beginning time'), null=False, blank=True,db_column='startotday')
    EndOTDay = models.DateTimeField(_(u'ending time'), null=True,blank=True,db_column='endotday')
    YUANYING=models.CharField(_(u'reson'), max_length=200,null=True,blank=True,db_column='yuanying')
    ApplyDate=models.DateTimeField(_(u'apply date'), null=True, blank=True,db_column='applydate')
    State=models.IntegerField(_(u'state'), null=True, default=0, blank=True,db_column='state', choices=AUDIT_STATES, editable=False)#0-申请；2-通过；3-拒绝；6-重新申请；>10表示审批中，此时减去10等于管理员id(职务审批）或者人员id(人员审批)
    AsMinute=models.IntegerField(_(u'As Minute'), null=True, default=0, blank=True,db_column='asminute')
    roleid = models.IntegerField(_(u'current reviewer'),editable=True)
    process=models.CharField(_(u'Audit Process'),max_length=80,null=True,blank=True)
    oldprocess=models.CharField(_(u'Audited Process'),max_length=80,null=True,blank=True)
    processid=models.IntegerField(_(u'Audit Process id'),editable=True)
    procSN=models.IntegerField(_(u'reviewed serial number'),default=0,editable=True,db_column='procsn')
    processtype = models.IntegerField(_('process type'), default=0, editable=True, choices=PROCESSTYPE,db_column='processtype')
    employeelist = models.CharField(_('current reviewer'), max_length=80, null=True, blank=True)

    def __str__(self):
        return u"%s" % (self.UserID.__str__())


    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True,'frozen':True},
            {'name':'PIN','index':'UserID__PIN','width':80,'label':u"%s"%(_('PIN')),'frozen':True},
            {'name':'EName','index':'UserID__EName','width':80,'label':u"%s"%(_('EName')),'frozen':True},
            {'name':'StartOTDay','index':'StartOTDay','width':120,'label':u"%s"%(USER_OVERTIME._meta.get_field('StartOTDay').verbose_name)},
            {'name':'EndOTDay','index':'EndOTDay','width':120,'label':u"%s"%(USER_OVERTIME._meta.get_field('EndOTDay').verbose_name)},
            {'name':'AsMinute','sortable':False,'width':100,'label':u"%s"%(USER_OVERTIME._meta.get_field('AsMinute').verbose_name)},
            {'name':'DeptID','index':'UserID__DeptID','width':70,'label':u"%s"%(_('department number'))},
            {'name':'DeptName','index':'UserID__DeptID__DeptName','width':120,'label':u"%s"%(_('department name'))},
            {'name':'State','index':'State','width':80,'label':u"%s"%(USER_OVERTIME._meta.get_field('State').verbose_name)},
            {'name':'process','sortable':False,'index':'process','width':120,'label':u"%s"%(_(u'Audit flow'))},
            {'name':'YUANYING','sortable':False,'width':120,'label':u"%s"%(USER_OVERTIME._meta.get_field('YUANYING').verbose_name)},
            {'name':'ApplyDate','index':'ApplyDate','width':120,'label':u"%s"%(USER_OVERTIME._meta.get_field('ApplyDate').verbose_name)}
            ]
    class Admin:
        list_filter =['State','UserID','StartOTDay','ApplyDate']
        lock_fields=['UserID']
        search_fields = ['UserID__PIN','UserID__EName']
    class Meta:
        db_table = 'user_overtime'
        verbose_name = _('OverTime')
        verbose_name_plural=verbose_name
        unique_together = (("UserID", "StartOTDay", 'State'),)
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
                                ('overtimeAudit_user_overtime','Audit OverTime'),
                    )
class PROCESS_RESULT(models.Model):
    exceptid = models.IntegerField(db_column='exceptid',default=1,null=False,blank=False)
    excepttype=models.SmallIntegerField(_('excepttype'), null=True, db_column='excepttype',default=0, blank=True, choices=EXCEPTTYPE, editable=False)
    # User = models.ForeignKey(settings.AUTH_USER_MODEL,db_column='user_id', verbose_name=_('administrator'), null=True, blank=True, on_delete=models.CASCADE)
    # comments=models.CharField(_(u'Approval comments'),max_length=400,null=True,blank=True)
    ProcessingTime = models.DateTimeField(_(u'Processing time'), null=True, blank=True,db_column='processingtime')
    procSN = models.IntegerField(_(u'Approval level'),db_column='procsn', null=True,blank=True, editable=True)
    State=models.SmallIntegerField(_('state'), null=True, db_column='state',default=0, blank=True, choices=AUDIT_STATES_PROCESS, editable=False)
    employeeid = models.ForeignKey(employee, db_column='userid', verbose_name=_('employee'), null=True,blank=False, on_delete=models.CASCADE)
    class Meta:
        default_permissions = ()
#用户职务表
class userRoles(models.Model):
    roleid=models.IntegerField(_('role id'),db_column='roleid',unique=True)
    roleName=models.CharField(_('role Name'),db_column='rolename',max_length=80,unique=True)
    roleLevel=models.IntegerField(_('role Level'), db_column='rolelevel',null=True, blank=True,editable=True,help_text=_(u"The higher the number, the higher the level"))
    State = models.IntegerField(_('state'),default=0,editable=False,db_column='state', choices=BOOLEANS)
    @staticmethod
    def getallrole():
        rol=cache.get("%s_userRoles_all"%(settings.UNIT))
        if rol is not None:
            return rol
        rols=userRoles.objects.all()
        rol={}
        for r in rols:
            rol[r.roleid]=r.roleName
        cache.set("%s_userRoles_all"%(settings.UNIT), rol, 5*60)
        return rol
    @staticmethod
    def objByID(id):
        if (id==None) or (id==0) or (id=='0') or (id<0):
            return None
        role=cache.get("%s_iclock_userRoles_%s"%(settings.UNIT, id))
        if role:
            return role
        try:
            u=userRoles.objects.get(roleid=id)
        except Exception as e:
            print ("objByID===",id,e)
            u=None
        if u:
            cache.set("%s_iclock_userRoles_%s"%(settings.UNIT,u.roleid),u)
        return u
    def __unicode__(self):
        try:
            return u"%d %s"%(self.roleid, self.roleName.decode("utf-8"))
        except:
            return u"%d %s"%(self.roleid, u"%s"%(self.roleName))
    def __str__(self):
        try:
            return u"%d %s"%(self.roleid, self.roleName.decode("utf-8"))
        except:
            return u"%d %s"%(self.roleid, u"%s"%(self.roleName))

    def save(self, *args, **kwargs):
        if self.roleid in (0, '0'):
            raise Exception(u"%s" % (_(u"Role number %s is disabled") % self.roleid))
        super(userRoles, self).save()

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'roleid','index':'roleid','width':80,'label':u"%s"%(userRoles._meta.get_field('roleid').verbose_name)},
            {'name':'roleName','index':'roleName','width':200,'label':u"%s"%(userRoles._meta.get_field('roleName').verbose_name)},
            {'name':'roleLevel','index':'roleLevel','width':200,'label':u"%s"%(userRoles._meta.get_field('roleLevel').verbose_name)}
            ]
    class Admin:
        search_fields=['roleid','roleName']
    class Meta:
        db_table = 'userroles'
        verbose_name = _('userRoles')
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete','export')


#补签原因表
class forgetcause(models.Model):
    cause_Name=models.CharField(_(u'Description of Reasons'),max_length=255, unique=True)

    def __unicode__(self):
        return u"%s"%(self.cause_Name)
    def __str__(self):
        return u"%s"%(self.cause_Name)

    @staticmethod
    def colModels():
        return [
            {'name':'cause_Name','index':'cause_Name','align':'center','width':360,'label':u"%s"%(forgetcause._meta.get_field('cause_Name').verbose_name),'editable':True}
            ]
    class Admin:
        pass
    class Meta:
        db_table = 'forgetcause'
        verbose_name = _(u'Replacement reasons')
        verbose_name_plural=verbose_name
        default_permissions = ('browse')


#OTHER_STATES=(
#	(0,_('User OverTime')),
#	(1,_('Forget Checkin/out'))
#)

class userRole(models.Model):
    userid=models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    roleid=models.ForeignKey(userRoles,db_column='roleid',null=True,editable=False, on_delete=models.CASCADE)
    def __unicode__(self):
        return u"%s"%(self.userid)
    def __str__(self):
        return u"%s"%(self.userid)
    class Admin:
        list_display=("userid","roleid", )
    class Meta:
        verbose_name=_("admin granted userRole")
        verbose_name_plural=verbose_name
        default_permissions = ()


#审核流程表
class process(models.Model):
    processName=models.CharField(_('process Name'),db_column='processname',max_length=80,unique=True)
    State = models.IntegerField(_('state'),default=0,editable=True, choices=BOOLEANS,db_column='state')
    notitle=models.IntegerField(_('state'),default=0,editable=True, choices=BOOLEANS)
    smallday = models.IntegerField(_('Shortest'), null=True, blank=True,editable=True)
    bigday = models.IntegerField(_('Longest'), null=True, blank=True,editable=True)
    processtype = models.IntegerField(_('process type'), default=0, editable=True, choices=PROCESSTYPE, db_column='processtype')

    class Admin:
        pass
    class Meta:
        db_table = 'process'
        verbose_name = _('process')
        verbose_name_plural=verbose_name
        default_permissions = ()
    @staticmethod
    def objbyprocessid(id):
        if id == None: return None
        d = cache.get("%s_iclock_process_%s" % (settings.UNIT, id))
        if d: return d
        try:
            d = process.objects.get(pk=id)
        except:
            d = None
        if d:
            cache.set("%s_iclock_process_%s" % (settings.UNIT, id), d)
        return d

    @staticmethod
    def get_processemp_detail(id):
        if id == None: return None
        processemp = cache.get("%s_iclock_processemp_%s" % (settings.UNIT, id))
        if processemp: return processemp
        try:
            processemp=''
            pr=process.objbyprocessid(id)
            pes=processemployee.objects.filter(processsn__gt=0,employeeid__isnull=False,processid=id).order_by('processsn')
            urd = {}
            ur = userRolesDell.objects.filter(processid=id)
            for u in ur:
                pes = processemployee.objects.filter(processid=u.processid, processsn=u.procSN).exclude(processsn=0)
                empnamelist = []
                empname=''
                for pe in pes:
                    empnamelist.append(str(pe.employeeid.EName or ''))

                if u.State == 0:
                    empname='&'.join(empnamelist)
                elif u.State == 1:
                    empname='|'.join(empnamelist)
                # 0表示无论什么样职务的人员全部需要审批
                urd[u.procSN] =  empname+'('+str(u.days)+')' # 所有符合标准的职务

            ull = list(urd.keys())
            ull.sort()  # 使用procSN进行排序
            empnamelist=[]
            for k in ull:
                empnamelist.append(urd[k])
            processemp='-->'.join(empnamelist)
        except:
            processemp = None
        if not processemp:
            processemp='--'
        cache.set("%s_iclock_processemp_%s" % (settings.UNIT, id), processemp)
        return processemp
class processemployee(models.Model):
    processid = models.ForeignKey(process, db_column='processid', null=True, editable=False, on_delete=models.CASCADE)#审批流id
    processsn = models.IntegerField(_(u'Sequence number'), db_column='processsn', null=True, blank=True, editable=True)#对应第几级审批
    employeeid =models.ForeignKey(employee, db_column='userid',verbose_name=_('employee'), null=False, blank=False, on_delete=models.CASCADE)
    class Admin:
        pass
    class Meta:
        db_table = 'processemployee'
        verbose_name = _('processemployee')
        verbose_name_plural=verbose_name
        unique_together = (("processid","processsn","employeeid"),)
        default_permissions = ('browse', 'add', 'change', 'delete')
#审核流程分配表
class prodeptmapping(models.Model):
    processid = models.ForeignKey(process,db_column='processid',null=True,editable=False, on_delete=models.CASCADE)
    DeptID = models.ForeignKey(department,db_column="defaultdeptid", verbose_name=DEPT_NAME, editable=True, null=True, on_delete=models.CASCADE)
    class Admin:
        pass
    class Meta:
        db_table = 'prodeptmapping'
        verbose_name = _('prodeptmapping')
        verbose_name_plural=verbose_name
        unique_together = (("processid","DeptID"),)
        default_permissions = ('browse', 'add', 'change', 'delete')


#审核流程假类表
#10000表示加班、如果还有其他需要审批的选项从10001开始排列。9999及其以下为假类
class proleave(models.Model):
    processid = models.ForeignKey(process,db_column='processid',null=True,editable=False, on_delete=models.CASCADE)
    leaveid=models.IntegerField(_('Leave Class'),db_column='leaveid', null=False,default=-1,blank=True, editable=True)
    class Admin:
        pass
    class Meta:
        db_table = 'proleave'
        verbose_name = _('proleave')
        verbose_name_plural=verbose_name
        unique_together = (("processid","leaveid"),)
        default_permissions = ()

class protitle(models.Model):
    processid = models.ForeignKey(process,db_column='processid',null=True,editable=False, on_delete=models.CASCADE)
    roleid=models.ForeignKey(userRoles,db_column='roleid',null=True,editable=False, on_delete=models.CASCADE)
    class Admin:
        pass
    class Meta:
        db_table = 'protitle'
        verbose_name = _('protitle')
        verbose_name_plural=verbose_name
        unique_together = (("processid","roleid"),)
        default_permissions = ()


class userRolesDell(models.Model):
    roleid=models.ForeignKey(userRoles,db_column='roleid',null=True,editable=False, on_delete=models.CASCADE)
    processid = models.ForeignKey(process,db_column='processid',null=True,editable=False, on_delete=models.CASCADE)
    procSN=models.IntegerField(_(u'Sequence number'), db_column='procsn',null=True, blank=True,editable=True)
    State = models.IntegerField(_('state'),default=0,editable=True,db_column='state', choices=BOOLEANS)
    days=models.FloatField(_('days'), null=True, blank=True,editable=True)
    class Admin:
        pass
    class Meta:
        db_table = 'userrolesdell'
        verbose_name = _('userRolesDell')
        verbose_name_plural=verbose_name
        unique_together = (("roleid","processid","procSN"),)
        default_permissions = ('browse', 'add', 'change', 'delete','export')

#设置APP位置
class applocation(models.Model):
    address = models.CharField(_('address'),max_length=250,null=True)#签到地址
    valid_range = models.CharField(_('valid_range'), max_length=250, null=True)#有效范围
    longitude = models.CharField(_('longitude'), max_length=250, null=True)#经度
    latitude = models.CharField(_('latitude'), max_length=250, null=True)#纬度
    # AuthedDept = models.CharField(_('AuthedDept'), max_length=250, null=True, db_column='authed_dept',blank=False)  #用于前端

    def __str__(self):
        return "%s %sm" % (self.address, self.valid_range)

    @staticmethod
    def colModels(request=None):
        ret = [
            # {'name': 'id', 'hidden': True},
            {'name': 'id', 'width': 60, 'sortable': True, 'label': u"%s"%(_('Numbering'))},
            {'name': 'address', 'width': 250, 'sortable': True, 'label': u"%s"%(_('address'))},
            {'name': 'longitude', 'width': 120, 'sortable': True, 'label': u"%s"%(_('longitude'))},
            {'name': 'latitude', 'width': 120, 'sortable': True, 'label': u"%s"%(_('latitude'))},
            {'name': 'valid_range', 'width': 100, 'sortable': True, 'label': u"%s"%(_('valid_range'))},
            {'name': 'DeptIDS', 'sortable': False, 'width': 180, 'label': u"%s"%(_(u'Home Department')), 'mod': 'acc;att'}
        ]
        if not request:return ret
        using_m=request.GET.get('mod_name','att')
        ret_Data=[]
        is_att_zone=GetParamValue('opt_basic_att_zone', '0')
        for t in ret:
            try:
                mod=t['mod']
                if using_m in mod:
                    if t['name']=='DeptIDS' and using_m in ['att']:
                        if is_att_zone == '1':
                            t['label']=u'%s'%(_(u'home zone'))
                    ret_Data.append(t)
            except:
                ret_Data.append(t)
        return ret_Data

    class Admin:
        list_display=('address')

    class Meta:
        db_table = 'app_location'
        verbose_name=_('applocation')
        default_permissions = ('browse','add', 'change', 'delete', 'export')
        permissions = (
            ('setzone', 'Setting the home area'),
        )

class DeptLocation(models.Model):
    location = models.ForeignKey(applocation,  null=False, blank=False, on_delete=models.CASCADE)
    dept = models.ForeignKey(department, null=False, blank=False, on_delete=models.CASCADE)

    class Admin:
        list_display = ("address", "dept",)

    class Meta:
        db_table = 'app_locationdept'
        verbose_name = _("deptlocation")
        verbose_name_plural = verbose_name

#地图管理表
class MapManage(models.Model):
    mapid=models.AutoField(primary_key=True, null=False,editable=False)
    MapName = models.CharField(_(u'map name'),db_column="mapname",null=False,max_length=80)
    Remarks = models.TextField(_(u'Remarks'),null=True, blank=True,db_column='remarks')
    Reserved = models.IntegerField(null=True,default=1, blank=True,editable=False,db_column='reserved')
    Reserved1 = models.CharField(null=True,max_length=80, blank=True,editable=False,db_column='reserved1')
    Reserved2 = models.CharField(null=True,max_length=80, blank=True,editable=False,db_column='reserved2')
    class Admin:
        pass
    class Meta:
        db_table = 'mapmanage'
        verbose_name=_('mapmanage')
        default_permissions = ('browse','add', 'change', 'delete')
        permissions = (
            ('MapManage_SetMap','Set Map'),#设置地图
            ('MapManage_SaveStyle','Save Style'),#保存样式
            ('MapManage_RemoveMap','Remove Map'),#清除地图
            ('Map_Monitor','Map Monitor'),#电子地图监控
        )

#地图与门禁样式表
class MapIaccessStyle(models.Model):
    id=models.AutoField(primary_key=True, null=False,editable=False)
    iclock_id=models.ForeignKey(iclock,db_column='iclock_id',null=True,editable=True, on_delete=models.CASCADE)
    mapmanage_id=models.ForeignKey(MapManage,db_column='mapmanage_id',null=True,editable=True, on_delete=models.CASCADE)
    styles = models.CharField(_(u'style'),max_length=400,null=True, blank=True)
    Reserved = models.IntegerField(null=True,default=1, blank=True,editable=False,db_column='reserved')
    Reserved1 = models.CharField(null=True,max_length=80, blank=True,editable=False,db_column='reserved1')
    Reserved2 = models.CharField(null=True,max_length=80, blank=True,editable=False,db_column='reserved2')
    class Admin:
        pass
    class Meta:
        db_table = 'mapiaccessstyle'
        unique_together = (("iclock_id","mapmanage_id"),)
        verbose_name=_("mapiaccessstyle")
        verbose_name_plural=verbose_name
        default_permissions = ()

class days_off (models.Model):
    id=models.AutoField(primary_key=True, null=False,editable=False)
    DeptID = models.IntegerField(_("department"), db_column='deptid')
    UserID = models.IntegerField(_('employee'),db_column="userid",null=True)
    FromDate = models.DateField(_(u'Rehearsal date'), null=False, blank=False,editable=True,db_column='fromdate')
    ToDate = models.DateField(_(u'Transfer to date'), null=False, blank=False,editable=True,db_column='todate')
    ApplyDate=models.DateTimeField(_('apply date'), null=True, blank=True,db_column='applydate')

    def __str__(self):
        emp_obj = employee.objByID(self.UserID)
        return u"%s" % (emp_obj)

    def department(self): #cached employee
        try:
            return department.objByID(self.DeptID)
        except:
            return None
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID)
        except:
            return None
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'DeptNumber','sortable':False,'width':80,'label':u"%s"%(_('department number'))},
            {'name':'DeptName','sortable':False,'width':120,'label':u"%s"%(_('department name'))},
            {'name':'PIN','width':80,'sortable':False,'label':u"%s"%(_('PIN'))},
            {'name':'EName','width':80,'sortable':False,'label':u"%s"%(_('EName'))},
            {'name':'FromDate','index':'FromDate','width':120,'label':u"%s"%(_(u'Rehearsal date'))},
            {'name':'ToDate','index':'ToDate','width':120,'label':u"%s"%(_(u'Transfer to date'))},
            {'name':'ApplyDate','index':'ApplyDate','width':120,'label':u"%s"%(_(u'Operation time'))}
            ]
    class Admin:
        my_search_fields =[{'model':employee,'name':u"%s"%(_(u'personnel')),'search_field':['PIN','EName'],'fromsearch_field':'id','tosearch_field':'UserID__in'},{'model':department,'name':u"%s"%(_(u'department')),'search_field':['DeptID','DeptName'],'fromsearch_field':'DeptID','tosearch_field':'DeptID__in'}]
    class Meta:
        db_table = 'daysoff'
        verbose_name = _(u'Personnel change')
        verbose_name_plural=verbose_name
        unique_together = (("DeptID","UserID","FromDate"),("DeptID","UserID","ToDate"),)
        default_permissions = ('browse','add', 'change', 'delete', 'export')

class Eventtype(models.Model):
    id=models.AutoField(primary_key=True, null=False,editable=False)
    typename=models.CharField(_(u'type name'),max_length=20,null=True, blank=True)
    color=models.IntegerField(_('display color'),null=True,default=16715535,blank=True,editable=True)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'typename','index':'typename','width':60,'label':u"%s"%(_(u'type name'))},
            {'name':'color','index':'color','width':120,'label':u"%s"%(_(u'colour'))},
            ]
    class Admin:
        pass
    class Meta:
        db_table = 'eventtype'
        verbose_name = _('Eventtype')
        verbose_name_plural=verbose_name
        default_permissions = ()

ALARM_UNITS=(
    (0, _('Email')),
    (1, _('short message')),
)

ALARM_STATE=(
    (0, _(u'Unsent')),
    (1, _(u'Has been sent')),
    (2, _(u'invalid')),
)

class Alarmclock(models.Model):
    id=models.AutoField(primary_key=True, null=False,editable=False)
    StartTime = models.DateTimeField(_(u'Starting time'), db_column='starttime',null=False, blank=False,editable=True)
    AlarmType=models.IntegerField(_(u'Reminder mode'),db_column='alarmtype',default=0,editable=True, choices=ALARM_UNITS)
    photooremail=models.CharField(_(u'Mobile phone or mailbox'),max_length=30,null=True, blank=True)
    description = models.CharField(_(u'Event Description'),max_length=250,null=True, blank=True)
    title=models.CharField(_(u'theme'),max_length=50,null=True, blank=True)
    State = models.IntegerField(_(u'status'),db_column='state',default=0,editable=True, choices=ALARM_STATE)
    ApplyDate=models.DateTimeField(_(u'Generation time'), null=True, db_column='applydate', blank=True)
    sentTime = models.DateTimeField(_(u'Send time'),null=True, db_column='senttime',blank=True)
    class Admin:
        pass
    class Meta:
        db_table = 'alarmclock'
        verbose_name = _('Alarmclock')
        verbose_name_plural=verbose_name
        default_permissions = ()

class daysPlanner(models.Model):
    id=models.AutoField(primary_key=True, null=False,editable=False)
    StartDate = models.DateField(_(u'start date'), null=False, blank=False,editable=True,db_column='startdate')
    EndDate = models.DateField(_(u'end date'), null=True, blank=False,editable=True,db_column='enddate')
    StartTime = models.TimeField(_('beginning time'),null=False, blank=False,db_column='starttime')
    EndTime = models.TimeField(_('ending time'),null=True, blank=False,db_column='endtime')
    description = models.CharField(_(u'Event Description'),max_length=200,null=True, blank=True)
    allDay=models.IntegerField(_(u'Overall logo'),default=0,editable=True, choices=BOOLEANS,db_column='allday')
    note=models.CharField(_(u'Remarks'),max_length=400,null=True, blank=True)
    type=models.ForeignKey(Eventtype,db_column='typeid',null=True,editable=True, on_delete=models.CASCADE)
    isalarm= models.IntegerField(_(u'Reminder'),default=0,editable=True, choices=BOOLEANS)
    notify=models.IntegerField(_(u'Advance time'), null=True, default=0, blank=True)
    Unit =models.IntegerField(_(u'unit'),default=0,editable=True, choices=LEAVE_UNITS,db_column='unit')
    AlarmType=models.IntegerField(_(u'Reminder mode'),default=0,editable=True, choices=ALARM_UNITS,db_column='alarmtype')
    phoneoremail=models.CharField(_(u'Mobile phone or mailbox'),max_length=30,null=True, blank=True)
    alarmid=models.ForeignKey(Alarmclock,null=True,editable=True, on_delete=models.CASCADE)
    userid=models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    employeeID = models.IntegerField(_('employee'),db_column="userid",null=True)
    class Admin:
        pass
    class Meta:
        unique_together = (("StartDate","StartTime","description"),)
        db_table = 'daysplanner'
        verbose_name = _('daysPlanner')
        verbose_name_plural=verbose_name
        default_permissions = ()

class annual_leave(models.Model):
    UserID = models.ForeignKey("employee", db_column='userid', on_delete=models.CASCADE)
    inyear = models.IntegerField(_(u'Year of the year'), db_column='in_year',null=True,blank=True)
    months = models.IntegerField(_(u'working age'), db_column='months',null=True,blank=True)
    annual_calc=models.FloatField(_(u'The standard annual number of days off in the year'),db_column='annual_calc',null=True,default=0,blank=True)
    annual_attach=models.FloatField(_(u'Enterprise Standard Days of the Year'),db_column='annual_attach',null=True,default=0,blank=True)
    annual_out=models.FloatField(_(u'The number of days that have been taken in the year'),db_column='annual_out',null=True,default=0,blank=True)
    reserved=models.FloatField(_(u'Remarks'),db_column='reserved',null=True,default=0,blank=True)

    class Admin:
        pass
    class Meta:
        unique_together = (("UserID","inyear"),)
        db_table = 'annual_leave'
        verbose_name = _('annual leave')
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete', 'export')
#		permissions = (
#			('annual_emp','annual emp'),#年休假报表
#		)


    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            #{'name':'DeptID','width':60,'index':'UserID__DeptID','label':u"%s"%(_('department number'))},
            {'name':'DeptName','sortable':False,'width':180,'label':u"%s"%(_('department name'))},
            {'name':'PIN','width':60,'label':u"%s"%(_('PIN'))},
            {'name':'EName','width':80,'label':u"%s"%(_('EName'))},
            {'name':'Birthday','search':False,'width':80,'label':u"%s"%(employee._meta.get_field('Birthday').verbose_name)},
            {'name':'Hiredday','search':False,'width':100,'label':u"%s"%(employee._meta.get_field('Hiredday').verbose_name)},
            {'name':'WorkAge','sortable':False,'width':80,'label':u"%s"%(_(u'working age'))},
            {'name':'annual_std','sortable':False,'width':100,'label':u"%s"%(_(u'Statutory annual rest days'))},
            {'name':'annual_ent','sortable':False,'width':120,'label':u"%s"%(_(u'Enterprise Standard Annual Days'))},
            {'name': 'reserved', 'sortable': False, 'width': 120,'label': u"%s"%(_(u'Annual holiday days'))}
#			{'name':'TTime','width':120,'search':False,'label':u"%s"%(transaction._meta.get_field('TTime').verbose_name)},
#			{'name':'State','width':80,'search':False,'label':u"%s"%(transaction._meta.get_field('State').verbose_name)},
#			{'name':'Verify','width':80,'search':False,'label':u"%s"%(transaction._meta.get_field('Verify').verbose_name)},
#			{'name':'Device','index':'SN','width':180,'label':u"%s"%(_('Device name'))},
#			{'name':'thumbnailUrl','search':False,'sortable':False,'width':100,'label':u"%s"%(_('Picture'))}
            ]

    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None

class annual_settings(models.Model):
    id=models.AutoField(primary_key=True, null=False,editable=False)
    Name=models.CharField(_('name'),db_column='name',max_length=30,null=False)
    Type=models.CharField(_('type'),db_column='type',max_length=10,null=False)
    Value=models.CharField(_('value'),db_column='value',max_length=100,null=False)
    Sequence= models.CharField(_(u'serial number'),db_column='sequence',max_length=10, null=False,blank=True)
    class Admin:
        @staticmethod
        def initial_data():

            if annual_settings.objects.all().count()==0:
                annual_settings(Name='month_s',Type="",Value="12",Sequence="").save()
                annual_settings(Name='day_s',Type="",Value="31",Sequence="").save()
                annual_settings(Name='type_b',Type="",Value="1",Sequence="").save()
                annual_settings(Name='guize',Type="",Value="1",Sequence="").save()
                annual_settings(Name='type_f',Type="",Value="Hiredday",Sequence="").save()
                annual_settings(Name='type_g',Type="1",Value="Hiredday",Sequence="").save()
                annual_settings(Name='type_g',Type="2",Value="Hiredday",Sequence="").save()
                annual_settings(Name='type_g',Type="3",Value="Hiredday",Sequence="").save()
                annual_settings(Name='type_g',Type="4",Value="Hiredday",Sequence="").save()
                annual_settings(Name='month_g',Type="1",Value="5",Sequence="").save()
                annual_settings(Name='xi_g',Type="2",Value="0",Sequence="").save()
                annual_settings(Name='xi_g',Type="3",Value="0",Sequence="").save()
                annual_settings(Name='xi_g',Type="4",Value="0",Sequence="").save()
                annual_settings(Name='man_g',Type="2",Value="0",Sequence="").save()
                annual_settings(Name='zeng_g',Type="2",Value="0",Sequence="").save()
                annual_settings(Name='max_g',Type="2",Value="0",Sequence="").save()
                annual_settings(Name='day_1_10',Type="3",Value="0",Sequence="").save()
                annual_settings(Name='day_10_20',Type="3",Value="0",Sequence="").save()
                annual_settings(Name='day_20',Type="3",Value="0",Sequence="").save()
                annual_settings(Name='nian_s',Type="4",Value="0",Sequence="1").save()
                annual_settings(Name='nian_e',Type="4",Value="0",Sequence="1").save()
                annual_settings(Name='nian_day',Type="4",Value="0",Sequence="1").save()
                annual_settings(Name='nian_end',Type="4",Value="0",Sequence="").save()
                annual_settings(Name='nian_day_end',Type="4",Value="0",Sequence="").save()
                annual_settings(Name='day_g',Type="1",Value="0",Sequence="").save()
                annual_settings(Name='RemaindProc',Type="",Value="0",Sequence="").save()



    class Meta:
        unique_together = (("Name","Type","Sequence"),)
        db_table = 'annual_settings'
        verbose_name = _('annual settings')
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('annual_leave_standard_export','Annual leave standard export'),
            ('annual_leave_details_export','Annual leave details export')
        )


#设备上的人员
class empofdevice(models.Model):
    #UserID = models.ForeignKey(employee, db_column='userid', on_delete=models.CASCADE)
    id=models.AutoField(primary_key=True)
    PIN = models.CharField(_('PIN'),db_column="badgenumber",null=False,max_length=24)
    SN = models.CharField(_('serial number'), max_length=20,db_column='sn')
    pri=models.IntegerField(default=0, choices=PRIV_CHOICES)
    def __unicode__(self):
        return u"%s %s"%(self.PIN, self.SN)
    def __str__(self):
        return u"%s %s"%(self.PIN, self.SN)
    class Admin:
        pass
    class Meta:
        verbose_name = _('empofdevice')
        verbose_name_plural=verbose_name
        unique_together = (("PIN","SN"),)
        db_table = 'empofdevice'
        default_permissions=('browse',)

    def employee(self): #cached employee
        try:
            return employee.objByPIN(self.PIN)
        except:
            return None

    def save(self, *args, **kwargs):
        super(empofdevice, self).save(*args, **kwargs)

    def Device(self):
        return getDevice(self.SN)
    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'PIN','index':'PIN','width':100,'label':u"%s"%(_('PIN'))},
            {'name':'EName','sortable':False,'width':100,'label':u"%s"%(_('Emp Name'))},
            {'name':'pri','sortable':False,'width':100,'sortable':True,'label':u"%s"%(_(u'Device Permissions'))},
            {'name':'DeptName','sortable':False,'width':100,'label':u"%s"%(_('department name'))},
            {'name':'Card','sortable':False,'width':80,'label':u"%s"%(employee._meta.get_field('Card').verbose_name)},
            {'name':'Title','sortable':False,'width':80,'label':u"%s"%(_('Title'))},
            {'name':'SN','sortable':False,'width':90,'label':u"%s"%(iclock._meta.get_field('SN').verbose_name)},
            {'name':'Alias','sortable':False,'width':140,'label':u"%s"%(iclock._meta.get_field('Alias').verbose_name)}
            ]



def saveEmpInDevice(pin,sn,pri=-1):
    #try:
    #    sql="insert into empofdevice(badgenumber,sn) values(%s,%s)"
    #    params=(pin,sn)
    #    customSqlEx(sql,params)
    #except Exception as e:
    #	#print "saveEmpInDevice",e
    #	pass
    if not pri:pri=0
    objs=empofdevice.objects.filter(PIN=pin,SN=sn)
    if objs:
        obj=objs[0]
        if pri!=-1 and obj.pri!=pri:
            obj.pri=pri
            obj.save()
    else:
        if pri==-1:pri=0

        empofdevice(PIN=pin,SN=sn,pri=pri).save(force_insert=True)

AD_TYPE=(
    (1, _(u'image')),
    (2, _(u'video')),
    (3, _(u'text(doorplate)')),
    (4, _(u'image(doorplate)')),
)

#广告屏设置
class adsetting(models.Model):
    adcode = models.CharField(_(u'Ad number'), blank=False,max_length=20)
    adname = models.CharField(_(u'Ad Name'), null=False,max_length=20, blank=False)
    adtype = models.IntegerField(_(u'advertisement type'),default=1, choices=AD_TYPE)
    adindex = models.IntegerField(_(u'play sequence number'), null=False, default=0,editable=False)
    startime = models.DateTimeField(_(u'Starting time'), null=True,blank=True,editable=False)
    endtime = models.DateTimeField(_(u'End Time'), null=True,blank=True,editable=False)
    adfile = models.CharField(_(u'Advertising Document'), null=True,max_length=100,blank=True)
    adtext = models.TextField(_(u'concrete content'), null=True, blank=True)
    opstamp = models.DateTimeField(_(u'modify time'), null=True, blank=True)

    def __unicode__(self):
        return u"%s"%(u"%s, %s"%( self.adcode, self.adname))
    def __str__(self):
        return u"%s"%(u"%s, %s"%( self.adcode, self.adname))
    class Admin:
        search_fields = ['adcode','adname']

    class Meta:
        verbose_name = _(u'Ad Settings')
        verbose_name_plural=verbose_name
        unique_together = (("adcode"),)
        db_table = 'adsetting'
        default_permissions = ('browse','add', 'change', 'delete')

    def save(self, *args, **kwargs):
        if self.id:
            old_ad = adsetting.objects.get(id=self.id)
            if old_ad.adcode != self.adcode or old_ad.adname != self.adname or old_ad.adtype != self.adtype or old_ad.adfile != self.adfile:
                self.opstamp = datetime.datetime.now()
                devs = iclock.get_available_locker_devs()
                for dev in devs:
                    cache.set(dev+'ad_change_flag', 1)
            super(adsetting, self).save(force_update=True)
        else:
            self.opstamp = datetime.datetime.now()
            devs = iclock.get_available_locker_devs()
            for dev in devs:
                cache.set(dev+'ad_change_flag', 1)
            super(adsetting, self).save(*args, **kwargs)

    def get_adfile_URL(self):
        if sys.version[0] == '3':
            from urllib.parse import quote
        else:
            from urllib import quote
        if self.adfile:
            return getStoredFileURL('ad', None, quote(self.adfile.encode("utf-8")))
        else:
            return ""

    def delete(self):
        try:
            adobjs = Admachine.objects.filter(ad__adcode=self.adcode)
            for adobj in adobjs:
                cmdStr = "DelAdvFile Type=%d\tFileName=%s"%(self.adtype, self.adfile)
                saveCmd(adobj.SN.SN,cmdStr)
            super(adsetting, self).delete()
        except:
            pass

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'adcode','index':'adcode','width':80,'label':u"%s"%(_(u'Ad number'))},
            {'name':'adname','sortable':False,'width':150,'label':u"%s"%(_(u'Ad Name'))},
            {'name':'adtype','sortable':False,'width':80,'label':u"%s"%(_(u'advertisement type'))},
            {'name':'adfile','sortable':False,'width':200,'label':u"%s"%(_(u'Advertising Document'))},
#            {'name':'adindex','sortable':True,'width':80,'label':u"%s"%(_(u'play sequence number'))},
#            {'name':'startime','sortable':False,'width':120,'label':u"%s"%(_(u'Starting time'))},
#            {'name':'endtime','sortable':False,'width':120,'label':u"%s"%(_(u'End Time'))},
            ]

class Admachine(models.Model):
        ad = models.ForeignKey(adsetting,db_column='ad_id', on_delete=models.CASCADE)
        SN = models.ForeignKey(iclock,db_column='sn_id', on_delete=models.CASCADE)
        def __unicode__(self):
                return u"%s"%(self.SN)
        def __str__(self):
                return u"%s"%(self.SN)
        class Admin:
                list_display=("SN", )
        class Meta:
                verbose_name=_(u"available device")
                verbose_name_plural=verbose_name
                unique_together = (("SN", "ad"),)

AUDIT_TYPE = (
    (0,_("Personnel registration")),
    (1,_("Mobile Register Photo Review"))
)

class ademployee(models.Model):
    adpin = models.CharField(_(u'Personnel number'), blank=False,max_length=20)
    adname = models.CharField(_(u'Emp Name'), null=True,blank=False,max_length=20)
    adphoto = models.CharField(_(u'photo file'), null=True,blank=False,max_length=50)
    addept = models.CharField(_(u'department name'), blank=False,null=True,max_length=40)
    addeptnum = models.CharField(_(u'Department Number'), blank=True,null=True,max_length=20)
    audit=models.SmallIntegerField(_(u"Approval Status"), null=True,default=0, choices=BOOLEANS)
    audit_type = models.SmallIntegerField(_(u'Audit Type'), null=True, default=0, choices=AUDIT_TYPE, editable=False)
    sn = models.CharField(_(u'registration device'), default="",null=True,blank=True,max_length=20)
    upload_time = models.DateTimeField(_(u'Upload Time'), null=True, blank=True, editable=False)  #上传时间
    audit_time = models.DateTimeField(_(u'Audit Time'), null=True, blank=True, editable=False)  #审核时间
    remarks = models.CharField(_('Remarks'), null=True, default='', blank=True, max_length=40)

    def __unicode__(self):
        return u"%s"%(u"%s, %s"%(self.adpin, self.adname))

    def __str__(self):
        return u"%s"%(u"%s, %s"%(self.adpin, self.adname))

    @staticmethod
    def colModels():
        return [
            {'name':'id','hidden':True},
            {'name':'adpin','index':'adpin','width':80,'label':u"%s"%(_(u'Personnel number'))},
            {'name':'adname','index':'adname','width':80,'label':u"%s"%(_(u'Emp Name'))},
            {'name': 'addeptnum', 'index': 'addeptnum', 'width': 80, 'label': u"%s"%(_(u'Department Number'))},
            {'name': 'addept', 'index': 'addept', 'width': 80, 'label': u"%s"%(_(u'department name'))},
            {'name':'audit','sortable':False,'width':80,'label':u"%s"%(_(u'AuditExc'))},
            {'name':'sn','sortable':False,'width':200,'label':u"%s"%(_(u'devise serial number'))},
            {'name':'photo','search':False,'sortable':False,'width':100,'label':u"%s"%(_("Picture"))},
            {'name':'upload_time','search':False,'width':150,'label':u"%s"%(_("Upload Time"))},
            {'name':'audit_time','search':False,'width':150,'label':u"%s"%(_("Audit Time"))},
            {'name':'remarks','search':False,'width':150,'label':u"%s"%(_("Remarks"))}
            ]

    def getThumbnailUrl(self):
        url = ''

        tbName_by_name = getStoredFileName("photo", None, self.adphoto)
        tbName_by_pin = getStoredFileName("photo", None, f"ad_{devicePIN(self.adpin)}.jpg")
        if os.path.exists(tbName_by_name):
            url = getStoredFileURL("photo", None, self.adphoto)
        elif os.path.exists(tbName_by_pin):
            url = getStoredFileURL("photo", None, f"ad_{devicePIN(self.adpin)}.jpg")

        if url:
            import time

            url += f'?{time.time():.0f}'
        return url

    def save(self, *args, **kwargs):
        is_create = False if self.pk else True
        if is_create and self.audit_type == 0:
            if ademployee.objects.filter(adpin=self.adpin, adname=self.adname).exists():
                raise IntegrityError(_('Personnel registration record already exists'))
        elif is_create and self.audit_type == 1:
            if ademployee.objects.filter(adpin=self.adpin, adname=self.adname, audit=0).exists():
                raise IntegrityError(_('There are records to be reviewed, do not submit them repeatedly'))
        super(ademployee, self).save(*args, **kwargs)

    class Admin:
        list_display = ("adpin", "adname", "addept", "adphoto", "audit", "sn")
        search_fields = ['adpin','adname']

    class Meta:
        db_table = 'ademployee'
        verbose_name=_(u"Information Screen Registration Officer")
        verbose_name_plural=verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('adrotatepic_ademployee', 'Pictures Rotating'),
        )



TERMINAL_TYPE=(
    (1, _('APP')),
    (2, _('Third Party Applications')),
    (3, _('applet')),
)
AUTH_STATE=(
    (0, _('Unactivated')),
    (1, _('Activated')),
    (2, _('Disabled'))
)
#终端授权管理
class AuthTerminal(models.Model):
    user = models.ForeignKey(employee, verbose_name=_("employee"), db_column='userid', null=True, blank=True, on_delete=models.CASCADE)  #允许为空，若用于第三方接口调用，就不需要这个字段了
    ttype = models.IntegerField(_(u'Terminal Type'),default=1, choices=TERMINAL_TYPE)  #终端类型
    deviceid = models.CharField(_(u'Device ID'), blank=True, null=True, max_length=50)  #设备id，如IMEI
    registration_code  = models.CharField(_(u'Registration Code'), blank=True, null=True, max_length=20)  #注册码，这个码应该随机动态生成才是,先留着这个字段吧
    state = models.SmallIntegerField(_(u"Authorization State"), null=True, default=0, choices=AUTH_STATE)
    active_time = models.DateTimeField(_(u'Active Time'), null=True, blank=True, editable=False)  #生效时间
    disabled_time = models.DateTimeField(_(u'Disabled Time'), null=True, blank=True, editable=False)  #失效时间
    bind_check = models.BooleanField(_('whether binging'), default=True) # 用于区分首次登录设备绑定和APP多账号签到限制两种设置下的数据

    @staticmethod
    def colModels():
        col_models = [
            {'name':'id','hidden':True},
            {'name':'PIN','index':'user__PIN','width':80,'label':u"%s"%(_('PIN')),'frozen':True},
            {'name':'EName','index':'user__EName','width':100,'label':u"%s"%(_('EName')),'frozen':True},
            {'name':'DeptName','index':'UserID__DeptID__DeptName','width':120,'label':u"%s"%(_('department name'))},
            {'name':'ttype','sortable':False,'width':110,'label':u"%s"%(AuthTerminal._meta.get_field('ttype').verbose_name)},
            {'name':'state','sortable':False,'width':110,'label':u"%s"%(AuthTerminal._meta.get_field('state').verbose_name)},
            {'name':'deviceid','sortable':False,'width':100,'label':u"%s"%(AuthTerminal._meta.get_field('deviceid').verbose_name)},
            {'name':'active_time','sortable':False,'width':120,'label':u"%s"%(AuthTerminal._meta.get_field('active_time').verbose_name)},
            {'name':'disabled_time','sortable':False,'width':120,'label':u"%s"%(AuthTerminal._meta.get_field('disabled_time').verbose_name)},
        ]
        # 设置多账号签到限制时不显示设备id列
        if GetParamValue('iapp_app_check_device', '0', 'app') == 1:
            del col_models[6]
        return col_models

    def delete(self):
        if self.state == 1:
            raise Exception(u"%s"%(_(u'The active account is not allowed to be deleted')))
        super(AuthTerminal, self).delete()

    class Admin:
        list_display = ("user__PIN", "user__EName", "deviceid", 'registration_code',"state", "active_time", "disabled_time")
        search_fields = ['user__PIN', 'user__EName']

    class Meta:
        db_table = 'auth_terminal'
        verbose_name = _(u"Terminal Authorization")
        verbose_name_plural = verbose_name
        # unique_together = ["user","ttype","deviceid","state"]
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('terminalStop_authterminal', 'Stop Authterminal'),
        )



OP_TYPE = (
    ("create", _(u'Add')),
    ("update", _(u'Modify')),
    ("delete", _(u'delete')),
    # ("leave", _(u'dimission')),
    # ("restore", _(u'Restore Employee leave')),
)

OP_FLAG = (
    (0, _(u'wait for process')),
    (1, _(u'processed')),
    (2, _(u'process failure')),
)

def GetFlag():
    ret = []
    for op in OP_FLAG:
        dflag = {}
        dflag['symbol'] = op[0]
        dflag['pName'] = str(op[1])
        ret.append(dflag)
    return ret

def GetOpType():
    ret = []
    for i in OP_TYPE:
        o_type = {}
        o_type['symbol'] = i[0]
        o_type['pName'] = str(i[1])
        ret.append(o_type)
    return ret

class middletable(models.Model):
    u"""中间表"""
    table_name = models.CharField(verbose_name=_(u"table name"), max_length=30, null=False)
    data_content=models.CharField(verbose_name=_(u"data content"),max_length=250,blank=False,null=False)
    op_type = models.CharField(verbose_name=_(u"operation type"), max_length=10, null=False, choices=OP_TYPE)
    flag = models.IntegerField(verbose_name=_(u"process state"), null=False, default=0, choices=OP_FLAG)
    msg = models.CharField(verbose_name=_(u"Remarks"), max_length=250, blank=True, null=True)
    create_time = models.DateTimeField(_(u'create time'), null=False)
    update_time = models.DateTimeField(_(u'process time'), null=True)

    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'table_name', 'width': 100, 'label': u"%s"%(_('table name'))},
            {'name': 'data_content', 'sortable': False, 'width': 200, 'align': 'center', 'label': u"%s"%(_(u'data content'))},
            {'name': 'op_type', 'width': 100, 'label': u"%s"%(_(u'operation type'))},
            {'name': 'flag', 'width': 100, 'label': u"%s"%(_(u'process state'))},
            {'name': 'msg', 'sortable': False, 'width': 150, 'label': u"%s"%(_(u'Remarks'))},
            {'name': 'create_time', 'width': 170, 'label': u"%s"%(_(u'create time'))},
            {'name': 'update_time', 'width': 170, 'label': u"%s"%(_(u'process time'))}
        ]

    def save(self, *args, **kwargs):
        self.update_time = datetime.datetime.now()
        super(middletable, self).save(*args, **kwargs)

    class Admin:
        list_filter = ['table_name', 'op_type', 'flag']
        search_fields = ['table_name']

    class Meta:
        db_table = 'middle_table'
        verbose_name = _(u"middle_table")
        verbose_name_plural = verbose_name
        default_permissions = ('browse','delete')


def is_demo_system():
    """
    是否是演示系统数据库
        当数据库的用户表有DEMO系统信息时，也认为是DEMO系统，防止软件连接演示数据库测试时，误删表
    """
    from mysite.accounts.models import MyUser
    try:
        user_count = MyUser.objects.filter(DelTag__in=[-1,-2]).count()
    except:
        user_count = 0
    if settings.DEMO or user_count > 0:
        return True
    else:
        return False

# IS_DEMO_DB = is_demo_system()


class AppUpgradeLog(models.Model):
    #APP服务端信息
    customer_code = models.CharField(_('Customer Number'), max_length=20)  #客户编码
    server_ver = models.CharField(_('Server Verion'), max_length=50)  #客户的ecopro软件版本
    #APP终端信息
    version = models.CharField(_('App Verion'), max_length=50)  #app版本
    appid = models.CharField(_('AppID'), max_length=50)
    os = models.CharField(_('OS'), max_length=50)  #手机系统 Android/iOS
    uuid = models.CharField(_('UUID'), max_length=50)  #手机序列号

    create_time = models.DateTimeField(_(u'Create Time'), null=True, blank=True)
    update_time = models.DateTimeField(_(u'modify time'), null=True, blank=True)  #暂时没用到这个字段


    @staticmethod
    def colModels():
        return [
            {'name':'id', 'hidden':True},
            {'name':'customer_code', 'width':100, 'label': u"%s"%(AppUpgradeLog._meta.get_field('customer_code').verbose_name)},
            {'name':'server_ver', 'width':200, 'label': u"%s"%(AppUpgradeLog._meta.get_field('server_ver').verbose_name)},
            {'name':'version', 'width':150, 'label': u"%s"%(AppUpgradeLog._meta.get_field('version').verbose_name)},
            {'name':'appid', 'width':100, 'label': u"%s"%(AppUpgradeLog._meta.get_field('appid').verbose_name)},
            {'name':'os', 'width':100, 'label': u"%s"%(AppUpgradeLog._meta.get_field('os').verbose_name)},
            {'name':'uuid', 'width':200, 'label': u"%s"%(AppUpgradeLog._meta.get_field('uuid').verbose_name)},
            {'name':'create_time', 'width':150, 'label': u"%s"%(AppUpgradeLog._meta.get_field('create_time').verbose_name)},
            # {'name':'update_time', 'width':150, 'label': u"%s"%(AppUpgradeLog._meta.get_field('update_time').verbose_name)},
        ]

    def save(self, *args, **kwargs):
        if not self.create_time:
            self.create_time = datetime.datetime.now()
        else:
            self.update_time = datetime.datetime.now()
        super(AppUpgradeLog,self).save()

    class Meta:
        db_table = 'app_upgrade_log'
        verbose_name = _(u'App Upgrade Log')
        verbose_name_plural = verbose_name


PRODUCTCODE = (
        (11, _(u'ZKEcoPro')),
        (14, _(u'ZKTime')),
)

class CustomerInfo(models.Model):
    customer_code = models.CharField(_('Customer Number'), max_length=20)  #客户编码
    server_ver = models.CharField(_('Server Verion'), max_length=50)  #软件版本
    productcode = models.IntegerField(verbose_name=_('Product Code'), null=False, default=11, choices=PRODUCTCODE)
    company = models.CharField(_('company name'), max_length=50, blank=True, null=True)
    auth_device_count = models.IntegerField(verbose_name=_('authorization points'), null=False, default=0)
    register_date = models.CharField(_('authorization time'), max_length=50, blank=True, null=True)
    device_count = models.IntegerField(verbose_name=_('number of devices'), null=False, default=0)
    emp_count = models.IntegerField(verbose_name=_('Number of people'), null=False, default=0)
    ip = models.CharField(_('IP'), max_length=100, null=True, blank=True)
    processor = models.CharField(_('processor'), max_length=100, blank=True, null=True)
    opsystem = models.CharField(_('operating system'), max_length=50, blank=True, null=True)
    mac = models.CharField(_('MAC'), max_length=50, blank=True, null=True)
    dbengine = models.CharField(_('DB Engine'), max_length=50, blank=True, null=True)
    create_time = models.DateTimeField(_(u'Create Time'), null=True, blank=True)
    update_time = models.DateTimeField(_(u'modify time'), null=True, blank=True)

    @staticmethod
    def colModels():
        return [
            {'name':'id', 'hidden':True},
            {'name':'customer_code', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('customer_code').verbose_name)},
            {'name':'company', 'width':150, 'label': u"%s"%(CustomerInfo._meta.get_field('company').verbose_name)},
            {'name':'productcode', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('productcode').verbose_name)},
            {'name':'server_ver', 'width':200, 'label': u"%s"%(CustomerInfo._meta.get_field('server_ver').verbose_name)},
            {'name':'register_date', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('register_date').verbose_name)},
            {'name':'auth_device_count', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('auth_device_count').verbose_name)},
            # {'name':'device_count', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('device_count').verbose_name)},
            {'name':'emp_count', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('emp_count').verbose_name)},
            # {'name':'ip', 'width':150, 'label': u"%s"%(CustomerInfo._meta.get_field('ip').verbose_name)},
            # {'name':'processor', 'width':150, 'label': u"%s"%(CustomerInfo._meta.get_field('processor').verbose_name)},
            {'name':'opsystem', 'width':150, 'label': u"%s"%(CustomerInfo._meta.get_field('opsystem').verbose_name)},
            {'name':'mac', 'width':120, 'label': u"%s"%(CustomerInfo._meta.get_field('mac').verbose_name)},
            {'name':'dbengine', 'width':100, 'label': u"%s"%(CustomerInfo._meta.get_field('dbengine').verbose_name)},
            # {'name':'create_time', 'width':150, 'label': u"%s"%(CustomerInfo._meta.get_field('create_time').verbose_name)},
            {'name':'update_time', 'width':150, 'label': u"%s"%(CustomerInfo._meta.get_field('update_time').verbose_name)},
        ]

    def save(self, *args, **kwargs):
        if not self.create_time:
            self.create_time = datetime.datetime.now()
        else:
            self.update_time = datetime.datetime.now()
        super(CustomerInfo,self).save()

    class Meta:
        db_table = 'customer_info'
        verbose_name = _(u'Customer Information')
        verbose_name_plural = verbose_name


class on_the_job(models.Model):
    @staticmethod
    def colModels():
        return [
            {'name':'EName','sortable':False,'width':100,'label':u"%s"%(_('Emp Name'))},
            {'name':'AttDate','index':'AttDate','width':120,'label':u"%s"%(_('AttDate'))},
            {'name':'DeptID','width':60,'index':'UserID__DeptID','label':u"%s"%(_('department number'))},
            {'name': 'DeptName', 'width': 220, 'label': u"%s"%(department._meta.get_field('DeptName').verbose_name)},
            {'name': 'In_Service_Information', 'index': 'parent', 'width': 220, 'label': u"%s"%(_('in-service information'))},
            {'name':'StartTime','sortable':False,'width':100,'label':u"%s"%(_('Check in time'))},
            {'name':'EndTime','sortable':False,'width':100,'label':u"%s"%(_('Signing back time'))},
        ]


class AttendanceGroup(models.Model):
    """
    考勤组
    """
    code = models.CharField(_(u'Numbering'), blank=False, db_column='code', null=False, max_length=40, default='')   #用于显示的编号
    name = models.CharField(_('group name'), max_length=30, null=False, blank=False, db_column='name', default='')

    def __str__(self):
        return "%s %s" % (self.code, self.name)


    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'width': 100, 'hidden': True},
            {'name': 'code', 'label': u"%s" % (_('Numbering'))},
            {'name': 'name', 'index': 'name', 'width': 200, 'label': u"%s" % (_('group name'))},
            {'name': 'emp_count', 'sortable': False, 'width': 80, 'label': u"%s" % (_('employee count'))},
            {'name': 'action', 'sortable': False, 'width': 200, 'label': u"%s" % (_(u'operating'))}
        ]


    def emp_count(self):
        return employee.objects.filter(group_id=self.id).exclude(DelTag=1).exclude(OffDuty=1).count()


    class Meta:
        unique_together = ["code"]
        verbose_name = _('Attendance group')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
            ('set_attendance_group_employee', 'Set attendance group employee'),
            ('browse_attendance_group_employee', 'Browse attendance group employee'),
        )

    @staticmethod
    def objById(id):
        if id == None:
            return None
        d = cache.get("%s_attendance_group_%s" % (settings.UNIT, id))
        if d:
            return d
        try:
            d = AttendanceGroup.objects.get(id=id)
        except:
            d = None
        if d:
            cache.set("%s_attendance_group_%s" % (settings.UNIT, id), d)
        return d

#以下的删除只是删除记录在表empofdevice中相应的数据，和设备的数据没有任何关系
def delEmpInDevice(pin,sn):
    try:
        if pin!=-1 and sn!=-1:# 删除某设备上的某人
            sql="delete from empofdevice where badgenumber=%s and sn=%s"
            params=(pin,sn)
        elif sn!=-1 and pin==-1:# 删除某设备上的所有人
            sql="delete from empofdevice where sn=%s"
            params=(sn,)
        elif sn==-1 and pin!=-1:# 删除所有设备上的某人
            sql="delete from empofdevice where badgenumber=%s"
            params=(pin,)
        elif sn==-1 and pin==-1:# 删除所有数据
            sql="delete from empofdevice"
            params=()

        customSql(sql,params)
    except Exception as e:
        #connection.commit()
        print ("delEmpInDevice",e)
        pass




def batchSql(sqls):
    for s in sqls:
        customSql(s)
#			print "OK1: ", s









def __mci_init__(self, field):
    self.field = field
    q=[]
    try:
        for obj in field.queryset.all():
            q.append(obj)
        self.queryset=q
    except:
        connection.close()
        for obj in field.queryset.all():
            q.append(obj)
    self.queryset=q

def __mci_iter__(self):
    if self.field.empty_label is not None:
        yield (u"", self.field.empty_label)
    if self.field.cache_choices:
        if self.field.choice_cache is None:
            self.field.choice_cache = [
                self.choice(obj) for obj in self.queryset
            ]
        for choice in self.field.choice_cache:
            yield choice
    else:
        for obj in self.queryset:
            yield self.choice(obj)

#if settings.DATABASE_ENGINE=="sql_server":
#	ModelChoiceIterator.__init__=__mci_init__
#	ModelChoiceIterator.__iter__=__mci_iter__







def UpdateDeptCache():
    stamp='%s%s'%(settings.UNIT,datetime.datetime.now().strftime("%Y%m%d%H%M%S%f"))
    SetParamValue('DEPTVERSION',stamp)

    settings.DEPTVERSION=stamp











#需要为老版客户升级数据库时使用此函数， db建立数据库时不执行此函数
def UpdateDatabase():  #2009.2.25之后尽量使用此功能升级数据库
#	from iclock.iutils import GetParamValue
    dbVer=int(GetParamValue('ADMSDBVersion',100))
    if dbVer==-10000:
        return
    if dbVer<600:#ZKEcoPro从600开始
        sqls=(
              "ALTER TABLE %s ADD MinsDay integer NULL"%(attShifts._meta.db_table),
              "ALTER TABLE %s ADD AbNormiteID integer NULL" % (transactions._meta.db_table),
              "ALTER TABLE %s ADD SchID integer NULL" % (transactions._meta.db_table),
              "ALTER TABLE %s ADD NewType VARCHAR(3) NULL" % (transactions._meta.db_table),

        )
        batchSql(sqls)

        # NewType = models.CharField(_('NewType'), max_length=3, null=True, blank=True)
        # AbNormiteID = models.IntegerField(null=True, blank=True)
        # SchID = models.IntegerField(_('Schclass'), null=True, blank=True)

        #	if settings.DATABASE_ENGINE == 'oracle':
    #		sqls=("ALTER TABLE %s ADD opStamp TIMESTAMP DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD StartRestTime TIMESTAMP DEFAULT NULL"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD EndRestTime TIMESTAMP DEFAULT NULL"%(SchClass._meta.db_table),
    #		)
    #	elif settings.DATABASE_ENGINE == 'sql_server':
    #		sqls=("ALTER TABLE %s ADD opStamp datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD StartRestTime datetime DEFAULT NULL"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD EndRestTime datetime DEFAULT NULL"%(SchClass._meta.db_table),
    #		)
    #	else:
    #		sqls=("ALTER TABLE %s ADD opStamp datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD StartRestTime time DEFAULT NULL"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD EndRestTime time DEFAULT NULL"%(SchClass._meta.db_table),
    #		)
    #
    #	batchSql(sqls)
    #if dbVer<109:
    #	if settings.DATABASE_ENGINE == 'mysql':
    #		sqls=(
    #			"CREATE TABLE accounts(id int(11) NOT NULL AUTO_INCREMENT,StartTime  datetime NOT NULL,EndTime datetime NOT NULL,"\
    #			"Type int(11) NOT NULL,Status int(11) DEFAULT 0,  Reserved varchar(20) DEFAULT NULL,  User_id int(11) DEFAULT NULL,"\
    #			"PRIMARY KEY (`id`),  UNIQUE KEY StartTime (StartTime,EndTime,Type,Status),  KEY accounts_User_id (User_id)) ENGINE=MyISAM DEFAULT CHARSET=utf8",
    #		)
    #		batchSql(sqls)
    #	if settings.DATABASE_ENGINE == 'sql_server':
    #		sqls=(
    #			"CREATE TABLE accounts(id INT IDENTITY(1,1) NOT NULL ,"\
    #			"StartTime datetime NOT NULL,"\
    #			"EndTime datetime NOT NULL,Type int NOT NULL,Status int DEFAULT 0,Reserved varchar(20) NULL,User_id int NULL,PRIMARY KEY (id),"\
    #			"UNIQUE NONCLUSTERED (StartTime,EndTime,Type,Status),"\
    #			"FOREIGN KEY (User_id) REFERENCES auth_user(id))",
    #			)
    #		batchSql(sqls)
    #	if settings.DATABASE_ENGINE == 'oracle':
    #		sqls=(
    #		    '''CREATE TABLE "ACCOUNTS" ( "ID" NUMBER(11) NOT NULL PRIMARY KEY,"STARTTIME" TIMESTAMP NOT NULL,"ENDTIME" TIMESTAMP NOT NULL,"TYPE" NUMBER(11) NOT NULL,"STATUS" NUMBER(11) NULL,"RESERVED" NVARCHAR2(20) NULL,"USER_ID" NUMBER(11) NULL REFERENCES "AUTH_USER" ("ID") DEFERRABLE INITIALLY DEFERRED,UNIQUE ("STARTTIME", "ENDTIME", "TYPE", "STATUS"))''',
    #	   "DECLARE"\
    #		"   i INTEGER;"\
    #		"BEGIN"\
    #		"   SELECT COUNT(*) INTO i FROM USER_CATALOG"\
    #		"	   WHERE TABLE_NAME = 'ACCOUNTS_SQ' AND TABLE_TYPE = 'SEQUENCE';"\
    #		"   IF i = 0 THEN"\
    #		"	   EXECUTE IMMEDIATE 'CREATE SEQUENCE ACCOUNTS_SQ';"\
    #		"  END IF;"\
    #		"END;"\
    #		"/",
    #
    #		"CREATE OR REPLACE TRIGGER ACCOUNTS_TR"\
    #		"    BEFORE INSERT ON ACCOUNTS"\
    #		"    FOR EACH ROW"\
    #		"    WHEN (new.ID IS NULL)"\
    #		"   BEGIN"\
    #		"	   SELECT ACCOUNTS_SQ.nextval"\
    #		"	   INTO :new.ID FROM dual;"\
    #		"   END;"\
    #		"/",
    #					   )
    #
    #		batchSql(sqls)
    #	checkAndCreateModelPermissions(iclock._meta.app_label)
    #if dbVer<110:
    #	sqls=("ALTER TABLE %s ADD LeaveType integer DEFAULT 0"%(LeaveClass._meta.db_table),
    #	      "ALTER TABLE %s ADD  clearance integer DEFAULT NULL"%(LeaveClass._meta.db_table),
    #	      )
    #	batchSql(sqls)
    #if dbVer<111:
    #	sqls=("ALTER TABLE %s ADD Type integer DEFAULT 0"%(attCalcLog._meta.db_table),
    #	      "update attcalclog set type=0",
    #	      "ALTER TABLE %s alter column  Checktype VARCHAR(5) "%(transactions._meta.db_table),
    #	      )
    #	#batchSql(sqls)
    #if dbVer<112:
    #	checkAndCreateModelPermissions(iclock._meta.app_label)
    #if dbVer<113:
    #	if settings.DATABASE_ENGINE == 'sql_server':
    #		sqls=("CREATE TABLE [iclock_iclockdept] ("\
    #			"[id] int IDENTITY (1, 1) NOT NULL PRIMARY KEY,"\
    #			"[SN_id] varchar(20) NOT NULL REFERENCES [iclock] ([SN]),"\
    #			"[dept_id] int NOT NULL REFERENCES [departments] ([DeptID]))",
    #			)
    #	elif settings.DATABASE_ENGINE == 'oracle':
    #		sqls=('''CREATE TABLE "ICLOCK_ICLOCKDEPT" ( "ID" NUMBER(11) NOT NULL PRIMARY KEY, "SN_ID" NVARCHAR2(20) NOT NULL REFERENCES "ICLOCK" ("SN") DEFERRABLE INITIALLY DEFERRED,"DEPT_ID" NUMBER(11) NOT NULL REFERENCES "DEPARTMENTS" ("DEPTID") DEFERRABLE INITIALLY DEFERRED)''',
    #			 )
    #	else:
    #		sqls=("CREATE TABLE iclock_iclockdept ("\
    #			  "id int(11) NOT NULL AUTO_INCREMENT,"\
    #			  "SN_id varchar(20) NOT NULL,"\
    #			  "dept_id int(11) NOT NULL,"\
    #			  "PRIMARY KEY (id),"\
    #				"FOREIGN KEY (SN_id) REFERENCES iclock (SN),"\
    #				"FOREIGN KEY (dept_id) REFERENCES departments (DeptID)) ENGINE=MyISAM DEFAULT CHARSET=utf8",
    #				)
    #
    #	batchSql(sqls)
    #
#	if dbVer<114:
#		if settings.DATABASE_ENGINE == 'mysql':
#			sqls=("ALTER TABLE %s modify column  badgenumber VARCHAR(24) "%(employee._meta.db_table),
#		      "ALTER TABLE %s modify column  street VARCHAR(80) "%(employee._meta.db_table),
#		      )
#		else:
#			sqls=("ALTER TABLE %s alter column  badgenumber VARCHAR(24) "%(employee._meta.db_table),
#			"ALTER TABLE %s alter column  street VARCHAR(80) "%(employee._meta.db_table),
#		      )
#		batchSql(sqls)
#		if settings.DATABASE_ENGINE == 'mysql':
#			sqls=("CREATE TABLE attpriReport(id int(11) NOT NULL AUTO_INCREMENT,userid int(11) NOT NULL,AttDate datetime NOT NULL,AttChkTime varchar(100) DEFAULT NULL,AttAddChkTime varchar(100) DEFAULT NULL,AttLeaveTime varchar(100) DEFAULT NULL,SchName varchar(100) DEFAULT NULL,OP int(11) DEFAULT NULL,Reserved varchar(50) DEFAULT NULL,PRIMARY KEY (id),UNIQUE KEY userid(userid,AttDate), KEY attpriReport_userid (userid)) ENGINE=MyISAM DEFAULT CHARSET=utf8"),
#		elif settings.DATABASE_ENGINE == 'sql_server':
#			sqls=("CREATE TABLE attpriReport(id INT IDENTITY NOT NULL,userid INT NOT NULL,AttDate DATETIME NOT NULL,AttChkTime VARCHAR (100) NULL,AttAddChkTime VARCHAR (100) NULL,AttLeaveTime VARCHAR (100) NULL,SchName VARCHAR (100) NULL,OP INT NULL,Reserved VARCHAR (50) NULL,PRIMARY KEY (id),UNIQUE (userid,AttDate),FOREIGN KEY (userid) REFERENCES userinfo (userid))"),
#		elif settings.DATABASE_ENGINE == 'oracle':
##			sqls=('''CREATE TABLE "ATTPRIREPORT" ("ID" NUMBER(11) NOT NULL PRIMARY KEY,"USERID" NUMBER(11) NOT NULL REFERENCES "USERINFO" ("USERID") DEFERRABLE INITIALLY DEFERRED,"ATTDATE" TIMESTAMP NOT NULL,"ATTCHKTIME" NVARCHAR2(100),"ATTADDCHKTIME" NVARCHAR2(100),"ATTLEAVETIME" NVARCHAR2(100),"SCHNAME" NVARCHAR2(100),"OP" NUMBER(11),"RESERVED" NVARCHAR2(50),UNIQUE ("USERID", "ATTDATE"));''',)
#			sqls=("CREATE TABLE ATTPRIREPORT (ID NUMBER(11) NOT NULL PRIMARY KEY,"\
#				"USERID NUMBER(11) NOT NULL REFERENCES USERINFO (USERID) DEFERRABLE INITIALLY DEFERRED,"\
#				"ATTDATE TIMESTAMP NOT NULL,"\
#				"ATTCHKTIME NVARCHAR2(100),"\
#				"ATTADDCHKTIME NVARCHAR2(100),"\
#				"ATTLEAVETIME NVARCHAR2(100),"\
#				"SCHNAME NVARCHAR2(100),"\
#				"OP NUMBER(11),"\
#				"RESERVED NVARCHAR2(50),"\
#				"UNIQUE (USERID, ATTDATE));",
#
#				"DECLARE"\
#				"    i INTEGER;"\
#				"BEGIN"\
#				"    SELECT COUNT(*) INTO i FROM USER_CATALOG"\
#				"        WHERE TABLE_NAME = 'ATTPRIREPORT_SQ' AND TABLE_TYPE = 'SEQUENCE';"\
#				"    IF i = 0 THEN"\
#				"        EXECUTE IMMEDIATE 'CREATE SEQUENCE ATTPRIREPORT_SQ';"\
#				"    END IF;"\
#				"END;"\
#				"/",
#
#				"CREATE OR REPLACE TRIGGER ATTPRIREPORT_TR"\
#				"    BEFORE INSERT ON ATTPRIREPORT"\
#				"    FOR EACH ROW"\
#				"    WHEN (new.ID IS NULL)"\
#				"    BEGIN"\
#				"        SELECT ATTPRIREPORT_SQ.nextval"\
#				"        INTO :new.ID FROM dual;"\
#				"    END;"\
#				"/",
#				)
#
#		batchSql(sqls)
    #if dbVer<115:
    #	checkAndCreateModelPermissions(iclock._meta.app_label)
    #if dbVer<116:
    #	if settings.DATABASE_ENGINE=="mysql":
    #		sqls=("ALTER TABLE %s modify  object VARCHAR(100) NULL"%(adminLog._meta.db_table),
    #			  "ALTER TABLE %s ADD  UserSpedayID integer DEFAULT 0"%(AttException._meta.db_table),
    #			)
    #	elif settings.DATABASE_ENGINE == 'oracle':
    #		sqls=('''ALTER TABLE iclock_adminlog modify ("OBJECT" NVARCHAR2(100))''',
    #			 "ALTER TABLE %s ADD  UserSpedayID integer DEFAULT 0"%(AttException._meta.db_table),
    #			)
    #	else:
    #		sqls=("ALTER TABLE %s alter column  object VARCHAR(100) NULL"%(adminLog._meta.db_table),
    #			   "ALTER TABLE %s ADD  UserSpedayID integer DEFAULT 0"%(AttException._meta.db_table),
    #			)
    #
    #	batchSql(sqls)
    #if dbVer<200:
    #	if settings.DATABASE_ENGINE == 'oracle':
    #		sqls=("ALTER TABLE %s ADD StartRestTime1 TIMESTAMP DEFAULT NULL"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD EndRestTime1 TIMESTAMP DEFAULT NULL"%(SchClass._meta.db_table),
    #		)
    #	elif settings.DATABASE_ENGINE == 'sql_server':
    #		sqls=("ALTER TABLE %s ADD StartRestTime1 datetime DEFAULT NULL"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD EndRestTime1 datetime DEFAULT NULL"%(SchClass._meta.db_table),
    #		)
    #	else:
    #		sqls=("ALTER TABLE %s ADD StartRestTime1 time DEFAULT NULL"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD EndRestTime1 time DEFAULT NULL"%(SchClass._meta.db_table),
    #		)
    #	batchSql(sqls)
    #if dbVer<202:
    #	checkAndCreateModelPermissions(iclock._meta.app_label)
#	if dbVer<205:
#		sqls=("delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('import_employee'),
#			"delete from auth_permission where codename='%s'"%('import_employee'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('optionsDatabase_employee'),
#			"delete from auth_permission where codename='%s'"%('optionsDatabase_employee'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('clearObsoleteData_transaction'),
#			"delete from auth_permission where codename='%s'"%('clearObsoleteData_transaction'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('init_database'),
#			"delete from auth_permission where codename='%s'"%('init_database'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('add_user_temp_sch'),
#			"delete from auth_permission where codename='%s'"%('add_user_temp_sch'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('delete_user_temp_sch'),
#			"delete from auth_permission where codename='%s'"%('delete_user_temp_sch'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_user_temp_sch'),
#			"delete from auth_permission where codename='%s'"%('change_user_temp_sch'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_useracprivilege'),
#			"delete from auth_permission where codename='%s'"%('change_useracprivilege'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('AuditedTrans_transaction'),
#			"delete from auth_permission where codename='%s'"%('AuditedTrans_transaction'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('definedReport_itemdefine'),
#			"delete from auth_permission where codename='%s'"%('definedReport_itemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('upgradefw_iclock'),
#			"delete from auth_permission where codename='%s'"%('upgradefw_iclock'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('copyudata_iclock'),
#			"delete from auth_permission where codename='%s'"%('copyudata_iclock'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('resetPwd_iclock'),
#			"delete from auth_permission where codename='%s'"%('resetPwd_iclock'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('restoreData_iclock'),
#			"delete from auth_permission where codename='%s'"%('restoreData_iclock'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('add_itemdefine'),
#			"delete from auth_permission where codename='%s'"%('add_itemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('delete_itemdefine'),
#			"delete from auth_permission where codename='%s'"%('delete_itemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_itemdefine'),
#			"delete from auth_permission where codename='%s'"%('change_itemdefine'),
#
#			)
#		batchSql(sqls)
#		checkAndCreateModelPermissions(iclock._meta.app_label)
#	if dbVer<500:
    #if dbVer<501:
    #	AttParam.objects.filter(ParaName__startswith='opt_check_').delete()
    #if dbVer<502:
    #	sqls=("ALTER TABLE %s ADD  ProductType integer DEFAULT 1"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD  AlgVer integer DEFAULT 0"%(fptemp._meta.db_table),	)
    #
    #	batchSql(sqls)
    #if dbVer<503:
    #	sqls=("ALTER TABLE %s ADD Authentication integer DEFAULT 1"%(iclock._meta.db_table),)
    #	batchSql(sqls)
    #if dbVer<504:
    #	sqls=(" ALTER TABLE %s ADD  VerifyType integer DEFAULT 0"%(ACGroup._meta.db_table),
    #		  "ALTER TABLE %s ADD  HolidayValid integer DEFAULT 0"%(ACGroup._meta.db_table),)
    #	batchSql(sqls)
    #	checkAndCreateModelPermissions(iclock._meta.app_label)
#	if dbVer<505:
#		sqls=(
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('add_iaccdevitemdefine'),
#			"delete from auth_permission where codename='%s'"%('add_iaccdevitemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('delete_iaccdevitemdefine'),
#			"delete from auth_permission where codename='%s'"%('delete_iaccdevitemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_iaccdevitemdefine'),
#			"delete from auth_permission where codename='%s'"%('change_iaccdevitemdefine'),
#
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('add_iaccempitemdefine'),
#			"delete from auth_permission where codename='%s'"%('add_iaccempitemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('delete_iaccempitemdefine'),
#			"delete from auth_permission where codename='%s'"%('delete_iaccempitemdefine'),
#			"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_iaccempitemdefine'),
#			"delete from auth_permission where codename='%s'"%('change_iaccempitemdefine'),
#
#			)
#		batchSql(sqls)
#		checkAndCreateModelPermissions(iclock._meta.app_label)
    #if dbVer<507:
    #	sqls=("ALTER TABLE %s ADD isFace integer DEFAULT 0"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD isFptemp integer DEFAULT 0"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD isUSERPIC integer DEFAULT 0"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD FaceAlgVer varchar(20) NULL"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD faceNumber integer DEFAULT 0"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD faceTempNumber integer DEFAULT 0"%(iclock._meta.db_table),
    #		"ALTER TABLE %s ADD pushver varchar(20) NULL"%(iclock._meta.db_table),
    #		"drop index USERFINGER on %s"%(fptemp._meta.db_table),
    #		"drop index userid on %s"%(fptemp._meta.db_table),
    #		"CREATE UNIQUE INDEX USERFINGER ON %s(USERID, FINGERID,ALGVER)"%(fptemp._meta.db_table),
    #		)
    #	batchSql(sqls)
    #	checkAndCreateModelPermissions(iclock._meta.app_label)
    #if dbVer<513:
    #	User=get_user_model()
    #
    #	sqls=("ALTER TABLE %s ADD AutheTimeDept integer DEFAULT NULL"%(User._meta.db_table),
    #		"ALTER TABLE %s ADD ophone varchar(30) NULL"%(User._meta.db_table),
    #		"ALTER TABLE %s ADD is_alldept bit"%(User._meta.db_table),
    #		"ALTER TABLE %s ADD is_public bit"%(User._meta.db_table),
    #		"ALTER TABLE %s ADD CheckInMins1 integer DEFAULT 120"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD CheckInMins2 integer DEFAULT 120"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD CheckOutMins1 integer DEFAULT 120"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD CheckOutMins2 integer DEFAULT 120"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD TimeZoneType integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD WorkTimeType integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD BeforeInMins integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD AfterInMins integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD IsCalcOverTime integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD OverTimeMins integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD NextDay integer DEFAULT 1"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD TimeZoneOfDept integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD isHidden integer DEFAULT 0"%(SchClass._meta.db_table),
    #		"ALTER TABLE %s ADD Num_RunOfDept integer DEFAULT 0"%(NUM_RUN._meta.db_table),
    #		)
    #	batchSql(sqls)
    ##if dbVer<514:
    #	from mysite.auth_code import auth_code
    #	SetParamValue('InstallDate',auth_code(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),'ENCODE'))
    #
    #	#sqls=("ALTER TABLE %s ADD DeptNo integer DEFAULT NULL"%(department._meta.db_table),
    #	#	"ALTER TABLE %s ADD deptid integer DEFAULT 1"%(transactions._meta.db_table),
    #	 #     )
        #batchSql(sqls)
    #if dbVer<515:
    #	sqls=("ALTER TABLE %s ADD Educational varchar(2) DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Trialstarttime datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Trialendtime datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Startwork datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Worktime datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Contractstarttime datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Contractendtime datetime DEFAULT NULL"%(employee._meta.db_table),
    #		"ALTER TABLE %s ADD Employeetype varchar(2) DEFAULT NULL"%(employee._meta.db_table),
    #	      )
    #	batchSql(sqls)

    #if dbVer<516:
    #	if settings.DATABASE_ENGINE=="mysql":
    #		sqls=( "ALTER TABLE %s ADD  DeptNumber varchar(40) not null"%(department._meta.db_table),
    #		    "ALTER TABLE %s modify  column DeptID int auto_increment"%(department._meta.db_table),
    #
    #			)
    #	elif settings.DATABASE_ENGINE == 'oracle':
    #		sqls=("ALTER TABLE %s modify Deptid int"%(department._meta.db_table),
    #			 "ALTER TABLE %s ADD  DeptNumber varchar(40) not null"%(department._meta.db_table),
    #			)
    #	else:
    #		sqls=(
    #		    "ALTER TABLE %s ADD  DeptNumber varchar(40)"%(department._meta.db_table),
    #		    "ALTER TABLE %s alter column  DeptID int IDENTITY (1, 1) NOT NULL"%(department._meta.db_table),
    #
    #			)
    #	batchSql(sqls)
    #	objs=department.objects.all()
    #	for o in objs:
    #	    o.DeptNumber=str(o.DeptID)
    #	    try:
    #		o.save()
    #	    except Exception as e:
    #		print "DeptNumber==",e,o.DeptID
    #if dbVer<517:
    #	sqls=("ALTER TABLE %s ADD roleLevel integer DEFAULT NULL"%(userRoles._meta.db_table),
    #	"ALTER TABLE %s ADD roleid integer"%(USER_OVERTIME._meta.db_table),
    #	"ALTER TABLE %s ADD roleid integer"%(USER_SPEDAY._meta.db_table),
    #	"ALTER TABLE %s ADD process varchar(80)"%(USER_OVERTIME._meta.db_table),
    #	"ALTER TABLE %s ADD process varchar(80)"%(USER_SPEDAY._meta.db_table),
    #	      )
    #	batchSql(sqls)
    #if dbVer<518:
    #	sqls=("ALTER TABLE %s ADD State integer DEFAULT 0"%(userRoles._meta.db_table),
    #		  )
    #	batchSql(sqls)
    #if dbVer<519:
    #	sqls=("ALTER TABLE %s ADD oldprocess varchar(80)"%(USER_OVERTIME._meta.db_table),
    #			"ALTER TABLE %s ADD oldprocess varchar(80)"%(USER_SPEDAY._meta.db_table),
    #		  )
    #	batchSql(sqls)
    #if dbVer<520:
    #	if settings.DATABASE_ENGINE=="mysql":
    #		sqls=( "ALTER TABLE %s modify column AUTHOR_id int DEFAULT NULL"%(ItemDefine._meta.db_table),
    #		    "ALTER TABLE %s ADD datest datetime DEFAULT NULL"%(USER_OF_RUN._meta.db_table),
    #
    #			)
    #	elif settings.DATABASE_ENGINE == 'oracle':
    #		sqls=(
    #		    "ALTER TABLE %s ADD  datest TIMESTAMP DEFAULT NULL"%(USER_OF_RUN._meta.db_table),
    #		    "ALTER TABLE %s modify column  author_id int  NULL"%(ItemDefine._meta.db_table),
    #			)
    #	else:
    #		sqls=(
    #		    "ALTER TABLE %s ADD  datest datetime DEFAULT NULL"%(USER_OF_RUN._meta.db_table),
    #		    "ALTER TABLE %s alter column  author_id int  NULL"%(ItemDefine._meta.db_table),
    #			)
    #	batchSql(sqls)
    #if dbVer<524:
    #	sqls=(
    #		"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_employeelog'),
    #		"delete from auth_permission where codename='%s'"%('change_employeelog'),
    #		"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('add_employeelog'),
    #		"delete from auth_permission where codename='%s'"%('add_employeelog'),
    #		"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('add_adminlog'),
    #		"delete from auth_permission where codename='%s'"%('add_adminlog'),
    #		"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('change_adminlog'),
    #		"delete from auth_permission where codename='%s'"%('change_adminlog'),
    #		)
    #	batchSql(sqls)
    #if dbVer<525:
    #	sqls=(
    #		"delete from auth_group_permissions where PERMISSION_ID=(select id from auth_permission where codename='%s')"%('browse_user_temp_sch'),
    #		"delete from auth_permission where codename='%s'"%('browse_user_temp_sch'),
    #		)
    #	batchSql(sqls)

    #if dbVer<540 and dbVer>0:
    #	User=get_user_model()
    #
    #	sqls=(
    #	"ALTER TABLE %s ADD Purpose integer DEFAULT 0"%(iclock._meta.db_table),
    #	"ALTER TABLE %s ADD emp_pin varchar(30) null"%(User._meta.db_table),
    #	)
    #	#batchSql(sqls)
    #
    #	if settings.DATABASE_ENGINE=="mysql":
    #		sqls=(
    #		"ALTER TABLE %s CHANGE COLUMN templateid id  int(11) NOT NULL AUTO_INCREMENT FIRST"%(facetemp._meta.db_table),
    #		"ALTER TABLE %s CHANGE COLUMN templateid id  int(11) NOT NULL AUTO_INCREMENT FIRST"%(fptemp._meta.db_table),
    #		)
    #		#batchSql(sqls)
    #	elif 'sql_server' in settings.DATABASE_ENGINE:
    #		sqls=(
    #		"EXEC sp_rename '[facetemplate].[templateid]', 'id', 'COLUMN'",
    #		"EXEC sp_rename '[template].[templateid]', 'id', 'COLUMN'",
    #		)
    #		#batchSql(sqls)
    #	elif 'oracle' in settings.DATABASE_ENGINE:
    #		sqls=(
    #		"ALTER TABLE %s RENAME COLUMN TEMPLATEID TO ID"%(facetemp._meta.db_table.upper()),
    #		"ALTER TABLE %s RENAME COLUMN TEMPLATEID TO ID"%(fptemp._meta.db_table.upper()),
    #		)
    #		#batchSql(sqls)
    #elif dbVer<541:
    #	sqls=(
    #	"ALTER TABLE %s ADD roleid integer "%(checkexact._meta.db_table),
    #	"ALTER TABLE %s ADD process varchar(80) null"%(checkexact._meta.db_table),
    #	"ALTER TABLE %s ADD oldprocess varchar(80) null"%(checkexact._meta.db_table),
    #	"ALTER TABLE %s ADD processid integer"%(checkexact._meta.db_table),
    #	"ALTER TABLE %s ADD procSN integer"%(checkexact._meta.db_table),
    #	)
    #	batchSql(sqls)
    #elif dbVer<542:
    #	if settings.DATABASE_ENGINE=="mysql":
    #		sqls=( "ALTER TABLE %s modify column Overtime float DEFAULT 0"%(attShifts._meta.db_table),
    #		)
    #	elif 'sql_server' in settings.DATABASE_ENGINE:
    #		sqls=(
    #		"ALTER TABLE %s  ALTER COLUMN  OverTime float "%(attShifts._meta.db_table),
    #		)
    #		batchSql(sqls)
    #	elif 'oracle' in settings.DATABASE_ENGINE:
    #		pass
    #elif dbVer<543:
    #	from mysite.meeting.models import Meet_order,Meet
    #	if 'oracle' in settings.DATABASE_ENGINE:
    #		sqls=(
    #		    "ALTER TABLE %s ADD  lunchtimestr TIMESTAMP DEFAULT NULL"%(Meet_order._meta.db_table),
    #		    "ALTER TABLE %s ADD  lunchtimeend TIMESTAMP DEFAULT NULL"%(Meet_order._meta.db_table),
    #		    "ALTER TABLE %s ADD  lunchtimestr TIMESTAMP DEFAULT NULL"%(Meet._meta.db_table),
    #		    "ALTER TABLE %s ADD  lunchtimeend TIMESTAMP DEFAULT NULL"%(Meet._meta.db_table),
    #		)
    #	else:
    #		sqls=(
    #		"ALTER TABLE %s ADD lunchtimestr datetime DEFAULT NULL"%(Meet_order._meta.db_table),
    #		    "ALTER TABLE %s ADD lunchtimeend datetime DEFAULT NULL"%(Meet_order._meta.db_table),
    #		    "ALTER TABLE %s ADD lunchtimestr datetime DEFAULT NULL"%(Meet._meta.db_table),
    #		    "ALTER TABLE %s ADD lunchtimeend datetime DEFAULT NULL"%(Meet._meta.db_table),
    #		)
    #	batchSql(sqls)
    #elif dbVer<544:
    #	sqls=(
    #	"ALTER TABLE %s ADD pri integer "%(empofdevice._meta.db_table),
    #	)
    #	batchSql(sqls)
    #
    if dbVer<600:           #更新成最新版本号 要参看base\management\_init_.py中的upgradeDB
        SetParamValue('ADMSDBVersion','600')


try:
    UpdateDatabase()    #配合models的对数据库的修改实现对数据库的相应更新
except Exception as e:
    print ("updatedatabase=",e)
    pass




settings.DEPTVERSION=GetParamValue('DEPTVERSION')
createDir(reportDir())
createDir(photoDir())
