{% extends "data_list.html" %}
{% load i18n %}
{% load iclock_tags %}
<script>
{% block tblHeader %}
jqOptions[g_activeTabID].colModel={{colModel}}
tblName[g_activeTabID]='FirmwareConfig';
jqOptions[g_activeTabID].sortname='sort_order';
jqOptions[g_activeTabID].pager="id_pager_FirmwareConfig"
options[g_activeTabID].dlg_width=525;
options[g_activeTabID].dlg_height=400;
options[g_activeTabID].edit_col=1;
dtFields = "{{ dtFields }}";
$(function(){

    smenu="<ul><li  class='subnav_on' onclick=submenuClick('/iclock/data/FirmwareConfig/',this);><a href='#'>{% trans "Maintenance Maintenance" %}</a></li></ul>"

	$("#"+g_activeTabID+" #id_newrec").click(function(event){
		processNewModel();
	});
	$("#"+g_activeTabID+" #id_custom").remove()
	$("#"+g_activeTabID+" #queryButton").hide()
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowFirmwareConfig();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
	    if(event.keyCode==13)
	    searchShowFirmwareConfig();
	});
	$("#"+g_activeTabID+" #searchbar").val('{% trans "特性项名称" %}')
});
function process_dialog_FirmwareConfig(htmlObj){
	$('#id_feature_name',htmlObj).css('width','150px')
	if ((navigator.userAgent.indexOf('MSIE') >= 0) && (navigator.userAgent.indexOf('Opera') < 0)){
		$('#id_roleName',htmlObj).css('width','150px')
	} else{
		$('#id_roleName',htmlObj).css('width','152px')
	}
	$('#id_roleLevel',htmlObj).css('width','150px')
	f=$(htmlObj).find("#id_edit_form").get(0)
	$(f).validate({
				rules: {
					"feature_name":{"required":true,},
					"parameter_value":{"required":true,}
				}
	});
}
function strOfData_FirmwareConfig(data)
{
	return stripHtml(data.feature_name)+" "+data.roleName;
}

//模糊查询
function searchShowFirmwareConfig(){
	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
	var url=g_urls[g_activeTabID]+"?q="+encodeURI(v)
	savecookie("search_urlstr",url);
	$("#id_grid_"+tblName[g_activeTabID]).jqGrid('setGridParam',{url:url,datatype:"json"}).trigger("reloadGrid");
}

function afterPost_FirmwareConfig(flag, obj) {
    $('#id_feature_name',obj).val(auto_increase_number($('#id_feature_name',obj).val()));
    $('#id_roleName').val("")
    reloadData()
}

{% endblock %}

</script>

{% block importOp %}
{% endblock %}

{% block exportOp %}
{% endblock %}
