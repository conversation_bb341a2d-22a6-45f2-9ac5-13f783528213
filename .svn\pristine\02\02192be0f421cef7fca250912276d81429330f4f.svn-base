#!/usr/bin/env python
#coding=utf-8
from mysite.base.models import AuthDevice

def check_max_device():
    """
    检查是否到达设备上限
    """
    from mysite.base.models import Tenant
    from mysite.iclock.models import iclock

    max_device_capacity = Tenant.objects.first().max_device_capacity
    device_count = iclock.objects.filter(DelTag=0).count()
    if max_device_capacity != 0 and device_count >= max_device_capacity:
        return False
    return True

def check_auth_device(SN):
    """
    检查设备序列号是否已授权
    """
    SN = SN.upper()
    try:
        AuthDevice.objects.get(SN=SN)
        return True
    except:
        return False

def check_max_user():
    """
    检查是否到达人员上限
    """
    from mysite.base.models import Tenant
    from mysite.iclock.models import employee

    max_user_capacity = Tenant.objects.first().max_user_capacity
    user_count = employee.objects.filter(DelTag=0, OffDuty=0).count()
    if max_user_capacity != 0 and user_count >= max_user_capacity:
        return False
    return True