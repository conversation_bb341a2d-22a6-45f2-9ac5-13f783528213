# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2018-08-24 16:15
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ipos', '0013_auto_20180706_0806'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='aliwxfulllog',
            options={'default_permissions': ('browse',), 'verbose_name': 'Network Recharge Table', 'verbose_name_plural': 'Network Recharge Table'},
        ),
        migrations.AlterModelOptions(
            name='allowance',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('allowanceAudit_allowance', 'Audit Allowance'),), 'verbose_name': 'subsidy form', 'verbose_name_plural': 'subsidy form'},
        ),
        migrations.AlterModelOptions(
            name='backcard',
            options={'default_permissions': ('browse',), 'verbose_name': 'Retire card form', 'verbose_name_plural': 'Retire card form'},
        ),
        migrations.AlterModelOptions(
            name='batchtime',
            options={'default_permissions': ('browse', 'change'), 'verbose_name': 'Consumption time period', 'verbose_name_plural': 'Consumption time period'},
        ),
        migrations.AlterModelOptions(
            name='cardcashsz',
            options={'verbose_name': 'Card cash receipts and payments', 'verbose_name_plural': 'Card cash receipts and payments'},
        ),
        migrations.AlterModelOptions(
            name='cardcashszbak',
            options={'default_permissions': (), 'verbose_name': 'Card Cash Revenue Backup Table', 'verbose_name_plural': 'Card Cash Revenue Backup Table'},
        ),
        migrations.AlterModelOptions(
            name='cardcashtype',
            options={'default_permissions': (), 'verbose_name': 'Card Cash Consumption Type', 'verbose_name_plural': 'Card Cash Consumption Type'},
        ),
        migrations.AlterModelOptions(
            name='cardmanage',
            options={'default_permissions': ('browse',), 'verbose_name': 'Manage Card Table', 'verbose_name_plural': 'Manage Card Table'},
        ),
        migrations.AlterModelOptions(
            name='dininghall',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Restaurant Information', 'verbose_name_plural': 'Restaurant Information'},
        ),
        migrations.AlterModelOptions(
            name='handconsume',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Manual replenishment table', 'verbose_name_plural': 'Manual replenishment table'},
        ),
        migrations.AlterModelOptions(
            name='iccard',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Card Information', 'verbose_name_plural': 'Card Information'},
        ),
        migrations.AlterModelOptions(
            name='iccardmechine',
            options={'default_permissions': (), 'verbose_name': 'available device', 'verbose_name_plural': 'available device'},
        ),
        migrations.AlterModelOptions(
            name='iccardposmeal',
            options={'default_permissions': (), 'verbose_name': 'Can eat', 'verbose_name_plural': 'Can eat'},
        ),
        migrations.AlterModelOptions(
            name='icconsumerlist',
            options={'default_permissions': ('browse',), 'verbose_name': 'Consumer details', 'verbose_name_plural': 'Consumer details'},
        ),
        migrations.AlterModelOptions(
            name='icconsumerlistbak',
            options={'default_permissions': ('browse',), 'verbose_name': 'Consumer details', 'verbose_name_plural': 'Consumer details'},
        ),
        migrations.AlterModelOptions(
            name='iclockdininghall',
            options={'default_permissions': (), 'permissions': (('iclockdininghall_cardcashsz', 'cardcashsz'), ('iclockdininghall_icconsumerlist', 'icconsumerlist'), ('iclockdininghall_reports', 'reports')), 'verbose_name': 'owned restaurant', 'verbose_name_plural': 'owned restaurant'},
        ),
        migrations.AlterModelOptions(
            name='issuecard',
            options={'default_permissions': ('browse',), 'permissions': (('issuecard_issuecard', 'issuecard'), ('issuecard_oplosecard', 'oplosecard'), ('issuecard_oprevertcard', 'oprevertcard'), ('issuecard_cancelmanagecard', 'cancelmanagecard'), ('issuecard_nocardretirecard', 'nocardretirecard'), ('issuecard_supplement', 'supplement'), ('issuecard_reimburse', 'reimburse'), ('issuecard_retreatcard', 'retreatcard'), ('issuecard_updatecard', 'updatecard'), ('issuecard_initcard', 'initcard')), 'verbose_name': 'Card Form', 'verbose_name_plural': 'Card Form'},
        ),
        migrations.AlterModelOptions(
            name='keydetail',
            options={'default_permissions': ('browse',), 'verbose_name': 'Key-value consumption schedule', 'verbose_name_plural': 'Key-value consumption schedule'},
        ),
        migrations.AlterModelOptions(
            name='keyvalue',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'key value data', 'verbose_name_plural': 'key value data'},
        ),
        migrations.AlterModelOptions(
            name='keyvaluemechine',
            options={'default_permissions': (), 'verbose_name': 'available device', 'verbose_name_plural': 'available device'},
        ),
        migrations.AlterModelOptions(
            name='loseunitecard',
            options={'default_permissions': ('browse',), 'verbose_name': 'Lost Loss Notes', 'verbose_name_plural': 'Lost Loss Notes'},
        ),
        migrations.AlterModelOptions(
            name='meal',
            options={'default_permissions': ('browse', 'change'), 'verbose_name': 'meal information', 'verbose_name_plural': 'meal information'},
        ),
        migrations.AlterModelOptions(
            name='mealmachine',
            options={'verbose_name': 'available device', 'verbose_name_plural': 'available device'},
        ),
        migrations.AlterModelOptions(
            name='merchandise',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Product Information', 'verbose_name_plural': 'Product Information'},
        ),
        migrations.AlterModelOptions(
            name='replenishcard',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Change Card Table', 'verbose_name_plural': 'Change Card Table'},
        ),
        migrations.AlterModelOptions(
            name='splittime',
            options={'default_permissions': ('browse', 'change'), 'verbose_name': 'segmentation', 'verbose_name_plural': 'segmentation'},
        ),
        migrations.AlterModelOptions(
            name='splittimemechine',
            options={'default_permissions': (), 'verbose_name': 'available device', 'verbose_name_plural': 'available device'},
        ),
        migrations.AlterModelOptions(
            name='storedetail',
            options={'default_permissions': ('browse',), 'verbose_name': 'Product Model Schedule', 'verbose_name_plural': 'Product Model Schedule'},
        ),
        migrations.AlterModelOptions(
            name='timebrush',
            options={'default_permissions': ('browse',), 'verbose_name': 'Timekeeping Consumption Table', 'verbose_name_plural': 'Timekeeping Consumption Table'},
        ),
        migrations.AlterModelOptions(
            name='timedetail',
            options={'default_permissions': ('browse',), 'verbose_name': 'Timekeeping Consumption Table', 'verbose_name_plural': 'Timekeeping Consumption Table'},
        ),
        migrations.AlterModelOptions(
            name='timeslice',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Card cash receipts and payments', 'verbose_name_plural': 'Card cash receipts and payments'},
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='balance on the card'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='card',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='card_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='logtype',
            field=models.IntegerField(blank=True, null=True, verbose_name='Transaction Type'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='opid',
            field=models.IntegerField(blank=True, null=True, verbose_name='Operation ID'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='paysource',
            field=models.IntegerField(blank=True, choices=[(1, 'recharge on Alipay'), (2, 'WeChat recharge')], editable=False, null=True, verbose_name='means of transaction'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='posmoney',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='Recharge amount'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='posoptime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Recharge time'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='sn',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='serial number'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='sys_card_no',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='tradeno',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='order number'),
        ),
        migrations.AlterField(
            model_name='aliwxfulllog',
            name='userid',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='allow_date',
            field=models.DateTimeField(verbose_name='subsidy time'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='base_batch',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='Subsidy base'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='batch',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='subsidy lot'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='is_ok',
            field=models.IntegerField(blank=True, choices=[(1, 'Yes'), (0, 'No')], default=0, editable=False, null=True, verbose_name='Whether to receive'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='is_pass',
            field=models.IntegerField(blank=True, choices=[(1, 'Yes'), (0, 'No')], default=0, editable=False, verbose_name='Is it approved?'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='is_transfer',
            field=models.IntegerField(blank=True, choices=[(1, 'Yes'), (0, 'No')], default=0, editable=False, null=True, verbose_name='Is it issued?'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=8, verbose_name='subsidy amount'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='pass_name',
            field=models.CharField(blank=True, editable=False, max_length=100, null=True, verbose_name='operator'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='receive_date',
            field=models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Subsidy collection time'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='receive_money',
            field=models.DecimalField(decimal_places=2, editable=False, max_digits=8, null=True, verbose_name='receipt amount'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='remark',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='sys_card_no',
            field=models.IntegerField(blank=True, default=0, editable=False, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='allowance',
            name='valid_date',
            field=models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Subsidy Ineffective Date'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='back_money',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=19, null=True, verbose_name='Refund amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='back_money_B',
            field=models.DecimalField(blank=True, db_column='back_money_b', decimal_places=2, max_digits=19, null=True, verbose_name='Refund subsidy wallet amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='card_money',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=8, null=True, verbose_name='Expense card cost (yuan)'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='card_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='cardno',
            field=models.CharField(max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='checktime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Retirement time'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operator'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='operate_type',
            field=models.IntegerField(blank=True, choices=[(1, 'Recharge'), (2, 'subsidy'), (7, 'Card cost'), (4, 'Expense card cost'), (5, 'Refund'), (6, 'consumption'), (11, 'management fee'), (8, 'Manual supplement consumption'), (9, 'correction'), (10, 'count'), (12, 'counting error correction'), (13, 'Recharge offer'), (14, 'No Card Retirement'), (15, 'There is a card to withdraw the card'), (16, 'Supplement card'), (21, 'recharge on Alipay'), (22, 'WeChat recharge')], default=15, null=True, verbose_name='Return card type'),
        ),
        migrations.AlterField(
            model_name='backcard',
            name='sys_card_no',
            field=models.IntegerField(blank=True, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='code',
            field=models.CharField(editable=False, max_length=3, verbose_name='Numbering'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='endtime',
            field=models.TimeField(verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='isvalid',
            field=models.BooleanField(default=True, verbose_name='is it effective'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='name',
            field=models.CharField(max_length=20, verbose_name='time period name'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='pos_time',
            field=models.CharField(blank=True, choices=[('1', 'fixed time period'), ('2', 'The second batch'), ('3', '3rd batch'), ('4', 'The 4th Batch'), ('5', 'The 5th batch'), ('6', 'The sixth batch'), ('7', 'The 7th batch'), ('8', 'The 8th batch'), ('9', 'The 9th batch')], editable=False, max_length=10, null=True, verbose_name='batch number'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='remarks',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='batchtime',
            name='starttime',
            field=models.TimeField(verbose_name='Starting time'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='CashType',
            field=models.ForeignKey(db_column='cashtype_id', on_delete=django.db.models.deletion.CASCADE, to='ipos.CardCashType', verbose_name='Types of'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='allow_balance',
            field=models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=9, null=True, verbose_name='Subsidy balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='allow_base_batch',
            field=models.IntegerField(blank=True, null=True, verbose_name='Subsidy base'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='allow_batch',
            field=models.IntegerField(blank=True, null=True, verbose_name='subsidy lot'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='allow_type',
            field=models.IntegerField(blank=True, choices=[(0, 'Accumulative subsidy'), (1, 'Zero subsidy')], null=True, verbose_name='Subsidy type'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True, verbose_name='balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='card',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='cardserial',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='checktime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Operation time'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='dept',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.department', verbose_name='department'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='dining',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Dininghall', verbose_name='restaurant'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='discount',
            field=models.IntegerField(blank=True, default=0, editable=False, null=True, verbose_name='discount(%)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='hide_column',
            field=models.IntegerField(blank=True, choices=[(1, 'Recharge'), (2, 'subsidy'), (7, 'Card cost'), (4, 'Expense card cost'), (5, 'Refund'), (6, 'consumption'), (11, 'management fee'), (8, 'Manual supplement consumption'), (9, 'correction'), (10, 'count'), (12, 'counting error correction'), (13, 'Recharge offer'), (14, 'No Card Retirement'), (15, 'There is a card to withdraw the card'), (16, 'Supplement card'), (21, 'recharge on Alipay'), (22, 'WeChat recharge')], null=True, verbose_name='type name'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='log_flag',
            field=models.IntegerField(blank=True, choices=[(1, 'Device Upload'), (2, 'System Addition'), (3, 'Error Correction'), (4, 'recharge on Alipay'), (5, 'WeChat recharge'), (99, 'Data Detection'), (100, 'abnormal record')], default=2, editable=False, null=True, verbose_name='record type'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='meal',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Meal', verbose_name='meal'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='money_A',
            field=models.DecimalField(blank=True, db_column='money_a', decimal_places=2, default=0.0, max_digits=19, null=True, verbose_name='Cash operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='money_B',
            field=models.DecimalField(blank=True, db_column='money_b', decimal_places=2, default=0.0, max_digits=19, null=True, verbose_name='Subsidy operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='pos_model',
            field=models.IntegerField(blank=True, choices=[(1, 'fixed mode'), (2, 'Amount Mode'), (3, 'key value mode'), (4, 'counting mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Accounting Mode'), (8, 'Manual supplement consumption'), (9, 'Device error correction')], null=True, verbose_name='Consumption Type'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='serialnum',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='sn',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='sys_card_no',
            field=models.IntegerField(blank=True, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='cardcashsz',
            name='wallet_type',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Cash Wallet'), (2, 'subsidized wallet'), (3, 'Cash and subsidies')], default=1, null=True, verbose_name='Operating Wallet'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='allow_balance',
            field=models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=9, null=True, verbose_name='Subsidy balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='allow_base_batch',
            field=models.SmallIntegerField(blank=True, null=True, verbose_name='Subsidy base'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='allow_batch',
            field=models.SmallIntegerField(blank=True, null=True, verbose_name='subsidy lot'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='allow_type',
            field=models.SmallIntegerField(blank=True, null=True, verbose_name='Subsidy type'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='balance'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='cardserial',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='checktime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Operation time'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='hide_column',
            field=models.SmallIntegerField(blank=True, null=True, verbose_name='Operation type'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='log_flag',
            field=models.SmallIntegerField(blank=True, default=2, editable=False, null=True, verbose_name='record type'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=19, verbose_name='Operating amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='money_A',
            field=models.DecimalField(blank=True, db_column='money_a', decimal_places=2, default=0.0, max_digits=19, null=True, verbose_name='Cash operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='money_B',
            field=models.DecimalField(blank=True, db_column='money_b', decimal_places=2, default=0.0, max_digits=19, null=True, verbose_name='Subsidy operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='physical_card_no',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='Original card number'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='serialnum',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='sn_name',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='sys_card_no',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='user_dept_name',
            field=models.CharField(db_column='deptname', max_length=100, verbose_name='Department name'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='user_name',
            field=models.CharField(blank=True, default='', max_length=24, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='user_pin',
            field=models.CharField(max_length=20, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='cardcashszbak',
            name='wallet_type',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Cash Wallet'), (2, 'subsidized wallet'), (3, 'Cash and subsidies')], default=1, null=True, verbose_name='Operating Wallet'),
        ),
        migrations.AlterField(
            model_name='cardcashtype',
            name='itype',
            field=models.IntegerField(choices=[(1, 'income'), (2, 'expenditure')], verbose_name='Types of'),
        ),
        migrations.AlterField(
            model_name='cardcashtype',
            name='name',
            field=models.CharField(max_length=50, verbose_name='type name'),
        ),
        migrations.AlterField(
            model_name='cardcashtype',
            name='remark',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='card_no',
            field=models.CharField(editable=False, max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='card_privage',
            field=models.CharField(blank=True, choices=[('0', 'regular card'), ('1', 'management card'), ('2', 'Operation card')], default=1, max_length=20, null=True, verbose_name='card permissions'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='cardstatus',
            field=models.CharField(blank=True, choices=[('1', 'effective'), ('3', 'loss'), ('4', 'expired'), ('2', 'Logout'), ('6', 'invalid'), ('5', 'deactivate'), ('9', 'inactivated'), ('999', 'Logout')], max_length=3, verbose_name='card status'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='dining',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Dininghall', verbose_name='owned restaurant'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='pass_word',
            field=models.CharField(blank=True, editable=False, max_length=6, null=True, verbose_name='card password'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='sys_card_no',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='cardmanage',
            name='time',
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name='issuing date'),
        ),
        migrations.AlterField(
            model_name='cardserial',
            name='cardno',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='cardserial',
            name='serialnum',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='dingadmin',
            name='ding',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ipos.Dininghall', verbose_name='authorized restaurant'),
        ),
        migrations.AlterField(
            model_name='dininghall',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='dininghall',
            name='code',
            field=models.IntegerField(verbose_name='restaurant number'),
        ),
        migrations.AlterField(
            model_name='dininghall',
            name='name',
            field=models.CharField(max_length=100, verbose_name='restaurant name'),
        ),
        migrations.AlterField(
            model_name='dininghall',
            name='remark',
            field=models.CharField(blank=True, max_length=200, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='allow_blance',
            field=models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=9, null=True, verbose_name='subsidy balance'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='cash balance'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='card',
            field=models.CharField(blank=True, max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='card_serial_no',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='hand_date',
            field=models.DateTimeField(verbose_name='consumption time'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='meal',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ipos.Meal', verbose_name='meal'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=8, verbose_name='Amount of consumption'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='name',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='pin',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='posdevice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='equipment'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='sys_card_no',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='handconsume',
            name='wallet_type',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Cash Wallet'), (2, 'subsidized wallet'), (3, 'Cash and subsidies')], default=1, editable=False, null=True, verbose_name='Operating Wallet'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='code',
            field=models.IntegerField(unique=True, verbose_name='card class number'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='date_max_count',
            field=models.IntegerField(default=0, verbose_name='Maximum number of daily consumption'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='date_max_money',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=8, verbose_name='Maximum amount of daily consumption (yuan)'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='discount',
            field=models.IntegerField(default=0, help_text='Discount 80%% is 20%% off', verbose_name='discount(%)'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='less_money',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=8, verbose_name='Minimum card balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='max_money',
            field=models.DecimalField(decimal_places=0, default=9999, max_digits=8, verbose_name='Maximum card balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='meal_max_count',
            field=models.IntegerField(default=0, verbose_name='Maximum number of meals consumed'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='meal_max_money',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=8, verbose_name='Maximum amount of meal consumption (yuan)'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='name',
            field=models.CharField(max_length=24, verbose_name='card class name'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='per_max_money',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=8, verbose_name='Maximum amount of secondary consumption (yuan)'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='pos_time',
            field=models.CharField(choices=[('1', 'fixed time period'), ('2', 'The second batch'), ('3', 'The third batch'), ('4', 'Fourth batch'), ('5', 'The fifth batch'), ('6', 'The sixth batch'), ('7', 'The seventh batch'), ('8', 'The eighth batch'), ('9', 'The ninth batch')], default=1, max_length=10, verbose_name='Consumption time period'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='remark',
            field=models.CharField(blank=True, max_length=200, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='use_date',
            field=models.IntegerField(default=0, verbose_name='Effective days of use'),
        ),
        migrations.AlterField(
            model_name='iccard',
            name='use_fingerprint',
            field=models.IntegerField(blank=True, choices=[(1, 'effective'), (0, 'invalid')], default=0, editable=False, verbose_name='Using a fingerprint card'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='allow_balance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=19, null=True, verbose_name='Subsidy balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='balance',
            field=models.DecimalField(decimal_places=2, max_digits=19, verbose_name='balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='card',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='card_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='create_operator',
            field=models.CharField(max_length=20, verbose_name='operator'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='dept',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.department', verbose_name='department'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='dev_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='dev_sn',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='dining',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Dininghall', verbose_name='restaurant'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='discount',
            field=models.IntegerField(blank=True, default=0, editable=False, null=True, verbose_name='discount(%)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='log_flag',
            field=models.IntegerField(blank=True, choices=[(1, 'Device Upload'), (2, 'System Supplement'), (3, 'Error Correction'), (100, 'abnormal record')], editable=False, null=True, verbose_name='record mark'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='meal',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Meal', verbose_name='meal'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='meal_data',
            field=models.DateTimeField(blank=True, null=True, verbose_name='meal date'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=19, verbose_name='Operating amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='money_B',
            field=models.DecimalField(blank=True, db_column='money_b', decimal_places=2, max_digits=19, null=True, verbose_name='subsidized consumption amount'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='pos_model',
            field=models.IntegerField(blank=True, choices=[(1, 'fixed mode'), (2, 'Amount Mode'), (3, 'key value mode'), (4, 'counting mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Accounting Mode'), (8, 'Manual supplement consumption'), (9, 'Device error correction')], null=True, verbose_name='Consumption Type'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='pos_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='consumption time'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='sys_card_no',
            field=models.IntegerField(blank=True, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='type_name',
            field=models.IntegerField(blank=True, choices=[(6, 'consumption'), (8, 'Replenishment'), (9, 'correction'), (10, 'count')], null=True, verbose_name='type name'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='user_id',
            field=models.CharField(max_length=20, verbose_name='Personnel ID'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='user_name',
            field=models.CharField(blank=True, default='', max_length=24, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='user_pin',
            field=models.CharField(max_length=20, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlist',
            name='wallet_type',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Cash Wallet'), (2, 'subsidized wallet'), (3, 'Cash and subsidies')], null=True, verbose_name='Operating Wallet'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='allow_balance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=19, null=True, verbose_name='Subsidy balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='balance',
            field=models.DecimalField(decimal_places=2, max_digits=19, verbose_name='balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='card',
            field=models.CharField(blank=True, default='', max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='card_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='create_operator',
            field=models.CharField(max_length=20, verbose_name='operator'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='dept',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.department', verbose_name='department'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='dev_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='dev_sn',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='dining',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Dininghall', verbose_name='restaurant'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='discount',
            field=models.SmallIntegerField(blank=True, default=0, editable=False, null=True, verbose_name='discount'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='log_flag',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Device Upload'), (2, 'manual replenishment'), (3, 'Error Correction')], editable=False, null=True, verbose_name='record mark'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='meal',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.Meal', verbose_name='meal'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='meal_data',
            field=models.DateTimeField(blank=True, null=True, verbose_name='meal date'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=19, verbose_name='Operating amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='money_B',
            field=models.DecimalField(blank=True, db_column='money_b', decimal_places=2, max_digits=19, null=True, verbose_name='Subsidy operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='pos_model',
            field=models.IntegerField(blank=True, choices=[(1, 'fixed mode'), (2, 'Amount Mode'), (3, 'key value mode'), (4, 'counting mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Accounting Mode'), (8, 'Manual supplement consumption'), (9, 'Device error correction')], null=True, verbose_name='Consumption Type'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='pos_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='consumption time'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='sys_card_no',
            field=models.IntegerField(blank=True, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='type_name',
            field=models.SmallIntegerField(blank=True, choices=[(6, 'consumption'), (8, 'Replenishment'), (9, 'correction'), (10, 'count')], null=True, verbose_name='type name'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='user_id',
            field=models.CharField(max_length=20, verbose_name='Personnel ID'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='user_name',
            field=models.CharField(blank=True, default='', max_length=24, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='user_pin',
            field=models.CharField(max_length=20, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='icconsumerlistbak',
            name='wallet_type',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Cash Wallet'), (2, 'subsidized wallet'), (3, 'Cash and subsidies')], null=True, verbose_name='Operating Wallet'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='Password',
            field=models.CharField(blank=True, db_column='password', default=123456, max_length=6, null=True, verbose_name='excess password'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='UserID',
            field=models.ForeignKey(blank=True, db_column='userid_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='allow_balance',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=9, null=True, verbose_name='subsidy balance'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=9, null=True, verbose_name='balance'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='card_cost',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=8, null=True, verbose_name='Card cost (yuan)'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='card_privage',
            field=models.CharField(blank=True, choices=[('0', 'regular card'), ('1', 'management card'), ('2', 'Operation card')], default='0', max_length=20, null=True, verbose_name='card type'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='card_serial_num',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='card stream no'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='cardno',
            field=models.CharField(max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='cardpwd',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='card password'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='cardstatus',
            field=models.CharField(blank=True, choices=[('1', 'effective'), ('3', 'loss'), ('4', 'expired'), ('2', 'Logout'), ('6', 'invalid'), ('5', 'deactivate'), ('9', 'inactivated'), ('999', 'Logout')], max_length=3, verbose_name='card status'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='date_count',
            field=models.IntegerField(blank=True, default=0, editable=False, null=True, verbose_name='Number of daily consumption'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='date_money',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, editable=False, max_digits=10, null=True, verbose_name='Maximum amount of daily consumption'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='effectivenessdate',
            field=models.DateField(blank=True, editable=False, null=True, verbose_name='effective date'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='failuredate',
            field=models.DateTimeField(blank=True, editable=False, null=True, verbose_name='expiration date'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='issuedate',
            field=models.DateTimeField(blank=True, editable=False, null=True, verbose_name='issuing date'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='isvalid',
            field=models.BooleanField(choices=[(1, 'effective'), (0, 'invalid')], default=1, editable=False, verbose_name='is it effective'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='itype',
            field=models.ForeignKey(blank=True, default=1, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.ICcard', verbose_name='card class name'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='meal_count',
            field=models.IntegerField(blank=True, default=0, editable=False, null=True, verbose_name='Number of meal consumption'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='meal_money',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, editable=False, max_digits=10, null=True, verbose_name='meal consumption amount'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='meal_type',
            field=models.IntegerField(blank=True, default=0, editable=False, null=True, verbose_name='Consumer meal'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='mng_cost',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=8, null=True, verbose_name='Management fee (yuan)'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='operate_time',
            field=models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Last operating time'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='pos_date',
            field=models.DateField(blank=True, editable=False, null=True, verbose_name='consumption date'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='pos_time',
            field=models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Last time spent'),
        ),
        migrations.AlterField(
            model_name='issuecard',
            name='sys_card_no',
            field=models.IntegerField(blank=True, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='RecSum',
            field=models.CharField(db_column='recsum', max_length=20, verbose_name='serial number'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='dev_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='dev_sn',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='key_code',
            field=models.CharField(blank=True, editable=False, max_length=4, null=True, verbose_name='button number'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='list_code_id',
            field=models.CharField(max_length=20, null=True, verbose_name='Detail number'),
        ),
        migrations.AlterField(
            model_name='keydetail',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=9, verbose_name='Amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='keyvalue',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='keyvalue',
            name='code',
            field=models.IntegerField(verbose_name='key number'),
        ),
        migrations.AlterField(
            model_name='keyvalue',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=6, verbose_name='Unit price (yuan)'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='Losetime',
            field=models.DateTimeField(auto_now_add=True, db_column='losetime', null=True, verbose_name='Operation date'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='Password',
            field=models.CharField(blank=True, db_column='password', editable=False, max_length=6, null=True, verbose_name='card password'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='card_privage',
            field=models.CharField(blank=True, choices=[('0', 'regular card'), ('1', 'management card'), ('2', 'Operation card')], default='0', max_length=20, null=True, verbose_name='card type'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='cardno',
            field=models.CharField(blank=True, max_length=20, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='cardstatus',
            field=models.CharField(blank=True, choices=[('3', 'loss'), ('1', 'Solutions Hanging')], editable=False, max_length=3, null=True, verbose_name='card status'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='itype',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ipos.ICcard', verbose_name='card class'),
        ),
        migrations.AlterField(
            model_name='loseunitecard',
            name='sys_card_no',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='card account'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='available',
            field=models.BooleanField(default=True, verbose_name='is it effective'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='code',
            field=models.CharField(max_length=20, verbose_name='meal number'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='endtime',
            field=models.TimeField(verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=8, verbose_name='cost (yuan)'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='name',
            field=models.CharField(max_length=100, verbose_name='meal name'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='remark',
            field=models.CharField(blank=True, max_length=100, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='meal',
            name='starttime',
            field=models.TimeField(verbose_name='Starting time'),
        ),
        migrations.AlterField(
            model_name='merchandise',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='merchandise',
            name='barcode',
            field=models.CharField(blank=True, max_length=20, verbose_name='barcode'),
        ),
        migrations.AlterField(
            model_name='merchandise',
            name='code',
            field=models.IntegerField(verbose_name='Product Number'),
        ),
        migrations.AlterField(
            model_name='merchandise',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=6, verbose_name='Unit price (yuan)'),
        ),
        migrations.AlterField(
            model_name='merchandise',
            name='name',
            field=models.CharField(max_length=10, verbose_name='product name'),
        ),
        migrations.AlterField(
            model_name='merchandise',
            name='rebate',
            field=models.IntegerField(default=0, help_text='Discount 80%% is 20%% off', verbose_name='discount(%)'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='allow_balance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='subsidy balance'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='cash balance'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='carno',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='devname',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='device name'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='money_A',
            field=models.DecimalField(blank=True, db_column='money_a', decimal_places=2, default=0, max_digits=19, null=True, verbose_name='Cash operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='money_B',
            field=models.DecimalField(blank=True, db_column='money_b', decimal_places=2, default=0, max_digits=19, null=True, verbose_name='Subsidy operation amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='pin',
            field=models.CharField(db_column='badgenumber', max_length=20, null=True, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='posOpTime',
            field=models.DateTimeField(blank=True, db_column='posoptime', null=True, verbose_name='consumption time'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='posmodel',
            field=models.IntegerField(blank=True, choices=[(2, 'Amount Mode'), (1, 'fixed mode'), (4, 'counting mode'), (3, 'key value mode'), (5, 'commodity model'), (6, 'Timekeeping Mode'), (7, 'Consumption Error Correction'), (8, 'counting error correction'), (9, 'Time to start correcting errors'), (10, 'Time to end error correction')], null=True, verbose_name='consumption operation'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='posmoney',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='Amount of consumption'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='serialnum',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='sn',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='serial number'),
        ),
        migrations.AlterField(
            model_name='poslog',
            name='wallet_type',
            field=models.SmallIntegerField(blank=True, choices=[(1, 'Cash Wallet'), (2, 'subsidized wallet'), (3, 'Cash and subsidies')], default=1, null=True, verbose_name='Operating Wallet'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='allow_blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='subsidy amount'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='blance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True, verbose_name='Cash amount'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='create_operator',
            field=models.CharField(blank=True, editable=False, max_length=20, null=True, verbose_name='operating'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='newcardno',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Current card number'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='oldcardno',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Original card number'),
        ),
        migrations.AlterField(
            model_name='replenishcard',
            name='time',
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name='Replacement date'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='code',
            field=models.CharField(max_length=20, verbose_name='Numbering'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='endtime',
            field=models.TimeField(verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='fixedmonery',
            field=models.DecimalField(decimal_places=2, max_digits=19, verbose_name='Amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='isvalid',
            field=models.BooleanField(default=True, verbose_name='is it effective'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='name',
            field=models.CharField(max_length=20, verbose_name='first name'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='remarks',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='splittime',
            name='starttime',
            field=models.TimeField(verbose_name='Starting time'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='RecSum',
            field=models.CharField(db_column='recsum', max_length=20, verbose_name='serial number'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='dev_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='dev_sn',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='list_code_id',
            field=models.CharField(max_length=20, null=True, verbose_name='Detail number'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='money',
            field=models.DecimalField(decimal_places=2, max_digits=9, verbose_name='Amount (yuan)'),
        ),
        migrations.AlterField(
            model_name='storedetail',
            name='store_code',
            field=models.CharField(blank=True, editable=False, max_length=4, null=True, verbose_name='Product Number'),
        ),
        migrations.AlterField(
            model_name='timebrush',
            name='begintime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Starting time'),
        ),
        migrations.AlterField(
            model_name='timebrush',
            name='carno',
            field=models.CharField(max_length=10, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='timebrush',
            name='endtime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='timebrush',
            name='itype',
            field=models.IntegerField(blank=True, null=True, verbose_name='status'),
        ),
        migrations.AlterField(
            model_name='timebrush',
            name='serialnum',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='timebrush',
            name='sn',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='begin_money',
            field=models.DecimalField(decimal_places=2, max_digits=9, verbose_name='Starting balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='begintime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Starting time'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='convey_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Upload time'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='end_money',
            field=models.DecimalField(decimal_places=2, max_digits=9, verbose_name='End balance (yuan)'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='endtime',
            field=models.DateTimeField(blank=True, null=True, verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='list_code_id',
            field=models.CharField(max_length=20, null=True, verbose_name='Detail number'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='timedetail',
            name='sn',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='devise serial number'),
        ),
        migrations.AlterField(
            model_name='timeslice',
            name='code',
            field=models.CharField(max_length=20, verbose_name='Time Period Number'),
        ),
        migrations.AlterField(
            model_name='timeslice',
            name='endtime',
            field=models.TimeField(verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='timeslice',
            name='isvalid',
            field=models.BooleanField(choices=[(1, 'effective'), (0, 'invalid')], default=True, verbose_name='is it effective'),
        ),
        migrations.AlterField(
            model_name='timeslice',
            name='remarks',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='timeslice',
            name='starttime',
            field=models.TimeField(verbose_name='Starting time'),
        ),
    ]
