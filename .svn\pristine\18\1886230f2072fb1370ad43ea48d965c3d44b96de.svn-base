{% load iclock_tags %}
{% load i18n %}
{% block content_title %}{% endblock %}
<script>
function ActionHint(action, aName){return ""}
pageQueryString=location.search;
var groupHeaders=[]
var jqOptions=copyObj(jq_Options);
jqOptions.rowNum={{limit}}

canDefine={% if user|HasPerm:"iclock.preferences_user" %}true{% else %}false{% endif %}
{% autoescape off %}
{% endautoescape %}
isSelectAll=false;
extraBatchOp=[];
tblName="";
var Custom_Jqgrid_Height=""
var page_tab=""

function validate_form(){   //验证表单的合法性(人员(可不选，在计算项目中计算选中部门的人员，其他的就默认值0)、开始时间、结束时间)
	var t_ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
	if(t_ComeTime==""){
		t_ComeTime="2012-01-01"
	}
	var cTime=t_ComeTime.split("-");
	var t_EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
	if(t_EndDate==""){
		t_EndDate="2212-01-01"
	}
	var eTime=t_EndDate.split("-");
	cdate=new Date(parseInt(cTime[0],10),parseInt(cTime[1],10)-1,parseInt(cTime[2],10));
	edate=new Date(parseInt(eTime[0],10),parseInt(eTime[1],10)-1,parseInt(eTime[2],10));
	var days=(edate.valueOf()-cdate.valueOf())/(1000*3600*24)+1;
	if(cdate>edate || t_ComeTime=="" || t_EndDate==""||!valiDate(t_ComeTime)||!valiDate(t_EndDate)||days>31){
		return 1;
	}else{
		return 0
	}
}



{% block RenderReportGrid %}

function RenderReportGrid(urlStr){
   $("#id_grid").jqGrid("GridUnload")
   //$.jgrid.gridUnload("#id_grid")
    
	if(urlStr.indexOf("?")!=-1){
		urlStr=urlStr+"&stamp="+moment().unix();
	}
	else{
		urlStr=urlStr+"?stamp="+moment().unix();
	}
    
    $.ajax({
	    type:"GET",
	    url:urlStr+"&title=1",
	    dataType:"json",
	    success:function(json){
		    jqOptions=copyObj(jq_Options)
		    grid_disabledfields=json['disabledcols']
		    jqOptions.colModel=json['colModel']
		    var groupHeaders=[]

		    if (json['groupHeaders'])
			var groupHeaders=json['groupHeaders']
		    
		    get_grid_fields(jqOptions)
		    hiddenfields(jqOptions)
		    jqOptions.url=urlStr 
		    var hcontent=$("#id_content").height();
		    var hbar=$("#id_top").height();
		    var height=hcontent-hbar-65;
		    if (groupHeaders.length>0)
		     height=height-30;
		    
		    if(typeof(Custom_Jqgrid_Height)!='undefined'&&Custom_Jqgrid_Height!=""){
			    jqOptions.height=Custom_Jqgrid_Height;
		    }else{jqOptions.height=height;}
		    
		    jqOptions.sortname=''//getSidx('original_records')
			jqOptions.url=urlStr 
			
		    $("#id_grid").jqGrid(jqOptions);
		    $("#id_grid").jqGrid('setFrozenColumns');
		    $("#id_grid").jqGrid('setGroupHeaders', {useColSpanStyle: true,groupHeaders:groupHeaders})
	 }
    });        
}
{% endblock %} 



$(function(){
	
	
	
	$("#id_export").iMenu();
	$("#"+g_activeTabID+" #searchButton").click(function(){
	    searchShowReport();
	});
	$("#"+g_activeTabID+" #searchbar").keypress(function(event){
		if(event.keyCode==13)
		searchShowReport();
	 
	});

	




	var currDate=new Date();
	var dateTime=moment().format("YYYY-MM-DD")
	
	if($.cookie("ComeTime")){
		$("#"+g_activeTabID+" #id_ComeTime").val($.cookie("ComeTime"))
		$("#"+g_activeTabID+" #id_EndTime").val($.cookie("EndDate"))
	}
	else{
		$("#"+g_activeTabID+" #id_ComeTime").val(dateTime)
		$("#"+g_activeTabID+" #id_EndTime").val(dateTime)
	}
	
	
	
	$("#"+g_activeTabID+" #id_ComeTime").datepicker(datepickerOptions);
	$("#"+g_activeTabID+" #id_EndTime").datepicker(datepickerOptions);
	
		
	if(!canDefine) $('#id_custom').remove()
	var hcontent=$("#id_content").height();
	var hbar=$("#id_top").length>0?$("#id_top").height():0;
	var h=hcontent-hbar
	$('.module').css('height',h)
	
//	get_grid_fields(jqOptions);           //此句应该在下一句的前面
	
	//g_urls[g_activeTabID]="/acc/data/records/"
	urlStr=g_urls[g_activeTabID]
	//savecookie("search_urlstr",urlStr);
	
	//loadPageData();
	RenderReportGrid(urlStr)





	
	$("#"+g_activeTabID+" #id_reload").click(function(){
		reloadData();
	});
	$("#"+g_activeTabID+" #id_custom").click(function(){
		ShowCustomField();
	});
	
	$("#"+g_activeTabID+" #queryButton").click(function(){
		//createQueryDlg();
	});

	
	//根据条件查询考勤记录
	$("#"+g_activeTabID+" #id_search").click(function(){
		queryShowReport()
	});
                	

 
});

function resetError()
{
	$("#id_error").hide();
	$("#id_error").html('')

}
function searchShowReport(){

	var flag=$("#"+g_activeTabID+" #searchbar").attr('role');
	if (flag!='cansearch'&&flag!='defvalue') return;
	if (flag!='defvalue')
	    var v=$("#"+g_activeTabID+" #searchbar")[0].value;
	else
	    var v=""
	
	
	var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
	var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
	var isError=validate_form();
	$.cookie("ComeTime",ComeTime, { expires: 7 });
	$.cookie("EndDate",EndDate, { expires: 7 });
	$("#id_con_error").css("display","none");
	urlStr=g_urls[g_activeTabID]
	if(!isError)
		urlStr+="?TTime__gte="+ComeTime+"&TTime__lt="+EndDate
	if (urlStr.indexOf('daily_devices')!=-1){
		var CheckDate=$("#id_CheckTime").val();
		urlStr+="&checkDate="+CheckDate
	}	
	if(urlStr.indexOf("?")==-1){
		urlStr+="?q="+encodeURI(v)
	}
	else{
		urlStr+="&q="+encodeURI(v)
	}
	savecookie("search_urlstr",urlStr);
	$("#id_grid").jqGrid('setGridParam',{url:urlStr,datatype:"json"}).trigger("reloadGrid");	
	
}


function queryShowReport(){
	
	
	var deptIDs=getSelected_dept("showTree_")
	var ComeTime=$("#"+g_activeTabID+" #id_ComeTime").val();
	var EndDate=$("#"+g_activeTabID+" #id_EndTime").val();
     var search_event_no=$("#search_id_event_no").val();
	if(deptIDs==undefined)
		deptIDs=''
	var isError=validate_form();
	$.cookie("ComeTime",ComeTime, { expires: 7 });
	$.cookie("EndDate",EndDate, { expires: 7 });
	$("#id_con_error").css("display","none");
	var urlStr="";
	var urlTime="";
	var urlTime1="";
	var ord="asc";
	var sortname="";
	if(!isError){
		urlTime1="TTime__gte="+ComeTime+"&TTime__lt="+EndDate
	}else{
		$("#id_con_error").css("display","block");
		$("#id_con_error").html("<span class='Se_Tran_errorlist'>{% trans 'Please check that you select one or more employees or select department and the date is validate!' %}</span>");
		return false;
	}
		
	urlTime="&"+urlTime1
	//urlStr+=urlTime
	//if(deptIDs!='' && deptIDs!=undefined ){
	//       urlStr+="&deptIDs="+deptIDs 
	//}
	
	urlStr+=urlTime
	
	$.cookie("url",urlStr, { expires: 7 });
	ischecked=0;
	if(search_event_no!=''&& search_event_no!=undefined){
                            urlStr+="&event_no="+search_event_no
                        }
	if($("#id_cascadecheck_").prop("checked"))
		ischecked=1;
	var url="/acc/report/records/?"

	if (urlStr!="" &&  urlStr!=null)
		url+=urlStr;

	sortname="TTime"
	savecookie("search_urlstr",url);
	$("#id_grid").jqGrid('setGridParam',{url:url,datatype:"json",sortorder:ord,sortname:sortname}).trigger("reloadGrid");

}



</script>
<div id="id_top">
	<div id="id_line" style="padding: 2px;width: 100%;overflow: hidden;">
		<span style="width:385px;">
		
		   <label  >{% trans "Begin Date" %}</label>
		   <input type="text" name="ComeTime" maxlength="10" id="id_ComeTime" size="9" />
		
		   <label  >{% trans "End Date" %}</label>
		   <input type="text" name="EndTime" maxlength="10" id="id_EndTime"  size="9" />
		     <label  >{% trans 'Event Description' %}</label>
			<select name='event_no' id='search_id_event_no'>
            <option value=''>--------</option><option value='-1'>{% trans 'nothing' %}</option><option value='0'>{% trans 'Normal card opening' %}</option><option value='1'>{% trans 'Punch during Passage Mode Time Zone' %}</option>
            <option value='2'>{% trans 'First card opening (swipe card)' %}</option><option value='3'>{% trans 'Doka open door (swipe card)' %}</option><option value='4'>{% trans 'Emergency password opens' %}</option><option value='5'>{% trans 'Open during Passage Mode Time Zone' %}</option>
            <option value='6'>{% trans 'trigger linkage event' %}</option><option value='7'>{% trans 'Cancel the alarm' %}</option><option value='8'>{% trans 'Open the door remotely' %}</option><option value='9'>{% trans 'remote closing' %}</option>
            <option value='10'>{% trans 'Disable the usual opening time of the day' %}</option><option value='11'>{% trans 'Enable the usual opening time of the day' %}</option><option value='12'>{% trans 'Turn on auxiliary output' %}</option><option value='13'>{% trans 'Turn off auxiliary output' %}</option>
            <option value='20'>{% trans 'Swipe card interval is too short' %}</option><option value='21'>{% trans 'Do not valid time period' %}</option><option value='22'>{% trans 'Illegal time period' %}</option><option value='23'>{% trans 'Unauthorized access' %}</option>
            <option value='24'>{% trans 'anti-submarine' %}</option><option value='25'>{% trans 'interlocking' %}</option><option value='26'>{% trans 'Multi-card verification (swipe)' %}</option><option value='27'>{% trans 'People are not registered' %}</option>
            <option value='28'>{% trans 'door open timeout' %}</option><option value='29'>{% trans 'The card has expired' %}</option><option value='30'>{% trans 'wrong password' %}</option><option value='36'>{% trans 'The door is not valid (press the exit button)' %}</option>
            <option value='37'>{% trans 'Unable to close the door during the normal opening period' %}</option><option value='101'>{% trans 'Duress password to open the door' %}</option><option value='102'>{% trans 'The door was accidentally opened' %}</option><option value='200'>{% trans 'The door is open' %}</option>
            <option value='201'>{% trans 'The door is closed' %}</option><option value='202'>{% trans 'Going out the door to open the door' %}</option><option value='204'>{% trans 'The end of the normally open time period' %}</option><option value='205'>{% trans 'Remote opening is always open' %}</option>
            <option value='206'>{% trans 'Device startup' %}</option><option value='220'>{% trans 'Auxiliary input point disconnected' %}</option><option value='221'>{% trans 'Auxiliary input point short circuit' %}</option>
            </select>
		</span>
                
                <span id='id_search' ><a class='m-btn  purple rnd mini'>{% trans 'Query' %}</a></span>
                
		<span style="float:right;">
			<div id="id_searchbar" class='search-pu'>
				<div class="nui-ipt nui-ipt-hasIconBtn " >
					<input id="searchbar" class="search-input" type="text"  role='defvalue' autocomplete="off"    />
				</div>
				<div class="main-search-btn" style="height:22px">
					<span><img id="searchButton" src="{{ MEDIA_URL }}/img/s.gif" title="{% trans "Search" %}" style="cursor:hand;"></span>
				</div>
			</div>
		</span>		
	</div>

	<div id="id_toolbar">
			<UL class="toolbar" id="navi">
				<LI id="id_reload" class="first button-refresh"><SPAN></SPAN>{% trans "Reload" %}</LI>
				<LI id="id_export"  class="button-export"><SPAN></SPAN>{% trans "Export" %}
				        <ul id="op_menu_export" class="op_menu">
						<li><span>{% trans "file format" %}</span>
							<ul>
							<li onclick="javascript:clickexport(tblName[g_activeTabID],0);"><a href="#">EXCEL</a></li>
							<li onclick="javascript:clickexport(tblName[g_activeTabID],1);"><a href="#">TXT</a></li>
							<li onclick="javascript:clickexport(tblName[g_activeTabID],2);"><a href="#">PDF</a></li>
							</ul>
						</li>
					</ul>
				</LI>
				<LI id="id_custom" class="button-custom"><SPAN></SPAN>{% trans "Preferences" %}</LI>
			</UL>
        </div>

	
</div>


<!--	<div id="show_field_selected">
		<div class="title"><span class='close' onclick='hideFields_define();'></span></div>
		<div id="id_fields_selected"></div>
	</div>
-->

<div class="module" style="position:relative; width: 99%;margin-top: 2px;">
			<table id="id_grid" >	</table>
			<div id="id_pager"></div>
	 
</div>
<div id="id_tip" class="tip" style="visibility:hidden"></div>



