/* Reset CSS */


h1{ font-size:28px;}
h2{ font-size:26px;}
h3{ font-size:18px;}
h4{ font-size:16px;}
h5{ font-size:14px;}
h6{ font-size:12px;}
h1,h2,h3,h4,h5,h6{ color:#563D64;}



a{ text-decoration: none; }
a:hover{ text-decoration: underline; }
.alignleft { float: left; margin-right: 15px; }
.alignright { float: right; margin-left: 15px; }
#globalfooter a:hover{
	color: #FFFFFF;
}

html,body {
    height: 100%;
    margin: 0;
    min-height:100%;
    width: 100%;
}
.login_wrapper
{
	height: 100%;
	width: 100%;
	min-height: 570px;
	min-width: 800px;
	-moz-background-size:100% 100%;
	background-size:100% 100%;
	position: relative;
	background-color: #474b4f;
}
#content{
	margin: 0 auto;
	padding: 0 0 0;
	position: relative;
	text-align: center;
	width: 340px;
	border-radius: 5px;
	height:400px;
	background-color:rgba(255, 255, 255,1);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff,endColorstr=#ffffff);
}


#content form { margin: 0 20px; position: relative }
#login-form p label{color: #999}
#content form input[type="text"],
#content form input[type="password"] {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.5s ease;
	-moz-transition: all 0.5s ease;
	-ms-transition: all 0.5s ease;
	-o-transition: all 0.5s ease;
	transition: all 0.5s ease;
	border: 1px solid #999;
	color: #373232;
	margin: 0 0 10px;
	padding: 10px 10px 10px 35px;
	width: 175px;
	height: 14px;
	
  -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
    font-size: 14px;

	
}
#content form input[type="text"]{
	background: url(../../../img/login/login_user.png) no-repeat #FFFFFF;

}


#content form input[type="password"] {
	background: url(../../../img/login/password.png) no-repeat #FFFFFF;
}

#content form input[type="text"]:focus,
#content form input[type="password"]:focus {
	background-color: #fff;
	border: 1px solid #7ace43;
	outline: none;
}
#id_user_name { background-position: 10px 10px !important }
#id_password { background-position: 10px 10px !important }

#content form input[type="submit"] {
	background: rgb(254,231,154);
	background: -moz-linear-gradient(top,  rgba(254,231,154,1) 0%, rgba(254,193,81,1) 100%);
	background: -webkit-linear-gradient(top,  rgba(254,231,154,1) 0%,rgba(254,193,81,1) 100%);
	background: -o-linear-gradient(top,  rgba(254,231,154,1) 0%,rgba(254,193,81,1) 100%);
	background: -ms-linear-gradient(top,  rgba(254,231,154,1) 0%,rgba(254,193,81,1) 100%);
	background: linear-gradient(top,  rgba(254,231,154,1) 0%,rgba(254,193,81,1) 100%);
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	-ms-border-radius: 30px;
	-o-border-radius: 30px;
	border-radius: 30px;
	-webkit-box-shadow: 0 1px 0 rgba(255,255,255,0.8) inset;
	-moz-box-shadow: 0 1px 0 rgba(255,255,255,0.8) inset;
	-ms-box-shadow: 0 1px 0 rgba(255,255,255,0.8) inset;
	-o-box-shadow: 0 1px 0 rgba(255,255,255,0.8) inset;
	box-shadow: 0 1px 0 rgba(255,255,255,0.8) inset;
	border: 1px solid #D69E31;
	color: #85592e;
	cursor: pointer;
	float: left;
	height: 35px;
	margin: 20px 0 35px 15px;
	position: relative;
	text-shadow: 0 1px 0 rgba(255,255,255,0.5);
	width: 120px;
}
#content form input[type="submit"]:hover {
	background: rgb(254,193,81);
	background: -moz-linear-gradient(top,  rgba(254,193,81,1) 0%, rgba(254,231,154,1) 100%);
	background: -webkit-linear-gradient(top,  rgba(254,193,81,1) 0%,rgba(254,231,154,1) 100%);
	background: -o-linear-gradient(top,  rgba(254,193,81,1) 0%,rgba(254,231,154,1) 100%);
	background: -ms-linear-gradient(top,  rgba(254,193,81,1) 0%,rgba(254,231,154,1) 100%);
	background: linear-gradient(top,  rgba(254,193,81,1) 0%,rgba(254,231,154,1) 100%);
}

.login_wrapper .top{
	height:30px;
	margin: 0 auto;
	overflow: hidden;
	width:100%;
}
.logo {
    display: block;
    float: left;
    padding: 2px 0 0;
}

.login_logo{
	width:300px;
	margin: 0 auto;
	padding: 20px;
}


#changeform{
	float: right;
	padding-top: 10px;
	
}


#globalfooter {
    bottom: 5px;
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100%;
}
.foot {

	height: 40px;
	width: 100%;
	text-align: center;
	color: #909090;
	float:left;
	padding-top: 80px;

}



#login-form{
	height: 171px;
	
}

.login_top{
	height: 50px;	
}

.login_top h3.tabs {
    margin: 0 auto;
    position: relative;
    top: 7px;
    width: 80%;
}
.login_top h3.tabs span {
    color: #000000;
    cursor: pointer;
    float: left;
    font-size: 18px;
    height: 27px;
    line-height: 27px;
    text-align: center;
    text-indent: 0;
    width: 50%;
    font-weight:500;
	border-bottom: 1px solid #333333;
    
}
.login_top h3.tabs span.default {
   	border-top-left-radius: 6px;
	border-top-right-radius: 6px;
	-webkit-border-top-left-radius: 6px;
	-moz-border-top-right-radius: 6px;

	color: #7ac143;
	height: 27px;
	width: 50%;
	border-bottom: 2px solid #7ac143;
}

#content #submit{
	margin-top: 30px;
}
.login-middle{
	background: url("../../../img/login/login_bg0.jpg") no-repeat scroll center center #090B1A;
    margin: 0 auto;
    position: relative;
    min-height: 400px;
    background-size:cover;
    height: 480px;
}

#btn_login{
    color: white;
    text-shadow: none;
    background: #7ac143;
}
#login_forget{
    float:center;
    color:#7ac143;
}


