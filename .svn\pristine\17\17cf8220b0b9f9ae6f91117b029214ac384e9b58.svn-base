#!/usr/bin/env python
#coding=utf-8
from __future__ import division
from mysite.iclock.models import *
from django.template import loader, Context
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from django.core.exceptions import ObjectDoesNotExist
import string
import datetime
from django.contrib.auth.decorators import login_required,permission_required
from django.contrib.auth.models import User
from mysite.cab import *
from mysite.iclock import reb
import os, time,re
from mysite.iclock.dataproc import *
from django.conf import settings
from mysite.iclock.iutils import *
from django.utils.translation import gettext_lazy as _
#from django.utils.translation import gettext
from mysite.core.zktools import *
from django.core.cache import cache
from mysite.utils import *
from mysite.iclock.schedule import *
from mysite.iclock.datas import LoadSchPlan
import calendar
from mysite.iclock.dataview import sync_card_to_issuecard
import codecs
from mysite.acc.models import level,level_emp
from mysite.asset.models import *
from mysite.asset.utils import auto_generate_asset_code
from decimal import Decimal
from mysite.payroll.models import SalarySet, SalarySetData
lineFmt=u"&nbsp;&nbsp;&nbsp;&nbsp;%s<br />"
def InfoErrorRec(title, errorRecs):
    if not errorRecs: return ""
#	fname="import_%s.txt"%(datetime.datetime.now().isoformat().replace(":",""))
#	tmpFile(fname, "\n".join(errorRecs))
    return lineFmt%(_("%(num)d record(s) is duplicated or invalid.") % {'num':len(errorRecs)}) #u"<a href='/iclock/tmp/%s'> %d 条数据</a>为重复或无效记录。<br />"

def reportError(title, i, i_insert, i_update, i_rep, errorRecs):
    info=[]
    if i_insert: info.append(u"%s"%_("Inserted %(object_num)d successfully") % {'object_num':i_insert})
    if i_update: info.append(u"%s"%_("Updated %(object_num)d successfully") % {'object_num':i_update})
    if i_rep: info.append(u"%s"%_(" %(object_num)d already exists in the database")% {'object_num':i_rep})
    result = u"<h2>%s:</h2> <p />%s%s%s<br />" % (title,
        lineFmt%(u"%s"%_("In the data files %(object_num)d  %(object_name)s ")%{'object_num':i,'object_name': u"%s"%_('records')}),
        info and lineFmt%(u", ".join(info)) or "",
        InfoErrorRec(title, errorRecs))
    return result

def AppendUserDev(dispEmp, SNs, cursor):
    needSave=False
    try:
        pin=formatPIN(dispEmp[0])
        try:
            e=employee.objects.get(PIN=pin)
            if e.SN and (SNs!=settings.ALL_TAG) and (e.SN.SN not in SNs):
                return _("No permission to do on device %s!")%e.SN.SN #u"没有权限传送 %s 上登记的人员"%e.SN.SN
        except:
            e=employee(PIN=pin, DeptID=getDefaultDept())
            needSave=True
        try:
            device=getDevice(dispEmp[1])
            if (SNs!=settings.ALL_TAG) and (device.SN not in SNs):
                return _("No permission to download employee data to device %s!")%device.SN #u"没有权限传送人员到设备 %s"%device.SN
        except:
            if dispEmp[1]:
                return _("Designated device does not exist!") #u"指定设备不存在"
            elif SNs!=settings.ALL_TAG:
                return _("Not specified a device!") #u"没有指定设备"
            device=None
#		print "employee: dept=%s, sn=%s"%(e.DeptID, e.SN)
        if not e.DeptID:	#没有部门表示该员工已经被删除，所以需要恢复
            e.DeptID=getDefaultDept()
            needSave=True
#			print "save Dept of employee:", e
        if device and not e.SN: #没有登记设备的人员，则指定登记设备
            e.SN=device
            needSave=True
#			print "save device of employee:", e
        if needSave: e.save()
        if not device:	return
        cmdStr=getEmpCmdStr(e)
        if not cmdStr:
            return None
        appendDevCmd(device, cmdStr) #, UserName=User.username)
        for afp in fptemp.objects.filter(UserID=e):
#			print e.PIN, afp.FingerID
            appendDevCmd(device, u"DATA FP PIN=%s\tFID=%d\tSize=%d\tValid=%d\tTMP=%s"%\
                (e.pin(), afp.FingerID, 0, afp.Valid, afp.temp()))
    except Exception as e:
        errorLog()
        return "%s"%e

#"DATA DEL_USER PIN=%s"
def DelUserDev(dispEmp, SNs, cursor):
    try:
        pin=formatPIN(dispEmp[0])
        try:
            e=employee.objects.get(PIN=pin)
            if not e.DeptID:
                return _("Designated employee does not exist!")#u"指定人员不存在"
        except:
            return _("Designated employee does not exist!")#u"指定人员不存在"
        try:
            device=getDevice(dispEmp[1])
            if (SNs!=settings.ALL_TAG) and (device.SN not in SNs):
                return _("No permission to deete the employee&#39;s in device %s")%device #"没有权限删除设备 %s 中的人员"%device
        except:
            if dispEmp[1]:
                return _("Designated device does not exist!")
            elif (SNs!=settings.ALL_TAG and (not e.SN or (e.SN_id not in SNs))):
                if e.SN:
                    return _("Not specified a device, and no permission to delete employee from device %s.")%e.SN #u"没有指定设备"+u", 并且没有权限从登记设备 %s 中删除该人员"%e.SN
                else:
                    return _("Not specified a device, and the employee has no a enrollment device.") #u"没有指定设备，并且该人员没有登记设备")
            device=None
        delEmpFromDev(SNs==settings.ALL_TAG, e, device)
    except Exception as e:
        return "%s"%e

def NameUserDev(dispEmp, SNs, cursor):
    newUser=False
    assignDev=False
    try:
        device=getDevice(dispEmp[2])
    except:
        device=None
    try:
        userName=dispEmp[1]
    except:
        userName=u""
    try:
        pin=dispEmp[0]
        if (len(pin)!=PIN_WIDTH) or (not pin.isdigit()) or (int(pin,10) in DISABLED_PINS):
            return _("%s is not a valid PIN.")%pin #pin+u" 不是一个合法的考勤号码"
        try:
            e=employee.objects.get(PIN=pin)
        except:
            e=employee(PIN=pin, EName=userName, DeptID=getDefaultDept())
            if device: e.SN=device
            e.save()
            newUser=True
        else:
            if (not e.DeptID) or (userName and (userName!=e.EName)) or (device and not e.SN):
                e.DeptID=getDefaultDept()
                e.EName=userName
                if not e.SN:
                    assignDev=True
                    e.SN=device
                e.save()

            userName=e.EName
            if not device:
                device=e.SN
        if not device:
            return _("Not specified a valid device, or the employee has no a enrollment device!")#u"没有指定合适的考勤机，或者该人员没有登记考勤机"
        if (not newUser) and not userName:
            return _("The employee no in the database.")#u"该用户在数据库中还没有录入姓名"
        sql= u"DATA USER PIN=%s\tName=%s"%(e.pin(), e.EName)
        appendDevCmd(device, sql, cursor)
        #backDev=device.BackupDevice()
        #if backDev:
            #appendDevCmd(backDev, sql, cursor)
        if assignDev: #新用户同时传送指纹
            for afp in fptemp.objects.filter(PIN=e):
                #print e.PIN, afp.FingerID
                sql=u"DATA FP PIN=%s\tFID=%d\tSize=%d\tValid=%d\tTMP=%s"%\
                    (e.pin(), afp.FingerID, 0, afp.Valid, afp.temp())
                appendDevCmd(device, sql, cursor)
                #if backDev:
                    #appendDevCmd(backDev, sql, cursor)
    except Exception as e:
        return "%s"%e


def disp_emp(request, delemp):
    from django.db import connection as conn
    cursor = conn.cursor()
    task=(delemp and u"deluser" or ((request.path.find("name_emp")>=0) and u"username" or u"userinfo"))
    titles={"deluser": _("Delete employee from device"),# u"删除考勤机上的人员",
        "username":_("Download employee&#39;s name to device"),# u"传送人员姓名到考勤机",
        "userinfo":_("Dispatch employee to device")}# u"分派人员到考勤机"}
    title=titles[task]
    if not (request.method == 'POST'):
        return render("disp_emp.html", {"title": title, 'task': task})
    #POST
    cc=u""

    SNs=request.user.is_superuser and settings.ALL_TAG or getUserIclocks(request.user)

    process=(task=="deluser") and DelUserDev or ((task=="userinfo") and AppendUserDev or NameUserDev)
    errorLines=[]
    i=0;
    okc=0;
    f=request.FILES["fileUpload"]
    lines=""
    for chunk in f.chunks():
        lines+=chunk
    lines=lines.decode("GBK").split("\n")
    for line in lines:
        i+=1;
        if line:
            if line[-1] in ['\r','\n']: line=line[:-1]
        if line:
            if line[-1] in ['\r','\n']: line=line[:-1]
        try:
#			print line
            if line:
                if line.find("\t")>=0:
                    data=(line+"\t").split("\t")
                elif line.find(",")>=0:
                    data=(line+",").split(",")
                else:
                    data=(line+" ").split(" ",1)
                error=process(data,SNs,cursor)
                if error:
                    errorLines.append(u"Line %d(%s):%s"%(i,line,error))
                okc+=1
        except Exception as e:
            errorLines.append(u"Line %d(%s):%s"%(i,line,str(e)))
    if okc:
        conn._commit()
    if len(errorLines)>0:
        if okc>0:
            cc+=(_("%s employee&#39;s data is ready to transfer, but following record(s) is missing:")%okc)+"</p><pre>" # u"%d 位员工处理数据已经提交准备传送, 但是发生如下错误:</p><pre>"%okc
        else:
            cc+=_("There are wrong: ")+"</p><pre>" # u"没有员工处理数据被传送, 发生如下错误:\n</p><pre>"
        cc+=u"\n".join(errorLines)
        cc+=u"</pre>"
    else:
        cc+=_("%s employee(s) data is ready to transfer.")%okc# u"%d 位员工处理数据已经提交准备传送"%okc
    return render("info.html", {"title": title, "content": cc})

@login_required
def FileDelEmp(request):
    return disp_emp(request, True)

@login_required
def FileChgEmp(request):
    return disp_emp(request, False)

@login_required
def disp_emp_log(request):
    return render("disp_emp_log.html",{"title": _("Dispatch employee data to device")})# u"分派人员到考勤机"})

def saveUser(pin, pwd, ename, card, grp, tzs):
    try:
        e=employee.objects.get(PIN=pin)
    except:
        e=employee(PIN=pin, DeptID=getDefaultDept())
    if ename: e.EName=u"%s"%(ename,"gb2312")
#	if card: e.Card=card
    if pwd: e.MVerifyPass=pwd
    if grp: e.AccGroup=grp
    if tzs: e.TimeZones=tzs
    e.save()

def saveFTmp(pin, fid, tmp):
    try:
        t=fptemp.objects.get(PIN=employee.objects.get(PIN=pin), FingerID=fid)
        if t.Template!=tmp:
            t.Template=tmp
            t.save()
    except:
        t=fptemp(PIN=employee.objects.get(PIN=pin), FingerID=fid, Valid=1, Template=tmp)
        t.save()
#iNewCount=0
#iUpdateCount=0
#i_insert, i_update, i_rep = 0, 0, 0
@login_required
def importLevelEmp(request):
    try:
        imFields = ["levelnumber", "badgenumber"]
        imFieldsInfo = {}
        imDatalist = []
        sUserids = []
        i_insert = 0
        i_update = 0
        i_rep = 0

        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
            except:
                cc = _("levelemp&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import Access Control Group Employee list"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        levels=[]
        errorlist=[]
        # 普通管理员只能看到自己所管理区域的权限组，如果超级管理员创建的权限组包含自己的区域但也包含另外的区域，也不允许看到
        if not request.user.is_superuser and not request.user.is_allzone:
            zas = ZoneAdmin.objects.filter(user=request.user).values('code')
            iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zas).values('SN')

            ld = level_door.objects.filter(door__device__in=iz).values('level')
            ex_level = level_door.objects.filter(level__in=ld).filter(~Q(door__device__in=iz)).values(
                'level')
            ld_list=set([str(obj['level']) for obj in ld])
            ex_list=set([str(obj['level']) for obj in ex_level])

        send_acc_list = []
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            jjj+=1
            dDict = {}
            if ext == "TXT":
                ls = t.split(b'\t')
            elif ext == 'CSV':
                ls = t.split(b',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 10:
                        continue
                except:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        dDict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        dDict[k] = ls[int(v) - 1]
            if dDict:
                try:
                    if dDict['levelnumber'] in levels:
                        level_obj=levels[dDict['levelnumber']]
                    else:
                        level_obj=level.objects.get(pk=dDict['levelnumber'])
                        if not request.user.is_superuser and not request.user.is_allzone:
                            if not(str(dDict['levelnumber']) in ld_list and dDict['levelnumber'] not in ex_list):
                                s = _(u"Section%sNo this access control group permission") % r
                                cc = u"%s,%s" % (s, _(u"imported failed"))
                                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                except:
                    s = _(u"Section%sNo relevant permission group can be found") % r
                    cc = u"%s,%s" % (s, _(u"imported failed"))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                try:
                    emp_obj=employee.objByPIN(dDict['badgenumber'])
                except:
                    s =_( u"Section%sDesignated employee does not exist!") % r
                    cc = u"%s,%s" % (s, _(u"imported failed"))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if not (request.user.is_superuser or request.user.is_alldept):
                    dept_list = userDeptList(request.user)
                    if emp_obj.DeptID.DeptID not in dept_list:
                        s = _(u"Section%sNo this employee permission") % r
                        cc = u"%s,%s" % (s, _(u"imported failed"))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                sql, params = getSQL_insert_new(level_emp._meta.db_table, level_id=dDict['levelnumber'], UserID_id=emp_obj.id)
                try:
                    customSqlEx(sql, params)
                    i_insert+=1
                except Exception as e:
                    pass
                doors = level_door.objects.filter(level=dDict['levelnumber']).values('door')
                devs = AccDoor.objects.filter(id__in=doors).distinct().values_list('device', flat=True)
                send_acc_list.append((emp_obj, devs))
        try:
            import threading
            thread = threading.Thread(target=level_import_send_acc_data, args=(send_acc_list,))
            thread.start()
        except:
            if settings.DEBUG:
                import traceback
                traceback.print_exc()
        result = reportError(u"%s" % _("Authority group personnel"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=level_emp._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        cc = u"%s,%s" % (e, _("Import failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

def level_import_send_acc_data(send_acc_list):
    '''
    导入人员门禁权限时，下发门禁权限用；
    '''
    related_devs = set()
    for acc_data in send_acc_list:
        emps = employee.objects.filter(pk=acc_data[0].id)
        devs = acc_data[1]
        zk_send_acc_data(emps, devs)
        for sn in devs:
            related_devs.add(sn)
    for sn in related_devs:
        dev = getDevice(sn)
        appendDevCmd(dev, 'INFO')

@login_required
def importTitle(request):
    try:
        imFields = ["roleid", "roleName","roleLevel"]
        imFieldsInfo = {}
        imDatalist = []
        sUserids = []
        i_insert = 0
        i_update = 0
        i_rep = 0
        # print (request.POST)

        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
            except:
                cc = _("title&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import title list"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        isoverride=request.POST.get("isoverride",'')
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        levels=[]
        errorlist=[]

        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            jjj+=1
            dDict = {}
            if ext == "TXT":
                ls = t.split(b'\t')
            elif ext == 'CSV':
                ls = t.split(b',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 10:
                        continue
                except:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        dDict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        dDict[k] = ls[int(v) - 1].replace('\t', '').replace('\n', '')
            if dDict:
                if dDict['roleid']:
                    rolecompile=re.compile('^[1-9][0-9]{0,8}$')
                    if rolecompile.match(dDict['roleid']):
                        if 'roleLevel' not in dDict or dDict['roleLevel'] == '':
                            dDict['roleLevel'] = None
                        dDict['State'] = 0
                        userroles_obj = userRoles.objByID(int(dDict['roleid']))
                        if userroles_obj:
                            if isoverride == 'on':
                                dDict['whereroleid']=dDict['roleid']
                                del dDict['roleid']
                                sql, params = getSQL_update_new(userRoles._meta.db_table, dDict)
                                try:
                                    customSqlEx(sql, params)
                                    i_update += 1
                                    cache.delete("%s_iclock_userRoles_%s" % (settings.UNIT, dDict['whereroleid']))
                                except:
                                    s = _(u"Section%sNo duplicate title") % r
                                    cc = u"%s,%s" % (s, _(u'imported failed'))
                                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                            else:
                                pass
                        else:
                            sql, params = getSQL_insert_new(userRoles._meta.db_table, dDict)
                            try:
                                customSqlEx(sql, params)
                                i_insert += 1
                                cache.delete("%s_iclock_userRoles_%s" % (settings.UNIT, dDict['roleid']))
                            except:
                                pass
                    else:
                        s = _(u"Section%sNo job number can only be a number between 1-999999999") % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        if i_insert or i_update:
            cache.delete("%s_userRoles_all" % (settings.UNIT))
        result = reportError(u"%s" % _("Job"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=userRoles._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        cc = u"%s,%s" % (e, _("Import failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")



@login_required
def importEmp(request):
#	if not (request.method == 'POST'):
#		return render("Import_emp.html",{"title": _("Upload employee list") # u"批量上传人员列表",
#		})

    try:
        issuecard_dic = []
        import_use_fingercons_pins = []  #有勾选用了'生物特征模板'的人员
        imFields=['badgenumber']
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        Male=request.POST["man"]
        Female=request.POST["woman"]
        OffDuty_yes=request.POST["OffDuty_yes"]
        fingercons_yes=request.POST.get("fingercons_yes", None)  # IC消费不上传fingercons_yes参数
        dept=request.POST.get("dept",'')
        oriFields=request.POST["fields"].split(',')
        fromline=request.POST.get("rowid2","1")
        isAllowNoDept=request.POST.get("nodept","")
        dupPin=request.POST.get("dupPin","")
        settings.CARDTYPE = int(GetParamValue('ipos_cardtype', 2, 'ipos'))
        deptid=''
        roster_value_list = []
        if dept:
            deptid=int(dept)
        if dupPin == 'on':
            dupPin =True
        else:
            dupPin = False

        if isAllowNoDept=='on':
            isAllowNoDept=True
        else:
            isAllowNoDept=False

        if fromline=="":
            fromline=1
        fromline=int(fromline)
        for t in oriFields:
            fldName=request.POST.get(t,"")
            if fldName=='on':
                imFields.append(t)
        maxCol=0
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
                if maxCol<imFieldsInfo[t]:maxCol=imFieldsInfo[t]
            except:
                cc=_("employee&#39;s data imported failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import employee list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        zhPattern = re.compile(u'[\u4e00-\u9fa5]+')
        lines=[]
        for chunk in f.chunks(): data+=chunk
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")#修改导入文件名称为中文报错问题
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols

            sn_flag = int(GetParamValue('opt_basic_ssnnorepeat', '0'))
            if (sn_flag) and 'SSN' in imFieldsInfo: # 判断上传文件中是否存在身份证号重复
                from collections import Counter
                try:
                    col_data = sh.col_values(imFieldsInfo['SSN'] - 1)
                except IndexError: # 身份证号整列为空时出现索引越界报错
                    col_data = []
                counter = dict(Counter(col_data))
                duplicate_ssn = [ssn for ssn, count in counter.items() if ssn != '' and count > 1]
                if duplicate_ssn:
                    cc = f"{_('Duplicate ID numbers exist in the uploaded file')}: {duplicate_ssn[0]}"
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            # 控卡判断
            if 'Card' in imFieldsInfo:
                try:
                    col_data = sh.col_values(imFieldsInfo['Card'] - 1)
                except IndexError: # Card整列为空时出现索引越界报错
                    col_data = []
                _col_data = []
                for _col in col_data:  # 这边作用是剔除无效卡号，如空格
                    # 尝试将字符串转换为整数
                    try:
                        int_card = int(_col)
                        _col_data.append(int_card)
                    except:
                        continue
                unauthorized_cards = [int(x) for x in _col_data if not check_card_validity(int(x))]
                if unauthorized_cards:
                    cc = f"{_('The current card number is not authorized!')}: {unauthorized_cards}"
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            for i in range(0,nrows):
                sv=[]
                c=0
                #try:
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                    c=c+1
                    if c>maxCol-1:break

#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        jj=0




        iCount=len(lines)          #import users count
        i_insert=0
        i_update=0
        i_rep=0
        errorRec=[]
        #adminLog(time=datetime.datetime.now(),User=request.user,model = employee._meta.verbose_name,action=u'%s'%_("import employee data"),count = iCount).save(force_insert = True)
        updateSqlList=[]
        insertSqlList=[]
        cardlist, have_card_emp_ids = [], []
        r=0
        issuecard_count = IssueCard.objects.filter(cardstatus__in=[CARD_LOST,CARD_VALID]).count()
        empleaves = list(empleavelog.objects.filter(deltag = 0).values_list("UserID_id", flat = True))
        if issuecard_count > 0:
            employee_qs = employee.objects.filter(Card__isnull = False).exclude(Card = '').values_list('Card','id')
            issuecard_qs = IssueCard.objects.filter(cardstatus=CARD_LOST).values_list('cardno','UserID_id') # ID模式下挂失操作会置空人员表中的卡号
            for e in employee_qs:
                cardlist.append(e[0])
                #have_card_emp_ids.append(e[1])
            for i in issuecard_qs:
                cardlist.append(i[0])
                #have_card_emp_ids.append(i[1])
            cardlist = list(set(cardlist))
            #have_card_emp_ids= list(set(have_card_emp_ids))
        else:
            cardlist = list(employee.objects.filter(Card__isnull=False).exclude(Card='').values_list('Card',flat=True))
        iclocks = iclock.objects.filter(ProductType__in=[4, 5, 15, 25]).exclude(DelTag=1)
        import_cardlist = []
        
        import_card_repeat = []

        #查询人员，用以判断人员编号是否已存在
        existed_pins = employee.objects.all().values_list('PIN', flat=True)
        existed_pins_set = set(list(existed_pins))

        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            jj+=1
            if jj<fromline:continue
            dDict={}
            extend_dir = {}  # 扩展属性
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>100:
                        continue
                except:
                    continue
                v=int(v)
                if ls[v-1] != None:
                    ls[v-1]=ls[v-1].strip()
                if k=='badgenumber':
#					ls[v-1]=ls[v-1]
                    if ls[v-1]=='':
                        errorRec.append(ls[v-1])
                        dDict={}
                        break
                    match = zhPattern.search(ls[v-1])
                    if match:
                        errorRec.append(ls[v-1])
                        s = _(u"Personnel number only allows letters or numbers")
                        cc = u"%s,%s" % (_(u'Import failed'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                    sUserids.append(ls[v-1])
                if k=="Gender":
                    if v<=len(ls):
                        if ext=='XLS' or ext=='XLSX':
                            s=ls[v-1]
                        else:
                            s=getStr_c_decode(ls[v-1])
                        if s==Male:
                            ls[v-1]="M"
                        elif s==Female:
                            ls[v-1]="F"
                        else:
                            ls[v-1]=""
                if k=="OffDuty":
                    if v<=len(ls):
                        if ext=='XLS' or ext=='XLSX':
                            s=ls[v-1]
                        else:
                            s=getStr_c_decode(ls[v-1])
                        if s==OffDuty_yes:
                            ls[v-1]=1
                        else:
                            ls[v-1]=0
                if k == "clock_in_mobile":
                    if v <= len(ls):
                        if ext == 'XLS' or ext == 'XLSX':
                            s = ls[v - 1]
                        else:
                            s = getStr_c_decode(ls[v-1])
                        if s == "启用":
                            ls[v - 1] = 0
                        elif s == "禁用":
                            ls[v - 1] = 1
                        else:
                            ls[v - 1] = None
                if k=="fingercons":
                    if v<=len(ls):
                        if ext=='XLS' or ext=='XLSX':
                            s=ls[v-1]
                        else:
                            s=getStr_c_decode(ls[v-1])
                        if (fingercons_yes is not None) and (s==fingercons_yes):
                            ls[v-1]=1
                        else:
                            ls[v-1]=0
                if v<=len(ls):
                    s=ls[int(v)-1]
                    if ext=='XLS' or ext=='XLSX':
                        if k in ['Birthday','Hiredday']:
                            if str(type(s))=="<type 'unicode'>":#因日期格式复杂，当为文本时暂不支持，建议修改EXCEL文件单元格格式为日期型

                                #bb=s.split('-')
                                #iDate1=datetime.datetime(int(bb[0]),int(bb[1]),int(bb[2]))
                                #dDict[k]=iDate1
                                dDict[k]=''
                            else:
                                #try:
                                #	aa=s
                                #	bb=aa.split('-')
                                #	iDate1=datetime.datetime(int(bb[0]),int(bb[1]),int(bb[2]))
                                #	dDict[k]=iDate1
                                #
                                #except:
                                #	try:
                                #		iDate = int(s)
                                #		lDate=xlrd.xldate_as_tuple(iDate,bk.datemode)
                                #		dDict[k]=datetime.datetime(lDate[0],lDate[1],lDate[2])
                                #	except:
                                #		dDict[k]=None
                                try:		#dDict[k]=datetime.datetime.now().strftime("%Y-%m-%d")
                                    iDate = int(s)
                                    lDate=xlrd.xldate_as_tuple(iDate,bk.datemode)
                                    dDict[k]=datetime.datetime(lDate[0],lDate[1],lDate[2])
                                except:
                                    dDict[k]=''
                        else:
                            if k.isdigit():  # 导入扩展属性的情形，扩展属性用到的列名实际为扩展属性表属性id，故为整数
                                extend_dir[k] = s
                            else:
                                dDict[k]=s
                                if k == 'badgenumber':
                                    extend_dir[k] = formatPIN(s)
                    else:
                        dDict[k]=getStr_c_decode(s)
            if extend_dir:
                roster_value_list.append(extend_dir)

            if dDict:
                if 'Birthday' in dDict:#.has_key('Birthday'):
                    if not dDict['Birthday']:
                        del dDict['Birthday']
                if 'Hiredday' in dDict:#.has_key('Hiredday'):
                    if not dDict['Hiredday']:
                        del dDict['Hiredday']
                if 'Card' in dDict and dDict['Card']:
                    import_cardlist.append(re.sub(r'^0*', '', dDict['Card']))
                    try:
                        int(dDict['Card'])
                    except:
                        s=u"%s"%(_(u"The card number can only be a number"))
                        cc=u"%s,%s"%(_('imported failed'),s)
                        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                # print (dDict['fingercons'])


                if 'defaultdeptid' not in dDict:#.has_key('defaultdeptid'):
                    if not deptid:
                        if not isAllowNoDept:
                            s=u"%s"%(_(u"The department number is not nullable"))
                            cc=u"%s,%s"%(_('imported failed'),s)
                            return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                    else:
                        dDict['defaultdeptid']=deptid
                        isAllowNoDept=False
                else:
                    isAllowNoDept=False
                    deptNum=dDict['defaultdeptid']   #实际为部门编号
                    dt=department.objByNumber(dDict['defaultdeptid'])
                    if dt:
                        dDict['defaultdeptid']=dt.DeptID
                    else:
                        errorRec.append(dDict['badgenumber'])
                        s=u"%s"%(_(u"The department number for which the attendance number is %s does not exist")) %(dDict['badgenumber'])
                        cc=u"%s,%s"%(_('imported failed'),s)
                        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                # *人员导入验证身份证是否重复
                if 'SSN' in dDict:
                    if sn_flag:
                        emp_ssn = list(employee.objects.all().values_list('SSN',flat=True))
                        if dDict['SSN'] and dDict['SSN'] in set(emp_ssn):
                            cc = u"%s,%s" % (_('Duplicate ID number'), dDict['SSN'])
                            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                jjj+=1
                dDict['badgenumber'] = formatPIN(dDict['badgenumber'])
                try:
                    if dDict['badgenumber'] in existed_pins_set:
                        if dupPin:
                            errorRec.append(dDict['badgenumber'])
                            continue
                        else:
                            e = employee.objects.get(PIN=dDict['badgenumber'])
                    else:
                        e = None
                except:
                    e=None
                dDict['DelTag']=0
                if 'OffDuty' not in dDict:#.has_key('OffDuty'):
                    dDict['OffDuty']=0

                if 'name' in dDict:
                    dDict['name']  = re.sub(u'[^\u4e00-\u9fa5a-zA-Z0-9_.（）()@\x20]', '', dDict['name'])

                p = re.compile(r'^[0-9a-zA-Z]+$')
                if not p.match(dDict['badgenumber']):
                    errorRec.append(dDict['badgenumber'])
                    continue

                if e:
                    if 'fingercons' not in dDict:
                        dDict['fingercons'] = e.fingercons if e.fingercons else 0
                    dDict['wherebadgenumber']=dDict['badgenumber']
                    if 'name' in dDict:#.has_key('name'):
                        if e.EName != dDict['name']:
                            dDict['OpStamp']=datetime.datetime.now()
                    if 'Card' in dDict:#.has_key('Card'):
                        dDict['Card'] = re.sub(r"^0*", "", dDict['Card'])
                        if ('ipos' in settings.SALE_MODULE)  and  e.Card and e.Card != dDict['Card']:
                            errorRec.append(dDict['badgenumber'])
                            continue
                        elif dDict['Card'] in cardlist:
                            have_card_emp_ids.append(dDict['Card'])
                            # errorRec.append(dDict['badgenumber'])
                            # continue
                            # elif ('ipos' in settings.SALE_MODULE) and e.id in have_card_emp_ids:
                            #     errorRec.append(dDict['badgenumber'])
                            #     continue
                        else:
                            if dDict['Card']:
                                cardlist.append(dDict['Card'])
                        dDict['OpStamp']=datetime.datetime.now()
                    if 'defaultdeptid' in dDict and e.DeptID_id != dDict['defaultdeptid']:
                        dDict['OpStamp']=datetime.datetime.now()
                    if dDict['OffDuty'] == 1:
                        dDict['OpStamp']=datetime.datetime.now()
                    dDict['SN']=''
                    del dDict['badgenumber']
                    updateSqlList.append((dDict,e))
                else:
                    if 'fingercons' not in dDict:
                        dDict['fingercons'] = 0
                    is_valid = ('Card' in dDict) and dDict['Card'] not in cardlist
                    if is_valid or ('Card' not in dDict):
                        if not isAllowNoDept:
                            if settings.DATABASE_ENGINE.lower() in ['postgresql']:
                                dDict['ATT'] = True
                                dDict['OverTime'] = True
                                dDict['Holiday'] = True
                            else:
                                dDict['ATT']=1
                                dDict['OverTime']=1
                                dDict['Holiday']=1
                            dDict['INLATE']=0
                            dDict['OutEarly']=0
                            dDict['OpStamp']=datetime.datetime.now()
                            dDict['Privilege']=0
                            dDict['sysPass']=''
                            # dDict['fingercons']=0
                            dDict['LastName']=''
                            dDict['localname']=''
                            insertSqlList.append(dDict)
                            if is_valid and dDict['Card']:
                                dDict['Card'] = re.sub(r"^0*", "", dDict['Card'])
                                cardlist.append(dDict['Card'])
                        else:
                            errorRec.append(dDict['badgenumber'])
                    else:
                        errorRec.append(dDict['badgenumber'])
        # if dept:
        #     imp_DeptNumber = department.objects.get(DeptID = int(dept),DelTag = 0).DeptNumber
        #     adminLog_object = u'%s:'%_(u"department number") + u'%s'%imp_DeptNumber
        #     adminLog(time = datetime.datetime.now(), User = request.user, model = employee._meta.verbose_name,
        #              object = adminLog_object, action = u'%s' % _(u"import employee data"),
        #              count = jjj).save(force_insert = True)
        # else:
        #     adminLog(time = datetime.datetime.now(), User = request.user, model = employee._meta.verbose_name,
        #             object = request.META["REMOTE_ADDR"], action = u'%s' % _(u"import employee data"),
        #              count = jjj).save(force_insert = True)#
        card_synced_emps = list(IssueCard.objects.filter(cardstatus__in = [CARD_VALID, CARD_INACTIVE, CARD_LOST]).values_list("UserID",flat = True))
        if 'areaCode' in imFields:
            zones = zone.objects.filter(DelTag=0).values('id', 'code')
            zones_dic = {i['code']:i['id'] for i in zones}

        adminLog_list = []  #管理员操作日志，采用bulk_create方式
        existing_balance = []

        for u,emp in updateSqlList:
            if 'fingercons' in u and u['fingercons']:
                import_use_fingercons_pins.append(emp.pin())

            if 'Card' in u and import_cardlist.count(u['Card']) > 1:
                import_card_repeat.append(u['Card'])
                continue
            try:
                # 卡内有余额的人员进行离职操作时 取消全部操作并返回提示
                if u.get('OffDuty', 0) == 1:
                    card_obj = IssueCard.objByPIN(emp.PIN)
                    if card_obj and (card_obj.blance != 0 or card_obj.allow_balance != 0):
                        existing_balance.append(emp.PIN)
                        continue
                    u['Card'] = ''

                areaCode=None
                if 'areaCode' in u:
                    areaCode = u['areaCode'].split(',')
                    del u['areaCode']
                sql, params = getSQL_update_new('userinfo', u)
                if customSqlEx(sql, params):
                    i_update += 1
                    cache.delete("%s_iclock_emp_%s" % (settings.UNIT, emp.id))
                    cache.delete("%s_iclock_emp_PIN_%s" % (settings.UNIT, emp.PIN))
                    if 'Card' in u:#.has_key('Card'):
                        if u['Card'] and u['Card'] not in have_card_emp_ids:
                            if emp.id not in card_synced_emps: # 兼容没有同步发卡表的情况
                                issuecard_dic.append({'user':emp.PIN, 'card':re.sub(r"^0*", "", u['Card']), 'is_new':True})
                            else:
                                issuecard_dic.append({'user':emp, 'card':re.sub(r"^0*", "", u['Card']), 'is_new':False})
                    if areaCode:
                        areaid = []
                        for arcode in areaCode:
                            arid = zones_dic.get(arcode, '')
                            if arid:
                                areaid.append(arid)
                        if areaid:
                            sync_area(emp,areaid)
                    adminLog_list.append(adminLog(**{
                            'time': datetime.datetime.now(),
                            'User_id': request.user.id,
                            'model': u"%s" % employee._meta.verbose_name,
                            'action': f"{_('Import')}-{_('Modify')}",
                            'object': emp,
                            'count':1
                        }))
                if 'OffDuty' in u and u['OffDuty'] == 1:
                    emppin = emp.pin()
                    if settings.IDFORPIN == 1:
                        emppin = emp.id
                    for dev in iclocks:
                        if dev.ProductType in [4]:
                            appendDevCmd(dev, "DATA DEL_USER PIN=%s" % emppin, None)
                        else:
                            delete_data(dev.SN, 'user', 'Pin=%s' % emppin)
                    # 导入人员时,如果为离职状态,存离职表,离职方式暂定为"自离",后续有反馈再修改
                    el = empleavelog.objects.filter(UserID_id=emp.id)
                    if el:
                        el[0].deltag = 0
                        el[0].save()
                    else:
                        empleav = empleavelog()
                        empleav.UserID = emp
                        empleav.leavedate = datetime.datetime.now()
                        empleav.leavetype = 0
                        empleav.reason = ''
                        empleav.createtime = datetime.datetime.now()
                        empleav.save()
                    level_emp.objects.filter(UserID=emp).delete()

                    # 导入离职，若人员卡内无余额直接退卡，有余额(包括补贴)不走处理
                    if card_obj:
                        if card_obj.cardstatus == CARD_INACTIVE:
                            card_obj.delete()  #未激活的卡，直接删除
                        elif card_obj.cardstatus in [CARD_VALID, CARD_LOST]:
                            if card_obj.blance == 0 and card_obj.allow_balance == 0:  #有效卡、挂失卡，无余额的，直接标记为无效
                                card_obj.isvalid = 0
                                card_obj.create_operator = 'leave'
                                card_obj.operate_time = datetime.datetime.now()
                                card_obj.cardstatus = CARD_INVALID
                                card_obj.save()
                                employee.objects.filter(PIN=emppin).update(Card='')
                        else:
                            pass  #其他状态的卡，不做更新
                else:
                    if emp.id in empleaves:
                        el = empleavelog.objects.get(UserID_id = emp.id)
                        el.deltag = 1
                        el.save()
            except Exception as e:
                print ("updateSqlList====", e)
        for i in insertSqlList:
            if 'Card' in i and import_cardlist.count(i['Card']) > 1:
                import_card_repeat.append(i['Card'])
                continue
            if 'fingercons' in i and i['fingercons']:
                import_use_fingercons_pins.append(i['badgenumber'])
            try:
                areaCode = None
                if 'areaCode' in i:
                    areaCode = i['areaCode'].split(',')
                    del i['areaCode']
                if 'clock_in_mobile' not in i:
                    i['clock_in_mobile'] = '0'
                sql, params = getSQL_insert_new('userinfo', i)
                if customSqlEx(sql, params):
                    i_insert += 1
                    if 'Card' in i:
                        if i['Card'] and i['Card'] not in have_card_emp_ids:
                            issuecard_dic.append({'user':i['badgenumber'], 'card':re.sub(r"^0*", "", i['Card']), 'is_new':True})
                    if areaCode:
                        areaid = []
                        for arcode in areaCode:
                            arid = zones_dic.get(arcode, '')
                            if arid:
                                areaid.append(arid)
                        if areaid:
                            emp = employee.objByPIN(i['badgenumber'])
                            sync_area(emp,areaid)
                    emp = employee.objByPIN(i['badgenumber'])
                    adminLog_list.append(adminLog(**{
                            'time': datetime.datetime.now(),
                            'User_id': request.user.id,
                            'model': u"%s" % employee._meta.verbose_name,
                            'action': f"{_('Import')}-{_('Create')}",
                            'object': emp,
                            'count':1
                        }))
                if 'OffDuty' in i and i['OffDuty'] == 1:
                    offemp = employee.objByPIN(i['badgenumber'])
                    el = empleavelog.objects.filter(UserID=offemp)
                    if offemp:
                        if el:
                            el[0].deltag = 0
                            el[0].leavedate = datetime.datetime.now()
                            el[0].createtime = datetime.datetime.now()
                            el[0].save()
                        else:
                            empleave = empleavelog()
                            empleave.UserID = offemp
                            empleave.leavedate = datetime.datetime.now()
                            empleave.leavetype = 0
                            empleave.reason = ''
                            empleave.createtime = datetime.datetime.now()
                            empleave.save()
            except Exception as e:
                print ("insertSqlList====", e)
        if adminLog_list:
            adminLog.objects.bulk_create(adminLog_list)

        # 更新花名册属性
        if roster_value_list:
            for roster in roster_value_list:
                try:
                    emp_id = employee.objects.filter(PIN=roster['badgenumber']).first().id
                except:
                    continue
                del roster['badgenumber']
                for key,value in roster.items():
                    ros = UserProperty.objByID(int(key))
                    emp_ros_value = UserPropertyValue.objByUnique(emp_id, ros.id)
                    if ros.type == 0:
                        if emp_ros_value:
                            emp_ros_value.value = value
                            emp_ros_value.save()
                        else:
                            UserPropertyValue.objects.create(userid=emp_id, PropertyId_id=ros.id, value=value)
                    elif ros.type == 1:
                        ros_value = ros.preset_attr_values  # 自定义花名册字段,其属性值是否为空
                        if ros_value:  # 花名册是否有设置值
                            value_list = ros_value.split(';')
                            if value != '':
                                if value in value_list:
                                    # 验证下人员花名册属性的值是否存在
                                    if emp_ros_value:
                                        emp_ros_value.value = value
                                        emp_ros_value.save()
                                    else:
                                        UserPropertyValue.objects.create(userid=emp_id,PropertyId_id=ros.id,value=value)
                                else:
                                    return getJSResponse({"ret": 0, "message": _(u"The value of %s is not in the property settings, import failed") % ros.name}, mtype="text/plain")
                        else:
                            if value:
                                return getJSResponse({"ret": 0, "message": _(u"Import failed due to setting the %s attribute value to empty in the custom roster") % ros.name}, mtype="text/plain")

                    elif ros.type == 2:
                        ros_value = ros.preset_attr_values  # 自定义花名册字段,其属性值是否为空
                        if ros_value:
                            ros_value_list = ros_value.split(";")
                            value_list = value.split(";")
                            if value != '':
                                if set(value_list).issubset(set(ros_value_list)):
                                    if emp_ros_value:
                                        emp_ros_value.value = ','.join(value_list)
                                        emp_ros_value.save(force_update=True)
                                    else:
                                        UserPropertyValue.objects.create(userid=emp_id,PropertyId_id=ros.id,value=value)
                                else:
                                    return getJSResponse({"ret": 0, "message": _(u"The value of %s is not in the property settings, import failed") % ros.name},mtype="text/plain")
                        else:
                            if value:  # 导入文件存在人员花名册字段的值
                                return  getJSResponse({'ret':0,"message": _(u"Import failed due to setting the %s attribute value to empty in the custom roster") % ros.name}, mtype="text/plain")
                    elif ros.type == 3:
                        ros_value = ros.preset_attr_values  # 自定义花名册字段,其属性值是否为空
                        if ros_value:  # 花名册是否有设置值
                            value_list = ros_value.split(';')
                            if value != '':
                                if value in value_list:
                                    # 验证下人员花名册属性的值是否存在
                                    if emp_ros_value:
                                        emp_ros_value.value = value
                                        emp_ros_value.save()
                                    else:
                                        UserPropertyValue.objects.create(userid=emp_id, PropertyId_id=ros.id, value=value)
                                else:
                                    return getJSResponse({"ret": 0, "message": _(u"The value of %s is not in the property settings, import failed") % ros.name},mtype="text/plain")
                        else:
                            if value:
                                return getJSResponse({"ret": 0, "message": _(u"Import failed due to setting the %s attribute value to empty in the custom roster") % ros.name},mtype="text/plain")

        if i_insert>0 or i_update>0:
            cache.set('emp_change_flag', 1) #同步人员到设备标记
        result=reportError(u"%s"%_("user information"), jjj, i_insert, i_update, i_rep, errorRec)
        msg=''
        if import_card_repeat:
            msg = u"%s%s %s " % (_(u"Repeat"),_(u'card number'),''.join(list(set(import_card_repeat))))
        if existing_balance:
            existing_balance = ','.join(existing_balance)
            msg += f"{_('The following personnel cards have a balance, and the data has not been updated:')}"
            msg += f'<div style="max-width: 250px;word-wrap: break-word;white-space: normal;overflow-wrap: break-word;">&nbsp;{existing_balance}</div>'
        settings.DEV_STATUS_SAVE=0
        return getJSResponse({"ret":0,"message": u"%s %s"%(result,msg)},mtype="text/plain")

    except Exception as e:
        import traceback
        traceback.print_exc()
        print ("importEmp====",e)
        settings.DEV_STATUS_SAVE=0
        cc=u"<h1>%s</h1><br/>%s"%(_("Import employee list"),_("employee&#39;s data imported failed"))+"</p><pre>"
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
#		cc+=request.POST.get(t+"2file","")
#		return HttpResponse(content="result=1",mimetype='text/plain')#render("info.html", {"title": _("Import employee list"), "content": cc})
    finally:
        sync_issuecard(request, issuecard_dic)
        # 当'支持生物模板消费'选择后，用来为没卡的人员发卡（卡记录）并激活，或是为未激活的卡进行激活
        sync_fingercons_issuecard(request, import_use_fingercons_pins)

@login_required
def importTrans(request):
    try:
        imFields=[]
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        i_insert=0
        i_update=0
        i_rep=0

        imFields=request.POST["fields"].split(',')
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_(u"Importing attendance record failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _(u"Batch upload attendance record"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        fl=fName.split('.')
        ext=''
        if fl:
            ext=fl[-1].upper()
        if ext not in ['TXT','CSV','XLS','XLSX']:
            result=u'%s'%(u"%s"%(_(u'Imported file name extension is incorrect')))
            return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj=0
        iCount=0
        sqlList=[]
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>10:
                        continue
                except:
                    continue
                if k.lower()=='PIN':

                    if ext!='XLS' and ext!='XLSX':
                        if ls[v-1]==''  or (not ls[v-1].isdigit()):
                            dDict={}
                            break
                    else:
                        if ls[v-1]=='' or isinstance(type(ls[v-1]),type(u'1')) or isinstance(type(ls[v-1]),type(1))or isinstance(type(ls[v-1]),type(1.0)):
                            dDict={}
                            break
                        else:
                            try:
                                ls[v-1]=int(ls[int(v)-1])
                            except:
                                dDict={}
                                break
                if v<=len(ls):
                    if ext!='XLS' and ext!='XLSX':
                        dDict[k]=getStr_c_decode(ls[int(v)-1])
                    else:
                        if k=='checktime':
                            if str(type(ls[int(v)-1]))=="<type 'unicode'>":
                                try:
                                    dDict['checktime']=datetime.datetime.now().strptime(ls[int(v)-1],"%Y-%m-%d %H:%M:%S")
                                except:
                                    dDict['checktime']=datetime.datetime.now().strptime(ls[int(v)-1],"%Y/%m/%d %H:%M:%S")
                            else:
                                try:
                                    iDate = int(ls[int(v)-1])
                                    lDate=xlrd.xldate_as_tuple(iDate,bk.datemode)
                                    dDict[k]=datetime.datetime(lDate[0],lDate[1],lDate[2])
                                except:
                                    dDict[k]=''
                        else:
                            dDict[k]=ls[int(v)-1]
            if dDict!={}:
                jjj+=1
                emp=employee.objects.filter(PIN=dDict['PIN'])
                if emp:
                    dDict['userid']=emp[0].id
                    del dDict['PIN']
                    sqlList.append(dDict)
                else:
                    #s=u"人员编号为 %s 的人员不存在" %(dDict['PIN'])
                    #cc=u"<h2>%s</h2><p>%s</p>"%(_(u'Import failed'),s)
                    #return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                    continue
        for dDict_n in sqlList:
            sql,params=getSQL_insert_new(transactions._meta.db_table,dDict_n)
            try:
                customSql(sql,params)
                i_insert+=1
            except:
                pass
        result=reportError(u"%s"%_(u"Import attendance record"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=transactions._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        print ("import transactions====%s"%e)
        cc=u"<h2>%s</h2>"%_(u"Importing attendance record failed")
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

@login_required
def importDept(request):
#	if not (request.method == 'POST'):
#		return render("Import_emp.html",{"title": _("Upload employee list") # u"批量上传人员列表",
#		})
    if settings.DEMO==1:
        if request.user.DelTag!=-2:
            cc = u"%s" % (u'This is Demo,Not allow to operate')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

    try:
        imFields=["DeptNumber","DeptName","supdeptnum"]
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        i_insert=0
        i_update=0
        i_rep=0

        oriFields=request.POST["fields"].split(',')
        for t in oriFields:
            fldName=request.POST.get(t,"")
            if fldName=='on':
                imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_("department&#39;s data imported failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import department list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            #data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
            fn="%s/%s"%(tmpDir(),fName)
            #这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>10:
                        continue
                except:
                    continue
                if k.lower()=='deptid':
                    if ext!='XLS' and ext!='XLSX':
                        if ls[v-1]==''  or (not ls[v-1].isdigit()):
                            dDict={}
                            break
                    else:
                        if ls[v-1]=='' or isinstance(type(ls[v-1]),type(u'1')) or isinstance(type(ls[v-1]),type(1))or isinstance(type(ls[v-1]),type(1.0)):
                            dDict={}
                            break
                        else:
                            try:
                                ls[v-1]=int(ls[int(v)-1])
                            except:
                                dDict={}
                                break
                if v<=len(ls):
                    if ext!='XLS' and ext!='XLSX':
                        dDict[k]=getStr_c_decode(ls[int(v)-1])
                    else:
                        dDict[k]=ls[int(v)-1]
            if dDict!={}:
                jjj+=1
                if not dDict['DeptNumber'] or not dDict['DeptName']:
                    s = u"%s"%(_(u"Section%s department number and department name must be filled"))%jjj
                    cc = u"%s,%s" % (s,_(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                dDict['DeptName'] = re.sub(u'[^\u4e00-\u9fa5a-zA-Z0-9_.（）()@\x20]', '', dDict['DeptName'])

                if not re.search('^[a-zA-Z0-9]{1,40}$', dDict['DeptNumber']):
                    s = u"%s"%(_(u"The department number is %s, only letters or numbers are allowed, and the string length cannot exceed 40"))%dDict['DeptNumber']
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                try:
                    dt = department.objects.get(DeptNumber=dDict['DeptNumber'])
                except:
                    dt = None
                supdt = department.objects.filter(DeptNumber = dDict['supdeptnum']).exclude(DelTag=1)
                if len(supdt)>0:
                    dDict['supdeptid'] = supdt[0].DeptID
                    # 兼容如果导入本条记录是总部门
                    if dt and dt.DeptID==1:
                        dDict['supdeptid'] = 0
                elif dt and dt.DeptID==1:#是否为根部门
                    dDict['supdeptid'] = 0
                else:
                    s = u"%s"%(_(u"Line %s upper department number must exist")) % jjj
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                if dt and dt.DeptID!=1:
                    global d_item
                    childlist = []
                    d_item = get_dept_as_dict()
                    AllChildrens(int(dt.DeptID), d_item, childlist)
                    childlist.append(dt.DeptID)
                    if supdt[0].DeptID in childlist:
                        s = u"%s"%(_(u"The department number %s cannot set the department&#39;s superior department for itself or its sub-sectors"))%dDict['DeptNumber']
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                dDict['DelTag']=0
                dDict['secondary']=0
                del dDict['supdeptnum']
                if dt:
                    isNewDept=dt.DelTag or 0
                    dDict['whereDeptID']=dt.DeptID
                    sql,params=getSQL_update_new('departments',dDict)
                    if customSqlEx(sql,params):
                        cache.delete("%s_iclock_dept_%s_%s"%(settings.UNIT,dt.DeptID,settings.DEPTVERSION))
                        i_update+=1
                        # 部门删除后再次导入相当于添加新部门，当添加新部门时 如果不是超级管理员将自动添加到自己的授权部门中
                        if isNewDept==1 and not request.user.is_superuser:
                            DeptAdmin(user=request.user, dept_id=dt.DeptID, iscascadecheck=0).save()
                else:
                    sql,params=getSQL_insert_new('departments',dDict)
                    if customSqlEx(sql,params):
                        i_insert+=1
                        # 当添加新部门时 如果不是超级管理员将自动添加到自己的授权部门中
                        if not request.user.is_superuser:
                            try:
                                newdept = department.objects.get(DeptNumber=dDict['DeptNumber'])
                                DeptAdmin(user=request.user, dept_id=newdept.DeptID, iscascadecheck=0).save()
                            except:
                                pass
                UpdateDeptCache()   #更新部门Cache
        result=reportError(u"%s"%_("department information"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=department._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        #t = u"%s"%(_(u"The %s line data is incorrect")) %jjj
        cc=u"%s,%s"%(e,_("department&#39;s data imported failed"))
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

@login_required
def importZone(request):
    try:
        imFields = ["code", "name", "supzonenum"]
        imFieldsInfo = {}
        imDatalist = []
        sUserids = []
        i_insert = 0
        i_update = 0
        i_rep = 0

        oriFields = request.POST["fields"].split(',')
        for t in oriFields:
            fldName=request.POST.get(t,"")
            if fldName=='on':
                imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_("department&#39;s data imported failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import department list"), "content": cc})

        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data+=chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s' % _('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():  # k:字段名(ZoneNumber)  y:所在列(1)
                try:
                    if v<1 or v>10:
                        continue
                except:
                    continue

                if v <= len(ls):
                    if ext!='XLS' and ext!='XLSX':
                        dDict[k]=getStr_c_decode(ls[int(v)-1])
                    else:
                        dDict[k]=ls[int(v)-1]
            if dDict!={}:
                jjj+=1
                if not dDict['code'] or not dDict['name']:
                    # 第%s行部门编号、部门名称必填
                    s = u"%s"%(_(u"Section%s zone number and zone name must be filled"))%jjj
                    cc = u"%s,%s" % (s,_(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                dDict['name'] = re.sub(u'[^\u4e00-\u9fa5a-zA-Z0-9_.（）()@\x20]', '', dDict['name'])

                # if not re.search('^[a-zA-Z0-9]{1,40}$', dDict['code']):
                #     s = u"%s"%(_(u"The zone number is %s, only letters or numbers are allowed, and the string length cannot exceed 40"))%dDict['code']
                #     cc = u"%s,%s" % (s, _(u'Import failed'))
                #     return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                try:
                    zt = zone.objects.get(code=dDict['code'])
                except:
                    zt = None
                supzone = zone.objects.filter(code=dDict['supzonenum']).exclude(DelTag=1)
                if len(supzone) > 0:
                    dDict['parent_id'] = supzone[0].id
                elif zt and zt.id==1:#是否为根区域
                    dDict['parent_id'] = 0
                else:
                    s = u"%s" % (_(u"Line %s upper zone number must exist")) % jjj
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                # 该区域的上级区域不能设置为其本身和其子区域中
                allchildlist = get_zone_list(zt.id, start=[]) if zt else []
                if dDict['code'] == dDict['supzonenum'] or dDict['supzonenum'] in allchildlist:
                    s = u"%s" % (_(u"The zone number %s cannot set the zone&#39;s superior zone for itself or its sub-sectors")) % dDict['code']
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                dDict['DelTag']=0
                del dDict['supzonenum']
                if zt:
                    dDict['wherecode']=zt.code
                    sql,params=getSQL_update_new('acc_zone',dDict)
                    if customSqlEx(sql,params):
                        i_update+=1
                else:
                    sql,params=getSQL_insert_new('acc_zone',dDict)
                    if customSqlEx(sql,params):
                        i_insert+=1
        result=reportError(u"%s"%_("zone information"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=zone._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        cc = u"%s,%s" % (e, _("zone&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


#作废
def importFptemp(request):
    if not (request.method == 'POST'):
        return render("Import_emp.html",{"title": _("Upload fptemp list")
        })
    try:
        imFields=[]
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        i_insert=0
        i_update=0
        i_rep=0
        imFields=request.POST["fields"].split(',')
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_("fptemp&#39;s data imported failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import fptemp list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        iCount=0
        sqlList=[]
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=="CSV":
                ls=t.split(b',')
            else:
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>10:
                        continue
                except:
                    continue
                if k.lower()=='userid':
                    if ls[v-1]=='':
                        dDict={}
                        break
                if v<=len(ls):
                    if ext=='XLS' or ext=='XLSX':
                        dDict[k]=ls[int(v)-1]
                    else:
                        dDict[k]=getStr_c_decode(ls[int(v)-1])
            if dDict!={}:
#				imDatalist.append(dDict.copy())
                jjj+=1
                try:
                    e=employee.objByPIN(dDict['badgenumber'])
                except:
                    e=None
                if e:
                    fingerid=int(dDict['FingerID'])
                    try:
                        f=fptemp.objects.get(UserID=e.id, FingerID=fingerid)
                    except:
                        f=None
                    sql=''
                    if f:
                        i_update+=1
                        sql="update template set template = '%s'  where userid=%s and fingerid=%s" % (dDict['Template'],  e.id, fingerid)
                    else:
                        i_insert+=1
                        sql = getSQL_insert("template", UserID=e.id, FingerID=fingerid,Template=dDict['Template'],Valid=1,DelTag=0,AlgVer=10)

                    if sql:
                        customSqlEx(sql)

        result=reportError(u"%s"%_("fptemp information"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=BioData._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        print ("import template%s"%e)
        cc=u"%s"%_("data imported failed")
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")



#		typedef struct _User_{		//size:72
#			U16 PIN;				//[:2]
#			U8 Privilege;			//[2:3]		Privilege
#			char Password[8];		//[3:11]	Password
#			char Name[24];			//[11:35]	EName
#			U8 Card[4];				//[35:39]					//卡号码，用于存储对应的ID卡的号码
#			U8 Group;				//[39:40]	AccGroup		//用户所属的分组
#			U16 TimeZones[4];		//[40:48]	TimeZones		//用户可用的时间段，位标志
#			char PIN2[24];			//[48:]		PIN
#		}GCC_PACKED TUser, *PUser;

@login_required
def importBiodata(request):
    if not (request.method == 'POST'):
        return render("Import_emp.html",{"title": _("Upload fptemp list")
        })
    try:
        imFields=[]
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        i_insert=0
        i_update=0
        i_rep=0
        imFields=request.POST["fields"].split(',')
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_("fptemp&#39;s data imported failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import fptemp list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        iCount=0
        sqlList=[]
        r=0

        new_lines = []
        face_dict = {}
        bio_type = request.POST.get('BioType',None)
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=="CSV":
                ls=t.split(b',')
            else:
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>10:
                        continue
                except:
                    continue
                if k.lower()=='userid':
                    if ls[v-1]=='':
                        dDict={}
                        break
                if v<=len(ls):
                    if ext=='XLS' or ext=='XLSX':
                        dDict[k]=ls[int(v)-1]
                    else:
                        dDict[k]=getStr_c_decode(ls[int(v)-1])
            new_lines.append(dDict)

        if bio_type == '2':
            #将多条数据合并为一条
            tmp_dict = {}
            for line in new_lines:
                pin = line['badgenumber']
                if pin not in tmp_dict:
                    tmp_dict[pin] = {
                                    'badgenumber':pin,
                                    'majorver':line['majorver'],
                                    'bio_tmp':{
                                        line['bio_index']:line['bio_tmp']
                                    }
                                }
                else:
                    tmp_dict[pin]['bio_tmp'].update({
                                        line['bio_index']:line['bio_tmp']
                                        })
            new_lines = []
            for k,v in tmp_dict.items():
                new_lines.append(v)

        for dDict in new_lines:
            if dDict!={}:
#				imDatalist.append(dDict.copy())
                jjj+=1
                try:
                    e=employee.objByPIN(dDict['badgenumber'])
                except:
                    e=None
                if e:
                    majorver = dDict['majorver']
                    try:
                        if bio_type != '2':
                            bio_index=int(dDict['bio_index'])
                            f=BioData.objects.get(UserID=e.id, bio_index=bio_index,bio_type=bio_type,majorver=majorver)
                        else:
                            f=BioData.objects.get(UserID=e.id,bio_type=bio_type,majorver=majorver)
                    except:
                        f=None
                    sql=''
                    utime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    if bio_type !='2':
                        if f:
                            i_update+=1
                            sql="update bio_data set bio_tmp = '%s',set UTime='%s'  where userid=%s and bio_index=%s" % (dDict['bio_tmp'],utime,  e.id, bio_index)
                        else:
                            i_insert+=1
                            sql = getSQL_insert("bio_data", UserID=e.id, bio_index=bio_index,bio_tmp=dDict['bio_tmp'],bio_no=bio_index,bio_type=bio_type,Valid=1,majorver=majorver,duress=0,UTime=utime)
                    else:
                        if f:
                            i_update+=1
                            sql="update bio_data set bio_tmp = '%s',set UTime='%s'  where userid=%s" % (dDict['bio_tmp'],utime,  e.id)
                        else:
                            i_insert+=1
                            sql = getSQL_insert("bio_data", UserID=e.id,bio_tmp=str(dDict['bio_tmp']).replace('\'','\"'),bio_type=bio_type,Valid=1,majorver=majorver,duress=0,UTime=utime)
                    if sql:
                        customSqlEx(sql)

        result=reportError(u"%s"%_(u"Import feature template information"), jjj, i_insert, i_update, i_rep, [])#result=reportError(u"%s"%_("template information"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=BioData._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        import traceback;traceback.print_exc()
        print ("import template%s"%e)
        cc=u"%s"%_("data imported failed")
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

#导入U盘数据
def upload_data(request):	# 上传导出到U盘的数据
    import struct
    emps=[]
    result = ""
    sqlList=[]
    sn = request.POST.get("SN","")
    device=getDevice(sn)
    if not device:
        return getJSResponse({"ret":1,"message":u"%s"%_("Not specified a target")})
    fs = request.FILES
    pin_pin2 = {}
    errorRecs=[]
    i_insert, i_update, i_rep = 0, 0, 0
    adminLog(time=datetime.datetime.now(),User_id=request.user.id, action=u"%s"%_(u"Import U-disk data files"),object = request.META["REMOTE_ADDR"]).save(force_insert = True)
    pin_pin2={}
    if "file_user" in fs:#.has_key("file_user"): # 用户信息
        try:
            dept = getDefaultDept()	# 默认部门
            f = fs["file_user"]
            data=""
            for chunk in f.chunks():
                data+=chunk
            upload_user=data
            i, count = 0, int(len(upload_user) / 72)
            if not (count>0 and count*72==len(upload_user)):
                raise Exception()
            i_insert, i_update, i_rep = 0, 0, 0
            errorRecs=[]
            while i < count:
                l=()
                buf = upload_user[i*72:(i+1)*72]
                try:
                    fmt="HB8s24s4sB8x24s"
                    l=struct.unpack(fmt,buf)
                except Exception as e:
                    print ("---------%s"%e)
                i+=1
                card=buf[35:39]
                card="%02X%02X%02X%02X"%(ord(buf[38]),ord(buf[37]),ord(buf[36]),ord(buf[35]))
                card=str(int(card,16))
                if not l:continue
                pin0=l[0]
                pin=formatPIN(getStr_c_decode(l[6]))    #考勤号码
                if pin in settings.DISABLED_PINS or (not pin.isdigit()):
                    continue
                priv=l[1]
                passwd=getStr_c_decode(l[2])
                ename=l[3]
                grp=l[5]
                fldNames=[]
                values=[]
                pin_pin2[pin0]=pin     #记住pin和pin2的对应,为后续的导入指纹使用
                if ename:
                    ename=getStr_c_decode(ename)
                emp=employee.objects.filter(PIN=pin)
                if emp:
                    e=emp[0]
                    if ename and (ename!=e.EName):
                        fldNames.append('name')
                        values.append(ename)
                    if passwd and (passwd!=e.MVerifyPass):
                        fldNames.append('MVerifyPass')      #考勤密码
                        values.append(passwd)
                    if (priv!=e.Privilege):
                        if (not (priv==0) and (e.Privilege==None)):
                            fldNames.append('privilege')
                            values.append(priv)
                    if card and card!=e.Card:
                        fldNames.append('Card')
                        values.append(card)
    #				if int(agrp)!=e.AccGroup:
    #					fldNames.append('AccGroup')
    #					values.append(agrp)
                    if len(fldNames)>0: #有更新的用户信息
                        sql="update userinfo set %s where badgenumber='%s'"%(','.join([u"%s=%s"%(fldNames[p],'%s') for p in range(len(fldNames))]), pin)
                    else:
                        sql=''
                        i_rep+=1
                    if sql:
                        customSqlEx(sql,values)
                        cache.delete("%s_iclock_emp_PIN_%s"%(settings.UNIT,pin))
                        cache.delete("%s_iclock_emp_%s"%(settings.UNIT,e.id))
                        i_update+=1
                else:
                    sql = getSQL_insert("userinfo", BadgeNumber = pin, defaultdeptid = dept.DeptID, OffDuty=0, DelTag=0,
                        Name = ename,Card=card, MVerifyPass = passwd,privilege=priv,ATT=1,OverTime=1,Holiday=1)
                    customSqlEx(sql)
                    i_insert+=1
            result+=reportError(u"%s"%_("user information"), i, i_insert, i_update, i_rep, errorRecs)
        except:
            return getJSResponse({"ret":1,"message":u"%s"%_("Employee information does not match the data, select the correct employee information data file")})
    if "file_fptemp" in fs:#.has_key("file_fptemp"): # 指纹模版
        try:
            if not pin_pin2:
                return getJSResponse({"ret":1,"message":u"%s"%_('No User data file')},mtype="text/plain")
            errorRecs=[]
            fp = fs["file_fptemp"]
            data=""
            for chunk in fp.chunks():
                data+=chunk
            upload_fptemp=data
            #fsn,upload_fptemp,sum=checkRecData(data,1024)
            #i, count = 0, round(len(upload_fptemp) / 1024)
            #if not (count>0 and ord(upload_fptemp[6])==0xA1 and ord(upload_fptemp[7])==0xCA ):
            #	raise Exception()
            #if not pin_pin2:
            #	return render("info.html", {"title": _("fail "),
             #                       "content": _("<h1> Data upload failure </ h1>, <br /> If the fingerprint template to upload, please also upload their associated user information!") });
            i_insert, i_update, i_rep = 0, 0, 0
            s=0
            while len(upload_fptemp)>0:
                sizebuf=upload_fptemp[:2]
                fmt="H"
                size_t=struct.unpack(fmt,sizebuf)
                size=size_t[0]
                fpsize=size-6
                buf=upload_fptemp[:size]
                try:
                    fmt="HHBB%ds"%fpsize
                    l=struct.unpack(fmt,buf)
                except Exception as e:
                    print ("---------%s"%e)
                upload_fptemp=upload_fptemp[size:]
                s+=1
                pin0=l[1]
                try:
                    pin=pin_pin2[pin0]
                except:
                    continue
                fingerid=l[2]
                valid=l[3]
                try:
                    e=employee.objByPIN(pin)
                except:
                    e=None
                if not e:continue          #如果人员不存在不保存指纹
                fpbuf=getFptemp_c(l[4])
                fptemplate=fpbuf.encode("base64").replace("\n","").replace("\r","")
                try:
                    fp=fptemp.objects.get(UserID=e.id, FingerID = fingerid)
                except:
                    fp=None
                sql=''
                if fp:
                    if not (fp.Template[:100]==fptemplate[:100]):
                        fp.Template=fptemplate
                        fp.save()
                        i_update+=1
                    else:
                        i_rep+=1
                else:
                    sql = getSQL_insert("template", UserID=e.id, Template = fptemplate, FingerID = fingerid, Valid = valid)
                if sql:
                    customSql(sql)
                    i_insert+=1

            result+=reportError(u"%s"%_("Fingerprint template"), s, i_insert, i_update, i_rep, errorRecs)
        except Exception as e:
            print ("fptemp===%s"%e)
#			errorLog(request)
#			info=_('Upload data files!')
            return getJSResponse({"ret":1,"message":(u"<h1>%s"%_('Data Import Results'))+"</h1><br />"+u"%s"%(result,)+(u"<br /><h1>%s"%_('Data Import failure'))+u"</h1><br /><br />%s"%_("Fingerprint template does not match the data or data file is empty, please select the correct <b>fingerprint template </b> data file!")},mtype="text/plain")
                                  #return getJSResponse({"ret":1,"message":info})
            #return getJSResponse({"ret":0,"message":"Upload data success"})
            #return render("info.html", {"title": _("Import data"),
            #		"content": _("<h1>Upload data success</h1><br /><br />%(object_name)s<br /><h1>Data Import failure</h1><br /><br />Fingerprint template does not match the data or data file is empty, please select the correct <b>fingerprint template </b>data files" )% {'object_name':result,}});


    if "file_facetemp" in fs:#.has_key("file_facetemp"): # 面部模版
        try:
            if not pin_pin2:
                return getJSResponse({"ret":1,"message":u"%s"%_('No User data file')},mtype="text/plain")
            errorRecs=[]
            fp = fs["file_facetemp"]
            data=""
            for chunk in fp.chunks():
                data+=chunk
            upload_facetemp=data
            i_insert, i_update, i_rep = 0, 0, 0
            s=0
            while len(upload_facetemp)>0:
                structsize=2576
                face_tmp_maxsize=2560
                buf=upload_facetemp[:structsize]
                buftemp=buf
                try:
                    fmt="HHBBHII%ds"%(face_tmp_maxsize)
                    l=struct.unpack(fmt,buf)
                except Exception as e:
                    print ("---------%s"%e)
                upload_facetemp=upload_facetemp[structsize:]
                s+=1
                pin0=l[1]
                try:
                    pin=pin_pin2[pin0]
                except:
                    continue
                faceid=l[2]
                valid=l[3]
                algver=l[4]
                utime=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                try:
                    e=employee.objByPIN(pin)
                except:
                    e=None
                if not e:continue          #如果人员不存在不保存面部
                fpbuf=buftemp[15:1230]
                fptemplate=fpbuf.encode("base64").replace("\n","").replace("\r","")
                fptemplate="AAAAAAAAAAAAAAAAAAAA%sAAAAAAAA"%fptemplate
                try:
                    fp=facetemp.objects.get(UserID=e.id, FaceID = faceid)
                except:
                    fp=None
                sql=''
                if fp:
                    if not (fp.Template[:100]==fptemplate[:100]):
                        fp.Template=fptemplate
                        fp.save()
                        i_update+=1
                    else:
                        i_rep+=1
                else:
                    sql = getSQL_insert("facetemplate", UserID=e.id, Template = fptemplate,FaceID = faceid, Valid = valid, SN=sn,UTime=utime, AlgVer=algver)
                if sql:
                    customSql(sql)
                    i_insert+=1

            result+=reportError(u"%s"%_("Face template"), s, i_insert, i_update, i_rep, errorRecs)
        except Exception as e:
            print ("facetemp===%s"%e)
            return getJSResponse({"ret":1,"message":(u"<h1>%s"%_('Data Import Results'))+"</h1><br />"+u"%s"%(result,)+(u"<br /><h1>%s"%_('Data Import failure'))+u"</h1><br /><br />%s"%_("Face template does not match the data or data file is empty, please select the correct <b>face template </b> data file!")},mtype="text/plain")




    if "file_transaction" in fs: # 考勤记录
        try:
            f = fs["file_transaction"]
            data=""
            for chunk in f.chunks():
                data+=chunk
            #fsn,upload_transaction,sum=checkALogData(data)
            arr = data.split("\n")
            i, count = 0, len(data) / 32
            i, i_insert,i_rep,i_update = 0, 0,0,0
            errorRecs=[]
            maxtime=(datetime.datetime.now()+datetime.timedelta(1, 0, 0)).strftime("%Y-%m-%d %H:%M:%S")
            for row in arr:
                if row=="": continue
                arr_row = row.split("\t")
                i += 1
                pin=formatPIN(arr_row[0].strip())
                time=arr_row[1]
                if pin.isdigit() and (len(time) in [19,16]) and (maxtime>time) and (not pin in settings.DISABLED_PINS):
                    try:
                        e=employee.objByPIN(pin)    #目的是如果导入的考勤记录中考勤号码在人员信息不存在不进行导入,要求必须先导入人员才能导入记录
                    except:
                        e=None
                        errorRecs.append(row)
                    if e:
                        sql = getSQL_insert("checkinout", userid=e.id, checktime=time, SN=sn,
                            checktype=arr_row[3], verifycode =arr_row[2])
                        try:
                            if customSqlEx(sql):
                                i_insert += 1
                        except Exception as e:
                            estr="%s"%e
                            if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or ("Duplicate entry" in estr) or ("unique constraint" in estr):
                                i_rep+=1
                            else:
                                errorRecs.append(row)

                else:
                    errorRecs.append(row)
            result+=reportError(_("Transactions"), i, i_insert,i_update, i_rep, errorRecs)
        except:
            return getJSResponse({"ret":1,"message":(u"<h1>%s"%_('Data Import Results'))+"</h1><br />"+u"%s"%(result,)+(u"<br /><h1>%s"%_('Data Import failure'))+u"</h1><br /><br />%s"%_("Transactions does not match data, Please select the correct <b>transaction</b> data file!")},mtype="text/plain")
            #return render("info.html", {"title": _("Import data"),
            #		"content": (u"<h1>%s"%_('Data Import Results'))+"</h1><br /><br />"+u"%s"%(result,)+(u"<br /><h1>%s"%_('Data Import failure'))+u"</h1><br /><br />%s"%_("Transactions does not match data, Please select the correct <b>transaction</b> data file!")});


    if "file_transpic" in fs: # 考勤记录照片
        try:
            f = fs["file_transpic"]
            request.user.iclock_url_rel='../..'
            from mysite.iclock.datamisc import saveUploadTranspic
            fname = getStoredFileName('upload/temp_transpic/%s'%sn, None, f)
            saveUploadTranspic(request, "file_transpic", fname)
        except Exception as e:
            print ('EEEEEEEEEE',e)

    return getJSResponse({"ret":0,"message": u"<h1>%s</h1>%s"%(_('Data Import Results'),result)},mtype="text/plain")


@login_required
def app_emp(request):
    if not (request.method == 'POST'):
        return render("disp_emp.html",{"title": _("Upload employee list"), # u"批量上传人员列表",
                'task':'userinfo'})
    i=0;
    f=request.FILES["fileUpload"]
    data=""
    for chunk in f.chunks(): data+=chunk
    lines=data.splitlines()
    pin,ename,pwd,card,grp,tzs='','','','','1',''
    userTmp=[]
    for line in lines:
#		try:
        if line.find('[Users_')==0:
            i+=1
            if(len(pin)>0):
                saveUser(pin, pwd, ename, card, grp, tzs)
                for tmp in userTmp:
                    saveFTmp(pin, tmp['fid'], tmp['tmp'])
            pin=line[7:-1]
            ename,pwd,card,grp,tzs='','','','1',''
            userTmp=[]
        elif line.find('Name=')==0: ename=line[5:]
        elif line.find('Password=')==0: pwd=line[9:]
        elif line.find('AccTZ1=')==0: tzs=line[7:]
        elif line.find('Card=')==0: card=line[5:]
        elif line.find('Grp=')==0: grp=line[4:]
        elif line.find('FPT_')==0:
            ftmp=line.split('=')
            fid=ftmp[0][4:]
            userTmp.append({"fid":fid, "tmp":ftmp[1]})
#		except Exception as e:
#			errorLines.append("LINE(%d):%s :%s"%(i,str(e),line))
    if(len(pin)>0):
        saveUser(pin, pwd, ename, card, grp, tzs)
        for tmp in userTmp:
            saveFTmp(pin, tmp['fid'], tmp['tmp'])
    response = HttpResponse(content_type='text/plain')
    #response.write(gettext("%(object)s employee has been successfully!")%{'object':i});
    return response


@login_required
def del_emp_log(request):
    pass
    #return render("disp_emp_log.html",{"title": gettext("Delete the employee(s) in device"), 'task':'deleteuser'})

@login_required
def upgrade(request):
    if not (request.method == 'POST'):
        return render("upgrade.html",{"title": gettext("Server upgrades")})
    i=0
    f=request.FILES["fileUpload"]
    bytes=""
    for chunk in f.chunks(): bytes+=chunk
    target=tmpDir()+"/mysite.zip"
    bkFile="%s/%s.zip"%(tmpDir(),time.strftime("%Y%m%d%H%M%S"))
    open(target,"wb+").write(bytes)
    zipDir('c:/mysite/', bkFile)
    fl=unzipFile(target, 'c:/')
    response = HttpResponse(content_type='text/plain')
    response.write("BACKUP OLD FILE TO: "+bkFile+"\r\n"+("\r\n".join([("%s\t%s"%(fl[f], f)) for f in fl.keys()])));
    if fl:
        restartThread("iclock-server").start()
        restartThread("iclock").start()
        restartThread("Apache2").start()
    return response

from gzip import *
def getGZipSize(fname):
    g=GzipFile(filename=fname, mode="rb")
    s=0
    while True:
        chunk=g.read(1024)
        cs=len(chunk)
        if cs>0:
            s+=cs
        else:
            break
    return s


@login_required
def restartDev(request):
    ip=request.GET.get("IP","")
    if ip:
        url, fname=getFW(iclock(Style="X938"))
        fwSize=getGZipSize(fname)
        ret=reb.tryDoInDev(ip,["ls /mnt/mtdblock/main -l","reboot"])
        if type(ret).__doc__.find("list")==0:
#			if ret[0].find(" %s"%fwSize)<10:
#				ret=reb.tryDoInDev(ip,["cd /mnt/mtdblock/","wget http://*************/iclock/file/fw/X938/main.gz","reboot"])
            ret=gettext("Restart device successfully: ")+ip+"</p><p>"+(ret[0].find(" %s"%fwSize)<10 and ret[0]+"</p><p>"+gettext("Its size and standard firmware(%(object_name)s) is inconsistent, Need to upgrade firmware If there is no automatic inspection later firmware update, or manually upgrade firmware")%{'object_name':fwSize or ""})
        else:
            ret=gettext("Failure to restart device:")+ip+"</p><p>"+ret
    else:
        ret=_("Please enter the IP address of the device")
    return render("info.html",{"title": _("Restart device"), 'content': ret})

@login_required
def autoRestartDev(request):
    iclocks=iclock.objects.filter(LastActivity__lt=datetime.datetime.now()-datetime.timedelta(0,(settings.REBOOT_CHECKTIME>30 and settings.REBOOT_CHECKTIME or 30)*60)).exclude(DelTag=1)
    ips=updateLastReboot(iclocks)
    rebDevsReal(ips)
    return render("info.html",{"title": _("Automatically check sluggish device"), 'content':gettext("The device(s) does not connect with the server more than half and an hour: </p><p>")+("<br />".join([u"%s: %s"%(i, i.IPAddress()) for i in iclocks]))+"</p><p>&nbsp;</p><p>"+(ips and gettext("The system will connect and re-start the following device automaticly: </p><p>")+("<br />".join(ips)) or "")})

def check_contain_chinese(check_str):
    for ch in check_str:
        if u'\u4e00' <= ch <= u'\u9fff':
            return True
        else:
            return False


@login_required
def importIclock(request):
    from mysite.core.zkmimi import check_sn_valid
    try:
        imFields=['SN']
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        oriFields=request.POST["fields"].split(',')
        fromline=request.POST.get("rowid2","1")
        if fromline=="":
            fromline=1
        fromline=int(fromline)
        for t in oriFields:
            fldName=request.POST.get(t,"")
            if fldName=='on':
                imFields.append(t)
        maxCol=0
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
                if maxCol<imFieldsInfo[t]:maxCol=imFieldsInfo[t]
            except:
                cc=_(u"Import device failed")+"</p><pre>"
                return render("info.html", {"title": _(u"Device List"), "content": ""})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        lines=[]
        for chunk in f.chunks(): data+=chunk
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
            # fName=safe_unicode(fName,"GB18030")#fName.encode("gb2312")#修改导入文件名称为中文报错问题
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                c=0
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                    c=c+1
                    if c>maxCol-1:break
                row_data = sv
                lines.append(row_data)
        jjj=0
        jj=0
        iCount=len(lines)          #import users count
        i_insert=0
        i_update=0
        i_rep=0
        errorRec=[]
        now=datetime.datetime.now()
        stoptag=0
        r=0
        deptlist=[]
        Diningcode = []
        if 'Diningcode' in oriFields:
            code = Dininghall.objects.get(pk = 1).code
            Diningcode.append(code)
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            jj+=1
            if jj<fromline:continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            iclockDeptDict = {}
            IclockDininghallDict = {}
            dDict = {}
            areacode = ''
            zone_obj=[]
            for k,v in imFieldsInfo.items():
                if v<1 or v>100:
                    continue
                v=int(v)
                if ls[v-1]!=None:
                    ls[v-1]=ls[v-1].strip()
                if k=='SN' and ls[v-1]=='':
                    s = u"%s"%(_(u"Number {rs} line {vs} column device serial number cannot be empty")).format(rs=r, vs=v)
                    cc=u"%s,%s"%(_('imported failed'),s)
                    return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                if k == 'SN' and not re.match('[a-zA-Z0-9]+$', ls[v-1]): # 限制设备序列号只能由字母和数字组成
                    s = u"%s" % (_(u"Number {rs} line {vs} column device serial number can only be called by letters and numbers")).format(rs=r, vs=v)
                    cc = u"%s,%s" % (_('imported failed'), s)
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if k=='SN' and (len(ls[v-1])<10 or len(ls[v-1])>20):
                    s = u"%s" % (_(u"%s device serial number cannot be less than 10")%ls[v-1])
                    cc = u"%s,%s" % (_('imported failed'), s)
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if k == 'SN':
                    value = ls[v-1]
                    # 遍历字符串 检测是否存在中文
                    for every_sty in iter(ls[v-1]):
                       if check_contain_chinese(every_sty):
                            s = u"%s,%s" % (_(u"%s SN Include only English letters and numbers"),ls[v-1])
                            cc = u"%s,%s" % (_('imported failed'), s)
                            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    if not ls[v-1].isalnum(): # 非字母或数字
                        s = u"%s,%s" % (_(u"%s SN Include only English letters and numbers"),ls[v-1])
                        cc = u"%s,%s" % (_('imported failed'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if k=='DeptID':
                    deptlist = ls[int(v) - 1].split(',')
                if k=='Diningcode':
                    restaurant_code = ls[int(v) - 1].strip()
                    if not restaurant_code:
                        s = u"%s" % (_(u"The %s line data is incorrect")%r)
                        m=u"%s%s"%(_(u"restaurant"),_(u"Number cannot be empty"))
                        cc = u"%s,%s,%s" % (_('imported failed'), s,m)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    dt = Dininghall.objects.filter(code=restaurant_code).exclude(DelTag__gt=0)
                    if not dt:
                        s = u"%s" % (_(u"The %s line: The Restaurant code %s don't exist!") % (r, restaurant_code))
                        cc = u"%s,%s" % (_('imported failed'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    Diningcode = ls[int(v) - 1].split(',')

                if k=='Area':
                    if not ls[int(v) - 1].strip():
                        s = u"%s" % (_(u"The %s line data is incorrect") % r)
                        cc = u"%s,%s" % (_('imported failed'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    areacode = ls[int(v) - 1].strip()
                    zone_obj = zone.objects.filter(code=areacode)
                    if not zone_obj:
                        s = u"%s" % (_(u"The %s line Area %s does not exist,Equipment can only belong to one area") % (r,areacode))
                        cc = u"%s,%s" % (_('imported failed'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if k=='Alias':
                    ls[int(v)-1]=ls[int(v)-1][:20]
                if v<=len(ls):
                    s=ls[int(v)-1]
                    if ext=='XLS' or ext=='XLSX':
                        dDict[k]=s
                    else:
                        dDict[k]=getStr_c_decode(s)
            if stoptag==1:
                break
            if 'SN' in dDict.keys():
                dDict['SN'] = dDict['SN'].upper()
                jjj+=1
                try:
                    ClockSN = getDevice(dDict['SN'])
                except:
                    ClockSN = None
                SN=dDict['SN']
                if 'DeptID' in dDict:
                    del dDict['DeptID']
                if 'Diningcode' in dDict:
                    del dDict['Diningcode']
                if 'Area' in dDict:
                    del dDict['Area']
                if ClockSN:
                    dDict['whereSN']=dDict['SN']
                    dDict['DelTag']=0
                    del dDict['SN']
                    if Diningcode:
                        dDict['ProductType'] = 11
                    else:
                        dDict['ProductType'] = 9
                    if not ClockSN.ProductType:
                        ClockSN.ProductType = 9
                    if dDict['ProductType'] != ClockSN.ProductType and ClockSN.DelTag == 0: #设备新增后，不能导入更改用途
                        s = u"%s" % (_(u"The %s line data is incorrect") % r)
                        cc = u"%s,%s" % (_('The equipment is already in use and cannot be replaced'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    sql,params=getSQL_update_new('iclock',dDict)
                    ic=iclock.objects.all().exclude(DelTag=1).count()
                    icup = iclock.objects.filter(SN=dDict['whereSN'],DelTag=1).count()
                    if ic+icup>settings.MAX_DEVICES:
                        return getJSResponse({"ret":1,"message": u"%s"%(u"%s"%(_(u'The number of devices specified by the system!')))},mtype="text/plain")
                    if customSqlEx(sql,params):
                        i_update+=1
                    cache.delete("iclock_"+dDict['whereSN'])
                else:
                    iclock_count= iclock.objects.all().count()
                    dDict['id'] = iclock_count+1
                    dDict['TransInterval']=1
                    dDict['AlgVer']='10'
                    dDict['TZAdj']=8
                    dDict['State']=1
                    dDict['UpdateDB']='1111100000'
                    dDict['AccFun']=0
                    dDict['DelTag']=0
                    dDict['LogStamp']=0
                    dDict['OpLogStamp']=0
                    dDict['PhotoStamp']=0
                    dDict['Authentication']=1
                    dDict['ProductType'] = 9
                    if Diningcode:
                        dDict['ProductType']=11
                        dDict['consume_model']=2
                        dDict['card_max_money']=999
                        dDict['favorable']=0
                    #dDict['saveStamp']=datetime.datetime.now()
                    #dDict['Purpose']=9
                    dDict['isUSERPIC']=0
                    dDict['isFptemp']=1
                    dDict['isFace']=0

                    dDict['is_add']=False
                    dDict['is_zeor']=False
                    dDict['is_OK']=False

                    dDict['check_black_list']=True
                    dDict['check_white_list']=False
                    dDict['is_cons_keap']=False
                    dDict['is_cons_keap']=False
                    dDict['is_check_operate']=False
                    dDict['consume_order']=4
                    dDict['offline_consumption']=False
                    dDict['time_price'] = 6

                    #dDict['wlan']=0
                    res = check_sn_valid(SN)  # 判断设备是否在加密文件devices.dat中
                    if res[1] == 1:
                        if not res[0]:
                            if settings.DEBUG == 1:
                                print ("add_device sn=,not found in devices.dat", SN)

                            return getJSResponse({"ret": 1, "message": u"%s%s-%s" % (_('Save failed'), _(u'The device is not in the list of authorized devices'), SN)})

                    ic=iclock.objects.all().exclude(DelTag=1).count()
                    if ic>=settings.MAX_DEVICES:
                        return getJSResponse({"ret":1,"message": u"%s"%(u"%s"%(_(u'The number of devices specified by the system!')))},mtype="text/plain")
                    sql,params=getSQL_insert_new('iclock',dDict)
                    if customSqlEx(sql,params):
                        i_insert+=1
                    #dev=iclock.objects.get(SN=dDict['SN'])
                    #cache.set("iclock_"+dDict['SN'], dev)
                if deptlist:
                    IclockDept.objects.filter(SN=ClockSN).delete()
                    iclockDeptDict['SN_id'] = SN
                    iclockDeptDict['iscascadecheck'] = 0
                    for deptid in deptlist:
                        dt = department.objByNumber(deptid)
                        if dt:
                            iclockDeptDict['dept_id'] = dt.DeptID
                            sql,params=getSQL_insert_new('iclock_iclockdept',iclockDeptDict)
                            customSqlEx(sql,params)
                if Diningcode:
                    IclockDininghall.objects.filter(SN=ClockSN).delete()
                    IclockDininghallDict['SN_id'] = SN
                    for diningcode in Diningcode:
                        dt = Dininghall.objects.filter(code = diningcode).exclude(DelTag__gt=0)
                        if dt:
                            IclockDininghallDict['dining_id'] = dt[0].id
                            sql,params=getSQL_insert_new('ipos_iclockdininghall',IclockDininghallDict)
                            customSqlEx(sql,params)

                if areacode:
                    if not ClockSN:
                        try:
                            ClockSN = getDevice(dDict['SN'])
                        except:
                            ClockSN = None
                    if zone_obj and ClockSN:
                        iczon = IclockZone.objects.filter(SN=ClockSN)
                        if not iczon:
                            IclockZone(SN=ClockSN, zone=zone_obj[0]).save()
                        else:
                            iczon[0].zone = zone_obj[0]
                            iczon[0].save()

        result=reportError(u"%s"%_("Data Import Results"), jjj, i_insert, i_update, i_rep, errorRec)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=iclock._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        cc=""u"<h1>%s</h1><br/>%s"%(_(u"Import device"),_(u"import device data Failed"))+"</p><pre>"
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")


@login_required
def importschclasstmpShift(request):
    try:
        imFields=[]
        imFieldsInfo={'pin','name','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31'}
        imDatalist=[]
        sUserids=[]
        i_insert=0
        i_update=0
        i_rep=0
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        typeofsch=request.POST["IsSchName"]
        year=int(request.POST["year2file"])
        month=int(request.POST["month2file"])
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        schclass_dict = {}
        SchClassObj = SchClass.objects.filter(DelTag=0)
        for scObj in SchClassObj:
            schclass_dict[scObj.SchName] = scObj.SchclassID
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        for chunk in f.chunks():
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(sr)
                        if sr.index(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        iCount=0
        sqlList=[]
        r=0
        time=datetime.datetime.now()
        time=datetime.datetime.strftime(time,'%Y-%m-%d %H:%M:%S')
        #d1="%s-%s-%s"%(year,month,1)
#		d1=datetime.datetime.strptime(d1,'%Y-%m-%d')
        d1=datetime.datetime(int(year),int(month),1,0,0,0)
        d2=d1+datetime.timedelta(days=32)
        d2=datetime.datetime(d2.year,d2.month,1,23,59,59)-datetime.timedelta(days=1)
        first_name=request.user.first_name
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=="CSV":
                ls=t.split(b',')
            else:
                ls=t
            for k in imFieldsInfo:
                if k.lower()=='name':continue
                if k.lower()=='pin':
                    if ls[0]=='':
                        dDict={}
                        break
                    else:
                        try:
                            pin=str(ls[0]).split(".")[0]
                            pin=formatPIN(pin)
                            userid=employee.objByPIN(pin)
                            #userid=userid.id
                            dDict['userid']=userid
                        except:
                            dDict={}
                            s=u"%s"%(_(u"The personnel number is %s does not exist")) %(pin)
                            cc=u"<h2>%s</h2><p style='color:red'>%s</p>"%(_(u'Import failed'),s)
                            return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                else:
                    try:
                        k=int(k)
                        monthRange = calendar.monthrange(year,month)
                        if k>monthRange[1]:continue

                        checktime="%s-%s-%s"%(year,month,k)
                        #dDict['datetime']=checktime
                        k=k+1
                        if ext=='XLS' or ext=='XLSX':
                            dDict[checktime]=ls[k].split(".")[0]
                        else:
                            dDict[checktime]=getStr_c_decode(ls[k]).split(".")[0]
                    except:
                        pass
            if dDict!={}:
                jjj+=1
                emp=dDict['userid']
                i=emp.id

                deleteCalcLog(UserID=int(i), StartDate=d1, EndDate=d2)
                deleteUserShifts(uid=i, startdate=d1, enddate=d2, schetype=0)
                din=0
                for key in dDict.keys():
                    if key !='userid':
                        try:
                            st=datetime.datetime.strptime(key,'%Y-%m-%d')
                            tl=dDict[key]
                            tl=tl.split(",")
                            for TName in tl:
                                if TName.strip() == '':
                                    continue
                                else:
                                    try:
                                        if typeofsch == 'Id':
                                            t=int(TName)
                                        else:
                                            t=int(schclass_dict[TName])
                                    except:
                                        s = u"%s%s%s" % (TName, _(u"Time Period"), _(u"does not exist"))
                                        cc = u"<h2>%s</h2><p style='color:red'>%s</p>" % (_(u'Import failed'), s)
                                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                                FTTimeZones=[]
                                UserSchPlan=LoadSchPlan(emp,True,False)
                                SchedulerShifts=GetUserScheduler(emp, st, st,UserSchPlan['HasHoliday'])
                                schClass=FindSchClassByID(t)
                                if schClass:
                                    stime=schClass['TimeZone']['StartTime']
                                    etime=schClass['TimeZone']['EndTime']
                                    NextDay=schClass['NextDay']
                                    tt=datetime.datetime.strptime(key,'%Y-%m-%d')
                                    starttime = datetime.datetime(tt.year,tt.month,tt.day,stime.hour,stime.minute)
                                    endtime = datetime.datetime(tt.year,tt.month,tt.day,etime.hour,etime.minute)+datetime.timedelta(days=NextDay)
                                    if TestTimeZone(FTTimeZones,starttime,endtime):
                                        AddScheduleShift(SchedulerShifts, starttime, endtime,t,0)
                                    sTemp={'StartDate':starttime,'EndDate':endtime,'schclassid':t}
                                    saveEmpScheduleLogToFile('emp_schedule','%s %s %s import'%(dumps(sTemp),request.user,datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')),emp.pin())

                                    SaveTempSch(i,st,st,SchedulerShifts)
                                else:
                                    s=u"%s"%(_(u"The time slot number %s may not exist")) %(t)
                                    cc=u"<h2>%s</h2><p style='color:red'>%s</p>"%(_(u'Import failed'),s)
                                    return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                        except Exception as e:
                            #print 'error=====',e
                            pass
                if din>0:
                    i_update+=1
                else:
                    i_insert+=1
        result=reportError(u"%s"%_(u"Import scheduling data"), jjj, i_insert, i_update, i_rep, [])
        #adminLog(time=datetime.datetime.now(),User=request.user, action=u"导入排班").save()
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=USER_TEMP_SCH._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        import traceback;traceback.print_exc()
        cc=u"%s"%_("data imported failed")
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")


@login_required
def importSpeday(request):
    from mysite.iclock.process import getprocess_EX,getemployeeprocess
    from mysite.iclock.nomodelview import gettimediff
    try:
        leave=[]
        qryLeaveClass=LeaveClass.objects.all().order_by('LeaveID')
        for t in qryLeaveClass:
            r=FetchLeaveClass(t)
            if len(r)>0:
                leave.append(r)
        imFields=['badgenumber','StartSpecDay','EndSpecDay','StartSpecDayTime','EndSpecDayTime','DateID']
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]

        #clearance_yes=request.POST["clearance_yes"]
        #clearance_no=request.POST["clearance_no"]
        oriFields=request.POST["fields"].split(',')
        fromline=request.POST.get("rowid2","1")
        if fromline=="":
            fromline=1
        fromline=int(fromline)
        for t in oriFields:
            fldName=request.POST.get(t,"")
            if fldName=='on':
                imFields.append(t)

        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_(u"Fake data import failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import employee list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        lines=[]
        for chunk in f.chunks(): data+=chunk
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")#修改导入文件名称为中文报错问题
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)

            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(sr)
                        try:
                            if sr.index(".0")!='-1':
                                sr=sr.split(".")[0]
                            else:
                                sr=sr
                        except:
                            sr=sr
                    sv.append(sr)
#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        jj=0
        iCount=len(lines)          #import users count
        i_insert=0
        i_update=0
        sql_insert=[]
        # sql_update=[]
        i_rep=0
        errorRec=[]
        #adminLog(time=datetime.datetime.now(),User=request.user, action=u"导入请假数据").save()
        sqlList=[]
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            jj+=1
            if jj<fromline:continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>100:
                        continue
                except:
                    continue
                v=int(v)
                try:
                    ls[v-1]=ls[v-1].strip()
                except:
                    s=u"%s"%(_(u"The {rs} line {vs} column has no value, please enter at least one space")).format(rs=r, vs=v)
                    cc=u"%s,%s"%(_('imported failed'),s)
                    return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                if k=='badgenumber':
                    if ls[v-1]=='': #or (not ls[v-1].isdigit()):
                        errorRec.append(ls[v-1])
                        dDict={}
                        break
                    sUserids.append(ls[v-1])
                #if k=="clearance":
                #	if v<=len(ls):
                #		if ext=='XLS'  or ext=='XLSX':
                #			s=ls[v-1]
                #		else:
                #			s=getStr_c_decode(ls[v-1])
                #		if s==clearance_yes:
                #			ls[v-1]=1
                #		elif s==clearance_no:
                #			ls[v-1]=0
                #		else:
                #			ls[v-1]=0
                if k=="DateID":
                    if v<=len(ls):
                        if ext=='XLS'  or ext=='XLSX':
                            s=ls[v-1]
                        else:
                            s=getStr_c_decode(ls[v-1])
                        isHave=0
                        for t in leave:
                            if s==t['LeaveName']:
                                tmp_l = LeaveClass.objects.get(LeaveName=s)
                                if tmp_l.DelTag==1:
                                    isHave=2
                                else:
                                    isHave=1
                                    ls[v-1]=t['LeaveID']
                        if isHave==0:
                            s=u"%s"%(_(u"The name of the system pseudoclass is %s does not exist")) %(s)
                            cc=u"<h2>%s</h2><p style='color:red'>%s</p>"%(_(u'Import failed'),s)
                            return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                        elif isHave==2:
                            s=u"%s"%(_(u"The system name is deleted in the name %s")) %(s)
                            cc=u"<h2>%s</h2><p style='color:red'>%s</p>"%(_(u'Import failed'),s)
                            return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                if v<=len(ls):
                    s=ls[int(v)-1]
                    if ext=='XLS' or ext=='XLSX':
                        if k in ['StartSpecDay','EndSpecDay']:
                            #if str(type(s))=="<type 'unicode'>":
                            if '-' in s:
                                lDate=s.split('-')
                                dDict[k]=datetime.datetime(int(lDate[0]),int(lDate[1]),int(lDate[2]))
                            elif '/' in s:
                                lDate = s.split('/')
                                dDict[k] = datetime.datetime(int(lDate[0]), int(lDate[1]), int(lDate[2]))
                            else:
                                iDate = int(s)
                                lDate=xlrd.xldate_as_tuple(iDate,bk.datemode)
                                iDate1=datetime.datetime(lDate[0],lDate[1],lDate[2])
                                dDict[k]=iDate1
                        else:
                            dDict[k]=s
                    else:
                        dDict[k]=getStr_c_decode(s)
            if dDict!={}:
                jjj+=1
                #print dDict
                if 'YUANYING' in dDict:
                    dDict['YUANYING']=dDict['YUANYING'].replace("\'","")
                    dDict['YUANYING']=dDict['YUANYING'].replace("\"","")
                else:
                    dDict['YUANYING']=''
                try:
                    e=employee.objByPIN(dDict['badgenumber'])
                    dDict['UserID']=e.id
                except:
                    s=u"%s"%(_(u"The person whose job number is %s does not exist")) %(dDict['badgenumber'])
                    cc=u"<h2>%s</h2><p style='color:red'>%s</p>"%(_(u'Import failed'),s)
                    return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                    #errorRec.append(dDict['badgenumber'])
                    #continue
                if dDict['StartSpecDayTime']:
                    if ':' in dDict['StartSpecDayTime']:
                        slTime=dDict['StartSpecDayTime'].split(':')
                        dDict['StartSpecDay']=dDict['StartSpecDay']+datetime.timedelta(hours=int(slTime[0]),minutes=int(slTime[1]))
                    else:
                        iTime = float(dDict['StartSpecDayTime'])
                        dDict['StartSpecDay']=dDict['StartSpecDay']+datetime.timedelta(days=iTime)


                del dDict['StartSpecDayTime']

                if dDict['EndSpecDayTime']:
                    if ':' in dDict['EndSpecDayTime']:
                        slTime=dDict['EndSpecDayTime'].split(':')
                        dDict['EndSpecDay']=dDict['EndSpecDay']+datetime.timedelta(hours=int(slTime[0]),minutes=int(slTime[1]))
                    else:
                        iTime = float(dDict['EndSpecDayTime'])
                        dDict['EndSpecDay']=dDict['EndSpecDay']+datetime.timedelta(days=iTime)
                tmp = USER_SPEDAY.objects.filter(UserID=dDict['UserID'], StartSpecDay__lte=dDict['EndSpecDay'],
                                                 EndSpecDay__gte=dDict['StartSpecDay']).exclude(State=3)
                if tmp:
                    # 验证数据库的时间重叠
                    s = u"%s %s" % ((dDict['badgenumber']), (_(u"There are overlaps in staff leave time")))
                    cc = u"<h2>%s</h2><p style='color:red'>%s</p>" % (_(u'Import failed'), s)
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                for check_data in sql_insert:
                    # 验证要导入数据的时间重叠
                    if (check_data['UserID'] == dDict['UserID']) and (check_data['StartSpecDay'] <= dDict['EndSpecDay']
                    ) and (check_data['EndSpecDay'] >= dDict['StartSpecDay']):
                        s = u"%s %s" % ((dDict['badgenumber']), (_(u"There are overlaps in staff leave time")))
                        cc = u"<h2>%s</h2><p style='color:red'>%s</p>" % (_(u'Import failed'), s)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if dDict['StartSpecDay']>dDict['EndSpecDay']:
                    s=u"%s"%(_(u"Error in start date and end date for staff with job number %s")) %(dDict['badgenumber'])
                    cc=u"<h2>%s</h2><p style='color:red'>%s</p>"%(_(u'Import failed'),s)
                    return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
                del dDict['EndSpecDayTime']
                dDict['clearance']=0
                processtype = 1
                employeelist = ''
                process=''
                roleid=0
                if GetParamValue('opt_basic_Auto_audit','0') == '1':  #是否支持自动审核，如支持自动将状态改为已接受2:
                    proc = []
                    pid = 0
                    state = 2
                else:
                    state = 0
                    st=dDict['StartSpecDay']
                    et=dDict['EndSpecDay']
                    minunit=gettimediff(et,st)

                    proc, pid = getemployeeprocess(e.id,dDict['DateID'],minunit)

                    if not proc:  # 优先多级人员审批
                        processtype=0
                        proc,pid=getprocess_EX(e.id,dDict['DateID'],minunit)
                        if len(proc)==0:
                            roleid=0
                            process=''
                        else:
                            roleid=proc[0]
                            process=','.join(str(x) for x in proc)
                            process=','+process+','
                    else:
                        roleid=0
                        employeelist=proc[0]
                        process = ','.join(str(x) for x in proc)
                        process = ',' + process + ','
                dDict['State']=state
                dDict['roleid']=roleid
                dDict['process']=process
                dDict['processid']=pid
                dDict['procSN']=0
                dDict['processtype'] = processtype
                dDict['employeelist'] = employeelist
                dDict['ApplyDate']=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                del dDict['badgenumber']
                sql_insert.append(dDict)
                #uspe=USER_SPEDAY.objects.filter(UserID=e,StartSpecDay=dDict['StartSpecDay'],DateID=dDict['DateID'])
                # uspe=USER_SPEDAY.objects.filter(UserID=e,StartSpecDay=dDict['StartSpecDay'])
                # if uspe:
                #     sql_update.append(dDict)
                # else:
                #     sql_insert.append(dDict)
        # 请假导入没有更新只有新增（因为更新找不到那条请假对应那条请假）
        # for up_dDict in sql_update:
        #     up_dDict['whereUserID']=up_dDict['UserID']
        #     up_dDict['whereStartSpecDay']=up_dDict['StartSpecDay']
        #     del up_dDict['UserID']
        #     del up_dDict['StartSpecDay']
        #     sql,params=getSQL_update_new('USER_SPEDAY',up_dDict)
        #     if customSqlEx(sql,params):
        #         i_update+=1

        for in_dDict in sql_insert:
            sql,params=getSQL_insert_new('USER_SPEDAY',in_dDict)
            try:
                insert_cursor=customSqlEx(sql, params)
            except:  # 同一个文件两天重复记录时会触发唯一索引异常，直接当做更新成功
                i_update += 1
                continue
            if insert_cursor:
                i_insert+=1
                e=employee.objByID(in_dDict['UserID'])
                uspe=USER_SPEDAY.objects.get(UserID=e,StartSpecDay=in_dDict['StartSpecDay'],DateID=in_dDict['DateID'])
                USER_SPEDAY_DETAILS(USER_SPEDAY_ID=uspe,remarks='',Place='',mobile='',successor='').save()

        lang_layer.delete("%s-%s-%s"%(settings.UNIT,'home_user_speadays',request.user.id))

        result=reportError(u"%s"%_(u"Leave Information"), jjj, i_insert, i_update, i_rep,errorRec)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=USER_SPEDAY._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")

    except Exception as e:
        import traceback;traceback.print_exc()
        print ('importSpeday error---',e)
        cc=u"<h1>%s</h1><br/>%s"%(_(u"Bulk upload leave list"),_(u"Failure information import failed"))+"</p><pre>"
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
#		cc+=request.POST.get(t+"2file","")
#		return HttpResponse(content="result=1",mimetype='text/plain')#render("info.html", {"title": _("Import employee list"), "content": cc})

@login_required
def importtransfile(request):
    from mysite.iclock.datautils import hasPerm
    if not hasPerm(request.user, '', 'iclock.upload_utransaction'):
        cc = (u"<br><br><br><br><br><br><br><br><ul class='errorlist'><li>%s"%_('You do not have the permission!'))+u"</li></ul>"
        return getJSResponse({"ret":-2,"message":u"%s"%cc},mtype="text/plain")
    try:
        result = ""
        f=request.FILES.get("utranslog",None)
        sn = f.name.split(".")[0].split('_')[0]
        filetype = f.name.split(".")[0].split('_')[-1]
        if filetype!='attlog':
            cc = (u"<br><br><br><br><br><br><br><br><ul class='errorlist'><li>%s"%_(u'This is not a valid attendance record file'))+u"</li></ul>"
            return getJSResponse({"ret":1,"message":u"%s"%cc},mtype="text/plain")
        try:
            dev = getDevice(sn)
        except:
            sn = 'null'
        data=""
        for chunk in f.chunks():
            if sys.version[0]=='3':
               data+=chunk.decode('GB18030')
            else:
               data+=chunk
        #fsn,upload_transaction,sum=checkALogData(data)
        arr = data.split("\n")
        i, count = 0, len(data) / 32
        i, i_insert,i_rep,i_update = 0, 0,0,0
        errorRecs=[]
        maxtime=(datetime.datetime.now()+datetime.timedelta(1, 0, 0)).strftime("%Y-%m-%d %H:%M:%S")
        for row in arr:
            if row=="": continue
            arr_row = row.split("\t")
            i += 1
            pin=formatPIN(arr_row[0].strip())
            time=arr_row[1]
            if  (len(time) in [19,16]) and (maxtime>time) and (not pin in settings.DISABLED_PINS):
                try:
                    e=employee.objByPIN(pin)    #目的是如果导入的考勤记录中考勤号码在人员信息不存在不进行导入,要求必须先导入人员才能导入记录
                except:
                    e=None
                    errorRecs.append(row)
                if e:
                    sql = getSQL_insert("checkinout", userid=e.id, checktime=time, SN=sn,
                        checktype=arr_row[3], verifycode =arr_row[4])
                    try:
                        if customSqlEx(sql):
                            i_insert += 1
                    except Exception as e:
                        estr=u"%s"%e
                        if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or ("Duplicate entry" in estr) or ("unique constraint" in estr):
                            i_rep+=1
                        else:
                            errorRecs.append(row)
            else:
                errorRecs.append(row)
        result=reportError(_("Transactions"), i, i_insert,i_update, i_rep, errorRecs)
        cc = u"<br><h3>%s"%_('Data Import Results')+u"</h3><br/>"+(u"%s"%result)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, object='', count=i_insert,
                 model=_(u'transaction'), action=_(u"Upload u disk transaction file")).save(force_insert=True)
    except Exception as e:
        cc = (u"<br><br><br><br><br><br><br><br><ul class='errorlist'><li>%s"%_("Transactions does not match data, Please select the correct <b>transaction</b> data file!"))+u"</li></ul>"
    return getJSResponse({"ret":0,"message":u"%s"%(cc)},mtype="text/plain")

@login_required
def importmeetemp(request):
    from mysite.meeting.models import participants_tpl
    try:
        result = ""
        tpl = request.GET.get('tpl','')
        line = request.GET.get('line','')
        index = request.GET.get('index','')
        imFieldsInfo = {'PIN':int(index)}
        f=request.FILES.get("meetemp",None)
        fName=f.name
        pt = participants_tpl.objects.filter(id=int(tpl))
        pt = pt[0]
        data=b""
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
            # fName=fName.encode("gb2312")
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r=0
        i_insert = 0
        errordata = []
        for t in lines:
            r+=1
            if int(line)>r:
                continue
            jjj+=1
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>100:
                        continue
                except:
                    continue
                if v<=len(ls):
                    if ext!='XLS' and ext!='XLSX':
                        pin = getStr_c_decode(ls[int(v)-1])

                    else:
                        pin = ls[int(v)-1]
                    try:
                        emp = employee.objByPIN(pin)
                        dDict['UserID_id']= emp.id
                    except:
                        errordata.append(pin)
                        continue
            if dDict!={}:
                if not (request.user.is_superuser or request.user.is_alldept):
                    dept_list = userDeptList(request.user)
                    if emp.DeptID.DeptID not in dept_list:
                        s = _(u"Section%sNo this employee permission") % r
                        cc = u"%s,%s" % (s, _(u"imported failed"))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                dDict['participants_tplID_id'] = int(tpl)
                sql,params=getSQL_insert_new('meeting_participants_details',dDict)
                try:
                    if customSqlEx(sql,params):
                        i_insert+=1
                except Exception as e:
                    errordata.append(pin)
        result=reportError(_(u"Participant template data"), jjj, i_insert,0, 0, errordata)
        cc = u"<br><h3>%s"%_('Data Import Results')+u"</h3><br/>"+(u"%s"%result)
    except Exception as e:
        print (e)
        cc = (u"<br><br><br><br><br><br><br><br><ul class='errorlist'><li>%s"%_(u"Please select the correct file"))+u"</li></ul>"
    return getJSResponse({"ret":0,"message":u"%s"%(cc)},mtype="text/plain")

@login_required
def importcheckexact(request):
    from mysite.iclock.process import getprocess_EX,getemployeeprocess
    try:
        import time
        imFields = ["badgenumber","checkdate_a","checktype","checktime"]
        oriFields=request.POST["fields"].split(',')
        fromline=request.POST.get("rowid2","1")
        ispass=request.POST.get("pass","2")
        state = 0
        if ispass=='1':
            state = 2
        if fromline=="":
            fromline=1
        fromline=int(fromline)
        for t in oriFields:
                fldName=request.POST.get(t,"")
                if fldName=='on':
                    imFields.append(t)
        imFieldsInfo = {}
        maxCol=0
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
                if maxCol<imFieldsInfo[t]:maxCol=imFieldsInfo[t]
            except:
                import traceback;traceback.print_exc()
                cc=_(u"Replacement data import failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _(u"Importing the patch list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        zhPattern = re.compile(u'[\u4e00-\u9fa5]+')
        lines=[]
        for chunk in f.chunks(): data+=chunk
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
    #			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")#修改导入文件名称为中文报错问题
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                c=0
                #try:
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                    c=c+1
                    if c>maxCol-1:break

    #				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        jj=0
        iCount=len(lines)          #import users count
        i_insert=0
        i_update=0
        update_flat=0
        i_rep=0
        duplicated=0
        errorRec=''
        error=[]
        empids = {}
        #adminLog(time=datetime.datetime.now(),User=request.user, action=u"导入请假数据").save()
        rstate={}
        for tstate in GetRecordStatus():
            rstate[tstate['pName']] = tstate['symbol']
        sn_list = iclock.objects.filter((Q(ProductType__in=[4,5,15,25])&Q(Purpose=1))|Q(ProductType__in=[1,9])|Q(ProductType__isnull=True)).exclude(DelTag=1).values_list('SN',flat=True)
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            jj+=1
            if jj<fromline:continue
            if ispass=='1':
                dDict={'isAudit':0}
            else:
                dDict={'isAudit':1}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>50:
                        continue
                except:
                    continue
                v=int(v)
                s = ''
                if k in ["badgenumber","checkdate_a","checkdate_b","checktype","checktime"]:
                    try:
                        ls[v-1] = ls[v-1].strip()
                        if ls[v-1]=='' and k!='checkdate_b':
                            raise Exception
                    except:
                        if k=='badgenumber':
                            s=u"%s"%(_(u"The %s line number is empty")) %r
                        elif k=='checkdate_a':
                            s=u"%s"%(_(u"The %s line start date is empty")) %r
                        elif k=='checktype':
                            s=u"%s"%(_(u"The %s line record type is empty")) %r
                        elif k=='checktime':
                            s=u"%s"%(_(u"The %s line is not empty")) %r
                    if s:
                        errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                        error.append(r)
                        dDict = {}
                        break
                    if k=='badgenumber':
                        try:
                            dDict['badgenumber'] = ls[v-1]
                            if ls[v-1] not in empids:
                                temp = employee.objByPIN(ls[v-1])
                                empids[temp.PIN] = temp.id
                                dDict['UserID'] = temp.id
                            else:
                                dDict['UserID'] = empids[ls[v-1]]
                            continue
                        except:
                            s=u"%s"%(_(u"The person whose job number is %s does not exist")) %(ls[v-1])
                            errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                            error.append(r)
                            dDict = {}
                            break
                    if k in ["checkdate_a","checkdate_b"]:
                        try:
                            if (k=='checkdate_b' and ls[v-1]!='') or k!='checkdate_b':
                                dDict[k] = datetime.datetime.strptime(ls[v-1],'%Y-%m-%d')
                                continue
                        except:
                            s=u"%s"%(_(u"The %s line date format is incorrect")) %(r)
                            errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                            error.append(r)
                            dDict = {}
                            break
                    if k=='checktype':
                        if ext!='XLS' and ext!='XLSX':
                            ls[v-1] = getStr_c_decode(ls[v-1])
                        if ls[v-1] in rstate:
                            dDict['checktype'] = rstate[ls[v-1]]
                            continue
                        else:
                            s=u"%s"%(_(u"The %s line record type is incorrect")) %(r)
                            errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                            error.append(r)
                            dDict = {}
                            break
                    if k=='checktime':
                        try:
                            if ':' in ls[v-1]:
                                lDate=ls[v-1].split(':')
                                dDict['checktime']=datetime.time(int(lDate[0]),int(lDate[1]))
                                continue
                            else:
                                raise Exception
                        except:
                            s=u"%s"%(_(u"The %s line patch time format is incorrect")) %(r)
                            errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                            error.append(r)
                            dDict = {}
                            break
                if k=='check_SN':
                    if ls[v-1] and ls[v-1] not in sn_list:
                        s=u"%s"%(_(u"The %s line device is not an attendance device")) %(r)
                        errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                        error.append(r)
                        dDict = {}
                        break
                if v<=len(ls):
                    s=ls[v-1]
                    if ext=='XLS' or ext=='XLSX':
                        dDict[k]=s
                    else:
                        dDict[k]=getStr_c_decode(s)
            if dDict!={}:
                jjj+=1
                #print dDict
                if 'reson' in dDict:
                    dDict['reson']=dDict['reson'].replace("\'","")
                    dDict['reson']=dDict['reson'].replace("\"","")
                else:
                    dDict['reson']=''
                dDict['checktime']=datetime.datetime(dDict['checkdate_a'].year,dDict['checkdate_a'].month,dDict['checkdate_a'].day,dDict['checktime'].hour,dDict['checktime'].minute,0)
                first_name=u'%s %s'%(request.user.username,request.user.first_name)
                uid=dDict['UserID']
                processtype = 1
                employeelist = ''
                roleid=0
                process = ''
                if ispass=='1':
                    proc = []
                    pid = 0
                else:
                    proc, pid = getemployeeprocess(uid,10001,1)

                    if not proc:  # 优先多级人员审批
                        proc, pid = getprocess_EX(uid,10001,1)
                        processtype = 0
                        if len(proc) == 0:
                            roleid = 0
                            process = ''
                        else:
                            roleid = proc[0]
                            process = ','.join(str(x) for x in proc)
                            process = ',' + process + ','
                    else:
                        roleid = 0
                        employeelist = proc[0]
                        process = ','.join(str(x) for x in proc)
                        process = ',' + process + ','

                try:
                    checkdate_a = dDict['checkdate_a']
                    checkdate_b = ''
                    if 'checkdate_b' in dDict:
                        checkdate_b = dDict['checkdate_b']
                    checktimes = dDict['checktime']
                    checktypes = dDict['checktype']
                    resons = dDict['reson']
                    SN = ''
                    if 'check_SN' in dDict:
                        SN=dDict['check_SN']
                    if SN:
                        SN=SN
                        device=getDevice(SN)
                        pp=device.ProductType
                    else:
                        SN=None
                        pp=9
                    time_check=datetime.datetime.now()
                    time_check=datetime.datetime(time_check.year,time_check.month,time_check.day,time_check.hour,time_check.minute,time_check.second)
                    ok = 0
                    if checkdate_b :#批量
                        if checkdate_a >time_check or (checkdate_b!='null' and checkdate_b >time_check):
                            s = u"%s"%(_(u'Import failed! The %s line replenishment time is greater than the current time'))%r
                            errorRec+=u"%s<br/>"%(s)
                            raise Exception
                        if checkdate_b!='null' and checkdate_a > checkdate_b:
                            s = u"%s"%(_(u'Import failed! The %s line end date is greater than the start date.'))%r
                            errorRec+=u"%s<br/>"%(s)
                            raise Exception
                        while (checkdate_b!='null' and checkdate_a <= checkdate_b):
                            checktime_a = datetime.datetime(checkdate_a.year,checkdate_a.month,checkdate_a.day,checktimes.hour,checktimes.minute,checktimes.second)
                            checktimes = datetime.datetime(checkdate_a.year,checkdate_a.month,checkdate_a.day,checktimes.hour,checktimes.minute,checktimes.second)
                            #sql_a,params_a=getSQL_insert_new(checkexact._meta.db_table,UserID=uid, CHECKTIME=checktime_a, CHECKTYPE=checktypes, YUYIN=resons,MODIFYBY=first_name,APPLYDATE=datetime.datetime.now(),SaveStamp=datetime.datetime.now(),STATE=state,SN=SN,)
                            #customSqlEx(sql_a,params_a)
                            if dDict['isAudit']==0:
                                # if checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktimes).exclude(State=3).count()==0:
                                ischeckexact_on = checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktimes).exclude(State=3)
                                if not ischeckexact_on:
                                    if not settings.PIRELLI:
                                        sql_a,params_a=getSQL_insert_new(checkexact._meta.db_table,UserID=uid, CHECKTIME=checktimes, CHECKTYPE=checktypes, YUYIN=resons,MODIFYBY=first_name,ApplyDate=datetime.datetime.now(),State=state,SN=SN,roleid=roleid,process=process,processid=pid,procSN=0,processtype=processtype,employeelist=employeelist)
                                        try:
                                            customSql(sql_a,params_a)
                                            ok += 1
                                        except:pass
                                    sql_as,params_as=getSQL_insert_new(transactions._meta.db_table,userid=uid, checktime=checktime_a, checktype=checktypes, verifycode='5',purpose =pp,SN=SN)
                                    try:
                                        customSql(sql_as,params_as)
                                    except:pass
                                else:
                                    ischeckexact_on.update(ApplyDate=datetime.datetime.now(), State=state, YUYIN=resons,SN=SN)
                                    update_flat = 1
                            else:
                                # if checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktime_a).exclude(State=3).count()==0:
                                ischeckexact_tw = checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktimes).exclude(State=3)
                                if not ischeckexact_tw:
                                    sql_a,params_a=getSQL_insert_new(checkexact._meta.db_table,UserID=uid, CHECKTIME=checktime_a, CHECKTYPE=checktypes, YUYIN=resons,MODIFYBY=first_name,ApplyDate=datetime.datetime.now(),State=state,SN=SN,roleid=roleid,process=process,processid=pid,procSN=0,processtype=processtype,employeelist=employeelist)
                                    try:
                                        customSql(sql_a,params_a)
                                        ok += 1
                                    except:pass
                                else:
                                    ischeckexact_tw.update(ApplyDate=datetime.datetime.now(), State=state, YUYIN=resons,SN=SN)
                                    update_flat = 1
                            checkdate_a += datetime.timedelta(days=1)
                        deleteCalcLog(UserID=uid,StartDate=dDict['checkdate_a'],EndDate=dDict['checkdate_b'])
                    else:#单天
                        if checkdate_a >time_check or (checkdate_b!='null' and checkdate_b and checkdate_b >time_check):
                            s = u"%s"%(_(u'Import failed! The %s line replenishment time is greater than the current time'))%r
                            errorRec+=u"%s<br/>"%(s)
                            raise Exception
                        checktimes = datetime.datetime(checkdate_a.year,checkdate_a.month,checkdate_a.day,checktimes.hour,checktimes.minute,checktimes.second)
                        # if checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktimes).exclude(State=3).count()==0:
                        ischeckexact = checkexact.objects.filter(UserID__id=uid,CHECKTIME=checktimes).exclude(State=3)
                        if not ischeckexact:
                            if dDict['isAudit']==0:
                                if not settings.PIRELLI:
                                    sql_a,params_a=getSQL_insert_new(checkexact._meta.db_table,UserID=uid, CHECKTIME=checktimes, CHECKTYPE=checktypes, YUYIN=resons,MODIFYBY=first_name,ApplyDate=datetime.datetime.now(),State=state,SN=SN,roleid=roleid,process=process,processid=pid,procSN=0,processtype=processtype,employeelist=employeelist)
                                    customSqlEx(sql_a,params_a)
                                sql_b,params_b=getSQL_insert_new(transactions._meta.db_table,userid=uid, checktime=checktimes, checktype=checktypes, verifycode='5',purpose = pp,SN=SN)
                                customSqlEx(sql_b,params_b)
                            else:
                                sql_a,params_a=getSQL_insert_new(checkexact._meta.db_table,UserID=uid, CHECKTIME=checktimes, CHECKTYPE=checktypes, YUYIN=resons,MODIFYBY=first_name,ApplyDate=datetime.datetime.now(),State=state,SN=SN,roleid=roleid,process=process,processid=pid,procSN=0,processtype=processtype,employeelist=employeelist)
                                customSqlEx(sql_a,params_a)
                            ok += 1
                            deleteCalcLog(UserID=uid,StartDate=dDict['checkdate_a'],EndDate=dDict['checkdate_a'])
                        else:
                            ischeckexact.update(ApplyDate=datetime.datetime.now(),State=state,YUYIN=resons,SN=SN)
                            update_flat = 1
                            deleteCalcLog(UserID=uid, StartDate=dDict['checkdate_a'], EndDate=dDict['checkdate_a'])

                    if ok:
                        i_insert += 1
                    # else:
                    #     duplicated += 1
                    if update_flat:
                        update_flat = 0
                        i_update += 1

                except Exception as e:
                    connection.commit()
                    # import traceback;traceback.print_exc()
                    error.append(r)
                    dDict = {}
                    pass

        info=[]
        if i_insert: info.append(u"%s"%_(u"Successfully inserted %(object_num)d line") % {'object_num':i_insert})
        # if duplicated: info.append(u"%s"%_(u"%(object_num)d line repeats") % {'object_num':duplicated})
        if i_update: info.append(u"%s"%_(u"Successfully update %(object_num)d line") % {'object_num':i_update})
        result = u"<h2>%s:</h2> <p />%s%s%s<br />" % (u"%s"%_(u"Replacement information"),
        lineFmt%(u"%s"%_(u"There are %(object_num)d rows in the data file")%{'object_num':jj}),
        info and lineFmt%(u", ".join(info)) or "",lineFmt%(_(u"%(num)d behavior is invalid.") % {'num':len(error)}))
        if len(error)>10:
            fname = 'import_BorrowList_fail-%s.html'%datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            fName = tmpDir()+'/'+fname
            with open(fName,'w') as f:
                f.write('<!DOCTYPE html><html><head><meta http-equiv="content-type" content="text/html; charset=utf-8" /></head><body>')
                f.write('<h3 style="color:red">')
                f.write(u"%s"%(_(u'This page only lists invalid records, does not contain duplicate records')).encode('utf-8'))
                f.write('</h3>')
                f.write(errorRec.encode('utf-8'))
                f.write('</body></html>')
            result += '<a href="#" onclick="javascript:window.open(\'/iclock/file/tmp/' + fname + u')">%s</a>' % _(u'Click to view error message')
        else:
            result += errorRec
        adminLog(time=datetime.datetime.now(), User=request.user, action=_("Import"),
                 model=checkexact._meta.verbose_name, object=fName, count=jj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")

    except Exception as e:
        import traceback;traceback.print_exc()
        print ('importcheckexact error---',e)
        cc=u"<h1>%s</h1><br/>%s"%(_(u"Batch upload patch list"),_(u"Replacement information import failed"))+"</p><pre>"
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

def sync_issuecard(request,issuecard_dic):
    """
        把导入的卡信息同步到IssueCard
    """
    qs = IssueCard.objects.filter(cardstatus__in = [CARD_LOST, CARD_VALID])
    valid_cardnos = list(qs.values_list('cardno',flat=True))
    for i in issuecard_dic:
        try:
            if i['card'] not in valid_cardnos:
                if i['is_new']:
                    emp = employee.objByPIN(i['user'])
                    sync_card_to_issuecard(i['card'],emp,request,i['is_new'])
                else:
                    sync_card_to_issuecard(i['card'],i['user'],request,i['is_new'])
                valid_cardnos.append(i['card'])
        except Exception as e:
            #import traceback
            #traceback.print_exc()
            print (u'sync_issuecard-----------%s'%e)

def sync_fingercons_issuecard(request, pins):
    """ 
    对于导入时设置了'支持特征模板消费'的人员进行发卡并激活处理
    """
    from mysite.iclock.dataview import activate_card
    qs = IssueCard.objects.filter(cardstatus__in=[CARD_LOST, CARD_VALID, CARD_INACTIVE, CARD_OVERDUE])
    valid_pins = list(qs.values_list('UserID__PIN',flat=True))
    for pin in pins:
        if pin not in valid_pins:  #新增的
            valid_pins.append(pin)
            emp = employee.objByPIN(pin)
            sync_card_to_issuecard('', emp, request, is_new=True)  #边边只是产生卡记录，不会激活卡
    #修改未取激活的记录
    inactive_cards = IssueCard.objects.filter(cardstatus=CARD_INACTIVE)
    for inactive_card in inactive_cards:
        if inactive_card.UserID.PIN in pins:
            try:
                activate_card(inactive_card)
            except:
                if settings.DEBUG:
                    import traceback;traceback.print_exc()

def sync_area(emp,areaid):
    addlist, dlist = [], []
    oldzones = empZone.objects.filter(UserID=emp).values_list('zone', flat=True)
    ids_old = set(oldzones)
    ids_new = set(areaid)
    ids_mid = ids_old & ids_new
    ids_del = ids_old - ids_mid
    ids_add = ids_new - ids_mid
    for x in ids_add:
        addlist.append(x)
    for y in ids_del:
        dlist.append(y)
    try:
        if len(dlist) > 0:
            deleteEmpFromDeviceByZone(emp, dlist)
            empZone.objects.filter(UserID=emp, zone_id__in=dlist).delete()
        for zone_id in addlist:
            empZone(UserID=emp, zone_id=zone_id).save()
    except Exception as e:
        print(u'sync_area-----------%s' % e)

#菜品导入
@login_required
def importcookbook(request):
    # import sys
    # reload(sys)
    # sys.setdefaultencoding('utf-8')
    try:
        imFields=[]
        imFieldsInfo={}
        imDatalist=[]
        sUserids=[]
        i_insert=0
        i_update=0
        i_rep=0

        oriFields=request.POST["fields"].split(',')
        for t in oriFields:
            # fldName=request.POST.get(t,"")
            # if fldName=='on':
            imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
            except:
                cc=_("department&#39;s data imported failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _("Import department list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        for chunk in f.chunks():
            data+=chunk
        lines = []
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
#			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.decode()
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            try:
                f.write(data)
            except:
                pass
            f.close()
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(sr)
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
#				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            dDict={}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>10:
                        continue
                except:
                    continue
                if k.lower()=='deptid':
                    if ext!='XLS' and ext!='XLSX':
                        if ls[v-1]==''  or (not ls[v-1].isdigit()):
                            dDict={}
                            break
                    else:
                        if ls[v-1]=='' or isinstance(type(ls[v-1]),type(u'1')) or isinstance(type(ls[v-1]),type(1))or isinstance(type(ls[v-1]),type(1.0)):
                            dDict={}
                            break
                        else:
                            try:
                                ls[v-1]=int(ls[int(v)-1])
                            except:
                                dDict={}
                                break
                if v<=len(ls):
                    if ext!='XLS' and ext!='XLSX':
                        dDict[k]=getStr_c_decode(ls[int(v)-1])
                    else:
                        dDict[k]=ls[int(v)-1]
            if dDict!={}:
                jjj+=1
                if not dDict['code'] or not dDict['itemname']or not dDict['Price']or not dDict['foodclassify_code']or not dDict['WeekDay']or not dDict['meal']:
                    s = _(u"Line %s is missing required")%jjj
                    cc = u"%s,%s" % (s,_(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if dDict['code']:
                    zhmodel = re.compile(u'[\u4e00-\u9fa5]')  # 检查中文
                    contents = dDict['code']
                    match = zhmodel.search(contents)
                    if match:
                        s = _(u"Line %s is existing Chinese") % jjj
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    else:
                        if not dDict['code'].isalnum():
                            s = _(u"Line %s is existing illegal character") % jjj
                            cc = u"%s,%s" % (s, _(u'Import failed'))
                            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'itemname' in dDict:
                    dDict['itemname']  = re.sub(u'[^\u4e00-\u9fa5a-zA-Z0-9_.（）()@\x20]', '', dDict['itemname'])
                if re.search(r'[，]{1,100}',dDict['WeekDay']):
                    s = _(u"Dishes meal time with item number %s must be separated by an English separator") % dDict['code']
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                if re.search(r'[，]{1,100}',dDict['meal']):
                    s = _(u"Dishes meal with item number %s must be separated by an English separator") % dDict['code']
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if not re.search('^[a-zA-Z0-9]{1,20}$', dDict['code']):
                    s = _(u"Only letters or numbers are allowed for dish number %s, and the string length cannot exceed 20")%dDict['code']
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                try:
                    dt = CookBook.objects.get(code=dDict['code'])
                except:
                    dt = None
                if dt:
                    dDict['wherecode']=dDict['code']
                    sql,params=getSQL_update_new('ipos_cookbook',dDict)
                    if customSqlEx(sql,params):
                        i_update+=1
                else:
                    sql,params=getSQL_insert_new('ipos_cookbook',dDict)
                    print(sql,params)
                    if customSqlEx(sql,params):
                        i_insert+=1
        result=reportError(u"%s"%_("App food information"), jjj, i_insert, i_update, i_rep, [])
        adminLog(time=datetime.datetime.now(), User=request.user, action=_("Import"),
                 model=CookBook._meta.verbose_name, object=fName,count=jjj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")
    except Exception as e:
        t = u"%s" % (_(u"The %s line data is incorrect") % jjj)
        cc=u"%s,%s,%s"%(t,e,_("department&#39;s data imported failed"))
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")


@login_required
def import_empZone(request):
    try:
        field_dict = {}
        subfix = ''
        data = b''
        lines = []
        i_insert = 0

        allFields = request.POST.get("fields").split(",")
        for f in allFields:
            try:
                value = request.POST.get(f + "2file", 0)
                field_dict[f] = int(value)
            except:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(f + "2file", 0)
                return render("info.html", {"title": _("Import empZone list"), "content": cc})

        if not all([field_dict["EmpNumber"], field_dict["AreaNumber"]]):
            cc = u"{}".format(_("EmpNumber and AreaNumber must be offered!"))
            return getJSResponse({"ret": 1, "message": cc})

        f = request.FILES.get("fileUpload")
        whatRow = request.POST.get('whatrowid2file', 1)
        fName = f.name.split(".")

        if fName:
            subfix = fName[-1].upper()
        for chunk in f.chunks():
            data += chunk
        if subfix == 'TXT' or subfix == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif subfix == 'XLS' or subfix == 'XLSX':
            lines = empZone_xls_process(f, data, lines)     # 对xls数据进行处理，返回行组成的列表

        dDict = {}
        flag = 0
        r = 0
        data_list = []
        all_data = []

        for l in lines:
            #excel数据原因，可能会取到一整行空数据 如 l: ['', '', '', ''], 这边过滤掉这种数据
            is_error_l = all(x == '' for x in l)
            if is_error_l:
                continue
            r += 1
            if int(whatRow) > r:
                continue
            if subfix == 'TXT':
                data_list = (l.decode()).split('\t')
            elif subfix == 'CSV':
                data_list = (l.decode()).split(',')
            elif subfix == 'XLS' or subfix == 'XLSX':
                data_list = l

            for key, value in field_dict.items():
                try:
                    if value < 1 or value > 5:
                        continue
                except:
                    continue
                if value <= len(data_list):
                    dDict[key] = data_list[int(value)-1]

            if dDict:
                flag += 1
                cc = empZone_varificate(dDict, flag)    # 对传进来的参数进行校验
                if cc:
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            try:
                emp = employee.objects.get(PIN=dDict["EmpNumber"])
            except:
                cc = u"%s" % (_('Personnel %s does not exist') % dDict['EmpNumber'])
                return getJSResponse({"ret": 1, "message":cc}, mtype="text/plain")

            if ',' in dDict['AreaNumber']:      # 英文状态下逗号隔开的区域编号
                area_list = dDict['AreaNumber'].split(',')
            elif '，' in dDict["AreaNumber"]:    # 中文状态下逗号隔开的区域编号
                area_list = dDict['AreaNumber'].split('，')
            else:
                area_list = [dDict['AreaNumber']]

            all_data.append((dDict["EmpNumber"], area_list))
            if area_list:
                for area in area_list:
                    try:
                        zone_obj = zone.objects.get(code=area.strip())
                    except:
                        cc = u"%s" % (_('AreaNumber %s does not exist') % area)
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                    empZone_obj = empZone.objects.filter(UserID=emp, zone=zone_obj)
                    if empZone_obj:
                        continue
                    else:
                        # empZone(UserID=emp, zone=zone_obj).save()
                        params_dict={"userid": emp.id, "zone_id": zone_obj.id}
                        sql, params = getSQL_insert_new('empzone', params_dict)
                        if customSqlEx(sql, params):
                            i_insert += 1
                        cache.delete("%s_iclock_emp_zoneids_%s" % (settings.UNIT, emp.id))
        result = reportError(u"%s" % _("empZone information"), flag, i_insert, 0, 0, [])

        for data in all_data: # 导入人员区域信息后自动下发到设备
            emp_obj = employee.objects.filter(PIN=data[0])
            for zone_code in data[1]:
                objs = IclockZone.objects.filter(zone__code=zone_code).filter(Q(SN__ProductType=None) | Q(SN__ProductType__in=[9, 8])).exclude(SN__DelTag=1)
                for obj in objs:
                    zk_set_data(getDevice(obj.SN.SN), 'userinfo', emp_obj, cmdTime=None, is_finger=1, is_face=1, is_pic=1,is_pv=1, is_fv=1)

        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=empZone._meta.verbose_name, object=fName, count=flag).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % result}, mtype="text/plain")

    except Exception as e:
        import traceback
        traceback.print_exc()

        cc = u"%s,%s" % (e, _("empZone&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


def empZone_xls_process(f, data, lines):
    import xlrd
    fn = "%s/%s" % (tempDir(), f.name)

    with codecs.open(fn, 'wb') as file:
        file.write(data)

    wb = xlrd.open_workbook(fn)
    sheetNames = wb.sheet_names()
    if not sheetNames:
        cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

    try:
        sh = wb.sheet_by_index(0)
    except:
        s = "no sheet in %s named %s" % (fn, sheetNames[0])
        cc = u"%s,%s" % (_('imported failed'), s)
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    nRows = sh.nrows
    for i in range(0, nRows):
        row_data = []
        for row_val in sh.row_values(i):
            if isinstance(row_val, float):
                row_val = str(int(row_val))
                if row_val.find('.0') != '-1':
                    row_val = row_val.split('.')[0]
                else:
                    row_val = row_val
            row_data.append(row_val)
        lines.append(row_data)
    return lines


def empZone_varificate(dDict, flag):
    cc = ''
    if 'EmpNumber' not in dDict.keys() or 'AreaNumber' not in dDict.keys():
        s = u"%s" % (_(u"Section%s Personnel number and area number must be filled")) % flag
        cc = u"%s,%s" % (s, _(u'Import failed'))

    if not re.search('^[a-zA-Z0-9]{1,24}$', dDict['EmpNumber']):
        s = u"%s" % (_(u"The EmpNumber is %s, only letters or numbers are allowed,"
                       u" and the string length cannot exceed 24")) % dDict['EmpNumber']
        cc = u"%s,%s" % (s, _(u'Import failed'))

    return cc







@login_required
def import_BorrowList(request):
    from mysite.iclock.process import getprocess_EX
    try:
        import time
        imFields = ["code","deptname","userinfo","is_bring_out","remark"]
        oriFields=request.POST["fields"].split(',')
        fromline=request.POST.get("rowid2","1")
        ispass=request.POST.get("pass","2")
        state = 0
        if ispass=='1':
            state = 2
        if fromline=="":
            fromline=1
        fromline=int(fromline)
        for t in oriFields:
                fldName=request.POST.get(t,"")
                if fldName=='on':
                    imFields.append(t)
        imFieldsInfo = {}
        maxCol=0
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
                if maxCol<imFieldsInfo[t]:maxCol=imFieldsInfo[t]
            except:
                import traceback;traceback.print_exc()
                cc=_(u"Replacement data import failed")+"</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _(u"Importing the patch list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        #ext=fName[fName.find('.')+1:].upper()
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        zhPattern = re.compile(u'[\u4e00-\u9fa5]+')
        lines=[]
        for chunk in f.chunks(): data+=chunk
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
    #			fn="%s/%s"%(tmpDir(),fName)
            # fName=fName.encode("gb2312")#修改导入文件名称为中文报错问题
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                c=0
                #try:
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                    c=c+1
                    if c>maxCol-1:break

    #				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj=0
        jj=0
        iCount=len(lines)          #import users count
        i_insert=0
        i_update=0
        i_rep=0
        duplicated=0
        errorRec=''
        error=[]
        empids = {}
        #adminLog(time=datetime.datetime.now(),User=request.user, action=u"导入请假数据").save()
        rstate={}
        for tstate in GetRecordStatus():
            rstate[tstate['pName']] = tstate['symbol']
        sn_list = iclock.objects.filter((Q(ProductType__in=[4,5,15,25])&Q(Purpose=1))|Q(ProductType__in=[1,9])|Q(ProductType__isnull=True)).exclude(DelTag=1).values_list('SN',flat=True)
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            jj+=1
            if jj<fromline:continue
            if ispass=='1':
                dDict={'isAudit':0}
            else:
                dDict={'isAudit':1}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>50:
                        continue
                except:
                    continue
                v=int(v)
                s = ''
                if k in ["code","deptname","userinfo","apply_time","remark"]:
                    try:
                        ls[v-1] = ls[v-1].strip()
                        if ls[v-1]=='' and k!='checkdate_b':
                            raise Exception
                    except:
                        if k=='badgenumber':
                            s=u"%s"%(_(u"The %s line number is empty")) %r
                        elif k=='checkdate_a':
                            s=u"%s"%(_(u"The %s line start date is empty")) %r
                        elif k=='checktype':
                            s=u"%s"%(_(u"The %s line record type is empty")) %r
                        elif k=='checktime':
                            s=u"%s"%(_(u"The %s line is not empty")) %r
                    if s:
                        errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                        error.append(r)
                        dDict = {}
                        break
                if v<=len(ls):
                    s=ls[v-1]
                    if ext=='XLS' or ext=='XLSX':
                        dDict[k]=s
                    else:
                        dDict[k]=getStr_c_decode(s)
            if dDict!={}:
                jjj+=1
                #print dDict
                if 'remark' in dDict:
                    dDict['remark']=dDict['remark'].replace("\'","")
                    dDict['remark']=dDict['remark'].replace("\"","")
                else:
                    dDict['remark']=''
                # dDict['apply_time']=datetime.datetime(dDict['apply_time'].year,dDict['apply_time'].month,dDict['apply_time'].day,dDict['apply_time'].hour,dDict['apply_time'].minute,0)
                first_name=u'%s %s'%(request.user.username,request.user.first_name)
                uid=dDict['userinfo']
                if ispass=='1':
                    proc = []
                    pid = 0
                else:
                    proc,pid=getprocess_EX(uid,10001,1)
                if len(proc)==0:
                    roleid=0
                    process=''
                else:
                    roleid=proc[0]
                    process=','.join(str(x) for x in proc)
                    process=','+process+','
                try:
                    deptname = dDict['deptname']
                    asset_id = dDict['code']
                    is_bring_out = dDict['is_bring_out']
                    print(f'is_bring_out:{is_bring_out}')
                    ok = 0

                    apply_time = datetime.datetime.now()
                    sql_a,params_a=getSQL_insert_new(BorrowSheet._meta.db_table,userid=uid,apply_time=apply_time,status=0,is_bring_out=is_bring_out)
                    try:
                        customSql(sql_a,params_a)
                        ok += 1
                    except:pass
                    results = BorrowSheet.objects.all().order_by('-id')
                    borrowsheet_id = results[0].id
                    sql_as,params_as=getSQL_insert_new(BorrowList._meta.db_table,asset_id=asset_id,borrowsheet_id=borrowsheet_id)
                    try:
                        customSql(sql_as,params_as)
                    except:pass
                    if ok:
                        i_insert += 1
                    else:
                        duplicated += 1
                except Exception as e:
                    connection.commit()
                    # import traceback;traceback.print_exc()
                    error.append(r)
                    dDict = {}
                    pass

        info=[]
        if i_insert: info.append(u"%s"%_(u"Successfully inserted %(object_num)d line") % {'object_num':i_insert})
        if duplicated: info.append(u"%s"%_(u"%(object_num)d line repeats") % {'object_num':duplicated})
        result = u"<h2>%s:</h2> <p />%s%s%s<br />" % (u"%s" % _(u"Borrowing approval information"),
        lineFmt%(u"%s"%_(u"There are %(object_num)d rows in the data file")%{'object_num':jj}),
        info and lineFmt%(u", ".join(info)) or "",lineFmt%(_(u"%(num)d behavior is invalid.") % {'num':len(error)}))
        if len(error)>10:
            fname = 'import_checkexact_fail-%s.html'%datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            fName = tmpDir()+'/'+fname
            with open(fName,'w') as f:
                f.write('<!DOCTYPE html><html><head><meta http-equiv="content-type" content="text/html; charset=utf-8" /></head><body>')
                f.write('<h3 style="color:red">')
                f.write(u"%s"%(_(u'This page only lists invalid records, does not contain duplicate records')).encode('utf-8'))
                f.write('</h3>')
                f.write(errorRec.encode('utf-8'))
                f.write('</body></html>')
            result += '<a href="#" onclick="javascript:window.open(\'/iclock/file/tmp/' + fname + u')">%s</a>' % _(u'Click to view error message')
        else:
            result += errorRec
        adminLog(time=datetime.datetime.now(), User=request.user, action=_("Import"),
                 model=BorrowList._meta.verbose_name, object=fName, count=jj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")

    except Exception as e:
        import traceback;traceback.print_exc()
        print ('importcheckexact error---',e)
        cc=u"<h1>%s</h1><br/>%s"%(_(u"Batch upload patch list"),_(u"Replacement information import failed"))+"</p><pre>"
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")


@login_required
def import_BringOut(request):
    from mysite.iclock.process import getprocess_EX
    try:
        import time
        imFields = ["code","userid","departure_time","bring_back_time","reason"]
        oriFields=request.POST["fields"].split(',')
        fromline=request.POST.get("rowid2","1")
        ispass=request.POST.get("pass","2")
        state = 0
        if ispass=='1':
            state = 2
        if fromline=="":
            fromline=1
        fromline=int(fromline)
        for t in oriFields:
                fldName=request.POST.get(t,"")
                if fldName=='on':
                    imFields.append(t)
        imFieldsInfo = {}
        maxCol=0
        for t in imFields:
            try:
                imFieldsInfo[t]=int(request.POST.get(t+"2file","-1"))
                if maxCol<imFieldsInfo[t]:maxCol=imFieldsInfo[t]
            except:
                import traceback;traceback.print_exc()
                cc = _(u"Import of data for bring out application failed") + "</p><pre>"
                cc+=request.POST.get(t+"2file","-1")
                return render("info.html", {"title": _(u"Importing the patch list"), "content": cc})
        f=request.FILES["fileUpload"]
        data=b""
        fName=f.name
        whatrow=request.POST["whatrowid2file"]
        ext=''
        fl=fName.split('.')
        if fl:
            ext=fl[-1].upper()
        if ext=='XLSX':ext='XLS'
        zhPattern = re.compile(u'[\u4e00-\u9fa5]+')
        lines=[]
        for chunk in f.chunks(): data+=chunk
        if ext=='TXT' or ext=='CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext=='XLS' or ext=='XLSX':
            import xlrd
            fn="%s/%s"%(tmpDir(),fName)
            with codecs.open(fn,'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames=bk.sheet_names()
            if not sheetNames:
                cc=u"%s,%s"%(_('imported failed'),_("No Sheet"))
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s="no sheet in %s named %s" %(fn,sheetNames[0])
                cc=u"%s,%s"%(_('imported failed'),s)
                return getJSResponse({"ret":1,"message": cc},mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0,nrows):
                sv=[]
                c=0
                #try:
                for sr in sh.row_values(i):
                    if type(sr)==type(1.0):
                        sr=str(int(sr))
                        if sr.find(".0")!='-1':
                            sr=sr.split(".")[0]
                        else:
                            sr=sr
                    sv.append(sr)
                    c=c+1
                    if c>maxCol-1:break

                row_data = sv
                lines.append(row_data)
        jjj=0
        jj=0
        iCount=len(lines)          #import users count
        i_insert=0
        i_update=0
        i_rep=0
        duplicated=0
        errorRec=''
        error=[]
        empids = {}
        rstate={}
        for tstate in GetRecordStatus():
            rstate[tstate['pName']] = tstate['symbol']
        sn_list = iclock.objects.filter((Q(ProductType__in=[4,5,15,25])&Q(Purpose=1))|Q(ProductType__in=[1,9])|Q(ProductType__isnull=True)).exclude(DelTag=1).values_list('SN',flat=True)
        r=0
        for t in lines:
            r+=1
            if int(whatrow)>r:
                continue
            jj+=1
            if jj<fromline:continue
            if ispass=='1':
                dDict={'isAudit':0}
            else:
                dDict={'isAudit':1}
            if ext=="TXT":
                ls=t.split(b'\t')
            elif ext=='CSV':
                ls=t.split(b',')
            elif ext=='XLS' or ext=='XLSX':
                ls=t
            for k,v in imFieldsInfo.items():
                try:
                    if v<1 or v>50:
                        continue
                except:
                    continue
                v=int(v)
                s = ''
                if k in ["code","userid","departure_time","bring_back_time","reason"]:
                    try:
                        ls[v-1] = ls[v-1].strip()
                        if ls[v-1]=='' and k!='checkdate_b':
                            raise Exception
                    except:
                        if k=='code':
                            s=u"%s"%(_(u"The %s line number is empty")) %r
                        elif k=='userid':
                            s=u"%s"%(_(u"The %s line start date is empty")) %r
                        elif k=='departure_time':
                            s=u"%s"%(_(u"The %s line record type is empty")) %r
                        elif k=='bring_back_time':
                            s=u"%s"%(_(u"The %s line is not empty")) %r
                    if s:
                        errorRec+=u"%s,%s<br/>"%(_(u'Import failed'),s)
                        error.append(r)
                        dDict = {}
                        break
                if v<=len(ls):
                    s=ls[v-1]
                    if ext=='XLS' or ext=='XLSX':
                        dDict[k]=s
                    else:
                        dDict[k]=getStr_c_decode(s)
            if dDict!={}:
                jjj+=1
                #print dDict
                if 'reason' in dDict:
                    dDict['reason']=dDict['reason'].replace("\'","")
                    dDict['reason']=dDict['reason'].replace("\"","")
                else:
                    dDict['reason']=''
                # dDict['apply_time']=datetime.datetime(dDict['apply_time'].year,dDict['apply_time'].month,dDict['apply_time'].day,dDict['apply_time'].hour,dDict['apply_time'].minute,0)
                first_name=u'%s %s'%(request.user.username,request.user.first_name)
                uid=dDict['userid']
                if ispass=='1':
                    proc = []
                    pid = 0
                else:
                    proc,pid=getprocess_EX(uid,10001,1)
                if len(proc)==0:
                    roleid=0
                    process=''
                else:
                    roleid=proc[0]
                    process=','.join(str(x) for x in proc)
                    process=','+process+','
                try:
                    asset_id = dDict['code']
                    departure_time = dDict['departure_time']
                    bring_back_time = dDict['bring_back_time']
                    departure_time = datetime.datetime.strptime(departure_time,'%Y-%m-%d')
                    bring_back_time = datetime.datetime.strptime(bring_back_time,'%Y-%m-%d')

                    if departure_time > bring_back_time:
                        s = u"%s" % (_(u'Import failed!The application departure time in line %s is greater than the application return time'))%r
                        errorRec+=u"%s<br/>"%(s)
                        raise Exception
                    ok = 0

                    apply_time = datetime.datetime.now()
                    try:
                        sql_a,params_a=getSQL_insert_new(BringOut._meta.db_table,userid_id=uid,asset_id_id=asset_id,departure_time=departure_time,bring_back_time=bring_back_time,status=0,apply_time=apply_time,reason=reason)
                    except Exception as e:
                        print(e)
                    try:
                        customSql(sql_a,params_a)
                        ok += 1
                    except:pass
                    if ok:
                        i_insert += 1
                    else:
                        duplicated += 1
                except Exception as e:
                    connection.commit()
                    # import traceback;traceback.print_exc()
                    error.append(r)
                    dDict = {}
                    pass

        info=[]
        if i_insert: info.append(u"%s"%_(u"Successfully inserted %(object_num)d line") % {'object_num':i_insert})
        if duplicated: info.append(u"%s"%_(u"%(object_num)d line repeats") % {'object_num':duplicated})
        result = u"<h2>%s:</h2> <p />%s%s%s<br />" % (u"%s" % _(u"Borrowing approval information"),
        lineFmt%(u"%s"%_(u"There are %(object_num)d rows in the data file")%{'object_num':jj}),
        info and lineFmt%(u", ".join(info)) or "",lineFmt%(_(u"%(num)d behavior is invalid.") % {'num':len(error)}))
        if len(error)>10:
            fname = 'import_checkexact_fail-%s.html'%datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            fName = tmpDir()+'/'+fname
            with open(fName,'w') as f:
                f.write('<!DOCTYPE html><html><head><meta http-equiv="content-type" content="text/html; charset=utf-8" /></head><body>')
                f.write('<h3 style="color:red">')
                f.write(u"%s"%(_(u'This page only lists invalid records, does not contain duplicate records')).encode('utf-8'))
                f.write('</h3>')
                f.write(errorRec.encode('utf-8'))
                f.write('</body></html>')
            result += '<a href="#" onclick="javascript:window.open(\'/iclock/file/tmp/' + fname + u')">%s</a>' % _(u'Click to view error message')
        else:
            result += errorRec
        adminLog(time=datetime.datetime.now(), User=request.user, action=_("Import"),
                 model=BorrowList._meta.verbose_name, object=fName, count=jj).save(force_insert=True)
        return getJSResponse({"ret":0,"message": u"%s"%(result)},mtype="text/plain")

    except Exception as e:
        import traceback;traceback.print_exc()
        print ('importcheckexact error---',e)
        cc=u"<h1>%s</h1><br/>%s"%(_(u"Batch upload patch list"),_(u"Replacement information import failed"))+"</p><pre>"
        return getJSResponse({"ret":1,"message": cc},mtype="text/plain")


@login_required
def import_AssetClass(request):
    # if settings.DEMO == 1:
    #     if request.user.DelTag != -2:
    #         cc = u"%s" % (u'This is Demo,Not allow to operate')
    #         return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    try:
        imFields = ["code", "name", "pid", "ascription", "symbol"]
        imFieldsInfo = {}
        imDatalist = []
        sUserids = []
        errorRecs = []
        i_insert = 0
        i_update = 0
        i_rep = 0

        oriFields = request.POST["fields"].split(',')
        for t in oriFields:
            fldName = request.POST.get(t, "")
            if fldName == 'on':
                imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
            except:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import asset category list"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        # ext=fName[fName.find('.')+1:].upper()
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                #				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        insert_sql_dict = []
        update_sql_dict = []
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            dDict = {}
            if ext == "TXT":
                ls = t.split(b'\t')
            elif ext == 'CSV':
                ls = t.split(b',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 10:
                        continue
                except:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        dDict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        dDict[k] = ls[int(v) - 1]
            if dDict != {}:
                jjj += 1
                if ('code' not in dDict or not dDict['code']) or ('name' not in dDict or not dDict['name'])\
                        or ('pid' not in dDict or not dDict['pid'])or ('ascription' not in dDict or not dDict['ascription']) :
                    s = u"%s" % (_(u"Line %s category code, category name, parent category and category ascription must be filled")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'symbol' not in dDict or not dDict['symbol']:
                    s = u"%s" % (_(u"Line %s category symbol must be filled")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                # 自动删除特殊符号。
                dDict['name'] = re.sub(u'[^\u4e00-\u9fa5a-zA-Z0-9_.（）()@\x20]', '', dDict['name'])
                if not re.search('^[a-zA-Z0-9-]{1,20}$', dDict['code']):
                    s = u"%s" % (_(
                        u"Line %s only letters or numbers or '-' are allowed, and the string length cannot exceed 20")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                try:
                    dt = AssetClass.objects.get(code=dDict['code'])
                except:
                    dt = None
                supdt = AssetClass.objects.filter(code=dDict['pid'], deltag=0)
                if dDict['ascription'] not in ('0', '1'):
                    s = u"%s" % (_(u"Line %s category ascription only 0(fixed assets) or 1(low-value consumption goods) are allowed")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                elif dt and dDict['ascription'] != dt.ascription:
                    # 修改分类归属，若分类下含有资产，不允许修改
                    asset_count = Asset.objects.filter(class_id=dt.id).count()
                    if asset_count > 0:
                        s = u"%s" % (_(u"Asset category in line %s contains assets, category ascription cannot be modified")) % r
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


                if dt and dt.id == 1:  # 是否为根资产类别
                    del dDict['pid']
                    del dDict['ascription']
                elif len(supdt) > 0:
                    dDict['pid'] = supdt[0].id
                else:
                    s = u"%s" % (_(u"Line %s parent category must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                if 'symbol' in dDict and dDict['symbol']:
                    if not dt and AssetClass.objects.filter(symbol=dDict['symbol']).count() > 0:  # 新增别名不能重复
                        s = u"%s" % (_(u"Duplicate category name for asset category on line %s")) % r
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    elif dt and dDict['symbol']!=dt.symbol and AssetClass.objects.filter(symbol=dDict['symbol']).count() > 0:  # 编辑别名不能重复
                        s = u"%s" % (_(u"Duplicate category name for asset category on line %s")) % r
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if dt and dt.id != 1:
                    childlist = []
                    AssetClass.objects.filter(id=dt.id).first().AllChildrenId(childlist)
                    childlist.append(dt.id)
                    if supdt[0].id in childlist:
                        s = u"%s" % (_(u"Line %s cannot set the parent category of asset category as its own or its sub asset category")) % r
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if dt  and dDict['symbol'] != dt.symbol:
                    # 修改符号（别名），若分类下含有资产，不允许修改
                    asset_count = Asset.objects.filter(class_id=dt).count()
                    if asset_count > 0:
                        s =u"%s" % (_(u'Asset category in line %s contains assets, category symbol cannot be modified'))% r
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    else:
                        dDict['attach_code']  = 0  # 若分类下没有资产，修改别名的时候，把最大附加编号重置为0
                else:
                    dDict['attach_code'] = 0
                dDict['deltag'] = 0
                # del dDict['pid']
                if dt:
                    dDict['whereid']=dt.id
                    update_sql_dict.append(dDict)
                else:
                    insert_sql_dict.append(dDict)
        for uuu in update_sql_dict:
            if 'symbol' in uuu:
                # asset_code根据类别符号做对应的修改。
                attach_code_list = []
                prefix_str = uuu['symbol'] + '-'
                asset_code_tuple = Asset.objects.filter(code__startswith=prefix_str).values_list('code', flat=True)
                for asset_code in asset_code_tuple:
                    asset_code = asset_code.lstrip(prefix_str)
                    if asset_code.isdigit():
                        attach_code_list.append(int(asset_code))
                if attach_code_list:
                    uuu['attach_code'] = max(attach_code_list)
                else:
                    uuu['attach_code'] = 0
            sql, params = getSQL_update_new('asset_class', uuu)
            if customSqlEx(sql, params):
                i_update += 1
        for iii in insert_sql_dict:
            if 'symbol' in iii:
                # asset_code根据类别符号做对应的修改。
                attach_code_list = []
                prefix_str = iii['symbol'] + '-'
                asset_code_tuple = Asset.objects.filter(code__startswith=prefix_str).values_list('code', flat=True)
                for asset_code in asset_code_tuple:
                    asset_code = asset_code.lstrip(prefix_str)
                    if asset_code.isdigit():
                        attach_code_list.append(int(asset_code))
                if attach_code_list:
                    iii['attach_code'] = max(attach_code_list)
                else:
                    iii['attach_code'] = 0
            sql, params = getSQL_insert_new('asset_class', iii)
            try:
                if customSqlEx(sql, params):
                    i_insert += 1
            except Exception as e:
                estr = "%s" % e
                if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or (
                        "Duplicate entry" in estr) or ("unique constraint" in estr):
                    i_rep += 1
                else:
                    errorRecs.append(row)
        result = reportError(u"%s" % _("Asset category information"), jjj, i_insert, i_update, i_rep, errorRecs)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=AssetClass._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        import traceback
        traceback.print_exc()
        # t = u"%s"%(_(u"The %s line data is incorrect")) %jjj
        cc = u"%s,%s" % (e, _("department&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

@login_required
def import_Asset(request):
    # if settings.DEMO == 1:
    #     if request.user.DelTag != -2:
    #         cc = u"%s" % (u'This is Demo,Not allow to operate')
    #         return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    try:
        imFields = ["code","name","class_id", "dept_id","purchase_time","specifications"]#必填字段
        imFieldsInfo = {}
        imDatalist = []
        sUserids = []
        i_insert = 0
        i_update = 0
        i_rep = 0
        errorRecs = []

        oriFields = request.POST["fields"].split(',')
        for t in oriFields:
            fldName = request.POST.get(t, "")
            if fldName == 'on':
                imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
            except:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import asset list"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        # ext=fName[fName.find('.')+1:].upper()
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                #				row_data = sh.row_values(i)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        insert_sql_dict = []
        update_sql_dict = []
        attach_code = {}
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            dDict = {}
            if ext == "TXT":
                ls = t.split(b'\t')
            elif ext == 'CSV':
                ls = t.split(b',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 22:
                        continue
                except:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        dDict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        dDict[k] = ls[int(v) - 1]
            if dDict != {}:
                jjj += 1
                if ('name' not in dDict or not dDict['name']) or ('class_id' not in dDict or not dDict['class_id'])or\
                        ('dept_id' not in dDict or not dDict['dept_id']) or ('purchase_time' not in dDict or not dDict['purchase_time']) \
                        or ('specifications' not in dDict or not dDict['specifications']):
                    s = u"%s" % (_(u"Line %s required item cannot be empty")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                auto_generate_flag = False
                if 'code' not in dDict or not dDict['code']:
                    auto_generate_flag = True
                else:
                    if not re.search('^[a-zA-Z0-9-]{1,20}$', dDict['code']):
                        s = u"%s" % (_(
                            u"Line %s only letters or numbers or '-' are allowed, and the string length cannot exceed 20")) % r
                        cc = u"%s,%s" % (s, _(u'Import failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                # 自动删除特殊符号。
                dDict['name'] = re.sub(u'[^\u4e00-\u9fa5a-zA-Z0-9_.（）()@\x20-]', '', dDict['name'])
                if auto_generate_flag:
                    dt = None
                else:
                    try:
                        dt = Asset.objects.get(code=dDict['code'])
                    except:
                        dt = None
                class_id = AssetClass.objects.filter(code=dDict['class_id'], deltag=0)
                if len(class_id) > 0:
                    dDict['class_id'] = class_id[0].id
                else:
                    s = u"%s" % (_(u"Line %s asset category must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if class_id[0].id == 1:
                    s = u"%s" % (_(u"Asset category of line %s is not allowed to be root asset category")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                dept_id = department.objects.filter(DeptNumber=dDict['dept_id'])
                if len(dept_id) > 0:
                    dDict['dept_id'] = dept_id[0].DeptID
                else:
                    s = u"%s" % (_(u"Line %s attribution department must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'area_id' in dDict:
                    area_id = zone.objects.filter(code=dDict['area_id']).exclude(DelTag=1)
                    if len(area_id) > 0:
                        dDict['area_id'] = area_id[0].id
                    else:
                        s = u"%s" % (_(u"Line %s storage area must exist")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'depreciation_period' in dDict:  # .has_key('purchase_time'):
                    try:
                        dDict['depreciation_period'] = int(dDict['depreciation_period'])
                        if dDict['depreciation_period'] < 1:
                            s = u"%s" % (_(u"Depreciation period of line% s must be greater than 0")) % r
                            cc = u"%s,%s" % (s, _(u'imported failed'))
                            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    except:
                        s = u"%s" % (_(u"Line %s depreciation period format error, must be an integer")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'purchase_time' in dDict:#.has_key('purchase_time'):
                    try:
                        purchase_time = datetime.datetime.strptime(dDict['purchase_time'], "%Y-%m-%d")
                    except:
                        import traceback
                        traceback.print_exc()
                        s = u"%s" % (_(u"Incorrect purchase date format on line %s")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'maintenance_deadline' in dDict:#.has_key('maintenance_deadline'):
                    try:
                        maintenance_deadline = datetime.datetime.strptime(dDict['maintenance_deadline'], "%Y-%m-%d")
                    except:
                        s = u"%s" % (_(u"Incorrect maintenance deadline format on line %s")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'UserID' in dDict and not dDict['UserID']:
                    # 当前使用人为空的时候代表不导入当前使用人
                    del dDict['UserID']
                dDict['deltag'] = 0
                if dt:
                    if dt.deltag==1:
                        s = u"%s" % (_(u"Duplicate asset code on line %s")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    if dt.class_id.ascription != class_id[0].ascription:
                        s = u"%s" % (_(u"The category ascription of the asset category modified in line %s must be the same")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    if 'quantity' in dDict:
                        del dDict['quantity'] #更新不允许改变数量。
                    if 'UserID' in dDict:
                        del dDict['UserID'] #更新不修改当前使用人员。
                    dDict['whereid'] = dt.id
                    update_sql_dict.append(dDict)
                else:
                    if int(class_id[0].ascription) == 0:
                        if 'quantity' not in dDict: # 固资为空的时候数量默认为1
                            dDict['quantity'] = 1
                        if 'UserID' in dDict:
                            dDict['status'] = 1
                        else:
                            dDict['status'] = 0
                        dDict['quantity'] = 1
                    else:
                        if 'quantity' not in dDict: # 易耗资产为空的数量默认为1
                            dDict['quantity'] = 0
                        if 'UserID' in dDict:
                            del dDict['UserID']  # 易耗不修改当前使用人员。
                        if 'quantity' in dDict:  # .has_key('purchase_time'):
                            try:
                                dDict['quantity'] = int(dDict['quantity'])
                                if dDict['quantity'] < 0:
                                    s = u"%s" % (_(u"Unit of measurement of line% s must be greater than or equal to 0")) % r
                                    cc = u"%s,%s" % (s, _(u'imported failed'))
                                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                            except:
                                s = u"%s" % (_(u"Line %s Unit of measurement format error, must be an integer")) % r
                                cc = u"%s,%s" % (s, _(u'imported failed'))
                                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                        if dDict['quantity'] > 0:
                            dDict['status'] = 11  # 有库存
                        else:
                            dDict['status'] = 12  # 无库存，状态改为在用
                    dDict['create_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    if 'UserID' in dDict:
                        empobj = employee.objects.filter(PIN=dDict['UserID'])
                        if len(empobj) > 0:
                            dDict['UserID'] = empobj[0].id
                        else:
                            s = u"%s" % (_(u"Line %s current user must exist")) % r
                            cc = u"%s,%s" % (s, _(u'imported failed'))
                            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    insert_sql_dict.append(dDict)
        for uuu in update_sql_dict:
            sql, params = getSQL_update_new('asset', uuu)
            if customSqlEx(sql, params):
                i_update += 1
        for iii in insert_sql_dict:
            if auto_generate_flag:
                iii['code'] = auto_generate_asset_code(iii['class_id'])
            sql, params = getSQL_insert_new('asset', iii)
            try:
                if customSqlEx(sql, params):
                    i_insert += 1
                    asset_obj = Asset.objects.get(code=iii['code'])
                    AssetChangeLog(
                        asset=asset_obj,
                        UserID=None,
                        operator=request.user.get_username(),
                        operate_type=0,
                        quantity=iii['quantity'],
                        current_quantity=iii['quantity'],
                        optime=datetime.datetime.now()
                    ).save()

                    code_tuple = iii['code'].rpartition('-')
                    asset_class_obj = AssetClass.objects.filter(symbol=code_tuple[0], deltag=0)
                    if code_tuple[2].isdigit() and asset_class_obj:
                        attach_code = int(code_tuple[2])
                        if attach_code > asset_class_obj[0].attach_code:
                            asset_class_obj[0].attach_code = attach_code
                        asset_class_obj[0].save()
                    if 'UserID' in iii:
                        from mysite.asset.utils import generate_document_number
                        document_number = generate_document_number('R')
                        emp_obj = employee.objByID(iii['UserID'])
                        asset_obj = Asset.objects.get(code = iii['code'])
                        st = datetime.datetime.now()
                        recipient_obj = Recipient.objects.create(
                            document_number=document_number,
                            UserID=emp_obj,
                            recipient_time=st,
                            remark=_(u"Import"),
                            operator=request.user.get_username(),
                            status=1,
                            place=''
                        )
                        RecipientList.objects.create(
                            recipient=recipient_obj,
                            asset=asset_obj,
                            quantity=1
                        )
                        AssetChangeLog(asset=asset_obj,
                                       UserID=emp_obj,
                                       operator=request.user.get_username(),
                                       operate_type=1,
                                       quantity=1,
                                       optime=datetime.datetime.now(),
                                       document_number=document_number
                                       ).save()

            except Exception as e:
                estr = "%s" % e
                if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or (
                        "Duplicate entry" in estr) or ("unique constraint" in estr):
                    i_rep += 1
                else:
                    errorRecs.append(row)
        result = reportError(u"%s" % _("Asset information"), jjj, i_insert, i_update, i_rep, errorRecs)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=AssetClass._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        # t = u"%s"%(_(u"The %s line data is incorrect")) %jjj
        cc = u"%s,%s" % (e, _("department&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


@login_required
def import_retiresheet(request):
    try:
        imFields = ["asset_id", "remark", "optime"]#必填字段
        imFieldsInfo = {}
        imDatalist = []
        sUserids = []
        i_insert = 0
        i_update = 0
        i_rep = 0
        errorRecs = []

        ispass = request.POST.get("pass", "2")
        status = 0
        if ispass == '1':
            status = 1
        oriFields = request.POST["fields"].split(',')
        for t in oriFields:
            fldName = request.POST.get(t, "")
            if fldName == 'on':
                imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
            except:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import retire asset list"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        # ext=fName[fName.find('.')+1:].upper()
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            ncols = sh.ncols
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        insert_sql_dict = []
        update_sql_dict = []
        attach_code = {}
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            dDict = {}
            if ext == "TXT":
                ls = t.split('\t')
            elif ext == 'CSV':
                ls = t.split(',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 22:
                        continue
                except:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        dDict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        dDict[k] = ls[int(v) - 1]
            if dDict != {}:
                jjj += 1
                if ('asset_id' not in dDict or not dDict['asset_id']) or ('remark' not in dDict or not dDict['remark'])or\
                        ('optime' not in dDict or not dDict['optime']):
                    s = u"%s" % (_(u"Line %s required item cannot be empty")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                # if not re.search('^[a-zA-Z0-9-]{1,20}$', dDict['asset_id']):
                #     s = u"%s" % (_(
                #         u"Line %s only letters or numbers or '-' are allowed, and the string length cannot exceed 20")) % r
                #     cc = u"%s,%s" % (s, _(u'Import failed'))
                #     return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                try:
                    asset = Asset.objects.get(code=dDict['asset_id'])
                except:
                    s = u"%s" % (_(u"Line %s asset is not exist")) % r
                    cc = u"%s,%s" % (s, _('imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if asset.status != 0:
                    s = u"%s" % (_(u"Line %s is not Idle assets")) % r
                    cc = u"%s,%s" % (s, _('imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if 'optime' in dDict:  # .has_key('optime'):
                    try:
                        optime = datetime.datetime.strptime(dDict['optime'], "%Y-%m-%d")
                    except:
                        import traceback
                        traceback.print_exc()
                        s = u"%s" % (_(u"Incorrect retire time format on line %s, the retire time cell format needs to be text. ")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                dDict['asset_id'] = asset.id
                dDict['liquidator'] = request.user.username
                dDict['status'] = status
                if asset.status == 4:
                    cc = u"%s,%s" % (_('imported failed'), _(u"The asset has been scrapped, repeat operation!"))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                try:
                    retire = RetireSheet.object.get(asset__id=dDict['asset_id'])
                    update_sql_dict.append(dDict)
                except:
                    insert_sql_dict.append(dDict)

        for uuu in update_sql_dict:
            sql, params = getSQL_update_new('asset_retiresheet', uuu)
            if customSqlEx(sql, params):
                i_update += 1
        for iii in insert_sql_dict:
            from mysite.asset.utils import generate_document_number
            iii['document_number'] = generate_document_number('E')
            iii['apply_time'] = iii['optime']
            if status == 0:
                del iii['optime']
                iii['liquidator'] = ''
            else:
                iii['optime'] = datetime.datetime.now().strftime('%Y-%m-%d')
            sql, params = getSQL_insert_new('asset_retiresheet', iii)
            try:
                if customSqlEx(sql, params):
                    i_insert += 1
                if status == 1:
                    asset = Asset.objects.get(id=iii['asset_id'])
                    asset.status = 4
                    asset.save()
                    AssetChangeLog(asset=asset,
                                   operator=request.user,
                                   operate_type=10,
                                   quantity=1,
                                   optime=datetime.datetime.now(),
                                   document_number=iii['document_number']
                                   ).save(force_insert=True)
            except Exception as e:
                estr = "%s" % e
                if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or (
                        "Duplicate entry" in estr) or ("unique constraint" in estr):
                    i_rep += 1
                else:
                    errorRecs.append(row)
        result = reportError(u"%s" % _("Asset information"), jjj, i_insert, i_update, i_rep, errorRecs)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=RetireSheet._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        cc = u"%s,%s" % (e, _("department&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


@login_required
def import_retreat(request):
    try:
        from mysite.asset.utils import generate_document_number
        from mysite.iclock.dataview import staDataRetreat

        imFields = ["asset_id", "dept_id", "optime"]  # 必填字段
        imFieldsInfo = {}
        i_insert = 0
        i_update = 0
        i_rep = 0
        errorRecs = []

        oriFields = request.POST["fields"].split(',')
        for t in oriFields:
            fldName = request.POST.get(t, "")
            if fldName == 'on':
                imFields.append(t)
        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
            except:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import retire asset list"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        # ext=fName[fName.find('.')+1:].upper()
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            for i in range(0, nrows):
                sv = []
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0
        insert_sql_dict = []
        update_sql_dict = []
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            dDict = {}
            if ext == "TXT":
                ls = t.split('\t')
            elif ext == 'CSV':
                ls = t.split(',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 22:
                        continue
                except:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        dDict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        dDict[k] = ls[int(v) - 1]
            if dDict != {}:
                jjj += 1
                if ('asset_id' not in dDict or not dDict['asset_id']) or \
                        ('dept_id' not in dDict or not dDict['dept_id']) or \
                        ('optime' not in dDict or not dDict['optime']):
                    s = u"%s" % (_(u"Line %s required item cannot be empty")) % r
                    cc = u"%s,%s" % (s, _(u'Import failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                # 资产
                try:
                    asset = Asset.objects.get(code=dDict['asset_id'])
                except:
                    s = u"%s" % (_(u"Line %s asset is not exist")) % r
                    cc = u"%s,%s" % (s, _('imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                if asset.status != 1:
                    s = u"%s" % (_(u"The asset in line %s is a non in use asset")) % r
                    cc = u"%s,%s" % (s, _('imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                # 部门
                dept_id = department.objects.filter(DeptNumber=dDict['dept_id'])
                if len(dept_id) > 0:
                    dDict['dept_id'] = dept_id[0].DeptID
                else:
                    s = u"%s" % (_(u"Line %s attribution department must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                # 退库时间
                try:
                    dDict['optime'] = datetime.datetime.strptime(dDict['optime'], "%Y-%m-%d %H:%M:%S")
                except:
                    s = u"%s" % (_(u"The format of the return time in line %s is incorrect")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                # 区域
                if 'area_id' in dDict:
                    area_id = zone.objects.filter(code=dDict['area_id']).exclude(DelTag=1)
                    if len(area_id) > 0:
                        dDict['area_id'] = area_id[0].id
                    else:
                        s = u"%s" % (_(u"Line %s storage area must exist")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

                # 存放地点
                if 'place' not in dDict:
                    dDict['place'] = ''

                dDict['asset_id'] = asset.id
                dDict['userid'] = asset.UserID.pk
                dDict['type'] = 2
                dDict['status'] = 0

                try:
                    retreat_obj = RetreatList.objects.get(asset__id=dDict['asset_id'], retreat__status=0)
                    s = u"%s" % (_(u"Asset in line %s has a pending approval return record")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                except:
                    insert_sql_dict.append(dDict)
        for iii in insert_sql_dict:
            iii['document_number'] = generate_document_number('D')
            asset_id = iii.pop('asset_id')
            sql, params = getSQL_insert_new('asset_retreat', iii)
            try:
                if customSqlEx(sql, params):
                    retreat = Retreat.objects.get(document_number=iii['document_number'])
                    i_insert += 1

                    retreat_list_insert_dict = {'retreat_id': retreat.id,
                                                'asset_id': asset_id,
                                                'quantity': 1}
                    retreat_list_insert__sql, params = getSQL_insert_new('asset_retreatlist', retreat_list_insert_dict)
                    customSqlEx(retreat_list_insert__sql, params)
                    staDataRetreat(request, Retreat, 1, [retreat.id])
            except Exception as e:
                estr = "%s" % e
                if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or (
                        "Duplicate entry" in estr) or ("unique constraint" in estr):
                    i_rep += 1
                else:
                    errorRecs.append(row)
        result = reportError(u"%s" % _("Asset information"), jjj, i_insert, i_update, i_rep, errorRecs)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=RetireSheet._meta.verbose_name, object=fName, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        cc = u"%s,%s" % (e, _("department&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


@login_required
def new_save_batch_add_asset(request):
    '''
    批量新增资产
    :param request:前端请求
    :return: ret（0代表新增成功，1新增失败），messgae：提示信息
    '''
    from mysite.iclock.datautils import GetModel
    from mysite.iclock.dataview import DataNewPost
    dataModel = GetModel('Asset', 'asset')
    asset_num = request.POST.get("asset_num")
    asset_tid = request._post.get("tid")
    try:
        asset_num = int(asset_num)
        if asset_num <= 0:
            cc = u"%s" % (_("Batch new quantity must be greater than 0"))
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    except Exception as e:
        cc = u"%s" % (_("The quantity added in batch must be an integer"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    if asset_tid:
        cc = u"%s" % (_("Batch add, label TID can only be empty"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    class_id = request.POST.get("class_id")
    dept_id = request.POST.get("dept_id")
    i = 0
    asset_class_obj = AssetClass.objByID(class_id)
    succeed_code_list = []
    if not asset_class_obj:
        cc = u"%s" % (_("Asset category does not exist!"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    if asset_class_obj.ascription != '0':
        cc = u"%s" % (_("Batch add must be fixed assets!"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    dept_obj = department.objByID(dept_id)
    if not dept_obj:
        cc = u"%s" % (_("Attribution department does not exist!"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    # QueryDict设置为可以修改
    request.POST._mutable = True
    while i < asset_num:
        try:
            # 编号自动生成
            request.POST['code'] = auto_generate_asset_code(class_id)
            asset_obj = Asset.objects.filter(code=request.POST['code'])
            if asset_obj:
                continue
            save_result = DataNewPost(request, dataModel)
            save_result = loads(save_result.content)
            if 'ret' in save_result and save_result['ret'] != 0:
                # 保存失败，删除刚刚生成的资产记录和入库记录
                succeed_asset_obj = Asset.objects.filter(code__in=succeed_code_list)
                succeed_asset_obj.delete()
                AssetChangeLog.objects.filter(asset__code__in=succeed_code_list).delete()
                cc = u"%s,%s" % (e, _("Batch adding failed"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        except Exception as e:
            # import traceback
            # traceback.print_exc()
            # 保存失败，删除刚刚生成的资产记录和入库记录
            succeed_asset_obj = Asset.objects.filter(code__in=succeed_code_list)
            succeed_asset_obj.delete()
            AssetChangeLog.objects.filter(asset__code__in=succeed_code_list).delete()
            cc = u"%s,%s" % (e, _("Batch adding failed"))
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        succeed_code_list.append(request.POST['code'])
        i += 1
    return getJSResponse({"ret": 0, "message": u"%s" % (_("Batch adding succeeded"))}, mtype="text/plain")


@login_required
def save_batch_add_asset(request):
    asset_num = request.POST.get("asset_num")
    try:
        asset_num = int(asset_num)
        if asset_num <= 0:
            cc = u"%s" % (_("Batch new quantity must be greater than 0"))
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    except Exception as e:
        cc = u"%s" % (_("Batch new quantity must be greater than 0"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    name = request.POST.get("name")
    class_id = request.POST.get("class_id")
    dept_id = request.POST.get("dept_id")
    i = 0
    asset_class_obj = AssetClass.objByID(class_id)
    succeed_code_list = []
    if not asset_class_obj:
        cc = u"%s" % (_("Asset category does not exist!"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    if asset_class_obj.ascription != '0':
        cc = u"%s" % (_("Batch add must be fixed assets!"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    dept_obj = department.objByID(dept_id)
    if not dept_obj:
        cc = u"%s" % (_("Attribution department does not exist!"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    while i < asset_num:
        code = auto_generate_asset_code(class_id)
        asset_obj = Asset.objects.filter(code=code)
        if asset_obj:
            continue
        try:
            Asset(
                code=code,
                name=name,
                class_id=asset_class_obj,
                dept_id=dept_obj).save()
        except Exception as e:
            succeed_asset_obj = Asset.objects.filter(code__in=succeed_code_list)
            succeed_asset_obj.delete()
            cc = u"%s,%s" % (e, _("Batch adding failed"))
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        succeed_code_list.append(code)
        i += 1
    for i in succeed_code_list:
        asset_obj = Asset.objects.get(code=i)
        AssetChangeLog(
            asset=asset_obj,
            UserID=None,
            operator=request.user,
            operate_type=0,
            quantity=1,
            optime=datetime.datetime.now()
        ).save()
    return getJSResponse({"ret": 0, "message": u"%s" % (_("Batch adding succeeded"))}, mtype="text/plain")

@login_required
def get_asset_code(request):
    class_id = request.POST.get("class_id")
    asset_code  = auto_generate_asset_code(class_id)
    asset_class = AssetClass.objects.get(id=class_id).ascription
    return getJSResponse({"ret": 0, "asset_code": asset_code, "asset_class": asset_class}, mtype="text/plain")

@login_required
def BorrowSheetRetuen(request):
    AssetId = request.GET.get('AssetId')
    BorrowSheetId = request.GET.get('BorrowSheetId')
    if AssetId:
        borrowsheet_obj = BorrowList.objects.filter(asset=AssetId).filter(borrowsheet__borrowing_status=1).first().borrowsheet
    if BorrowSheetId:
        borrowsheet_obj = BorrowList.objects.filter(borrowsheet=BorrowSheetId).first().borrowsheet
    if borrowsheet_obj:
        data = {}
        BorrowSheetAssetList = []
        username = borrowsheet_obj.UserID.EName
        userpin = borrowsheet_obj.UserID.PIN
        BorrowSheetData = {
            'id':borrowsheet_obj.id,
            'document_number':borrowsheet_obj.document_number,
            'start_ime':borrowsheet_obj.start_ime.strftime("%Y-%m-%d"),
            'username':username,
            'userpin':userpin,
            'planned_end_time':borrowsheet_obj.planned_end_time.strftime("%Y-%m-%d"),
            'operator':borrowsheet_obj.operator or '',
            'remark':borrowsheet_obj.remark or ''
        }
        data['BorrowSheetData'] = BorrowSheetData
        BorrowSheetAssetObj = BorrowList.objects.filter(borrowsheet=borrowsheet_obj).values_list('asset_id','asset__code','asset__name','asset__place','asset__specifications','asset__sn')
        for i in BorrowSheetAssetObj:
            d={}
            asset_id,code,name,place,specifications,sn = i[0],i[1],i[2],i[3],i[4],i[5]
            d['asset_id'] = asset_id
            d['code'] = code
            d['name'] = name
            d['place'] = place or ''
            d['specifications'] = specifications or ''
            d['sn'] = sn or ''
            BorrowSheetAssetList.append(d.copy())
        data['BorrowSheetAssetList'] = BorrowSheetAssetList

    return getJSResponse(data, mtype="text/plain")
@login_required
def InventoryImportAsset(request):
    try:
        imFields = ['state','code']
        imFieldsInfo = {}
        InventoryId = request.GET.get("InventoryId",'')
        Checked = request.POST["Checked"]
        UnChecked = request.POST["UnChecked"]
        oriFields = request.POST["fields"].split(',')
        fromline = request.POST.get("rowid2", "1")
        succeedCount = 0
        inventory_sheet = InventorySheet.objects.filter(id__exact=InventoryId, status=1).first()
        if not inventory_sheet:
            cc = "{}{}{}{}".format(_("Import Inventory Sheet data Failed"),",",_("The current inventory counting sheet is not approved and cannot be counted"),"</p><pre>")
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        if inventory_sheet.check_status == 2:
            cc = "{}{}{}{}".format(_("Import Inventory Sheet data Failed"),",",_("This operation is not allowed because the current counting sheet has ended!"),"</p><pre>")
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        if fromline == "":
            fromline = 1
        fromline = int(fromline)
        for t in oriFields:
            fldName = request.POST.get(t, "")
            if fldName == 'on':
                imFields.append(t)
        maxCol = 0
        for t in imFields:
            try:
                imFieldsInfo[t] = int(request.POST.get(t + "2file", "-1"))
                if maxCol < imFieldsInfo[t]: maxCol = imFieldsInfo[t]
            except:
                cc = "{}{}".format(_("Import Inventory Sheet data Failed"),"</p><pre>")
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import Inventory Sheet List"), "content": cc})
        f = request.FILES["fileUpload"]
        data = b""
        fName = f.name
        whatrow = request.POST["whatrowid2file"]
        ext = ''
        fl = fName.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX': ext = 'XLS'
        zhPattern = re.compile(u'[\u4e00-\u9fa5]+')
        lines = []
        for chunk in f.chunks(): data += chunk
        if ext == 'TXT' or ext == 'CSV':
            cc = u'%s'%_('Unsupported file format, please use the specified file format to upload')
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), fName)
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheetNames = bk.sheet_names()
            if not sheetNames:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            try:
                sh = bk.sheet_by_index(0)
            except:
                s = "no sheet in %s named %s" % (fn, sheetNames[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows
            for i in range(0, nrows):
                sv = []
                c = 0
                for sr in sh.row_values(i):
                    if type(sr) == type(1.0):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                    c = c + 1
                    if c > maxCol - 1: break
                row_data = sv
                lines.append(row_data)
        jjj = 0
        jj = 0
        r = 0
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            jj += 1
            if jj < fromline: continue
            dDict = {}
            if ext == "TXT":
                ls = t.split(b'\t')
            elif ext == 'CSV':
                ls = t.split(b',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in imFieldsInfo.items():
                try:
                    if v < 1 or v > 100:
                        continue
                except:
                    continue
                v = int(v)
                if ls[v - 1] != None:
                    ls[v - 1] = ls[v - 1].strip()
                if k == "state":
                    if v <= len(ls):
                        if ext == 'XLS' or ext == 'XLSX':
                            s = ls[v - 1]
                        else:
                            s = getStr_c_decode(ls[v - 1])
                        if s == Checked:
                            ls[v - 1] = 1
                        elif s == UnChecked:
                            ls[v - 1] = 0
                        else:
                            ls[v - 1] = ""
                if v <= len(ls):
                    s = ls[int(v) - 1]
                    if ext == 'XLS' or ext == 'XLSX':
                        dDict[k] = s
                    else:
                        dDict[k] = getStr_c_decode(s)
            if dDict:
                jjj += 1
                asset_id = Asset.objects.filter(code=dDict['code']).first()
                if asset_id:
                    InventoryObj = InventoryList.objects.filter(inventory_sheet_id=int(InventoryId),asset_id=asset_id.id).first()
                    if InventoryObj:
                        if dDict['state'] != "":
                            InventoryObj.status = dDict['state']
                            InventoryObj.save()
                        succeedCount+=1
                else:
                    cc = u"<h1>%s</h1><br/>%s %s %s" % (_("Import Asset Inventory data List"), _("Product Number is"), dDict['code'],_("doesn`t exist")) + "</p><pre>"
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        cc = u"<h1>%s</h1><br/>%s%s" % (_("Import Asset Inventory data List"), _("Number of successful imports:"),succeedCount) + "</p><pre>"
        InventorySheetObj = InventorySheet.objects.filter(id=int(InventoryId)).first()
        InventorySheetObj.check_status = 1
        InventorySheetObj.save()
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
    except Exception as e:
        import traceback
        traceback.print_exc()
        cc = u"<h1>%s</h1><br/>%s" % (_("Import Asset Inventory data List"), _("imported failed")) + "</p><pre>"
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


@login_required
def ChangeInventoryStatus(request):
    InventoryId = request.GET.get("InventoryId","")
    AssetId = request.GET.get("AssetId","")
    AssetCode = request.GET.get("AssetCode","")
    if AssetCode:
        try:
            d = Asset.objects.get(code=AssetCode)
            AssetId = d.id
        except:
            return getJSResponse({"ret": -1, "msg": u"%s" % (
                _("Asset code does not exist"))},
                                 mtype="text/plain")
    inventory_sheet = InventorySheet.objects.filter(id__exact=InventoryId,status=1).first()
    if not inventory_sheet:
        return getJSResponse({"ret": -1, "msg": u"%s"%(_("The current inventory counting sheet is not approved and cannot be counted" )) }, mtype="text/plain")
    if inventory_sheet.check_status == 2:
        return getJSResponse({"ret": -1, "msg": u"%s"%(_("This operation is not allowed because the current counting sheet has ended!" ))}, mtype="text/plain")
    if AssetId == '':
        inventory_sheet.check_status = 2
        inventory_sheet.save()
        return getJSResponse({"ret": 1, "state": ''}, mtype="text/plain")
    InventoryListObj = InventoryList.objects.filter(inventory_sheet_id=InventoryId,asset_id=AssetId).first()
    if InventoryListObj:
        status = InventoryListObj.status
        if status == 0:
            InventoryListObj.status = 1
        elif status == 1 and AssetCode:
            return getJSResponse({"ret": -1, "msg": u"%s" % (
                    _("Assets have been inventoried"))},
                                     mtype="text/plain")
        else:
            InventoryListObj.status = 0
        if inventory_sheet.check_status == 0:
            inventory_sheet.check_status = 1
            inventory_sheet.save()
        InventoryListObj.save()
        status = InventoryListObj.status
        return getJSResponse({"ret": 1, "state": status }, mtype="text/plain")
    else:
        return getJSResponse({"ret": -1, "msg": u"%s" % (_("The inventory list does not contain the asset" )) }, mtype="text/plain")

from django.utils.translation import gettext

LISTSTATUS = {
    '0':gettext("Unchecked"),
    '1':gettext("Checked")
}
@login_required
def InventorySheet_detail(request):
    id = request.GET.get("id","")
    InventoryDept = request.GET.get("InventoryDept","")
    InventoryClass = request.GET.get("InventoryClass","")
    screen = request.GET.get("screen","")
    statesort = request.GET.get("statesort","")
    page = int(request.GET.get("page",""))
    limit = int(request.GET.get("limit",""))
    exporttblName = request.GET.get("exporttblName","")
    cc = []
    try:
        if statesort == 'true':
            statesort = '-status'
        else:
            statesort = 'status'
        InventoryListObj = InventoryList.objects.filter(inventory_sheet_id=id).order_by(statesort,'inventory_sheet_id')
        asset_idList = InventoryListObj.values_list('asset_id',flat=True)
        assetObj = Asset.objects.filter(id__in=asset_idList)
        dept = {}
        for j in assetObj:
            if j.dept_id:
                dept[j.dept_id.DeptID] = j.dept_id.DeptName
        Classify = {}
        for c in assetObj:
            Classify[c.class_id.id] = c.class_id.name
        if InventoryDept:
            assetObj = assetObj.filter(dept_id=InventoryDept)
        if InventoryClass:
            assetObj = assetObj.filter(class_id=InventoryClass)
        if screen == 'asset':
            assetid = assetObj.order_by('code').values_list('id', flat=True)
            InventoryListObj = InventoryListObj.filter(asset_id__in=assetid).order_by(statesort,'asset_id')
        else:
            assetid = assetObj.values_list('id', flat=True)
            ChangeLogassetid = AssetChangeLog.objects.filter(asset__in=assetid).order_by('UserID__DeptID','UserID').values_list('asset', flat=True)

            InventoryListObj = InventoryListObj.filter(Q(asset_id__in=ChangeLogassetid) | Q(asset_id__in=assetid)).order_by(statesort,'asset_id')
        count = InventoryListObj.count()
        UnInventory = InventoryListObj.filter(status=0).count()
        HasInventory = count - UnInventory
        if exporttblName == '':
            InventoryListObj = InventoryListObj[limit * (page): (page + 1) * limit]
        for i in InventoryListObj.values_list('asset_id','status'):
            asset_id,status = i[0],i[1]
            d = {}
            assetObj = Asset.objects.get(id=asset_id)
            d['id'] = assetObj.id
            d['code'] = assetObj.code
            d['class'] = assetObj.class_id.name or ''
            d['name'] = assetObj.name or ''
            if assetObj.dept_id:
                d['DeptName'] = assetObj.dept_id.DeptName
            else:
                d['DeptName'] = ''
            if assetObj.UserID:
                d['EName'] = assetObj.UserID.EName
                d['PIN'] = assetObj.UserID.PIN
            else:
                d['EName'] = ''
                d['PIN'] = ''
            d['place'] = assetObj.place or ''
            d['unit'] = assetObj.unit or ''
            d['quantity'] = assetObj.quantity or ''
            d['specifications'] = assetObj.specifications or ''
            d['state'] = status
            d['operate'] = request.user.username
            if exporttblName == 'InventorySheet':
                d['state'] = LISTSTATUS[str(status)]
            cc.append(d)
        TotalCount = {
            'UnInventory':UnInventory,
            'HasInventory':HasInventory,
            'count':count
        }
        data = {
            'TotalCount':TotalCount,
            'AssetData':cc,
            'dept':dept,
            'Classify':Classify
        }
        if exporttblName == 'InventorySheet':
            return cc
        return getJSResponse({"ret": 1, "data": data}, mtype="text/plain")
    except Exception as e:
        cc = u"%s" % e
        return getJSResponse({"ret": 0, "message": cc}, mtype="text/plain")


@login_required
def import_salary(request):
    '''
    薪资导入
    :param request:
    :return:
    '''
    try:
        im_fields = ['pin']  # 必须导入的字段
        im_fields_info = {}  # 字段名对应的excel第几列
        error_recs = []
        i_insert = 0
        i_update = 0
        i_rep = 0
        ori_fields = request.POST["fields"].split(',')  # 所有字段名
        salary_set_id = request.POST["salary_set_id"]  # 账套id
        field_name = {}  # 字段对应字段名
        salary_items = Item.objects.filter(data_source=0, status=0)  # 显示的薪资项，并且为可以导入的
        for salary_item in salary_items:
            field = salary_item.code.lower()
            field_name[field] = salary_item.name
        for t in ori_fields:
            fld_name = request.POST.get(t, "")
            if fld_name == 'on':
                # 检查字段除了默认勾选的字段是否被勾选，如果已经勾选加入必须导入字段。
                im_fields.append(t)
        # 验证账套是否是否存在
        ssobj = SalarySet.objects.filter(id=salary_set_id)
        if not ssobj:
            s = u"%s" % (_(u"The imported salary set must exist")) % r
            cc = u"%s,%s" % (s, _(u'imported failed'))
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        else:
            ssobj = ssobj[0]
        for t in im_fields:
            try:
                # 字段名对应的excel第几列
                im_fields_info[t] = int(request.POST.get(t + "2file", "-1"))
            except Exception as e:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import asset category list"), "content": cc})
        f = request.FILES["fileUpload"]  # 获取文件
        data = b""
        f_name = f.name
        whatrow = request.POST["whatrowid2file"]  # 第几行开始导入
        ext = ''
        fl = f_name.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX' : ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            lines = data.splitlines()
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), f_name)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheet_names = bk.sheet_names()
            if not sheet_names:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except Exception as e:
                s = "no sheet in %s named %s" % (fn, sheet_names[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows  # 导入数据行数
            ncols = sh.ncols  # 导入数据列数
            for i in range(0, nrows):
                # 获取每一行的数据，放到lines列表
                sv = []
                for si, sr in enumerate(sh.row_values(i)):
                    if isinstance(sr, float):
                        if si == 0:
                            # 第一列是人员编号去掉小数点转为字符串
                            sr = str(int(sr))
                            if sr.find(".0") != '-1':
                                sr = sr.split(".")[0]
                            else:
                                sr = sr
                        else:
                            sr = str(sr)
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0  # 验证第几行
        insert_sql_dict = []  # 验证过的要新增的数据字典
        update_sql_dict = []  # 验证过的要更新的数据字典
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            d_dict = {}
            if ext == "TXT":
                ls = t.split('\t')
            elif ext == 'CSV':
                ls = t.split(',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in im_fields_info.items():
                try:
                    if v < 1 or v > 55:
                        # 55列以后的值不导入
                        continue
                except Exception as e:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        d_dict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        d_dict[k] = ls[int(v) - 1]
            if d_dict != {}:
                jjj += 1
                for k, v in d_dict.items():
                    if k in ('pin'):
                        continue
                    if not v:
                        # 如果没有值删掉
                        del d_dict[k]
                        # s = u"%s" % (_(u"Line %s %s must be filled")) % (r, field_name[k])
                        # cc = u"%s,%s" % (s, _(u'Import failed'))
                        # return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                    try:
                        d_dict[k] = Decimal(v)
                    except Exception as e:
                        s = u"%s" % (_(u"The data format in line %s is incorrect")) % r
                        cc = u"%s,%s" % (s, _(u'imported failed'))
                        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                # 验证人员是否存在
                try:
                    empobj = employee.objects.get(PIN=d_dict['pin'])
                except employee.DoesNotExist as e:
                    empobj = None
                if not empobj:
                    s = u"%s" % (_(u"Employee in line %s must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                del d_dict['pin']
                # 验证账套人员是否存在
                try:
                    sse = SalarySetEmployee.objects.get(salary_set=ssobj, employee=empobj)
                except SalarySetEmployee.DoesNotExist as e:
                    sse = None
                if not sse:
                    s = u"%s" % (_(u"The account set personnel in line %s must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                # 获取账套数据
                try:
                    ssd = SalarySetData.objects.get(salary_set=ssobj, employee=empobj)
                except SalarySetData.DoesNotExist as e:
                    ssd = None
                if ssd:
                    d_dict['ssd_id'] = ssd.id
                    update_sql_dict.append(d_dict)
                else:
                    d_dict['salary_set'] = ssobj
                    d_dict['employee'] = empobj
                    insert_sql_dict.append(d_dict)
        for uuu in update_sql_dict:
            ssd_id = uuu['ssd_id']
            del uuu['ssd_id']
            SalarySetData.objects.filter(id=ssd_id).update(**uuu)
            i_update += 1
        for iii in insert_sql_dict:
            try:
                SalarySetData.objects.create(**iii)
                i_insert += 1
            except Exception as e:
                estr = "%s" % e
                if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or (
                        "Duplicate entry" in estr) or ("unique constraint" in estr):
                    i_rep += 1
                else:
                    error_recs.append(t)
        result = reportError(u"%s" % _("Salary import information"), jjj, i_insert, i_update, i_rep, error_recs)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=SalarySetData._meta.verbose_name, object=f_name, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        import traceback
        traceback.print_exc()
        # t = u"%s"%(_(u"The %s line data is incorrect")) %jjj
        cc = u"%s,%s" % (e, _("department&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")


@login_required
def import_attendance(request):
    '''
    考勤导入
    :param request:
    :return:
    '''
    try:
        im_fields = ['pin']  # 必须导入的字段
        im_fields_info = {}  # 字段名对应的excel第几列
        error_recs = []
        i_insert = 0
        i_update = 0
        i_rep = 0
        ori_fields = request.POST["fields"].split(',')  # 所有字段名
        salary_set_id = request.POST["salary_set_id"]  # 账套id
        for t in ori_fields:
            fld_name = request.POST.get(t, "")
            if fld_name == 'on':
                # 检查字段除了默认勾选的字段是否被勾选，如果已经勾选加入必须导入字段。
                im_fields.append(t)
        # 验证账套是否是否存在
        ssobj = SalarySet.objects.filter(id=salary_set_id)
        if not ssobj:
            s = u"%s" % (_(u"The imported salary set must exist")) % r
            cc = u"%s,%s" % (s, _(u'imported failed'))
            return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
        else:
            ssobj = ssobj[0]
        for t in im_fields:
            try:
                # 字段名对应的excel第几列
                im_fields_info[t] = int(request.POST.get(t + "2file", "-1"))
            except Exception as e:
                cc = _("department&#39;s data imported failed") + "</p><pre>"
                cc += request.POST.get(t + "2file", "-1")
                return render("info.html", {"title": _("Import asset category list"), "content": cc})
        f = request.FILES["fileUpload"]  # 获取文件
        data = b""
        f_name = f.name
        whatrow = request.POST["whatrowid2file"]  # 第几行开始导入
        ext = ''
        fl = f_name.split('.')
        if fl:
            ext = fl[-1].upper()
        if ext == 'XLSX' : ext = 'XLS'
        for chunk in f.chunks():
            # data定义修改为b""，因python3下会报can only concatenate str (not "bytes") to str
            data += chunk
        lines = []
        if ext == 'TXT' or ext == 'CSV':
            lines = data.splitlines()
        elif ext == 'XLS' or ext == 'XLSX':
            import xlrd
            fn = "%s/%s" % (tmpDir(), f_name)
            # 这里引用codecs的作用并不是编码转换,这里实际问题为__builtin__引用问题(Python3下为builtins),__builtin__.open()是正常的,仅open不正常
            with codecs.open(fn, 'wb') as f:
                f.write(data)
            bk = xlrd.open_workbook(fn)
            sheet_names = bk.sheet_names()
            if not sheet_names:
                cc = u"%s,%s" % (_('imported failed'), _("No Sheet"))
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")

            shxrange = range(bk.nsheets)
            try:
                sh = bk.sheet_by_index(0)
            except Exception as e:
                s = "no sheet in %s named %s" % (fn, sheet_names[0])
                cc = u"%s,%s" % (_('imported failed'), s)
                return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
            nrows = sh.nrows  # 导入数据行数
            ncols = sh.ncols  # 导入数据列数
            for i in range(0, nrows):
                # 获取每一行的数据，放到lines列表
                sv = []
                for sr in sh.row_values(i):
                    if isinstance(sr, float):
                        sr = str(int(sr))
                        if sr.find(".0") != '-1':
                            sr = sr.split(".")[0]
                        else:
                            sr = sr
                    sv.append(sr)
                row_data = sv
                lines.append(row_data)
        jjj = 0
        r = 0  # 验证第几行
        insert_sql_dict = []  # 验证过的要新增的数据字典
        update_sql_dict = []  # 验证过的要更新的数据字典
        for t in lines:
            r += 1
            if int(whatrow) > r:
                continue
            d_dict = {}
            if ext == "TXT":
                ls = t.split('\t')
            elif ext == 'CSV':
                ls = t.split(',')
            elif ext == 'XLS' or ext == 'XLSX':
                ls = t
            for k, v in im_fields_info.items():
                try:
                    if v < 1 or v > 55:
                        # 55列以后的值不导入
                        continue
                except Exception as e:
                    continue
                if v <= len(ls):
                    if ext != 'XLS' and ext != 'XLSX':
                        d_dict[k] = getStr_c_decode(ls[int(v) - 1])
                    else:
                        d_dict[k] = ls[int(v) - 1]
            if d_dict != {}:
                jjj += 1
                for k, v in d_dict.items():
                    if k in ('pin'):
                        continue
                    if not v:
                        # 如果没有值删掉
                        del d_dict[k]
                    d_dict[k] = Decimal(v)
                # 验证人员是否存在
                try:
                    empobj = employee.objects.get(PIN=d_dict['pin'])
                except employee.DoesNotExist as e:
                    empobj = None
                if not empobj:
                    s = u"%s" % (_(u"Employee in line %s must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                del d_dict['pin']
                # 验证账套人员是否存在
                try:
                    sse = SalarySetEmployee.objects.get(salary_set=ssobj, employee=empobj)
                except SalarySetEmployee.DoesNotExist as e:
                    sse = None
                if not sse:
                    s = u"%s" % (_(u"The account set personnel in line %s must exist")) % r
                    cc = u"%s,%s" % (s, _(u'imported failed'))
                    return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
                # 获取账套数据
                try:
                    ssd = SalarySetData.objects.get(salary_set=ssobj, employee=empobj)
                except SalarySetData.DoesNotExist as e:
                    ssd = None
                if ssd:
                    d_dict['ssd_id'] = ssd.id
                    update_sql_dict.append(d_dict)
                else:
                    d_dict['salary_set'] = ssobj
                    d_dict['employee'] = empobj
                    insert_sql_dict.append(d_dict)
        for uuu in update_sql_dict:
            ssd_id = uuu['ssd_id']
            del uuu['ssd_id']
            SalarySetData.objects.filter(id=ssd_id).update(**uuu)
            i_update += 1
        for iii in insert_sql_dict:
            try:
                SalarySetData.objects.create(**iii)
                i_insert += 1
            except Exception as e:
                estr = "%s" % e
                if ('IntegrityError' in estr) or ("UNIQUE KEY" in estr) or ("are not unique" in estr) or (
                        "Duplicate entry" in estr) or ("unique constraint" in estr):
                    i_rep += 1
                else:
                    error_recs.append(t)
        result = reportError(u"%s" % _("Salary import information"), jjj, i_insert, i_update, i_rep, error_recs)
        adminLog(time=datetime.datetime.now(), User_id=request.user.id, action=_("Import"),
                 model=SalarySetData._meta.verbose_name, object=f_name, count=jjj).save(force_insert=True)
        return getJSResponse({"ret": 0, "message": u"%s" % (result)}, mtype="text/plain")
    except Exception as e:
        import traceback
        traceback.print_exc()
        # t = u"%s"%(_(u"The %s line data is incorrect")) %jjj
        cc = u"%s,%s" % (e, _("department&#39;s data imported failed"))
        return getJSResponse({"ret": 1, "message": cc}, mtype="text/plain")
