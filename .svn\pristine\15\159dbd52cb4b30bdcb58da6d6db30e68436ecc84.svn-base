# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-11-07 11:03
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0062_auto_20220829_1610'),
    ]

    operations = [
        migrations.CreateModel(
            name='InterfaceLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('op_ip', models.GenericIPAddressField(null=True, verbose_name='Request IP')),
                ('op_url', models.CharField(max_length=255, verbose_name='Request URL')),
                ('op_method', models.CharField(max_length=11, verbose_name='Request Method')),
                ('op_model', models.CharField(max_length=40, verbose_name='Operating Model')),
                ('op_type', models.CharField(default='Modify', max_length=40, verbose_name='operation')),
                ('request_params', models.TextField(null=True, verbose_name='Request Parameters')),
                ('response_content', models.TextField(null=True, verbose_name='Response Content')),
                ('result_status', models.SmallIntegerField(choices=[(0, 'success'), (1, 'failure')], default=0, verbose_name='Operating State')),
                ('result_msg', models.TextField(null=True, verbose_name='Operating Result')),
                ('op_time', models.DateTimeField(verbose_name='Operation time')),
            ],
            options={
                'verbose_name': 'API Interface Log',
                'verbose_name_plural': 'API Interface Log',
                'db_table': 'interface_log',
                'default_permissions': ('browse', 'delete'),
            },
        ),
        migrations.AddField(
            model_name='attshifts',
            name='overtime_id',
            field=models.IntegerField(blank=True, db_column='overtime_id', default=0, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='attshifts',
            unique_together=set([('UserID', 'AttDate', 'SchId', 'overtime_id')]),
        ),
    ]
